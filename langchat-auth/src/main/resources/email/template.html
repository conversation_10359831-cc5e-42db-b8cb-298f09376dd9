<!--
  ~ Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
  ~
  ~ Licensed under the GNU Affero General Public License, Version 3 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     https://www.gnu.org/licenses/agpl-3.0.html
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<!--
  ~ Project: LangChat
  ~ Author: TyCoding
  ~
  ~ Licensed under the GNU Affero General Public License, Version 3 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     https://www.gnu.org/licenses/agpl-3.0.html
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Title</title>
</head>
<body>
<div style="width: 100%; height: 100%;display: flex;justify-content: center">
  <div
      style="display: flex;justify-content: center;align-items: center;flex-direction: column;margin-top: 30px">
    <div style="color: #333;font-size: 20px;padding: 4px 0;">LangChat</div>
    <div style="color: #4d4c4cf0;margin-top: 12px;margin-bottom: 8px;">The verification code for this operation is as follows：</div>
    <div
        style="background: #F4F4F4;border: 1px solid #EEEEEE;padding: 16px 24px;border-radius: 3px;width: 100%;display: flex;justify-content: center">
      {}
    </div>
    <div style="font-size: 12px; margin-top: 8px;color: #AAAAAA;">Note: This operation verification code is valid for 10 minutes</div>
    <br/>
    <div style="color: #8a8a8a">
      官网首页：
      <a href="https://github.com/tycoding/langchat" style="color: #35b378;border-bottom: 1px solid #35b378;text-decoration: none;font-weight: bold;">
        https://github.com/tycoding/langchat
      </a>
    </div>
    <div style="color: #8a8a8a;margin-top: 4px">
      Github：
      <a href="https://github.com/tycoding/langchat" style="color: #35b378;border-bottom: 1px solid #35b378;text-decoration: none;font-weight: bold;">
        https://github.com/tycoding/langchat
      </a>
    </div>
    <div style="color: #8a8a8a;margin-top: 12px;margin-bottom: 30px;">
      <a href="https://github.com/tycoding/langchat" style="color: #35b378;border-bottom: 1px solid #35b378;text-decoration: none;font-weight: bold;">
        LangChat
      </a>
      Java生态下AI大模型产品解决方案，快速构建企业级AI知识库、AI机器人应用
    </div>
  </div>
</div>
</body>
</html>
