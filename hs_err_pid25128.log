#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 536870912 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3830), pid=25128, tid=18976
#
# JRE version:  (17.0.14+1) (build )
# Java VM: OpenJDK 64-Bit Server VM (17.0.14+1-b1367.22, mixed mode, emulated-client, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:24867,suspend=y,server=n -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2025.1\captureAgent\debugger-agent.jar=file:///C:/Users/<USER>/AppData/Local/Temp/capture9134410875643509474.props -agentpath:C:\Users\<USER>\AppData\Local\Temp\idea_libasyncProfiler_dll_temp_folder9909\libasyncProfiler.dll=version,jfr,event=wall,interval=10ms,cstack=no,file=C:\Users\<USER>\IdeaSnapshots\LangChatApp_2025_08_12_192245.jfr,dbghelppath=C:\Users\<USER>\AppData\Local\Temp\idea_dbghelp_dll_temp_folder5\dbghelp.dll,log=C:\Users\<USER>\AppData\Local\Temp\LangChatApp_2025_08_12_192245.jfr.log.txt,logLevel=DEBUG -XX:TieredStopAtLevel=1 -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dmanagement.endpoints.jmx.exposure.include=* -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 cn.tycoding.langchat.LangChatApp

Host: Intel(R) Xeon(R) CPU E5-2676 v3 @ 2.40GHz, 24 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.3636)
Time: Tue Aug 12 19:22:45 2025  Windows 10 , 64 bit Build 19041 (10.0.19041.3636) elapsed time: 0.034901 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x00000278133aea50):  JavaThread "Unknown thread" [_thread_in_vm, id=18976, stack(0x00000090dae00000,0x00000090daf00000)]

Stack: [0x00000090dae00000,0x00000090daf00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x68b969]  os::platform_print_native_stack+0xd9  (os_windows_x86.cpp:235)
V  [jvm.dll+0x84572a]  VMError::report+0xc6a  (vmError.cpp:868)
V  [jvm.dll+0x8473ae]  VMError::report_and_die+0x78e  (vmError.cpp:1766)
V  [jvm.dll+0x847a13]  VMError::report_and_die+0x43  (vmError.cpp:1552)
V  [jvm.dll+0x24a83f]  report_vm_out_of_memory+0x8f  (debug.cpp:321)
V  [jvm.dll+0x688629]  os::pd_commit_memory_or_exit+0xc9  (os_windows.cpp:3838)
V  [jvm.dll+0x67cf3a]  os::commit_memory_or_exit+0x2a  (os.cpp:1768)
V  [jvm.dll+0x30b83b]  G1PageBasedVirtualSpace::commit+0x19b  (g1PageBasedVirtualSpace.cpp:204)
V  [jvm.dll+0x312d26]  G1RegionsLargerThanCommitSizeMapper::commit_regions+0x96  (g1RegionToSpaceMapper.cpp:100)
V  [jvm.dll+0x3630fe]  HeapRegionManager::expand_any+0xee  (heapRegionManager.cpp:363)
V  [jvm.dll+0x36333f]  HeapRegionManager::expand_by+0x7f  (heapRegionManager.cpp:380)
V  [jvm.dll+0x2e18cc]  G1CollectedHeap::expand+0xbc  (g1CollectedHeap.cpp:1292)
V  [jvm.dll+0x2e2824]  G1CollectedHeap::initialize+0x664  (g1CollectedHeap.cpp:1713)
V  [jvm.dll+0x815ffb]  universe_init+0xbb  (universe.cpp:737)
V  [jvm.dll+0x370e88]  init_globals+0x48  (init.cpp:131)
V  [jvm.dll+0x7f4875]  Threads::create_vm+0x5e5  (thread.cpp:2909)
V  [jvm.dll+0x3f54ef]  JNI_CreateJavaVM_inner+0x8f  (jni.cpp:3654)
V  [jvm.dll+0x3f70c1]  JNI_CreateJavaVM+0x11  (jni.cpp:3746)
C  [jli.dll+0x52ab]  JavaMain+0x113  (java.c:413)
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17344]
C  [ntdll.dll+0x526b1]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ffd8d0b9018, length=0, elements={
}

Java Threads: ( => current thread )

Other Threads:
  0x0000027816e17f80 GCTaskThread "GC Thread#0" [stack: 0x00000090daf00000,0x00000090db000000] [id=10768]
  0x0000027816e28ed0 ConcurrentGCThread "G1 Main Marker" [stack: 0x00000090db000000,0x00000090db100000] [id=17912]
  0x0000027816e2a370 ConcurrentGCThread "G1 Conc#0" [stack: 0x00000090db100000,0x00000090db200000] [id=27372]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffd8c862197]

VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x0000027816d58650] Heap_lock - owner thread: 0x00000278133aea50

Heap address: 0x0000000602400000, size: 8156 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
<Empty>

Heap:
 garbage-first heap   total 0K, used 0K [0x0000000602400000, 0x0000000800000000)
  region size 4096K, 0 young (0K), 0 survivors (0K)
 Metaspace       used 0K, committed 0K, reserved 0K
  class space    used 0K, committed 0K, reserved 0K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)

Card table byte_map: [0x000002781c260000,0x000002781d250000] _byte_map_base: 0x000002781924e000

Marking Bits (Prev, Next): (CMBitMap*) 0x0000027816e184b0, (CMBitMap*) 0x0000027816e184f0
 Prev Bits: [0x000002781e240000, 0x00000278261b0000)
 Next Bits: [0x00000278261b0000, 0x000002782e120000)

GC Heap History (0 events):
No events

Dll operation events (2 events):
Event: 0.027 Loaded shared library C:\Users\<USER>\.jdks\jbr-17.0.14\bin\java.dll
Event: 0.029 Loaded shared library C:\Users\<USER>\.jdks\jbr-17.0.14\bin\zip.dll

Deoptimization events (0 events):
No events

Classes loaded (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

VM Operations (0 events):
No events

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff657970000 - 0x00007ff65797a000 	C:\Users\<USER>\.jdks\jbr-17.0.14\bin\java.exe
0x00007ffe2a090000 - 0x00007ffe2a288000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffe290c0000 - 0x00007ffe2917d000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffe27b20000 - 0x00007ffe27e16000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffe27930000 - 0x00007ffe27a30000 	C:\Windows\System32\ucrtbase.dll
0x00007ffe0e540000 - 0x00007ffe0e557000 	C:\Users\<USER>\.jdks\jbr-17.0.14\bin\jli.dll
0x00007ffe28610000 - 0x00007ffe287ae000 	C:\Windows\System32\USER32.dll
0x00007ffe102a0000 - 0x00007ffe102bb000 	C:\Users\<USER>\.jdks\jbr-17.0.14\bin\VCRUNTIME140.dll
0x00007ffe27900000 - 0x00007ffe27922000 	C:\Windows\System32\win32u.dll
0x00007ffe28a20000 - 0x00007ffe28a4c000 	C:\Windows\System32\GDI32.dll
0x00007ffe190e0000 - 0x00007ffe1937a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.3636_none_60b6a03d71f818d5\COMCTL32.dll
0x00007ffe277e0000 - 0x00007ffe278fa000 	C:\Windows\System32\gdi32full.dll
0x00007ffe28fe0000 - 0x00007ffe2907e000 	C:\Windows\System32\msvcrt.dll
0x00007ffe27a30000 - 0x00007ffe27acd000 	C:\Windows\System32\msvcp_win.dll
0x00007ffe29f90000 - 0x00007ffe29fc0000 	C:\Windows\System32\IMM32.DLL
0x00007ffe0e530000 - 0x00007ffe0e53c000 	C:\Users\<USER>\.jdks\jbr-17.0.14\bin\vcruntime140_1.dll
0x00007ffda1d80000 - 0x00007ffda1e0d000 	C:\Users\<USER>\.jdks\jbr-17.0.14\bin\msvcp140.dll
0x00007ffd8c570000 - 0x00007ffd8d1fe000 	C:\Users\<USER>\.jdks\jbr-17.0.14\bin\server\jvm.dll
0x00007ffe29730000 - 0x00007ffe297df000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffe281a0000 - 0x00007ffe2823c000 	C:\Windows\System32\sechost.dll
0x00007ffe288f0000 - 0x00007ffe28a16000 	C:\Windows\System32\RPCRT4.dll
0x00007ffe29330000 - 0x00007ffe2939b000 	C:\Windows\System32\WS2_32.dll
0x00007ffe26d20000 - 0x00007ffe26d6b000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffe1ffd0000 - 0x00007ffe1fff7000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffe21210000 - 0x00007ffe2121a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffe26b90000 - 0x00007ffe26ba2000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffe26010000 - 0x00007ffe26022000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffe0e520000 - 0x00007ffe0e52a000 	C:\Users\<USER>\.jdks\jbr-17.0.14\bin\jimage.dll
0x00007ffe25b40000 - 0x00007ffe25d24000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffe102c0000 - 0x00007ffe102f4000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffe27fb0000 - 0x00007ffe28032000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffe17fc0000 - 0x00007ffe17ffb000 	C:\Users\<USER>\.jdks\jbr-17.0.14\bin\jdwp.dll
0x00007ffe23f90000 - 0x00007ffe23f9e000 	C:\Users\<USER>\.jdks\jbr-17.0.14\bin\instrument.dll
0x00007ffe023e0000 - 0x00007ffe02405000 	C:\Users\<USER>\.jdks\jbr-17.0.14\bin\java.dll
0x00007ffd8c350000 - 0x00007ffd8c561000 	C:\Users\<USER>\AppData\Local\Temp\idea_libasyncProfiler_dll_temp_folder9909\libasyncProfiler.dll
0x00007ffe06e30000 - 0x00007ffe06e48000 	C:\Users\<USER>\.jdks\jbr-17.0.14\bin\zip.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Users\<USER>\.jdks\jbr-17.0.14\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.3636_none_60b6a03d71f818d5;C:\Users\<USER>\.jdks\jbr-17.0.14\bin\server;C:\Users\<USER>\AppData\Local\Temp\idea_libasyncProfiler_dll_temp_folder9909

VM Arguments:
jvm_args: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:24867,suspend=y,server=n -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2025.1\captureAgent\debugger-agent.jar=file:///C:/Users/<USER>/AppData/Local/Temp/capture9134410875643509474.props -agentpath:C:\Users\<USER>\AppData\Local\Temp\idea_libasyncProfiler_dll_temp_folder9909\libasyncProfiler.dll=version,jfr,event=wall,interval=10ms,cstack=no,file=C:\Users\<USER>\IdeaSnapshots\LangChatApp_2025_08_12_192245.jfr,dbghelppath=C:\Users\<USER>\AppData\Local\Temp\idea_dbghelp_dll_temp_folder5\dbghelp.dll,log=C:\Users\<USER>\AppData\Local\Temp\LangChatApp_2025_08_12_192245.jfr.log.txt,logLevel=DEBUG -XX:TieredStopAtLevel=1 -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dmanagement.endpoints.jmx.exposure.include=* -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 
java_command: cn.tycoding.langchat.LangChatApp
java_class_path (initial): D:\awork\work-aigc\aigc-manage\langchat-server\target\classes;D:\awork\work-aigc\aigc-manage\langchat-auth\target\classes;D:\awork\work-aigc\aigc-manage\langchat-common\langchat-common-core\target\classes;D:\java\apache-maven-3.9.10\repository\org\springframework\boot\spring-boot-starter-web\3.2.3\spring-boot-starter-web-3.2.3.jar;D:\java\apache-maven-3.9.10\repository\org\springframework\boot\spring-boot-starter-json\3.2.3\spring-boot-starter-json-3.2.3.jar;D:\java\apache-maven-3.9.10\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.15.4\jackson-datatype-jdk8-2.15.4.jar;D:\java\apache-maven-3.9.10\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.15.4\jackson-datatype-jsr310-2.15.4.jar;D:\java\apache-maven-3.9.10\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.15.4\jackson-module-parameter-names-2.15.4.jar;D:\java\apache-maven-3.9.10\repository\org\springframework\boot\spring-boot-starter-tomcat\3.2.3\spring-boot-starter-tomcat-3.2.3.jar;D:\java\apache-maven-3.9.10\repository\org\apache\tomcat\embed\tomcat-embed-core\10.1.19\tomcat-embed-core-10.1.19.jar;D:\java\apache-maven-3.9.10\repository\org\apache\tomcat\embed\tomcat-embed-el\10.1.19\tomcat-embed-el-10.1.19.jar;D:\java\apache-maven-3.9.10\repository\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.19\tomcat-embed-websocket-10.1.19.jar;D:\java\apache-maven-3.9.10\repository\org\springframework\boot\spring-boot-starter-data-redis\3.2.3\spring-boot-starter-data-redis-3.2.3.jar;D:\java\apache-maven-3.9.10\repository\io\lettuce\lettuce-core\6.3.1.RELEASE\lettuce-core-6.3.1.RELEASE.jar;D:\java\apache-maven-3.9.10\repository\io\netty\netty-common\4.1.107.Final\netty-common-4.1.107.Final.jar;D:\java\apache-maven-3.9.10\repository\io\netty\netty-handler\4.1.107.Final\netty-handler-4.1.107.Final.jar;D:\java\apache-maven-3.9.10\repository\io\netty\netty-resolver\4.1.107.Final\netty-resolver-4.1.107.Final.jar;D:\java\apache-maven-
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 5                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 18                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 536870912                                 {product} {ergonomic}
     bool ManagementServer                         = true                                      {product} {command line}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8552185856                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 0                                      {pd product} {ergonomic}
     bool ProfileInterpreter                       = false                                  {pd product} {command line}
    uintx ProfiledCodeHeapSize                     = 0                                      {pd product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8552185856                             {manageable} {ergonomic}
     intx TieredStopAtLevel                        = 1                                         {product} {command line}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
PATH=D:\soft\vmware\bin\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\java\apache-maven-3.9.10\bin;D:\java\jdk\jdk1.8.0_141\bin;D:\soft\git\Git\cmd;C:\Program Files (x86)\NetSarang\Xftp 8\;C:\Program Files (x86)\NetSarang\Xshell 7\;C:\Program Files (x86)\NetSarang\Xshell 8\;C:\Program Files\MySQL\MySQL Server 8.0\bin\;D:\java\nvm\nodejs\node_global;%NVM_HOME%;%NVM_SYMLINK%;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\app\idea\IntelliJ IDEA 2025.1.3\bin;;D:\soft\idea\IntelliJ IDEA Community Edition 2024.1.7\bin;;D:\soft\PyCharm Community Edition 2021.3.3\bin;;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;D:\java\apache-maven-3.9.10\bin;D:\java\nvm\nodejs\node_global;D:\app\Microsoft VS Code\bin;D:\app\codeBuddy\CodeBuddy\bin;D:\java\nvm\nvm;D:\java\nodejs
USERNAME=Administrator
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 63 Stepping 2, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled

JNI global refs:
JNI global refs: 0, weak refs: 0

JNI global refs memory usage: 0, weak refs: 0

Process memory usage:
Resident Set Size: 41472K (0% of 33403940K total physical memory with 4479176K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:


---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.3636)
OS uptime: 0 days 2:36 hours

CPU: total 24 (initial active 24) (12 cores per cpu, 2 threads per core) family 6 model 63 stepping 2 microcode 0x43, cx8, cmov, fxsr, ht, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, fma, vzeroupper, clflush
Processor Information for all 24 processors :
  Max Mhz: 2401, Current Mhz: 2401, Mhz Limit: 2401

Memory: 4k page, system-wide physical 32621M (4373M free)
TotalPageFile size 51765M (AvailPageFile size 158M)
current process WorkingSet (physical memory assigned to process): 40M, peak: 40M
current process commit charge ("private bytes"): 114M, peak: 600M

vm_info: OpenJDK 64-Bit Server VM (17.0.14+1-b1367.22) for windows-amd64 JRE (17.0.14+1-b1367.22), built on 2025-01-30 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
