-- 模型管理相关数据库表设计

-- 1. 模型分类表
CREATE TABLE `aigc_model_category` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `code` varchar(20) NOT NULL COMMENT '分类编码',
  `description` varchar(200) COMMENT '分类描述',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 0-禁用 1-启用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模型分类表';

-- 2. 模型供应商表
CREATE TABLE `aigc_model_provider` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `name` varchar(50) NOT NULL COMMENT '供应商名称',
  `code` varchar(20) NOT NULL COMMENT '供应商编码',
  `logo_url` varchar(200) COMMENT '供应商Logo',
  `description` varchar(500) COMMENT '供应商描述',
  `official_website` varchar(200) COMMENT '官方网站',
  `api_doc_url` varchar(200) COMMENT 'API文档地址',
  `default_base_url` varchar(200) COMMENT '默认BaseUrl',
  `auth_type` varchar(20) DEFAULT 'API_KEY' COMMENT '认证类型 API_KEY,OAUTH,LOCAL',
  `supported_model_types` json COMMENT '支持的模型类型(JSON数组)',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 0-禁用 1-启用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模型供应商表';

-- 3. 模型版本表
CREATE TABLE `aigc_model_version` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `provider_id` varchar(32) NOT NULL COMMENT '供应商ID',
  `category_id` varchar(32) NOT NULL COMMENT '分类ID',
  `model_name` varchar(100) NOT NULL COMMENT '模型名称',
  `model_code` varchar(50) NOT NULL COMMENT '模型编码',
  `display_name` varchar(100) COMMENT '显示名称',
  `description` varchar(500) COMMENT '模型描述',
  `version` varchar(20) COMMENT '版本号',
  `max_tokens` int COMMENT '最大Token数',
  `context_window` int COMMENT '上下文窗口大小',
  `input_price` decimal(10,6) COMMENT '输入价格(每1K tokens)',
  `output_price` decimal(10,6) COMMENT '输出价格(每1K tokens)',
  `supports_streaming` tinyint(1) DEFAULT 1 COMMENT '是否支持流式输出',
  `supports_function_call` tinyint(1) DEFAULT 0 COMMENT '是否支持函数调用',
  `supports_vision` tinyint(1) DEFAULT 0 COMMENT '是否支持视觉',
  `image_sizes` json COMMENT '支持的图片尺寸(JSON数组)',
  `image_qualities` json COMMENT '支持的图片质量(JSON数组)',
  `image_styles` json COMMENT '支持的图片风格(JSON数组)',
  `embedding_dimensions` int COMMENT '向量维度(仅向量模型)',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 0-禁用 1-启用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_provider_category` (`provider_id`, `category_id`),
  KEY `idx_model_code` (`model_code`),
  CONSTRAINT `fk_model_version_provider` FOREIGN KEY (`provider_id`) REFERENCES `aigc_model_provider` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_model_version_category` FOREIGN KEY (`category_id`) REFERENCES `aigc_model_category` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模型版本表';

-- 4. 修改现有的 aigc_model 表，添加关联字段
ALTER TABLE `aigc_model` 
ADD COLUMN `provider_id` varchar(32) COMMENT '供应商ID',
ADD COLUMN `model_version_id` varchar(32) COMMENT '模型版本ID',
ADD INDEX `idx_provider_version` (`provider_id`, `model_version_id`);

-- 初始化基础数据

-- 插入模型分类
INSERT INTO `aigc_model_category` (`id`, `name`, `code`, `description`, `sort_order`) VALUES
('cat_001', '对话模型', 'CHAT', '用于对话交互的大语言模型', 1),
('cat_002', '向量模型', 'EMBEDDING', '用于文本向量化的嵌入模型', 2),
('cat_003', '图像生成', 'TEXT_IMAGE', '文本生成图像的模型', 3),
('cat_004', '搜索引擎', 'WEB_SEARCH', '网络搜索引擎模型', 4);

-- 插入模型供应商
INSERT INTO `aigc_model_provider` (`id`, `name`, `code`, `description`, `official_website`, `default_base_url`, `supported_model_types`, `sort_order`) VALUES
('prov_001', 'OpenAI', 'OPENAI', 'OpenAI官方模型', 'https://openai.com', 'https://api.openai.com/v1', '["CHAT", "EMBEDDING", "TEXT_IMAGE"]', 1),
('prov_002', 'Azure OpenAI', 'AZURE_OPENAI', 'Microsoft Azure OpenAI服务', 'https://azure.microsoft.com/en-us/products/ai-services/openai-service', '', '["CHAT", "EMBEDDING", "TEXT_IMAGE"]', 2),
('prov_003', '百度千帆', 'Q_FAN', '百度千帆大模型平台', 'https://cloud.baidu.com/product/wenxinworkshop', 'https://aip.baidubce.com', '["CHAT", "EMBEDDING"]', 3),
('prov_004', '阿里百炼', 'Q_WEN', '阿里云百炼大模型服务', 'https://bailian.console.aliyun.com', 'https://dashscope.aliyuncs.com', '["CHAT", "EMBEDDING", "TEXT_IMAGE"]', 4),
('prov_005', '智谱清言', 'ZHIPU', '智谱AI大模型', 'https://www.zhipuai.cn', 'https://open.bigmodel.cn', '["CHAT", "EMBEDDING", "TEXT_IMAGE"]', 5),
('prov_006', '抖音豆包', 'DOUYIN', '字节跳动豆包大模型', 'https://www.volcengine.com/product/doubao', 'https://ark.cn-beijing.volces.com', '["CHAT", "EMBEDDING"]', 6),
('prov_007', 'Ollama', 'OLLAMA', '本地部署的开源模型', 'https://ollama.ai', 'http://localhost:11434', '["CHAT", "EMBEDDING"]', 7),
('prov_008', '硅基流动', 'SILICON', '硅基流动大模型服务', 'https://siliconflow.cn', 'https://api.siliconflow.cn', '["CHAT", "EMBEDDING", "TEXT_IMAGE"]', 8),
('prov_009', '讯飞星火', 'SPARK', '科大讯飞星火大模型', 'https://xinghuo.xfyun.cn', 'https://spark-api-open.xf-yun.com', '["CHAT"]', 9),
('prov_010', 'DeepSeek', 'DEEPSEEK', 'DeepSeek大模型', 'https://www.deepseek.com', 'https://api.deepseek.com', '["CHAT"]', 10),
('prov_011', 'Google Gemini', 'GEMINI', 'Google Gemini模型', 'https://ai.google.dev', 'https://generativelanguage.googleapis.com', '["CHAT", "EMBEDDING"]', 11),
('prov_012', 'Anthropic Claude', 'CLAUDE', 'Anthropic Claude模型', 'https://www.anthropic.com', 'https://api.anthropic.com', '["CHAT"]', 12),
('prov_013', '零一万物', 'YI', '零一万物大模型', 'https://www.lingyiwanwu.com', 'https://api.lingyiwanwu.com', '["CHAT"]', 13),
('prov_014', 'BOCHAAI', 'BOCHAAI', 'BOCHAAI搜索引擎', 'https://bochaai.com', 'https://api.bochaai.com', '["WEB_SEARCH"]', 14);

-- 插入模型版本数据（示例数据）
INSERT INTO `aigc_model_version` (`id`, `provider_id`, `category_id`, `model_name`, `model_code`, `display_name`, `description`, `max_tokens`, `context_window`, `supports_streaming`, `supports_function_call`, `supports_vision`, `sort_order`) VALUES
-- OpenAI Chat模型
('ver_001', 'prov_001', 'cat_001', 'gpt-3.5-turbo', 'gpt-3.5-turbo', 'GPT-3.5 Turbo', 'OpenAI GPT-3.5 Turbo模型', 4096, 16385, 1, 1, 0, 1),
('ver_002', 'prov_001', 'cat_001', 'gpt-4', 'gpt-4', 'GPT-4', 'OpenAI GPT-4模型', 8192, 8192, 1, 1, 0, 2),
('ver_003', 'prov_001', 'cat_001', 'gpt-4o', 'gpt-4o', 'GPT-4o', 'OpenAI GPT-4o多模态模型', 4096, 128000, 1, 1, 1, 3),
-- OpenAI Embedding模型
('ver_004', 'prov_001', 'cat_002', 'text-embedding-3-small', 'text-embedding-3-small', 'Text Embedding 3 Small', 'OpenAI文本嵌入模型(小)', NULL, 8191, 0, 0, 0, 1),
('ver_005', 'prov_001', 'cat_002', 'text-embedding-3-large', 'text-embedding-3-large', 'Text Embedding 3 Large', 'OpenAI文本嵌入模型(大)', NULL, 8191, 0, 0, 0, 2),
-- OpenAI Image模型
('ver_006', 'prov_001', 'cat_003', 'dall-e-3', 'dall-e-3', 'DALL-E 3', 'OpenAI DALL-E 3图像生成模型', NULL, NULL, 0, 0, 0, 1),
-- 百度千帆Chat模型
('ver_007', 'prov_003', 'cat_001', 'ERNIE-Bot', 'ERNIE-Bot', '文心一言', '百度文心一言基础模型', 2048, 5120, 1, 0, 0, 1),
('ver_008', 'prov_003', 'cat_001', 'ERNIE-Bot 4.0', 'ERNIE-Bot-4', '文心一言4.0', '百度文心一言4.0模型', 2048, 5120, 1, 1, 0, 2),
-- 百度千帆Embedding模型
('ver_009', 'prov_003', 'cat_002', 'bge-large-zh', 'bge-large-zh', 'BGE Large 中文', '百度BGE中文大模型', NULL, 512, 0, 0, 0, 1),
-- 智谱Chat模型
('ver_010', 'prov_005', 'cat_001', 'glm-4', 'glm-4', 'GLM-4', '智谱GLM-4模型', 1024, 128000, 1, 1, 1, 1),
-- 智谱Embedding模型
('ver_011', 'prov_005', 'cat_002', 'embedding-2', 'embedding-2', 'Embedding-2', '智谱嵌入模型2', NULL, 512, 0, 0, 0, 1),
-- 搜索引擎模型
('ver_012', 'prov_014', 'cat_004', 'bocha-ai', 'bocha-ai', 'BOCHAAI搜索', 'BOCHAAI智能搜索引擎', NULL, NULL, 0, 0, 0, 1);
