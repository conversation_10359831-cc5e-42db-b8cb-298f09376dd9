-- MCP模块数据库迁移脚本
-- 创建MCP相关表结构

-- 1. 创建MCP服务表
CREATE TABLE IF NOT EXISTS `mcp_service` (
    `id` varchar(64) NOT NULL COMMENT '主键ID',
    `name` varchar(100) NOT NULL COMMENT '服务名称（唯一标识）',
    `display_name` varchar(200) NOT NULL COMMENT '显示名称',
    `description` text COMMENT '服务描述',
    `version` varchar(50) DEFAULT '1.0.0' COMMENT '服务版本',
    `endpoint` varchar(500) NOT NULL COMMENT '服务端点URL',
    `type` varchar(50) NOT NULL DEFAULT 'HTTP' COMMENT '服务类型：HTTP, WEBSOCKET, GRPC',
    `category` varchar(50) NOT NULL DEFAULT 'external' COMMENT '服务分类：builtin, external, custom',
    `auth_type` varchar(50) DEFAULT 'none' COMMENT '认证类型：none, api_key, bearer, basic, oauth2',
    `auth_config` text COMMENT '认证配置JSON',
    `tools` longtext COMMENT '工具列表JSON',
    `config` text COMMENT '服务配置JSON',
    `enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
    `priority` int NOT NULL DEFAULT 50 COMMENT '优先级（数字越大优先级越高）',
    `timeout` int NOT NULL DEFAULT 30000 COMMENT '超时时间（毫秒）',
    `max_retries` int NOT NULL DEFAULT 3 COMMENT '最大重试次数',
    `health_check_url` varchar(500) COMMENT '健康检查URL',
    `health_status` varchar(20) DEFAULT 'unknown' COMMENT '健康状态：healthy, unhealthy, unknown',
    `last_health_check` datetime COMMENT '最后健康检查时间',
    `tags` varchar(500) COMMENT '标签（用于分组和过滤）',
    `create_by` varchar(64) COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` varchar(64) COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `remark` varchar(500) COMMENT '备注',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_mcp_service_name` (`name`),
    KEY `idx_mcp_service_category` (`category`),
    KEY `idx_mcp_service_enabled` (`enabled`),
    KEY `idx_mcp_service_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='MCP服务表';

-- 2. 创建MCP服务调用日志表
CREATE TABLE IF NOT EXISTS `mcp_service_log` (
    `id` varchar(64) NOT NULL COMMENT '主键ID',
    `service_id` varchar(64) NOT NULL COMMENT '服务ID',
    `service_name` varchar(100) NOT NULL COMMENT '服务名称',
    `tool_name` varchar(100) NOT NULL COMMENT '工具名称',
    `request_id` varchar(64) COMMENT '请求ID（用于追踪）',
    `request_params` longtext COMMENT '请求参数JSON',
    `response_data` longtext COMMENT '响应数据JSON',
    `error_message` text COMMENT '错误信息',
    `error_code` varchar(50) COMMENT '错误代码',
    `success` tinyint(1) NOT NULL DEFAULT 0 COMMENT '执行是否成功',
    `execution_time` bigint NOT NULL DEFAULT 0 COMMENT '执行时间（毫秒）',
    `retry_count` int NOT NULL DEFAULT 0 COMMENT '重试次数',
    `user_id` varchar(64) COMMENT '用户ID',
    `conversation_id` varchar(64) COMMENT '对话ID',
    `session_id` varchar(64) COMMENT '会话ID',
    `client_ip` varchar(50) COMMENT '客户端IP',
    `user_agent` varchar(500) COMMENT '用户代理',
    `source` varchar(100) COMMENT '请求来源',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '调用时间',
    `remark` varchar(500) COMMENT '备注',
    PRIMARY KEY (`id`),
    KEY `idx_mcp_log_service_id` (`service_id`),
    KEY `idx_mcp_log_service_name` (`service_name`),
    KEY `idx_mcp_log_tool_name` (`tool_name`),
    KEY `idx_mcp_log_user_id` (`user_id`),
    KEY `idx_mcp_log_conversation_id` (`conversation_id`),
    KEY `idx_mcp_log_success` (`success`),
    KEY `idx_mcp_log_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='MCP服务调用日志表';

-- 3. 修改AI应用表，添加MCP服务关联字段
ALTER TABLE `aigc_app` 
ADD COLUMN `mcp_service_ids` json COMMENT 'MCP服务ID列表' AFTER `knowledge_ids`;

-- 4. 插入示例MCP服务数据
INSERT INTO `mcp_service` (
    `id`, `name`, `display_name`, `description`, `endpoint`, `type`, `category`, 
    `auth_type`, `tools`, `enabled`, `priority`, `tags`, `create_by`
) VALUES 
(
    'wanx-image-generation-001',
    'wanx-image-generation',
    'Wanx文生图服务',
    '阿里云通义万相文生图服务，支持根据文本描述生成高质量图片',
    'https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis',
    'HTTP',
    'external',
    'api_key',
    '[
        {
            "name": "text_to_image",
            "description": "根据文本描述生成图片",
            "category": "image",
            "inputSchema": {
                "type": "object",
                "properties": {
                    "prompt": {
                        "type": "string",
                        "description": "图片描述文本"
                    },
                    "style": {
                        "type": "string",
                        "description": "图片风格",
                        "enum": ["photography", "cartoon", "oil_painting", "watercolor"],
                        "default": "photography"
                    },
                    "size": {
                        "type": "string",
                        "description": "图片尺寸",
                        "enum": ["1024*1024", "1344*768", "768*1344"],
                        "default": "1024*1024"
                    }
                },
                "required": ["prompt"]
            }
        }
    ]',
    1,
    80,
    'image,generation,ai,wanx',
    'system'
),
(
    'brave-search-001',
    'brave-search',
    'Brave搜索服务',
    'Brave搜索引擎API，提供网络搜索功能',
    'https://api.search.brave.com/res/v1/web/search',
    'HTTP',
    'external',
    'api_key',
    '[
        {
            "name": "web_search",
            "description": "网络搜索工具",
            "category": "search",
            "inputSchema": {
                "type": "object",
                "properties": {
                    "q": {
                        "type": "string",
                        "description": "搜索查询"
                    },
                    "count": {
                        "type": "integer",
                        "description": "返回结果数量",
                        "minimum": 1,
                        "maximum": 20,
                        "default": 10
                    },
                    "language": {
                        "type": "string",
                        "description": "搜索语言",
                        "default": "zh"
                    }
                },
                "required": ["q"]
            }
        }
    ]',
    1,
    70,
    'search,web,information',
    'system'
),
(
    'edgeone-pages-001',
    'edgeone-pages',
    'EdgeOne Pages发布服务',
    '腾讯云EdgeOne Pages静态网站发布服务',
    'https://edgeone.tencentcloudapi.com',
    'HTTP',
    'external',
    'api_key',
    '[
        {
            "name": "upload_file",
            "description": "上传文件到EdgeOne Pages",
            "category": "file",
            "inputSchema": {
                "type": "object",
                "properties": {
                    "file_url": {
                        "type": "string",
                        "description": "文件URL"
                    },
                    "file_name": {
                        "type": "string",
                        "description": "文件名称"
                    },
                    "title": {
                        "type": "string",
                        "description": "文件标题"
                    },
                    "description": {
                        "type": "string",
                        "description": "文件描述"
                    }
                },
                "required": ["file_url"]
            }
        }
    ]',
    1,
    60,
    'file,upload,storage,edgeone',
    'system'
);

-- 5. 创建示例AI应用，关联MCP服务
INSERT INTO `aigc_app` (
    `id`, `name`, `description`, `prompt`, `model_id`, `knowledge_ids`, `mcp_service_ids`, 
    `enabled`, `create_by`, `create_time`
) VALUES 
(
    'smart-creator-app-001',
    '智能创作助手',
    '集成图片生成、搜索、发布功能的智能创作助手',
    '你是一个专业的智能创作助手。你可以根据用户需求：\n1. 搜索相关资料\n2. 生成高质量图片\n3. 创作文章内容\n4. 发布到指定平台\n\n请根据用户的具体需求，智能选择合适的工具来完成任务。',
    'gpt-3.5-turbo',
    '[]',
    '["wanx-image-generation-001", "brave-search-001", "edgeone-pages-001"]',
    1,
    'system',
    NOW()
),
(
    'image-publisher-app-001',
    '图片生成发布助手',
    '专门用于图片生成和发布的AI助手',
    '你是一个专业的图片生成和发布助手。你可以：\n1. 根据用户描述生成高质量图片\n2. 将生成的图片发布到指定平台\n\n请根据用户需求，先生成图片，然后发布到合适的平台。',
    'gpt-3.5-turbo',
    '[]',
    '["wanx-image-generation-001", "edgeone-pages-001"]',
    1,
    'system',
    NOW()
);

-- 6. 创建索引优化查询性能
CREATE INDEX `idx_mcp_service_type_enabled` ON `mcp_service` (`type`, `enabled`);
CREATE INDEX `idx_mcp_service_category_enabled` ON `mcp_service` (`category`, `enabled`);
CREATE INDEX `idx_mcp_log_service_time` ON `mcp_service_log` (`service_name`, `create_time`);
CREATE INDEX `idx_mcp_log_user_time` ON `mcp_service_log` (`user_id`, `create_time`);

-- 7. 创建视图，方便查询统计信息
CREATE OR REPLACE VIEW `v_mcp_service_stats` AS
SELECT 
    s.id,
    s.name,
    s.display_name,
    s.category,
    s.enabled,
    s.health_status,
    COUNT(l.id) as total_calls,
    COUNT(CASE WHEN l.success = 1 THEN 1 END) as success_calls,
    COUNT(CASE WHEN l.success = 0 THEN 1 END) as failed_calls,
    ROUND(COUNT(CASE WHEN l.success = 1 THEN 1 END) * 100.0 / COUNT(l.id), 2) as success_rate,
    AVG(l.execution_time) as avg_execution_time,
    MAX(l.create_time) as last_call_time
FROM `mcp_service` s
LEFT JOIN `mcp_service_log` l ON s.id = l.service_id
GROUP BY s.id, s.name, s.display_name, s.category, s.enabled, s.health_status;

COMMIT;
