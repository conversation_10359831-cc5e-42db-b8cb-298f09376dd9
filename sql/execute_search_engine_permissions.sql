-- 搜索引擎管理权限配置 - 立即执行版本
-- 请在数据库管理工具中执行以下SQL语句

-- 使用数据库
USE jrzh_aigc_dev;

-- 1. 确保模型管理主菜单存在
INSERT IGNORE INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES
('model_mgmt_main', '模型管理', '0', 'model-management', 'model:management', 'menu', 30, 'SettingsOutline', 'Layout', 0, 0, 1, 1);

-- 2. 插入搜索引擎管理主菜单
INSERT IGNORE INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES
('search_engine_main', '搜索引擎管理', 'model_mgmt_main', 'search-engine', 'model:search-engine', 'menu', 40, 'SearchOutline', '/aigc/model-management/web-search/index', 0, 0, 1, 1);

-- 3. 插入搜索引擎权限按钮
INSERT IGNORE INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES
('search_engine_view', '查看搜索引擎', 'search_engine_main', NULL, 'aigc:search-engine:view', 'button', 5, NULL, NULL, 0, 0, 1, 1),
('search_engine_add', '新增搜索引擎', 'search_engine_main', NULL, 'aigc:search-engine:add', 'button', 10, NULL, NULL, 0, 0, 1, 1),
('search_engine_edit', '编辑搜索引擎', 'search_engine_main', NULL, 'aigc:search-engine:update', 'button', 20, NULL, NULL, 0, 0, 1, 1),
('search_engine_del', '删除搜索引擎', 'search_engine_main', NULL, 'aigc:search-engine:delete', 'button', 30, NULL, NULL, 0, 0, 1, 1),
('search_engine_test', '测试搜索引擎', 'search_engine_main', NULL, 'aigc:search-engine:test', 'button', 40, NULL, NULL, 0, 0, 1, 1);

-- 4. 为超级管理员角色分配权限
INSERT IGNORE INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES
('2827e950043adf67b7fe10306d3e94e4', 'model_mgmt_main'),
('2827e950043adf67b7fe10306d3e94e4', 'search_engine_main'),
('2827e950043adf67b7fe10306d3e94e4', 'search_engine_view'),
('2827e950043adf67b7fe10306d3e94e4', 'search_engine_add'),
('2827e950043adf67b7fe10306d3e94e4', 'search_engine_edit'),
('2827e950043adf67b7fe10306d3e94e4', 'search_engine_del'),
('2827e950043adf67b7fe10306d3e94e4', 'search_engine_test');

-- 5. 为默认人员角色分配权限
INSERT IGNORE INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES
('bbe1863be68ad07347b1dee0e358f18a', 'model_mgmt_main'),
('bbe1863be68ad07347b1dee0e358f18a', 'search_engine_main'),
('bbe1863be68ad07347b1dee0e358f18a', 'search_engine_view'),
('bbe1863be68ad07347b1dee0e358f18a', 'search_engine_add'),
('bbe1863be68ad07347b1dee0e358f18a', 'search_engine_edit'),
('bbe1863be68ad07347b1dee0e358f18a', 'search_engine_del'),
('bbe1863be68ad07347b1dee0e358f18a', 'search_engine_test');

-- 6. 为演示环境角色分配只读权限
INSERT IGNORE INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES
('d0d0cab7c147d865d35e70fc62f2f19e', 'model_mgmt_main'),
('d0d0cab7c147d865d35e70fc62f2f19e', 'search_engine_main'),
('d0d0cab7c147d865d35e70fc62f2f19e', 'search_engine_view');

-- 验证查询 - 查看插入的菜单
SELECT 
    m.id,
    m.name,
    m.parent_id,
    m.perms,
    m.type,
    m.order_no
FROM sys_menu m 
WHERE m.id LIKE 'search_engine_%' OR m.id = 'model_mgmt_main'
ORDER BY m.parent_id, m.order_no;

-- 验证查询 - 查看角色权限分配
SELECT 
    r.name as role_name,
    m.name as menu_name,
    m.perms
FROM sys_role_menu rm
JOIN sys_role r ON rm.role_id = r.id
JOIN sys_menu m ON rm.menu_id = m.id
WHERE rm.menu_id LIKE 'search_engine_%' OR rm.menu_id = 'model_mgmt_main'
ORDER BY r.name, m.order_no;
