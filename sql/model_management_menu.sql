-- 模型管理菜单和权限SQL
-- 基于项目实际的sys_menu表结构
-- 注意：请根据实际的AIGC菜单ID修改parent_id，通常AIGC菜单的parent_id为'0'

-- 查看现有AIGC相关菜单（可选，用于确认parent_id）
-- SELECT id, name, parent_id, path FROM sys_menu WHERE name LIKE '%AIGC%' OR name LIKE '%智能%';

-- 1. 插入模型管理主菜单
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES
('model_mgmt_main', '模型管理', '0', 'model-management', 'model:management', 'menu', 30, 'SettingsOutline', 'Layout', 0, 0, 1, 1);

-- 2. 插入模型分类管理菜单
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES
('model_mgmt_category', '模型分类', 'model_mgmt_main', 'category', 'model:category', 'menu', 10, 'ListOutline', '/aigc/model-management/category/index', 0, 0, 1, 1);

-- 3. 插入模型供应商管理菜单
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES
('model_mgmt_provider', '模型供应商', 'model_mgmt_main', 'provider', 'model:provider', 'menu', 20, 'BusinessOutline', '/aigc/model-management/provider/index', 0, 0, 1, 1);

-- 4. 插入模型版本管理菜单
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES
('model_mgmt_version', '模型版本', 'model_mgmt_main', 'version', 'model:version', 'menu', 30, 'CodeOutline', '/aigc/model-management/version/index', 0, 0, 1, 1);

-- 5. 插入模型分类权限按钮
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES
('model_category_add', '新增分类', 'model_mgmt_category', NULL, 'aigc:model:category:add', 'button', 10, NULL, NULL, 0, 0, 1, 1),
('model_category_edit', '编辑分类', 'model_mgmt_category', NULL, 'aigc:model:category:update', 'button', 20, NULL, NULL, 0, 0, 1, 1),
('model_category_del', '删除分类', 'model_mgmt_category', NULL, 'aigc:model:category:delete', 'button', 30, NULL, NULL, 0, 0, 1, 1);

-- 6. 插入模型供应商权限按钮
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES
('model_provider_add', '新增供应商', 'model_mgmt_provider', NULL, 'aigc:model:provider:add', 'button', 10, NULL, NULL, 0, 0, 1, 1),
('model_provider_edit', '编辑供应商', 'model_mgmt_provider', NULL, 'aigc:model:provider:update', 'button', 20, NULL, NULL, 0, 0, 1, 1),
('model_provider_del', '删除供应商', 'model_mgmt_provider', NULL, 'aigc:model:provider:delete', 'button', 30, NULL, NULL, 0, 0, 1, 1);

-- 7. 插入模型版本权限按钮
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES
('model_version_add', '新增版本', 'model_mgmt_version', NULL, 'aigc:model:version:add', 'button', 10, NULL, NULL, 0, 0, 1, 1),
('model_version_edit', '编辑版本', 'model_mgmt_version', NULL, 'aigc:model:version:update', 'button', 20, NULL, NULL, 0, 0, 1, 1),
('model_version_del', '删除版本', 'model_mgmt_version', NULL, 'aigc:model:version:delete', 'button', 30, NULL, NULL, 0, 0, 1, 1);

-- 8. 为管理员角色分配菜单权限
-- 注意：请先查询实际的管理员角色ID：
-- SELECT id, name FROM sys_role WHERE name LIKE '%管理员%' OR name LIKE '%admin%';

-- 常见的角色ID（请根据实际情况选择一个）：
-- 如果角色ID是数字，使用类似 '1', '2' 等
-- 如果角色ID是字符串，使用类似 'admin', 'administrator' 等

-- 方式一：如果管理员角色ID是 '1'
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES
-- 主菜单和子菜单
('your_admin_role_id', 'model_mgmt_main'),
('your_admin_role_id', 'model_mgmt_category'),
('your_admin_role_id', 'model_mgmt_provider'),
('your_admin_role_id', 'model_mgmt_version'),
-- 模型分类权限
('your_admin_role_id', 'model_category_add'),
('your_admin_role_id', 'model_category_edit'),
('your_admin_role_id', 'model_category_del'),
-- 模型供应商权限
('your_admin_role_id', 'model_provider_add'),
('your_admin_role_id', 'model_provider_edit'),
('your_admin_role_id', 'model_provider_del'),
-- 模型版本权限
('your_admin_role_id', 'model_version_add'),
('your_admin_role_id', 'model_version_edit'),
('your_admin_role_id', 'model_version_del');

-- 9. 查询验证SQL（执行前可以先运行这些查询来验证）
/*
-- 查看现有角色ID
SELECT id, name FROM sys_role;

-- 查看插入的菜单
SELECT
    m.id,
    m.parent_id,
    m.name,
    m.path,
    m.type,
    m.perms,
    m.order_no,
    m.is_disabled
FROM sys_menu m
WHERE m.id LIKE 'model_%'
ORDER BY m.parent_id, m.order_no;

-- 查看角色菜单关联
SELECT
    rm.role_id,
    rm.menu_id,
    m.name as menu_name,
    m.perms
FROM sys_role_menu rm
LEFT JOIN sys_menu m ON rm.menu_id = m.id
WHERE rm.menu_id LIKE 'model_%'
ORDER BY rm.menu_id;
*/

-- 10. 删除菜单SQL（如果需要回滚）
/*
-- 删除角色菜单关联
DELETE FROM sys_role_menu WHERE menu_id LIKE 'model_%';

-- 删除菜单（注意顺序：先删除子菜单，再删除父菜单）
DELETE FROM sys_menu WHERE parent_id LIKE 'model_mgmt_%';
DELETE FROM sys_menu WHERE id LIKE 'model_mgmt_%';
*/
