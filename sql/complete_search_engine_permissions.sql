-- 搜索引擎管理完整权限配置SQL
-- 执行前请先确认角色ID和模型管理主菜单是否存在

-- 0. 查询现有角色和菜单（执行前先运行确认）
/*
SELECT id, name FROM sys_role;
SELECT id, name, parent_id FROM sys_menu WHERE id = 'model_mgmt_main' OR name LIKE '%模型管理%';
*/

-- 1. 确保模型管理主菜单存在（如果不存在则创建）
INSERT IGNORE INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES
('model_mgmt_main', '模型管理', '0', 'model-management', 'model:management', 'menu', 30, 'SettingsOutline', 'Layout', 0, 0, 1, 1);

-- 2. 插入搜索引擎管理主菜单
INSERT IGNORE INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES
('search_engine_main', '搜索引擎管理', 'model_mgmt_main', 'search-engine', 'model:search-engine', 'menu', 40, 'SearchOutline', '/aigc/model-management/web-search/index', 0, 0, 1, 1);

-- 3. 插入搜索引擎权限按钮
INSERT IGNORE INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES
('search_engine_add', '新增搜索引擎', 'search_engine_main', NULL, 'aigc:search-engine:add', 'button', 10, NULL, NULL, 0, 0, 1, 1),
('search_engine_edit', '编辑搜索引擎', 'search_engine_main', NULL, 'aigc:search-engine:update', 'button', 20, NULL, NULL, 0, 0, 1, 1),
('search_engine_del', '删除搜索引擎', 'search_engine_main', NULL, 'aigc:search-engine:delete', 'button', 30, NULL, NULL, 0, 0, 1, 1),
('search_engine_test', '测试搜索引擎', 'search_engine_main', NULL, 'aigc:search-engine:test', 'button', 40, NULL, NULL, 0, 0, 1, 1),
('search_engine_view', '查看搜索引擎', 'search_engine_main', NULL, 'aigc:search-engine:view', 'button', 5, NULL, NULL, 0, 0, 1, 1);

-- 4. 为超级管理员角色分配权限（角色ID: 2827e950043adf67b7fe10306d3e94e4）
INSERT IGNORE INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES
-- 模型管理主菜单
('2827e950043adf67b7fe10306d3e94e4', 'model_mgmt_main'),
-- 搜索引擎管理菜单
('2827e950043adf67b7fe10306d3e94e4', 'search_engine_main'),
-- 搜索引擎权限
('2827e950043adf67b7fe10306d3e94e4', 'search_engine_view'),
('2827e950043adf67b7fe10306d3e94e4', 'search_engine_add'),
('2827e950043adf67b7fe10306d3e94e4', 'search_engine_edit'),
('2827e950043adf67b7fe10306d3e94e4', 'search_engine_del'),
('2827e950043adf67b7fe10306d3e94e4', 'search_engine_test');

-- 5. 为默认人员角色分配权限（角色ID: bbe1863be68ad07347b1dee0e358f18a）
INSERT IGNORE INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES
-- 模型管理主菜单
('bbe1863be68ad07347b1dee0e358f18a', 'model_mgmt_main'),
-- 搜索引擎管理菜单
('bbe1863be68ad07347b1dee0e358f18a', 'search_engine_main'),
-- 搜索引擎权限
('bbe1863be68ad07347b1dee0e358f18a', 'search_engine_view'),
('bbe1863be68ad07347b1dee0e358f18a', 'search_engine_add'),
('bbe1863be68ad07347b1dee0e358f18a', 'search_engine_edit'),
('bbe1863be68ad07347b1dee0e358f18a', 'search_engine_del'),
('bbe1863be68ad07347b1dee0e358f18a', 'search_engine_test');

-- 6. 为演示环境角色分配只读权限（角色ID: d0d0cab7c147d865d35e70fc62f2f19e）
INSERT IGNORE INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES
-- 模型管理主菜单
('d0d0cab7c147d865d35e70fc62f2f19e', 'model_mgmt_main'),
-- 搜索引擎管理菜单
('d0d0cab7c147d865d35e70fc62f2f19e', 'search_engine_main'),
-- 搜索引擎只读权限
('d0d0cab7c147d865d35e70fc62f2f19e', 'search_engine_view');

-- 7. 验证查询SQL
/*
-- 查看插入的搜索引擎菜单
SELECT
    m.id,
    m.parent_id,
    m.name,
    m.path,
    m.type,
    m.perms,
    m.order_no,
    m.is_disabled,
    m.is_show
FROM sys_menu m
WHERE m.id LIKE 'search_engine_%' OR m.id = 'model_mgmt_main'
ORDER BY m.parent_id, m.order_no;

-- 查看角色菜单关联
SELECT
    rm.role_id,
    r.name as role_name,
    rm.menu_id,
    m.name as menu_name,
    m.perms
FROM sys_role_menu rm
LEFT JOIN sys_menu m ON rm.menu_id = m.id
LEFT JOIN sys_role r ON rm.role_id = r.id
WHERE rm.menu_id LIKE 'search_engine_%' OR rm.menu_id = 'model_mgmt_main'
ORDER BY rm.role_id, rm.menu_id;

-- 查看特定用户的权限
SELECT DISTINCT
    m.perms
FROM sys_user u
LEFT JOIN sys_user_role ur ON u.id = ur.user_id
LEFT JOIN sys_role_menu rm ON ur.role_id = rm.role_id
LEFT JOIN sys_menu m ON rm.menu_id = m.id
WHERE u.username = 'your_username' 
AND m.perms LIKE '%search-engine%'
ORDER BY m.perms;
*/

-- 8. 清理SQL（如果需要回滚）
/*
-- 删除角色菜单关联
DELETE FROM sys_role_menu WHERE menu_id LIKE 'search_engine_%';
DELETE FROM sys_role_menu WHERE menu_id = 'model_mgmt_main';

-- 删除菜单（注意顺序：先删除子菜单，再删除父菜单）
DELETE FROM sys_menu WHERE parent_id = 'search_engine_main';
DELETE FROM sys_menu WHERE id = 'search_engine_main';
DELETE FROM sys_menu WHERE id = 'model_mgmt_main';
*/

-- 9. 权限说明
/*
权限标识符说明：
- model:management: 模型管理主菜单访问权限
- model:search-engine: 搜索引擎管理菜单访问权限
- aigc:search-engine:view: 查看搜索引擎权限
- aigc:search-engine:add: 新增搜索引擎权限
- aigc:search-engine:update: 编辑搜索引擎权限
- aigc:search-engine:delete: 删除搜索引擎权限
- aigc:search-engine:test: 测试搜索引擎权限

角色权限分配：
- 超级管理员角色: 拥有所有权限
- 默认人员角色: 拥有所有权限
- 演示环境角色: 只有查看权限
*/
