-- 搜索引擎表
CREATE TABLE `aigc_web_search_engine` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `name` varchar(50) NOT NULL COMMENT '搜索引擎名称',
  `provider` varchar(50) NOT NULL COMMENT '提供商',
  `api_key` varchar(255) DEFAULT NULL COMMENT 'API密钥',
  `base_url` varchar(255) DEFAULT NULL COMMENT '基础URL',
  `search_endpoint` varchar(100) DEFAULT '/search' COMMENT '搜索端点',
  `max_results` int DEFAULT 10 COMMENT '最大结果数',
  `timeout_seconds` int DEFAULT 30 COMMENT '超时时间(秒)',
  `additional_params` text COMMENT '额外参数(JSON格式)',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 0-禁用 1-启用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_provider` (`provider`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='搜索引擎配置表';