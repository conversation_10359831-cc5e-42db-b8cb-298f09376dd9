2025-09-02 15:15:22,942 [main] ERROR [io.milvus.client.AbstractMilvusGrpcClient] AbstractMilvusGrpcClient.java:3378 - DEADLINE_EXCEEDED: deadline exceeded after 9.878994800s. Name resolution delay 0.017552200 seconds. [closed=[], open=[[wait_for_ready, buffered_nanos=**********, waiting_for_connection]]]
2025-09-02 15:15:22,948 [main] ERROR [io.milvus.client.AbstractMilvusGrpcClient] AbstractMilvusGrpcClient.java:3378 - Failed to initialize connection. Error: DEADLINE_EXCEEDED: deadline exceeded after 9.878994800s. Name resolution delay 0.017552200 seconds. [closed=[], open=[[wait_for_ready, buffered_nanos=**********, waiting_for_connection]]]
2025-09-02 15:15:22,950 [main] ERROR [c.t.l.ai.core.provider.EmbeddingStoreFactory] EmbeddingStoreFactory.java:103 - 向量数据库初始化失败：[Milvus] --- [MILVUS]，数据库配置信息：[AigcEmbedStore(id=c92cfbaaff366b12bc87c1082149150a, name=Milvus, provider=MILVUS, host=************, port=19530, username=null, password=null, databaseName=default, tableName=ai, dimension=1024)]
