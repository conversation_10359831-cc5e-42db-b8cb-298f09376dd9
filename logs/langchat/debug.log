2025-09-03 09:43:50,641 [http-nio-8100-exec-7] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本：

2025-09-03 09:44:34,900 [http-nio-8100-exec-1] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本：《采购合同书》
                                                                                                     
 
合同编号： J1960880963642748930 下单日期：2025-08-28
需方：广东美特机械有限公司 供方：江苏日成橡胶有限公司
《中华人民共和国民法典》有关条款规定，经双方共同协商，达成以下条款，以资共同遵守。
一、采购信息：
序号 商品名称 数量 单位 单价 金额（元）
1 白砂糖 粗砂糖[ 40 吨 ￥1,000.00 ￥40,000.00
运费：￥160,000.00
总额：￥200,000.00
合计人民币（大写）：贰拾万元整
 
二、质量技术标准
质量标准：小麦(不含非瘟病毒)：出芽率不做强制要求，不纳入不完善指标籽粒整齐均匀、饱满，黄褐色或白色，无发酵、霉
变、虫蛙及异味、异臭、异物。水分≤13.5%；容重≥730G/L；杂质<1%；虫蛀<5%；不完善（包括病斑粒）s8%；黄曲霉毒素
<10ppb、赤霉烯酮≤150ppb、呕吐毒素≤1000ppb。
三、检验方式
以需方工厂检验为准。若供方对检验结果产生异议，可委托国家认可的检测部门检测。如检测结果达不到合同约定，检测费用
由供方承担，达到合同约定，检测费用则由需方承担。
四、交货地点
需方指定地点，运输费用由需方承担。
五、交货期限
在前交货，如供方不能按合同约定期限交货，需方有权单方变更或终止合同，并视为供方违约。
六、计量
以需方电子磅数量为准.
七、包装标准
散粮/包粮：编织袋包装，包装物由供方提供不计价，不返回，每条编织袋扣重 150克。
八、付款方式及期限
1、到检验合格，7天内凭发票以银行电汇付清货款。
2、供方必须提供增值税发票，如供方提供违规、虚假发票，需方有权对供方处于 20%-100%的货款处罚，并承担由此给需方造成
的一切经济损失和税务风险。（必须在发票备注栏注明纸制合同号）
九、违约责任
按《中华人民共和国民法典》有关规定执行；若发生违约纠纷，违约方按本合同金额 20%支付给守约方，由合同签订所在地人民
法院管辖。
十、其他条款
1、若货物质量达不到需方要求，供方自行处理，所发生的一切费用由供方承担；如果经过国家检验机构确定供方有掺假行为，
供方同意按不低于合同总价的 20%金额赔偿给需方。
2、物发出后 1个工作日内告之需方车号、数量等相关事宜；若火车运输需传真大票并寄小票到需方。
本合同一式两份(供需双方各执一份），须经双方代表签字盖章后有效，涂改无效，合同正本与传真件具有同等法律效力，未尽
事项双方协商解决。
需方（盖章）：广东美特机械有限公司 供方（盖章）：江苏日成橡胶有限公司
需方签名：          供方签名：          
签署日期： 签署日期：     

2025-09-03 09:46:52,028 [http-nio-8100-exec-10] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本：

2025-09-03 09:57:16,798 [http-nio-8100-exec-8] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本：

2025-09-03 10:24:49,572 [http-nio-8100-exec-2] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本：《采购合同书》
                                                                                                     
 
合同编号： J1960880963642748930 下单日期：2025-08-28
需方：广东美特机械有限公司 供方：江苏日成橡胶有限公司
《中华人民共和国民法典》有关条款规定，经双方共同协商，达成以下条款，以资共同遵守。
一、采购信息：
序号 商品名称 数量 单位 单价 金额（元）
1 白砂糖 粗砂糖[ 40 吨 ￥1,000.00 ￥40,000.00
运费：￥160,000.00
总额：￥200,000.00
合计人民币（大写）：贰拾万元整
 
二、质量技术标准
质量标准：小麦(不含非瘟病毒)：出芽率不做强制要求，不纳入不完善指标籽粒整齐均匀、饱满，黄褐色或白色，无发酵、霉
变、虫蛙及异味、异臭、异物。水分≤13.5%；容重≥730G/L；杂质<1%；虫蛀<5%；不完善（包括病斑粒）s8%；黄曲霉毒素
<10ppb、赤霉烯酮≤150ppb、呕吐毒素≤1000ppb。
三、检验方式
以需方工厂检验为准。若供方对检验结果产生异议，可委托国家认可的检测部门检测。如检测结果达不到合同约定，检测费用
由供方承担，达到合同约定，检测费用则由需方承担。
四、交货地点
需方指定地点，运输费用由需方承担。
五、交货期限
在前交货，如供方不能按合同约定期限交货，需方有权单方变更或终止合同，并视为供方违约。
六、计量
以需方电子磅数量为准.
七、包装标准
散粮/包粮：编织袋包装，包装物由供方提供不计价，不返回，每条编织袋扣重 150克。
八、付款方式及期限
1、到检验合格，7天内凭发票以银行电汇付清货款。
2、供方必须提供增值税发票，如供方提供违规、虚假发票，需方有权对供方处于 20%-100%的货款处罚，并承担由此给需方造成
的一切经济损失和税务风险。（必须在发票备注栏注明纸制合同号）
九、违约责任
按《中华人民共和国民法典》有关规定执行；若发生违约纠纷，违约方按本合同金额 20%支付给守约方，由合同签订所在地人民
法院管辖。
十、其他条款
1、若货物质量达不到需方要求，供方自行处理，所发生的一切费用由供方承担；如果经过国家检验机构确定供方有掺假行为，
供方同意按不低于合同总价的 20%金额赔偿给需方。
2、物发出后 1个工作日内告之需方车号、数量等相关事宜；若火车运输需传真大票并寄小票到需方。
本合同一式两份(供需双方各执一份），须经双方代表签字盖章后有效，涂改无效，合同正本与传真件具有同等法律效力，未尽
事项双方协商解决。
需方（盖章）：广东美特机械有限公司 供方（盖章）：江苏日成橡胶有限公司
需方签名：          供方签名：          
签署日期： 签署日期：     

2025-09-03 10:27:19,291 [http-nio-8100-exec-9] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本：《采购合同书》
                                                                                                     
 
合同编号： J1960880963642748930 下单日期：2025-08-28
需方：广东美特机械有限公司 供方：江苏日成橡胶有限公司
《中华人民共和国民法典》有关条款规定，经双方共同协商，达成以下条款，以资共同遵守。
一、采购信息：
序号 商品名称 数量 单位 单价 金额（元）
1 白砂糖 粗砂糖[ 40 吨 ￥1,000.00 ￥40,000.00
运费：￥160,000.00
总额：￥200,000.00
合计人民币（大写）：贰拾万元整
 
二、质量技术标准
质量标准：小麦(不含非瘟病毒)：出芽率不做强制要求，不纳入不完善指标籽粒整齐均匀、饱满，黄褐色或白色，无发酵、霉
变、虫蛙及异味、异臭、异物。水分≤13.5%；容重≥730G/L；杂质<1%；虫蛀<5%；不完善（包括病斑粒）s8%；黄曲霉毒素
<10ppb、赤霉烯酮≤150ppb、呕吐毒素≤1000ppb。
三、检验方式
以需方工厂检验为准。若供方对检验结果产生异议，可委托国家认可的检测部门检测。如检测结果达不到合同约定，检测费用
由供方承担，达到合同约定，检测费用则由需方承担。
四、交货地点
需方指定地点，运输费用由需方承担。
五、交货期限
在前交货，如供方不能按合同约定期限交货，需方有权单方变更或终止合同，并视为供方违约。
六、计量
以需方电子磅数量为准.
七、包装标准
散粮/包粮：编织袋包装，包装物由供方提供不计价，不返回，每条编织袋扣重 150克。
八、付款方式及期限
1、到检验合格，7天内凭发票以银行电汇付清货款。
2、供方必须提供增值税发票，如供方提供违规、虚假发票，需方有权对供方处于 20%-100%的货款处罚，并承担由此给需方造成
的一切经济损失和税务风险。（必须在发票备注栏注明纸制合同号）
九、违约责任
按《中华人民共和国民法典》有关规定执行；若发生违约纠纷，违约方按本合同金额 20%支付给守约方，由合同签订所在地人民
法院管辖。
十、其他条款
1、若货物质量达不到需方要求，供方自行处理，所发生的一切费用由供方承担；如果经过国家检验机构确定供方有掺假行为，
供方同意按不低于合同总价的 20%金额赔偿给需方。
2、物发出后 1个工作日内告之需方车号、数量等相关事宜；若火车运输需传真大票并寄小票到需方。
本合同一式两份(供需双方各执一份），须经双方代表签字盖章后有效，涂改无效，合同正本与传真件具有同等法律效力，未尽
事项双方协商解决。
需方（盖章）：广东美特机械有限公司 供方（盖章）：江苏日成橡胶有限公司
需方签名：          供方签名：          
签署日期： 签署日期：     

2025-09-03 10:27:40,251 [http-nio-8100-exec-8] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本：《采购合同书》
                                                                                                     
 
合同编号： J1960880963642748930 下单日期：2025-08-28
需方：广东美特机械有限公司 供方：江苏日成橡胶有限公司
《中华人民共和国民法典》有关条款规定，经双方共同协商，达成以下条款，以资共同遵守。
一、采购信息：
序号 商品名称 数量 单位 单价 金额（元）
1 白砂糖 粗砂糖[ 40 吨 ￥1,000.00 ￥40,000.00
运费：￥160,000.00
总额：￥200,000.00
合计人民币（大写）：贰拾万元整
 
二、质量技术标准
质量标准：小麦(不含非瘟病毒)：出芽率不做强制要求，不纳入不完善指标籽粒整齐均匀、饱满，黄褐色或白色，无发酵、霉
变、虫蛙及异味、异臭、异物。水分≤13.5%；容重≥730G/L；杂质<1%；虫蛀<5%；不完善（包括病斑粒）s8%；黄曲霉毒素
<10ppb、赤霉烯酮≤150ppb、呕吐毒素≤1000ppb。
三、检验方式
以需方工厂检验为准。若供方对检验结果产生异议，可委托国家认可的检测部门检测。如检测结果达不到合同约定，检测费用
由供方承担，达到合同约定，检测费用则由需方承担。
四、交货地点
需方指定地点，运输费用由需方承担。
五、交货期限
在前交货，如供方不能按合同约定期限交货，需方有权单方变更或终止合同，并视为供方违约。
六、计量
以需方电子磅数量为准.
七、包装标准
散粮/包粮：编织袋包装，包装物由供方提供不计价，不返回，每条编织袋扣重 150克。
八、付款方式及期限
1、到检验合格，7天内凭发票以银行电汇付清货款。
2、供方必须提供增值税发票，如供方提供违规、虚假发票，需方有权对供方处于 20%-100%的货款处罚，并承担由此给需方造成
的一切经济损失和税务风险。（必须在发票备注栏注明纸制合同号）
九、违约责任
按《中华人民共和国民法典》有关规定执行；若发生违约纠纷，违约方按本合同金额 20%支付给守约方，由合同签订所在地人民
法院管辖。
十、其他条款
1、若货物质量达不到需方要求，供方自行处理，所发生的一切费用由供方承担；如果经过国家检验机构确定供方有掺假行为，
供方同意按不低于合同总价的 20%金额赔偿给需方。
2、物发出后 1个工作日内告之需方车号、数量等相关事宜；若火车运输需传真大票并寄小票到需方。
本合同一式两份(供需双方各执一份），须经双方代表签字盖章后有效，涂改无效，合同正本与传真件具有同等法律效力，未尽
事项双方协商解决。
需方（盖章）：广东美特机械有限公司 供方（盖章）：江苏日成橡胶有限公司
需方签名：          供方签名：          
签署日期： 签署日期：     

2025-09-03 10:29:23,103 [http-nio-8100-exec-5] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本：《采购合同书》
                                                                                                     
 
合同编号： J1960880963642748930 下单日期：2025-08-28
需方：广东美特机械有限公司 供方：江苏日成橡胶有限公司
《中华人民共和国民法典》有关条款规定，经双方共同协商，达成以下条款，以资共同遵守。
一、采购信息：
序号 商品名称 数量 单位 单价 金额（元）
1 白砂糖 粗砂糖[ 40 吨 ￥1,000.00 ￥40,000.00
运费：￥160,000.00
总额：￥200,000.00
合计人民币（大写）：贰拾万元整
 
二、质量技术标准
质量标准：小麦(不含非瘟病毒)：出芽率不做强制要求，不纳入不完善指标籽粒整齐均匀、饱满，黄褐色或白色，无发酵、霉
变、虫蛙及异味、异臭、异物。水分≤13.5%；容重≥730G/L；杂质<1%；虫蛀<5%；不完善（包括病斑粒）s8%；黄曲霉毒素
<10ppb、赤霉烯酮≤150ppb、呕吐毒素≤1000ppb。
三、检验方式
以需方工厂检验为准。若供方对检验结果产生异议，可委托国家认可的检测部门检测。如检测结果达不到合同约定，检测费用
由供方承担，达到合同约定，检测费用则由需方承担。
四、交货地点
需方指定地点，运输费用由需方承担。
五、交货期限
在前交货，如供方不能按合同约定期限交货，需方有权单方变更或终止合同，并视为供方违约。
六、计量
以需方电子磅数量为准.
七、包装标准
散粮/包粮：编织袋包装，包装物由供方提供不计价，不返回，每条编织袋扣重 150克。
八、付款方式及期限
1、到检验合格，7天内凭发票以银行电汇付清货款。
2、供方必须提供增值税发票，如供方提供违规、虚假发票，需方有权对供方处于 20%-100%的货款处罚，并承担由此给需方造成
的一切经济损失和税务风险。（必须在发票备注栏注明纸制合同号）
九、违约责任
按《中华人民共和国民法典》有关规定执行；若发生违约纠纷，违约方按本合同金额 20%支付给守约方，由合同签订所在地人民
法院管辖。
十、其他条款
1、若货物质量达不到需方要求，供方自行处理，所发生的一切费用由供方承担；如果经过国家检验机构确定供方有掺假行为，
供方同意按不低于合同总价的 20%金额赔偿给需方。
2、物发出后 1个工作日内告之需方车号、数量等相关事宜；若火车运输需传真大票并寄小票到需方。
本合同一式两份(供需双方各执一份），须经双方代表签字盖章后有效，涂改无效，合同正本与传真件具有同等法律效力，未尽
事项双方协商解决。
需方（盖章）：广东美特机械有限公司 供方（盖章）：江苏日成橡胶有限公司
需方签名：          供方签名：          
签署日期： 签署日期：     

2025-09-03 10:30:07,704 [http-nio-8100-exec-6] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本：《采购合同书》
                                                                                                     
 
合同编号： J1960880963642748930 下单日期：2025-08-28
需方：广东美特机械有限公司 供方：江苏日成橡胶有限公司
《中华人民共和国民法典》有关条款规定，经双方共同协商，达成以下条款，以资共同遵守。
一、采购信息：
序号 商品名称 数量 单位 单价 金额（元）
1 白砂糖 粗砂糖[ 40 吨 ￥1,000.00 ￥40,000.00
运费：￥160,000.00
总额：￥200,000.00
合计人民币（大写）：贰拾万元整
 
二、质量技术标准
质量标准：小麦(不含非瘟病毒)：出芽率不做强制要求，不纳入不完善指标籽粒整齐均匀、饱满，黄褐色或白色，无发酵、霉
变、虫蛙及异味、异臭、异物。水分≤13.5%；容重≥730G/L；杂质<1%；虫蛀<5%；不完善（包括病斑粒）s8%；黄曲霉毒素
<10ppb、赤霉烯酮≤150ppb、呕吐毒素≤1000ppb。
三、检验方式
以需方工厂检验为准。若供方对检验结果产生异议，可委托国家认可的检测部门检测。如检测结果达不到合同约定，检测费用
由供方承担，达到合同约定，检测费用则由需方承担。
四、交货地点
需方指定地点，运输费用由需方承担。
五、交货期限
在前交货，如供方不能按合同约定期限交货，需方有权单方变更或终止合同，并视为供方违约。
六、计量
以需方电子磅数量为准.
七、包装标准
散粮/包粮：编织袋包装，包装物由供方提供不计价，不返回，每条编织袋扣重 150克。
八、付款方式及期限
1、到检验合格，7天内凭发票以银行电汇付清货款。
2、供方必须提供增值税发票，如供方提供违规、虚假发票，需方有权对供方处于 20%-100%的货款处罚，并承担由此给需方造成
的一切经济损失和税务风险。（必须在发票备注栏注明纸制合同号）
九、违约责任
按《中华人民共和国民法典》有关规定执行；若发生违约纠纷，违约方按本合同金额 20%支付给守约方，由合同签订所在地人民
法院管辖。
十、其他条款
1、若货物质量达不到需方要求，供方自行处理，所发生的一切费用由供方承担；如果经过国家检验机构确定供方有掺假行为，
供方同意按不低于合同总价的 20%金额赔偿给需方。
2、物发出后 1个工作日内告之需方车号、数量等相关事宜；若火车运输需传真大票并寄小票到需方。
本合同一式两份(供需双方各执一份），须经双方代表签字盖章后有效，涂改无效，合同正本与传真件具有同等法律效力，未尽
事项双方协商解决。
需方（盖章）：广东美特机械有限公司 供方（盖章）：江苏日成橡胶有限公司
需方签名：          供方签名：          
签署日期： 签署日期：     

2025-09-03 10:30:07,709 [http-nio-8100-exec-7] ERROR [com.agentsflex.core.llm.client.HttpClient] HttpClient.java:136 - java.lang.IllegalArgumentException: Expected URL scheme 'http' or 'https' but no scheme was found for null/c...
java.lang.IllegalArgumentException: Expected URL scheme 'http' or 'https' but no scheme was found for null/c...
	at okhttp3.HttpUrl$Builder.parse$okhttp(HttpUrl.kt:1261)
	at okhttp3.HttpUrl$Companion.get(HttpUrl.kt:1634)
	at okhttp3.Request$Builder.url(Request.kt:184)
	at com.agentsflex.core.llm.client.HttpClient.execute0(HttpClient.java:157)
	at com.agentsflex.core.llm.client.HttpClient.executeString(HttpClient.java:130)
	at com.agentsflex.core.llm.client.HttpClient.post(HttpClient.java:57)
	at com.agentsflex.llm.qwen.QwenLlm.chat(QwenLlm.java:63)
	at com.agentsflex.core.chain.node.LlmNode.execute(LlmNode.java:121)
	at com.agentsflex.core.chain.Chain.doExecuteNode(Chain.java:501)
	at com.agentsflex.core.chain.Chain.doExecuteNodes(Chain.java:472)
	at com.agentsflex.core.chain.Chain.doExecuteNode(Chain.java:539)
	at com.agentsflex.core.chain.Chain.doExecuteNodes(Chain.java:472)
	at com.agentsflex.core.chain.Chain.doExecuteNode(Chain.java:539)
	at com.agentsflex.core.chain.Chain.doExecuteNodes(Chain.java:472)
	at com.agentsflex.core.chain.Chain.executeInternal(Chain.java:455)
	at com.agentsflex.core.chain.Chain.runInLifeCycle(Chain.java:603)
	at com.agentsflex.core.chain.Chain.executeForResult(Chain.java:353)
	at com.agentsflex.core.chain.Chain.executeForResult(Chain.java:346)
	at cn.tycoding.langchat.ai.core.service.impl.WorkflowRunningServiceImpl.workflowRunning(WorkflowRunningServiceImpl.java:170)
	at cn.tycoding.langchat.server.controller.AiWorkflowController.tryRunning(AiWorkflowController.java:136)
	at jdk.internal.reflect.GeneratedMethodAccessor177.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:259)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:192)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-03 10:39:16,659 [http-nio-8100-exec-6] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本：《采购合同书》
                                                                                                     
 
合同编号： J1960880963642748930 下单日期：2025-08-28
需方：广东美特机械有限公司 供方：江苏日成橡胶有限公司
《中华人民共和国民法典》有关条款规定，经双方共同协商，达成以下条款，以资共同遵守。
一、采购信息：
序号 商品名称 数量 单位 单价 金额（元）
1 白砂糖 粗砂糖[ 40 吨 ￥1,000.00 ￥40,000.00
运费：￥160,000.00
总额：￥200,000.00
合计人民币（大写）：贰拾万元整
 
二、质量技术标准
质量标准：小麦(不含非瘟病毒)：出芽率不做强制要求，不纳入不完善指标籽粒整齐均匀、饱满，黄褐色或白色，无发酵、霉
变、虫蛙及异味、异臭、异物。水分≤13.5%；容重≥730G/L；杂质<1%；虫蛀<5%；不完善（包括病斑粒）s8%；黄曲霉毒素
<10ppb、赤霉烯酮≤150ppb、呕吐毒素≤1000ppb。
三、检验方式
以需方工厂检验为准。若供方对检验结果产生异议，可委托国家认可的检测部门检测。如检测结果达不到合同约定，检测费用
由供方承担，达到合同约定，检测费用则由需方承担。
四、交货地点
需方指定地点，运输费用由需方承担。
五、交货期限
在前交货，如供方不能按合同约定期限交货，需方有权单方变更或终止合同，并视为供方违约。
六、计量
以需方电子磅数量为准.
七、包装标准
散粮/包粮：编织袋包装，包装物由供方提供不计价，不返回，每条编织袋扣重 150克。
八、付款方式及期限
1、到检验合格，7天内凭发票以银行电汇付清货款。
2、供方必须提供增值税发票，如供方提供违规、虚假发票，需方有权对供方处于 20%-100%的货款处罚，并承担由此给需方造成
的一切经济损失和税务风险。（必须在发票备注栏注明纸制合同号）
九、违约责任
按《中华人民共和国民法典》有关规定执行；若发生违约纠纷，违约方按本合同金额 20%支付给守约方，由合同签订所在地人民
法院管辖。
十、其他条款
1、若货物质量达不到需方要求，供方自行处理，所发生的一切费用由供方承担；如果经过国家检验机构确定供方有掺假行为，
供方同意按不低于合同总价的 20%金额赔偿给需方。
2、物发出后 1个工作日内告之需方车号、数量等相关事宜；若火车运输需传真大票并寄小票到需方。
本合同一式两份(供需双方各执一份），须经双方代表签字盖章后有效，涂改无效，合同正本与传真件具有同等法律效力，未尽
事项双方协商解决。
需方（盖章）：广东美特机械有限公司 供方（盖章）：江苏日成橡胶有限公司
需方签名：          供方签名：          
签署日期： 签署日期：     

2025-09-03 10:39:16,661 [http-nio-8100-exec-2] ERROR [com.agentsflex.core.llm.client.HttpClient] HttpClient.java:136 - java.lang.IllegalArgumentException: Expected URL scheme 'http' or 'https' but no scheme was found for null/c...
java.lang.IllegalArgumentException: Expected URL scheme 'http' or 'https' but no scheme was found for null/c...
	at okhttp3.HttpUrl$Builder.parse$okhttp(HttpUrl.kt:1261)
	at okhttp3.HttpUrl$Companion.get(HttpUrl.kt:1634)
	at okhttp3.Request$Builder.url(Request.kt:184)
	at com.agentsflex.core.llm.client.HttpClient.execute0(HttpClient.java:157)
	at com.agentsflex.core.llm.client.HttpClient.executeString(HttpClient.java:130)
	at com.agentsflex.core.llm.client.HttpClient.post(HttpClient.java:57)
	at com.agentsflex.llm.qwen.QwenLlm.chat(QwenLlm.java:63)
	at com.agentsflex.core.chain.node.LlmNode.execute(LlmNode.java:121)
	at com.agentsflex.core.chain.Chain.doExecuteNode(Chain.java:501)
	at com.agentsflex.core.chain.Chain.doExecuteNodes(Chain.java:472)
	at com.agentsflex.core.chain.Chain.doExecuteNode(Chain.java:539)
	at com.agentsflex.core.chain.Chain.doExecuteNodes(Chain.java:472)
	at com.agentsflex.core.chain.Chain.doExecuteNode(Chain.java:539)
	at com.agentsflex.core.chain.Chain.doExecuteNodes(Chain.java:472)
	at com.agentsflex.core.chain.Chain.executeInternal(Chain.java:455)
	at com.agentsflex.core.chain.Chain.runInLifeCycle(Chain.java:603)
	at com.agentsflex.core.chain.Chain.executeForResult(Chain.java:353)
	at com.agentsflex.core.chain.Chain.executeForResult(Chain.java:346)
	at cn.tycoding.langchat.ai.core.service.impl.WorkflowRunningServiceImpl.workflowRunning(WorkflowRunningServiceImpl.java:170)
	at cn.tycoding.langchat.server.controller.AiWorkflowController.tryRunning(AiWorkflowController.java:136)
	at jdk.internal.reflect.GeneratedMethodAccessor177.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:259)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:192)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-03 10:55:52,229 [http-nio-8100-exec-3] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本：《采购合同书》
                                                                                                     
 
合同编号： J1960880963642748930 下单日期：2025-08-28
需方：广东美特机械有限公司 供方：江苏日成橡胶有限公司
《中华人民共和国民法典》有关条款规定，经双方共同协商，达成以下条款，以资共同遵守。
一、采购信息：
序号 商品名称 数量 单位 单价 金额（元）
1 白砂糖 粗砂糖[ 40 吨 ￥1,000.00 ￥40,000.00
运费：￥160,000.00
总额：￥200,000.00
合计人民币（大写）：贰拾万元整
 
二、质量技术标准
质量标准：小麦(不含非瘟病毒)：出芽率不做强制要求，不纳入不完善指标籽粒整齐均匀、饱满，黄褐色或白色，无发酵、霉
变、虫蛙及异味、异臭、异物。水分≤13.5%；容重≥730G/L；杂质<1%；虫蛀<5%；不完善（包括病斑粒）s8%；黄曲霉毒素
<10ppb、赤霉烯酮≤150ppb、呕吐毒素≤1000ppb。
三、检验方式
以需方工厂检验为准。若供方对检验结果产生异议，可委托国家认可的检测部门检测。如检测结果达不到合同约定，检测费用
由供方承担，达到合同约定，检测费用则由需方承担。
四、交货地点
需方指定地点，运输费用由需方承担。
五、交货期限
在前交货，如供方不能按合同约定期限交货，需方有权单方变更或终止合同，并视为供方违约。
六、计量
以需方电子磅数量为准.
七、包装标准
散粮/包粮：编织袋包装，包装物由供方提供不计价，不返回，每条编织袋扣重 150克。
八、付款方式及期限
1、到检验合格，7天内凭发票以银行电汇付清货款。
2、供方必须提供增值税发票，如供方提供违规、虚假发票，需方有权对供方处于 20%-100%的货款处罚，并承担由此给需方造成
的一切经济损失和税务风险。（必须在发票备注栏注明纸制合同号）
九、违约责任
按《中华人民共和国民法典》有关规定执行；若发生违约纠纷，违约方按本合同金额 20%支付给守约方，由合同签订所在地人民
法院管辖。
十、其他条款
1、若货物质量达不到需方要求，供方自行处理，所发生的一切费用由供方承担；如果经过国家检验机构确定供方有掺假行为，
供方同意按不低于合同总价的 20%金额赔偿给需方。
2、物发出后 1个工作日内告之需方车号、数量等相关事宜；若火车运输需传真大票并寄小票到需方。
本合同一式两份(供需双方各执一份），须经双方代表签字盖章后有效，涂改无效，合同正本与传真件具有同等法律效力，未尽
事项双方协商解决。
需方（盖章）：广东美特机械有限公司 供方（盖章）：江苏日成橡胶有限公司
需方签名：          供方签名：          
签署日期： 签署日期：     

2025-09-03 10:55:52,232 [http-nio-8100-exec-9] ERROR [com.agentsflex.core.llm.client.HttpClient] HttpClient.java:136 - java.lang.IllegalArgumentException: Expected URL scheme 'http' or 'https' but no scheme was found for null/c...
java.lang.IllegalArgumentException: Expected URL scheme 'http' or 'https' but no scheme was found for null/c...
	at okhttp3.HttpUrl$Builder.parse$okhttp(HttpUrl.kt:1261)
	at okhttp3.HttpUrl$Companion.get(HttpUrl.kt:1634)
	at okhttp3.Request$Builder.url(Request.kt:184)
	at com.agentsflex.core.llm.client.HttpClient.execute0(HttpClient.java:157)
	at com.agentsflex.core.llm.client.HttpClient.executeString(HttpClient.java:130)
	at com.agentsflex.core.llm.client.HttpClient.post(HttpClient.java:57)
	at com.agentsflex.llm.qwen.QwenLlm.chat(QwenLlm.java:63)
	at com.agentsflex.core.chain.node.LlmNode.execute(LlmNode.java:121)
	at com.agentsflex.core.chain.Chain.doExecuteNode(Chain.java:501)
	at com.agentsflex.core.chain.Chain.doExecuteNodes(Chain.java:472)
	at com.agentsflex.core.chain.Chain.doExecuteNode(Chain.java:539)
	at com.agentsflex.core.chain.Chain.doExecuteNodes(Chain.java:472)
	at com.agentsflex.core.chain.Chain.doExecuteNode(Chain.java:539)
	at com.agentsflex.core.chain.Chain.doExecuteNodes(Chain.java:472)
	at com.agentsflex.core.chain.Chain.executeInternal(Chain.java:455)
	at com.agentsflex.core.chain.Chain.runInLifeCycle(Chain.java:603)
	at com.agentsflex.core.chain.Chain.executeForResult(Chain.java:353)
	at com.agentsflex.core.chain.Chain.executeForResult(Chain.java:346)
	at cn.tycoding.langchat.ai.core.service.impl.WorkflowRunningServiceImpl.workflowRunning(WorkflowRunningServiceImpl.java:170)
	at cn.tycoding.langchat.server.controller.AiWorkflowController.tryRunning(AiWorkflowController.java:136)
	at jdk.internal.reflect.GeneratedMethodAccessor177.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:259)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:192)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-03 10:56:02,284 [http-nio-8100-exec-3] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本：《采购合同书》
                                                                                                     
 
合同编号： J1960880963642748930 下单日期：2025-08-28
需方：广东美特机械有限公司 供方：江苏日成橡胶有限公司
《中华人民共和国民法典》有关条款规定，经双方共同协商，达成以下条款，以资共同遵守。
一、采购信息：
序号 商品名称 数量 单位 单价 金额（元）
1 白砂糖 粗砂糖[ 40 吨 ￥1,000.00 ￥40,000.00
运费：￥160,000.00
总额：￥200,000.00
合计人民币（大写）：贰拾万元整
 
二、质量技术标准
质量标准：小麦(不含非瘟病毒)：出芽率不做强制要求，不纳入不完善指标籽粒整齐均匀、饱满，黄褐色或白色，无发酵、霉
变、虫蛙及异味、异臭、异物。水分≤13.5%；容重≥730G/L；杂质<1%；虫蛀<5%；不完善（包括病斑粒）s8%；黄曲霉毒素
<10ppb、赤霉烯酮≤150ppb、呕吐毒素≤1000ppb。
三、检验方式
以需方工厂检验为准。若供方对检验结果产生异议，可委托国家认可的检测部门检测。如检测结果达不到合同约定，检测费用
由供方承担，达到合同约定，检测费用则由需方承担。
四、交货地点
需方指定地点，运输费用由需方承担。
五、交货期限
在前交货，如供方不能按合同约定期限交货，需方有权单方变更或终止合同，并视为供方违约。
六、计量
以需方电子磅数量为准.
七、包装标准
散粮/包粮：编织袋包装，包装物由供方提供不计价，不返回，每条编织袋扣重 150克。
八、付款方式及期限
1、到检验合格，7天内凭发票以银行电汇付清货款。
2、供方必须提供增值税发票，如供方提供违规、虚假发票，需方有权对供方处于 20%-100%的货款处罚，并承担由此给需方造成
的一切经济损失和税务风险。（必须在发票备注栏注明纸制合同号）
九、违约责任
按《中华人民共和国民法典》有关规定执行；若发生违约纠纷，违约方按本合同金额 20%支付给守约方，由合同签订所在地人民
法院管辖。
十、其他条款
1、若货物质量达不到需方要求，供方自行处理，所发生的一切费用由供方承担；如果经过国家检验机构确定供方有掺假行为，
供方同意按不低于合同总价的 20%金额赔偿给需方。
2、物发出后 1个工作日内告之需方车号、数量等相关事宜；若火车运输需传真大票并寄小票到需方。
本合同一式两份(供需双方各执一份），须经双方代表签字盖章后有效，涂改无效，合同正本与传真件具有同等法律效力，未尽
事项双方协商解决。
需方（盖章）：广东美特机械有限公司 供方（盖章）：江苏日成橡胶有限公司
需方签名：          供方签名：          
签署日期： 签署日期：     

2025-09-03 10:56:02,286 [http-nio-8100-exec-7] ERROR [com.agentsflex.core.llm.client.HttpClient] HttpClient.java:136 - java.lang.IllegalArgumentException: Expected URL scheme 'http' or 'https' but no scheme was found for null/c...
java.lang.IllegalArgumentException: Expected URL scheme 'http' or 'https' but no scheme was found for null/c...
	at okhttp3.HttpUrl$Builder.parse$okhttp(HttpUrl.kt:1261)
	at okhttp3.HttpUrl$Companion.get(HttpUrl.kt:1634)
	at okhttp3.Request$Builder.url(Request.kt:184)
	at com.agentsflex.core.llm.client.HttpClient.execute0(HttpClient.java:157)
	at com.agentsflex.core.llm.client.HttpClient.executeString(HttpClient.java:130)
	at com.agentsflex.core.llm.client.HttpClient.post(HttpClient.java:57)
	at com.agentsflex.llm.qwen.QwenLlm.chat(QwenLlm.java:63)
	at com.agentsflex.core.chain.node.LlmNode.execute(LlmNode.java:121)
	at com.agentsflex.core.chain.Chain.doExecuteNode(Chain.java:501)
	at com.agentsflex.core.chain.Chain.doExecuteNodes(Chain.java:472)
	at com.agentsflex.core.chain.Chain.doExecuteNode(Chain.java:539)
	at com.agentsflex.core.chain.Chain.doExecuteNodes(Chain.java:472)
	at com.agentsflex.core.chain.Chain.doExecuteNode(Chain.java:539)
	at com.agentsflex.core.chain.Chain.doExecuteNodes(Chain.java:472)
	at com.agentsflex.core.chain.Chain.executeInternal(Chain.java:455)
	at com.agentsflex.core.chain.Chain.runInLifeCycle(Chain.java:603)
	at com.agentsflex.core.chain.Chain.executeForResult(Chain.java:353)
	at com.agentsflex.core.chain.Chain.executeForResult(Chain.java:346)
	at cn.tycoding.langchat.ai.core.service.impl.WorkflowRunningServiceImpl.workflowRunning(WorkflowRunningServiceImpl.java:170)
	at cn.tycoding.langchat.server.controller.AiWorkflowController.tryRunning(AiWorkflowController.java:136)
	at jdk.internal.reflect.GeneratedMethodAccessor177.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:259)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:192)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-03 10:56:07,594 [http-nio-8100-exec-10] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本：《采购合同书》
                                                                                                     
 
合同编号： J1960880963642748930 下单日期：2025-08-28
需方：广东美特机械有限公司 供方：江苏日成橡胶有限公司
《中华人民共和国民法典》有关条款规定，经双方共同协商，达成以下条款，以资共同遵守。
一、采购信息：
序号 商品名称 数量 单位 单价 金额（元）
1 白砂糖 粗砂糖[ 40 吨 ￥1,000.00 ￥40,000.00
运费：￥160,000.00
总额：￥200,000.00
合计人民币（大写）：贰拾万元整
 
二、质量技术标准
质量标准：小麦(不含非瘟病毒)：出芽率不做强制要求，不纳入不完善指标籽粒整齐均匀、饱满，黄褐色或白色，无发酵、霉
变、虫蛙及异味、异臭、异物。水分≤13.5%；容重≥730G/L；杂质<1%；虫蛀<5%；不完善（包括病斑粒）s8%；黄曲霉毒素
<10ppb、赤霉烯酮≤150ppb、呕吐毒素≤1000ppb。
三、检验方式
以需方工厂检验为准。若供方对检验结果产生异议，可委托国家认可的检测部门检测。如检测结果达不到合同约定，检测费用
由供方承担，达到合同约定，检测费用则由需方承担。
四、交货地点
需方指定地点，运输费用由需方承担。
五、交货期限
在前交货，如供方不能按合同约定期限交货，需方有权单方变更或终止合同，并视为供方违约。
六、计量
以需方电子磅数量为准.
七、包装标准
散粮/包粮：编织袋包装，包装物由供方提供不计价，不返回，每条编织袋扣重 150克。
八、付款方式及期限
1、到检验合格，7天内凭发票以银行电汇付清货款。
2、供方必须提供增值税发票，如供方提供违规、虚假发票，需方有权对供方处于 20%-100%的货款处罚，并承担由此给需方造成
的一切经济损失和税务风险。（必须在发票备注栏注明纸制合同号）
九、违约责任
按《中华人民共和国民法典》有关规定执行；若发生违约纠纷，违约方按本合同金额 20%支付给守约方，由合同签订所在地人民
法院管辖。
十、其他条款
1、若货物质量达不到需方要求，供方自行处理，所发生的一切费用由供方承担；如果经过国家检验机构确定供方有掺假行为，
供方同意按不低于合同总价的 20%金额赔偿给需方。
2、物发出后 1个工作日内告之需方车号、数量等相关事宜；若火车运输需传真大票并寄小票到需方。
本合同一式两份(供需双方各执一份），须经双方代表签字盖章后有效，涂改无效，合同正本与传真件具有同等法律效力，未尽
事项双方协商解决。
需方（盖章）：广东美特机械有限公司 供方（盖章）：江苏日成橡胶有限公司
需方签名：          供方签名：          
签署日期： 签署日期：     

2025-09-03 10:56:07,597 [http-nio-8100-exec-4] ERROR [com.agentsflex.core.llm.client.HttpClient] HttpClient.java:136 - java.lang.IllegalArgumentException: Expected URL scheme 'http' or 'https' but no scheme was found for null/c...
java.lang.IllegalArgumentException: Expected URL scheme 'http' or 'https' but no scheme was found for null/c...
	at okhttp3.HttpUrl$Builder.parse$okhttp(HttpUrl.kt:1261)
	at okhttp3.HttpUrl$Companion.get(HttpUrl.kt:1634)
	at okhttp3.Request$Builder.url(Request.kt:184)
	at com.agentsflex.core.llm.client.HttpClient.execute0(HttpClient.java:157)
	at com.agentsflex.core.llm.client.HttpClient.executeString(HttpClient.java:130)
	at com.agentsflex.core.llm.client.HttpClient.post(HttpClient.java:57)
	at com.agentsflex.llm.qwen.QwenLlm.chat(QwenLlm.java:63)
	at com.agentsflex.core.chain.node.LlmNode.execute(LlmNode.java:121)
	at com.agentsflex.core.chain.Chain.doExecuteNode(Chain.java:501)
	at com.agentsflex.core.chain.Chain.doExecuteNodes(Chain.java:472)
	at com.agentsflex.core.chain.Chain.doExecuteNode(Chain.java:539)
	at com.agentsflex.core.chain.Chain.doExecuteNodes(Chain.java:472)
	at com.agentsflex.core.chain.Chain.doExecuteNode(Chain.java:539)
	at com.agentsflex.core.chain.Chain.doExecuteNodes(Chain.java:472)
	at com.agentsflex.core.chain.Chain.executeInternal(Chain.java:455)
	at com.agentsflex.core.chain.Chain.runInLifeCycle(Chain.java:603)
	at com.agentsflex.core.chain.Chain.executeForResult(Chain.java:353)
	at com.agentsflex.core.chain.Chain.executeForResult(Chain.java:346)
	at cn.tycoding.langchat.ai.core.service.impl.WorkflowRunningServiceImpl.workflowRunning(WorkflowRunningServiceImpl.java:170)
	at cn.tycoding.langchat.server.controller.AiWorkflowController.tryRunning(AiWorkflowController.java:136)
	at jdk.internal.reflect.GeneratedMethodAccessor177.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:259)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:192)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-03 10:57:13,371 [http-nio-8100-exec-8] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本：《采购合同书》
                                                                                                     
 
合同编号： J1960880963642748930 下单日期：2025-08-28
需方：广东美特机械有限公司 供方：江苏日成橡胶有限公司
《中华人民共和国民法典》有关条款规定，经双方共同协商，达成以下条款，以资共同遵守。
一、采购信息：
序号 商品名称 数量 单位 单价 金额（元）
1 白砂糖 粗砂糖[ 40 吨 ￥1,000.00 ￥40,000.00
运费：￥160,000.00
总额：￥200,000.00
合计人民币（大写）：贰拾万元整
 
二、质量技术标准
质量标准：小麦(不含非瘟病毒)：出芽率不做强制要求，不纳入不完善指标籽粒整齐均匀、饱满，黄褐色或白色，无发酵、霉
变、虫蛙及异味、异臭、异物。水分≤13.5%；容重≥730G/L；杂质<1%；虫蛀<5%；不完善（包括病斑粒）s8%；黄曲霉毒素
<10ppb、赤霉烯酮≤150ppb、呕吐毒素≤1000ppb。
三、检验方式
以需方工厂检验为准。若供方对检验结果产生异议，可委托国家认可的检测部门检测。如检测结果达不到合同约定，检测费用
由供方承担，达到合同约定，检测费用则由需方承担。
四、交货地点
需方指定地点，运输费用由需方承担。
五、交货期限
在前交货，如供方不能按合同约定期限交货，需方有权单方变更或终止合同，并视为供方违约。
六、计量
以需方电子磅数量为准.
七、包装标准
散粮/包粮：编织袋包装，包装物由供方提供不计价，不返回，每条编织袋扣重 150克。
八、付款方式及期限
1、到检验合格，7天内凭发票以银行电汇付清货款。
2、供方必须提供增值税发票，如供方提供违规、虚假发票，需方有权对供方处于 20%-100%的货款处罚，并承担由此给需方造成
的一切经济损失和税务风险。（必须在发票备注栏注明纸制合同号）
九、违约责任
按《中华人民共和国民法典》有关规定执行；若发生违约纠纷，违约方按本合同金额 20%支付给守约方，由合同签订所在地人民
法院管辖。
十、其他条款
1、若货物质量达不到需方要求，供方自行处理，所发生的一切费用由供方承担；如果经过国家检验机构确定供方有掺假行为，
供方同意按不低于合同总价的 20%金额赔偿给需方。
2、物发出后 1个工作日内告之需方车号、数量等相关事宜；若火车运输需传真大票并寄小票到需方。
本合同一式两份(供需双方各执一份），须经双方代表签字盖章后有效，涂改无效，合同正本与传真件具有同等法律效力，未尽
事项双方协商解决。
需方（盖章）：广东美特机械有限公司 供方（盖章）：江苏日成橡胶有限公司
需方签名：          供方签名：          
签署日期： 签署日期：     

2025-09-03 10:57:15,507 [http-nio-8100-exec-1] ERROR [com.agentsflex.core.llm.client.HttpClient] HttpClient.java:136 - java.lang.IllegalArgumentException: Expected URL scheme 'http' or 'https' but no scheme was found for null/c...
java.lang.IllegalArgumentException: Expected URL scheme 'http' or 'https' but no scheme was found for null/c...
	at okhttp3.HttpUrl$Builder.parse$okhttp(HttpUrl.kt:1261)
	at okhttp3.HttpUrl$Companion.get(HttpUrl.kt:1634)
	at okhttp3.Request$Builder.url(Request.kt:184)
	at com.agentsflex.core.llm.client.HttpClient.execute0(HttpClient.java:157)
	at com.agentsflex.core.llm.client.HttpClient.executeString(HttpClient.java:130)
	at com.agentsflex.core.llm.client.HttpClient.post(HttpClient.java:57)
	at com.agentsflex.llm.qwen.QwenLlm.chat(QwenLlm.java:63)
	at com.agentsflex.core.chain.node.LlmNode.execute(LlmNode.java:121)
	at com.agentsflex.core.chain.Chain.doExecuteNode(Chain.java:501)
	at com.agentsflex.core.chain.Chain.doExecuteNodes(Chain.java:472)
	at com.agentsflex.core.chain.Chain.doExecuteNode(Chain.java:539)
	at com.agentsflex.core.chain.Chain.doExecuteNodes(Chain.java:472)
	at com.agentsflex.core.chain.Chain.doExecuteNode(Chain.java:539)
	at com.agentsflex.core.chain.Chain.doExecuteNodes(Chain.java:472)
	at com.agentsflex.core.chain.Chain.executeInternal(Chain.java:455)
	at com.agentsflex.core.chain.Chain.runInLifeCycle(Chain.java:603)
	at com.agentsflex.core.chain.Chain.executeForResult(Chain.java:353)
	at com.agentsflex.core.chain.Chain.executeForResult(Chain.java:346)
	at cn.tycoding.langchat.ai.core.service.impl.WorkflowRunningServiceImpl.workflowRunning(WorkflowRunningServiceImpl.java:170)
	at cn.tycoding.langchat.server.controller.AiWorkflowController.tryRunning(AiWorkflowController.java:136)
	at jdk.internal.reflect.GeneratedMethodAccessor177.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:259)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:192)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-03 11:15:53,501 [http-nio-8100-exec-5] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本： 
个人信用报告
 
报告编号：2025061016250556886580 报告时间：2025-06-10 16:25:05
姓名： 侯明 证件类型：身份证 证件号码：371322198905214915 已婚
 
信贷记录
这部分包含您的信用卡、贷款和其他信贷记录。金额类数据均以人民币计算，精确到元。
信 息概要
贷款
信用卡 其他业务 逾期记录可能影响对您的信用评价。
购房 其他
账户数 48 4 61 -- 购房贷款，包括个人住房贷款、个人商用
房（包括商住两用）贷款和个人住房公积
未结清/未销户账户数 19 -- 3 -- 金贷款。
发生过逾期的账户数 2 -- 2 -- 发生过逾期的信用卡账户，指曾经“未按
时还最低还款额”的贷记卡账户和“透支
发生过90天以上逾期的账户数 -- -- -- -- 超过60天”的准贷记卡账户。
为个人 为企业
相关还款责任账户数 1 3
信用卡
发生过逾期的贷记卡账户明细如下：
1. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（新加坡元账户，卡片尾号：0792）。截至2025年05月，信用
额度21,715，余额0，当前无逾期。最近5年内有1个月处于逾期状态，没有发生过90天以上逾期。
2. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（美元账户，卡片尾号：0792）。截至2025年05月，信用额度
21,060，余额695，当前有逾期。最近5年内有4个月处于逾期状态，没有发生过90天以上逾期。
从未逾期过的贷记卡及透支未超过60天的准贷记卡账户明细如下：
3. 2011年02月23日中国银行股份有限公司临沂分行发放的贷记卡（人民币账户，卡片尾号：4204）。截至2025年05月，信用额度
2,000，已使用额度0。
4. 2011年04月27日兴业银行股份有限公司发放的贷记卡（人民币账户）。截至2025年06月，信用额度8,000，已使用额度0。
5. 2018年12月06日中国工商银行股份有限公司临沂分行发放的贷记卡（人民币账户，卡片尾号：7133）。截至2025年05月，信用
额度0，已使用额度0。
6. 2020年08月18日中信银行股份有限公司信用卡中心发放的贷记卡（人民币账户）。截至2025年05月，信用额度50,000，已使用
额度0。
7. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（人民币账户，卡片尾号：0792）。截至2025年05月，信用
额度20,000，已使用额度0。
8. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（澳大利亚元账户，卡片尾号：0792）。截至2025年05月，
信用额度17,378，已使用额度0。
9. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（瑞士法郎账户，卡片尾号：0792）。截至2025年05月，信
用额度24,559，已使用额度0。
10. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（日元账户，卡片尾号：0792）。截至2025年05月，信用额
度16,584，已使用额度0。
11. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（香港元账户，卡片尾号：0792）。截至2025年05月，信用
额度20,965，已使用额度57。
第 1 页，共 7 页
12. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（欧元账户，卡片尾号：0792）。截至2025年05月，信用额
度19,671，已使用额度0。
13. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（英镑账户，卡片尾号：0792）。截至2025年05月，信用额
度20,782，已使用额度0。
14. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（新西兰元账户，卡片尾号：0792）。截至2025年05月，信
用额度17,587，已使用额度0。
15. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（加元账户，卡片尾号：0792）。截至2025年05月，信用额
度18,907，已使用额度0。
16. 2024年03月14日中国建设银行股份有限公司深圳深圳湾支行发放的贷记卡（人民币账户，卡片尾号：9345）。截至2025年
05月，信用额度0，余额230,750（含未出单的大额专项分期余额220,000）。
17. 2011年02月01日中国农业银行股份有限公司山东省分行发放的贷记卡（人民币账户，卡片尾号：3721），2022年10月销户。
18. 2017年07月31日上海浦东发展银行股份有限公司信用卡中心发放的贷记卡（人民币账户），2022年12月销户。
19. 2018年09月13日中国建设银行股份有限公司临沂新城支行发放的贷记卡（人民币账户，卡片尾号：2154），2022年09月销户。
20. 2018年09月13日中国建设银行股份有限公司临沂新城支行发放的贷记卡（美元账户，卡片尾号：2154），2022年09月销户。
21. 2018年10月18日中国建设银行股份有限公司临沂新城支行发放的贷记卡（人民币账户，卡片尾号：0929），2022年09月销户。
22. 2018年10月18日中国建设银行股份有限公司临沂新城支行发放的贷记卡（美元账户，卡片尾号：0929），2022年09月销户。
23. 2018年10月22日中国建设银行股份有限公司临沂新城支行发放的贷记卡（美元账户，卡片尾号：2539），2022年09月销户。
24. 2018年10月22日中国建设银行股份有限公司临沂新城支行发放的贷记卡（人民币账户，卡片尾号：2539），2022年09月销户。
25. 2018年10月23日中国建设银行股份有限公司临沂新城支行发放的贷记卡（美元账户，卡片尾号：4982），2022年09月销户。
26. 2018年10月23日中国建设银行股份有限公司临沂新城支行发放的贷记卡（人民币账户，卡片尾号：4982），2022年09月销户。
27. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（加元账户，卡片尾号：5529），2023年07月销户。
28. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（欧元账户，卡片尾号：5529），2023年07月销户。
29. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（人民币账户，卡片尾号：5529），2023年07月销户。
30. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（瑞士法郎账户，卡片尾号：5529），2023年07月销户。
31. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（新西兰元账户，卡片尾号：5529），2023年07月销户。
32. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（澳大利亚元账户，卡片尾号：5529），2023年07月销户。
33. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（新加坡元账户，卡片尾号：5529），2023年07月销户。
34. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（美元账户，卡片尾号：5529），2023年07月销户。
35. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（英镑账户，卡片尾号：5529），2023年07月销户。
36. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（香港元账户，卡片尾号：5529），2023年07月销户。
37. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（日元账户，卡片尾号：5529），2023年07月销户。
38. 2019年07月08日中国光大银行股份有限公司发放的贷记卡（人民币账户，卡片尾号：6876），2022年12月销户。
39. 2019年07月08日中国光大银行股份有限公司发放的贷记卡（美元账户，卡片尾号：6876），2022年12月销户。
40. 2019年07月08日中国光大银行股份有限公司发放的贷记卡（人民币账户，卡片尾号：2645），2022年12月销户。
41. 2020年08月26日中国建设银行股份有限公司临沂新城支行发放的贷记卡（人民币账户，卡片尾号：4977），2022年09月销户。
42. 2022年01月05日中国建设银行股份有限公司临沂新城支行发放的贷记卡（人民币账户，卡片尾号：4987），2022年09月销户。
43. 2023年02月02日交通银行股份有限公司太平洋信用卡中心发放的贷记卡（人民币账户，卡片尾号：6182），2023年03月销户。
44. 2023年02月02日交通银行股份有限公司太平洋信用卡中心发放的贷记卡（美元账户，卡片尾号：6182），2023年03月销户。
45. 2023年09月27日交通银行股份有限公司太平洋信用卡中心发放的贷记卡（人民币账户，卡片尾号：4965），2024年04月销户。
46. 2012年03月20日中国银行股份有限公司山东省分行发放的贷记卡（人民币账户，卡片尾号：1956）。截至2021年12月尚未激
活。
47. 2017年08月16日中国民生银行股份有限公司信用卡中心发放的贷记卡（人民币账户）。截至2025年05月尚未激活。
48. 2019年07月12日华夏银行股份有限公司信用卡中心发放的贷记卡（人民币账户）。截至2025年05月尚未激活。
 
贷款
发生过逾期的账户明细如下：
1. 2020年07月06日中国民生银行股份有限公司临沂兰陵支行发放的3,500,000元（人民币）个人经营性贷款，2021年07月已结清。最
近5年内有1个月处于逾期状态，没有发生过90天以上的逾期。
2. 2021年03月23日中国民生银行股份有限公司临沂兰陵支行发放的1,700,000元（人民币）个人经营性贷款，2022年03月已结清。最
近5年内有1个月处于逾期状态，没有发生过90天以上的逾期。
从未发生过逾期的账户明细如下：
3. 2025年03月05日山东临沂兰山农村商业银行股份有限公司发放的5,200,000元（人民币）个人经营性贷款，2026年02月22日到
期。截至2025年05月，余额5,200,000。
4. 2025年03月20日山东临沂兰山农村商业银行股份有限公司发放的2,800,000元（人民币）个人经营性贷款，2026年02月22日到
期。截至2025年05月，余额2,800,000。
第 2 页，共 7 页
5. 2024年03月28日中国邮政储蓄银行股份有限公司临沂市分行为个人经营性贷款授信，额度有效期至2028年03月28日，可循环使
用。截至2025年05月，信用额度3,000,000元（人民币），余额为3,000,000，当前无逾期。
6. 2010年11月16日国家开发银行山东省分行发放的6,000元（人民币）个人助学贷款，2015年12月已结清。
7. 2011年08月02日中国银行股份有限公司临沂分行发放的418,000元（人民币）个人住房商业贷款，2016年03月已结清。
8. 2011年11月16日国家开发银行山东省分行发放的6,000元（人民币）个人助学贷款，2015年12月已结清。
9. 2012年11月15日国家开发银行山东省分行发放的6,000元（人民币）个人助学贷款，2017年01月已结清。
10. 2017年01月16日浙江网商银行股份有限公司发放的19,000元（人民币）个人经营性贷款，2017年01月已结清。
11. 2017年02月16日浙江网商银行股份有限公司发放的20,000元（人民币）个人经营性贷款，2017年03月已结清。
12. 2017年06月11日浙江网商银行股份有限公司发放的5,000元（人民币）个人经营性贷款，2017年07月已结清。
13. 2017年06月28日梅赛德斯-奔驰汽车金融有限公司发放的249,900元（人民币）个人汽车消费贷款，2017年12月已结清。
14. 2017年07月24日浙江网商银行股份有限公司发放的5,000元（人民币）个人经营性贷款，2018年07月已结清。
15. 2017年08月03日日照银行股份有限公司发放的430,000元（人民币）个人住房商业贷款，2020年06月已结清。
16. 2017年11月01日中国工商银行股份有限公司临沂分行发放的645,000元（人民币）个人住房商业贷款，2023年02月已结清。
17. 2017年12月14日中国建设银行股份有限公司山东省分行发放的2,790,000元（人民币）个人住房商业贷款，2021年04月已结清。
18. 2018年10月25日四川新网银行股份有限公司发放的36,300元（人民币）其他个人消费贷款，2019年01月已结清。
19. 2019年02月02日浙江网商银行股份有限公司发放的2,000元（人民币）个人经营性贷款，2019年05月已结清。
20. 2019年06月05日浙江网商银行股份有限公司发放的321,000元（人民币）个人经营性贷款，2019年09月已结清。
21. 2019年07月09日中国民生银行股份有限公司临沂兰陵支行发放的3,500,000元（人民币）个人经营性贷款，2020年07月已结清。
22. 2019年08月11日重庆美团三快小额贷款有限公司发放的12,000元（人民币）其他个人消费贷款，2019年08月已结清。
23. 2019年09月22日重庆美团三快小额贷款有限公司发放的12,000元（人民币）其他个人消费贷款，2019年09月已结清。
24. 2019年10月08日浙江网商银行股份有限公司发放的224,000元（人民币）个人经营性贷款，2019年12月已结清。
25. 2019年12月17日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2020年12月已结清。
26. 2019年12月17日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2020年12月已结清。
27. 2019年12月25日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2020年12月已结清。
28. 2019年12月26日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2020年12月已结清。
29. 2020年01月01日中国邮政储蓄银行股份有限公司临沂市分行发放的400,000元（人民币）个人经营性贷款，2020年12月已结清。
30. 2020年02月17日中国建设银行股份有限公司山东省分行发放的150,000元（人民币）其他贷款，2020年12月已结清。
31. 2020年04月12日浙江网商银行股份有限公司发放的56,000元（人民币）个人经营性贷款，2020年04月已结清。
32. 2020年05月10日浙江网商银行股份有限公司发放的150,000元（人民币）个人经营性贷款，2020年05月已结清。
33. 2020年07月31日浙江网商银行股份有限公司发放的170,000元（人民币）个人经营性贷款，2020年11月已结清。
34. 2021年04月01日浙江网商银行股份有限公司发放的172,460元（人民币）个人经营性贷款，2021年04月已结清。
35. 2022年10月01日中国对外经济贸易信托有限公司发放的1,000,000元（人民币）个人经营性贷款，2024年07月已结清。
36. 2022年10月07日平安银行股份有限公司临沂分行发放的486,000元（人民币）个人经营性贷款，2023年05月已结清。
37. 2023年03月10日山东临沂兰山农村商业银行股份有限公司发放的2,800,000元（人民币）个人经营性贷款，2024年03月已结清。
38. 2024年03月07日山东临沂兰山农村商业银行股份有限公司发放的2,800,000元（人民币）个人经营性贷款，2025年03月已结清。
39. 2020年12月03日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2021年11月已结清。
40. 2020年12月04日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2021年11月已结清。
41. 2020年12月06日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2021年12月已结清。
42. 2020年12月07日中国邮政储蓄银行股份有限公司临沂市分行发放的400,000元（人民币）个人经营性贷款，2021年11月已结清。
43. 2020年12月08日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2021年12月已结清。
44. 2021年07月07日中国民生银行股份有限公司临沂兰陵支行发放的1,800,000元（人民币）个人经营性贷款，2022年04月已结清。
45. 2021年08月12日浙江网商银行股份有限公司发放的402,298元（人民币）个人经营性贷款，2022年02月已结清。
46. 2021年11月26日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2022年11月已结清。
47. 2021年11月29日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2022年11月已结清。
48. 2021年12月01日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2022年12月已结清。
49. 2021年12月02日中国邮政储蓄银行股份有限公司临沂市分行发放的400,000元（人民币）个人经营性贷款，2022年12月已结清。
50. 2021年12月03日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2022年12月已结清。
51. 2022年01月12日中国工商银行股份有限公司临沂分行发放的4,500,000元（人民币）其他贷款，2022年12月已结清。
52. 2022年11月27日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2023年11月已结清。
53. 2022年11月29日中国邮政储蓄银行股份有限公司临沂市分行发放的300,000元（人民币）个人经营性贷款，2023年11月已结清。
54. 2022年12月01日中国邮政储蓄银行股份有限公司临沂市分行发放的200,000元（人民币）个人经营性贷款，2023年11月已结清。
55. 2022年12月02日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2023年11月已结清。
56. 2022年12月03日中国邮政储蓄银行股份有限公司临沂市分行发放的400,000元（人民币）个人经营性贷款，2023年11月已结清。
57. 2022年12月04日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2023年11月已结清。
58. 2023年03月10日山东临沂兰山农村商业银行股份有限公司发放的5,200,000元（人民币）个人经营性贷款，2024年03月已结清。
59. 2023年11月30日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2024年11月已结清。
第 3 页，共 7 页
60. 2023年12月12日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2024年12月已结清。
61. 2023年12月13日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2024年12月已结清。
62. 2024年03月07日山东临沂兰山农村商业银行股份有限公司发放的5,200,000元（人民币）个人经营性贷款，2025年03月已结清。
63. 2021年11月11日浙江网商银行股份有限公司为个人经营性贷款授信，可循环使用。2022年02月已结清。
64. 2022年04月22日中国民生银行股份有限公司临沂兰陵支行为个人经营性贷款授信，可循环使用。2023年03月已结清。
65. 2022年10月28日中国民生银行股份有限公司临沂兰陵支行为个人经营性贷款授信，可循环使用。2023年03月已结清。
 
相关还款责任信息 
1. 2023年04月27日，为马景涛（证件类型：身份证，证件号码：371322198912203851）在临商银行股份有限公司成都路支行办理
的个人经营性贷款承担相关还款责任，责任人类型为保证人，相关还款责任金额2,000,000（保证合同编号：
D10014730H00072023042700000131）。截至2025年05月31日，个人经营性贷款余额0。
2. 2023年11月29日，为临沂竣豪电子商务有限责任公司（证件类型：中征码，证件号码：3713020005368380）在临沂市商业银行商
城支行办理的贷款承担相关还款责任，责任人类型为保证人，相关还款责任金额10,000,000（保证合同编号：
D10014730H00072023112900000132）。截至2024年11月30日，贷款余额0（人民币元）。
3. 2025年01月15日，为山东高新供应链管理服务有限公司（证件类型：中征码，证件号码：371300U6NRR64W37）在临商银行股
份有限公司高新区支行办理的贷款承担相关还款责任，责任人类型为保证人，相关还款责任金额300,000,000（保证合同编号：
D10014730H00072025011500000117）。截至2025年05月31日，贷款余额138,077,951（人民币元）。
4. 2025年03月25日，为山东高新供应链管理服务有限公司（证件类型：中征码，证件号码：371300U6NRR64W37）在临商银行股
份有限公司高新区支行办理的贷款承担相关还款责任，责任人类型为保证人，相关还款责任金额300,000,000（保证合同编号：
D10014730H00072025011500000117）。截至2025年05月31日，贷款余额48,549,571（人民币元）。
 
非信贷交易记录
系统中没有您最近5年内的非信贷交易记录。
公共记录
系统中没有您最近5年内的公共信息记录。
查询记录
这部分包含您的信用报告最近2年内被查询的记录。
机构查询记录明细
编号 查询日期 查询机构 查询原因
1 2025年06月04日 临商银行股份有限公司高新区支行 贷后管理
2 2025年05月23日 临商银行股份有限公司高新区支行 贷后管理
3 2025年05月07日 临商银行股份有限公司高新区支行 贷后管理
4 2025年04月25日 临商银行股份有限公司高新区支行 贷后管理
5 2025年04月19日 浙江网商银行股份有限公司 贷后管理
6 2025年04月14日 临商银行股份有限公司高新区支行 贷后管理
7 2025年04月03日 临商银行股份有限公司高新区支行 贷后管理
8 2025年03月24日 临商银行股份有限公司高新区支行 贷后管理
9 2025年03月19日 山东临沂兰山农村商业银行股份有 贷款审批
限公司
10 2025年03月17日 中国邮政储蓄银行股份有限公司临 贷后管理
沂市分行
第 4 页，共 7 页
11 2025年03月11日 临商银行股份有限公司高新区支行 贷后管理
12 2025年03月05日 交通银行股份有限公司太平洋信用 信用卡审批
卡中心
13 2025年02月28日 临商银行股份有限公司高新区支行 贷后管理
14 2025年02月07日 临商银行股份有限公司高新区支行 贷后管理
15 2025年01月24日 临商银行股份有限公司成都路支行 贷后管理
16 2025年01月17日 中国邮政储蓄银行股份有限公司山 贷款审批
东省分行
17 2025年01月14日 临商银行股份有限公司高新区支行 担保资格审查
18 2024年11月29日 狮桥融资租赁（中国）有限公司 资信审查
19 2024年11月29日 武汉众邦银行股份有限公司 法人代表、负责人、高管等资信审
查
20 2024年11月29日 深圳市大数融资担保有限公司 担保资格审查
21 2024年11月25日 临商银行股份有限公司成都路支行 贷后管理
22 2024年11月21日 浙江网商银行股份有限公司 贷款审批
23 2024年11月20日 中国邮政储蓄银行股份有限公司 贷后管理
24 2024年10月31日 临商银行股份有限公司成都路支行 贷后管理
25 2024年10月06日 中国邮政储蓄银行股份有限公司 贷后管理
26 2024年09月30日 平安银行股份有限公司 贷款审批
27 2024年09月03日 中国邮政储蓄银行股份有限公司 贷后管理
28 2024年08月03日 中国邮政储蓄银行股份有限公司 贷后管理
29 2024年07月31日 临商银行股份有限公司成都路支行 贷后管理
30 2024年07月05日 平安融易（江苏）融资担保有限公 担保资格审查
司
31 2024年07月01日 中国邮政储蓄银行股份有限公司 贷后管理
32 2024年05月17日 中国邮政储蓄银行股份有限公司 贷后管理
33 2024年04月16日 临商银行股份有限公司成都路支行 贷后管理
34 2024年04月12日 中国邮政储蓄银行股份有限公司 贷后管理
35 2024年04月02日 平安融易（江苏）融资担保有限公 担保资格审查
司
36 2024年03月13日 中国建设银行股份有限公司深圳华 信用卡审批
侨城支行
37 2024年03月07日 中国邮政储蓄银行股份有限公司山 贷款审批
东省分行
38 2024年03月07日 山东临沂兰山农村商业银行股份有 贷款审批
限公司
39 2024年03月06日 山东临沂兰山农村商业银行股份有 贷款审批
限公司
40 2024年03月02日 中国邮政储蓄银行股份有限公司 贷后管理
41 2024年02月19日 临商银行股份有限公司成都路支行 贷后管理
42 2024年01月29日 中国邮政储蓄银行股份有限公司 贷后管理
43 2024年01月02日 平安融易（江苏）融资担保有限公 担保资格审查
司
44 2023年11月28日 中国工商银行股份有限公司银行卡 贷后管理
业务部(牡丹卡中心)
第 5 页，共 7 页
45 2023年11月28日 临商银行股份有限公司商城支行营 担保资格审查
业部
46 2023年11月27日 中国邮政储蓄银行股份有限公司 贷款审批
47 2023年11月17日 中国邮政储蓄银行股份有限公司 贷后管理
48 2023年11月15日 临商银行股份有限公司成都路支行 贷后管理
49 2023年11月01日 临商银行股份有限公司商城支行营 担保资格审查
业部
50 2023年10月19日 临商银行股份有限公司高新区支行 担保资格审查
51 2023年10月02日 平安融易（江苏）融资担保有限公 担保资格审查
司
52 2023年09月27日 交通银行股份有限公司太平洋信用 信用卡审批
卡中心
53 2023年09月15日 中国邮政储蓄银行股份有限公司 贷后管理
54 2023年08月29日 中国工商银行股份有限公司银行卡 信用卡审批
业务部(牡丹卡中心)
55 2023年08月12日 临商银行股份有限公司成都路支行 贷后管理
56 2023年07月14日 中国邮政储蓄银行股份有限公司 贷后管理
57 2023年07月03日 平安融易（江苏）融资担保有限公 担保资格审查
司
个人查询记录明细
编号 查询日期 查询机构 查询原因
1 2025年03月19日 本人 本人查询（自助查询机）
2 2024年04月07日 本人 本人查询（自助查询机）
3 2024年04月01日 本人 本人查询（互联网个人信用信息服
务平台）
4 2023年09月21日 本人 本人查询（商业银行网上银行）
第 6 页，共 7 页
说  明
 
1.除查询记录外，本报告中的信息是依据截至报告时间个人征信系统记录的信息生成，征信中心不确保其真实性和准确性，但承
诺在信息汇总、加工、整合的全过程中保持客观、中立的地位。
2.本报告仅包含可能影响您信用评价的主要信息，如需获取您在个人征信系统中更详细的记录，请到当地信用报告查询网点查
询。信用报告查询网点的具体地址及联系方式可访问征信中心门户网站（www.pbccrc.org.cn）查询。
3.您有权对本报告中的内容提出异议。如有异议，可联系数据提供单位，也可到当地信用报告查询网点提出异议申请。
4.本报告仅供您了解自己的信用状况，请妥善保管。因保管不当造成个人隐私泄露的，征信中心将不承担相关责任。
5.更多咨询，请致电全国客户服务热线400-810-8866。
第 7 页，共 7 页

2025-09-03 11:15:53,505 [http-nio-8100-exec-10] ERROR [com.agentsflex.core.llm.client.HttpClient] HttpClient.java:136 - java.lang.IllegalArgumentException: Expected URL scheme 'http' or 'https' but no scheme was found for null/c...
java.lang.IllegalArgumentException: Expected URL scheme 'http' or 'https' but no scheme was found for null/c...
	at okhttp3.HttpUrl$Builder.parse$okhttp(HttpUrl.kt:1261)
	at okhttp3.HttpUrl$Companion.get(HttpUrl.kt:1634)
	at okhttp3.Request$Builder.url(Request.kt:184)
	at com.agentsflex.core.llm.client.HttpClient.execute0(HttpClient.java:157)
	at com.agentsflex.core.llm.client.HttpClient.executeString(HttpClient.java:130)
	at com.agentsflex.core.llm.client.HttpClient.post(HttpClient.java:57)
	at com.agentsflex.llm.qwen.QwenLlm.chat(QwenLlm.java:63)
	at com.agentsflex.core.chain.node.LlmNode.execute(LlmNode.java:121)
	at com.agentsflex.core.chain.Chain.doExecuteNode(Chain.java:501)
	at com.agentsflex.core.chain.Chain.doExecuteNodes(Chain.java:472)
	at com.agentsflex.core.chain.Chain.doExecuteNode(Chain.java:539)
	at com.agentsflex.core.chain.Chain.doExecuteNodes(Chain.java:472)
	at com.agentsflex.core.chain.Chain.doExecuteNode(Chain.java:539)
	at com.agentsflex.core.chain.Chain.doExecuteNodes(Chain.java:472)
	at com.agentsflex.core.chain.Chain.executeInternal(Chain.java:455)
	at com.agentsflex.core.chain.Chain.runInLifeCycle(Chain.java:603)
	at com.agentsflex.core.chain.Chain.executeForResult(Chain.java:353)
	at com.agentsflex.core.chain.Chain.executeForResult(Chain.java:346)
	at cn.tycoding.langchat.ai.core.service.impl.WorkflowRunningServiceImpl.workflowRunning(WorkflowRunningServiceImpl.java:170)
	at cn.tycoding.langchat.server.controller.AiWorkflowController.tryRunning(AiWorkflowController.java:136)
	at jdk.internal.reflect.GeneratedMethodAccessor177.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:259)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:192)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-03 11:16:01,284 [http-nio-8100-exec-1] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本： 
个人信用报告
 
报告编号：2025061016250556886580 报告时间：2025-06-10 16:25:05
姓名： 侯明 证件类型：身份证 证件号码：371322198905214915 已婚
 
信贷记录
这部分包含您的信用卡、贷款和其他信贷记录。金额类数据均以人民币计算，精确到元。
信 息概要
贷款
信用卡 其他业务 逾期记录可能影响对您的信用评价。
购房 其他
账户数 48 4 61 -- 购房贷款，包括个人住房贷款、个人商用
房（包括商住两用）贷款和个人住房公积
未结清/未销户账户数 19 -- 3 -- 金贷款。
发生过逾期的账户数 2 -- 2 -- 发生过逾期的信用卡账户，指曾经“未按
时还最低还款额”的贷记卡账户和“透支
发生过90天以上逾期的账户数 -- -- -- -- 超过60天”的准贷记卡账户。
为个人 为企业
相关还款责任账户数 1 3
信用卡
发生过逾期的贷记卡账户明细如下：
1. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（新加坡元账户，卡片尾号：0792）。截至2025年05月，信用
额度21,715，余额0，当前无逾期。最近5年内有1个月处于逾期状态，没有发生过90天以上逾期。
2. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（美元账户，卡片尾号：0792）。截至2025年05月，信用额度
21,060，余额695，当前有逾期。最近5年内有4个月处于逾期状态，没有发生过90天以上逾期。
从未逾期过的贷记卡及透支未超过60天的准贷记卡账户明细如下：
3. 2011年02月23日中国银行股份有限公司临沂分行发放的贷记卡（人民币账户，卡片尾号：4204）。截至2025年05月，信用额度
2,000，已使用额度0。
4. 2011年04月27日兴业银行股份有限公司发放的贷记卡（人民币账户）。截至2025年06月，信用额度8,000，已使用额度0。
5. 2018年12月06日中国工商银行股份有限公司临沂分行发放的贷记卡（人民币账户，卡片尾号：7133）。截至2025年05月，信用
额度0，已使用额度0。
6. 2020年08月18日中信银行股份有限公司信用卡中心发放的贷记卡（人民币账户）。截至2025年05月，信用额度50,000，已使用
额度0。
7. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（人民币账户，卡片尾号：0792）。截至2025年05月，信用
额度20,000，已使用额度0。
8. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（澳大利亚元账户，卡片尾号：0792）。截至2025年05月，
信用额度17,378，已使用额度0。
9. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（瑞士法郎账户，卡片尾号：0792）。截至2025年05月，信
用额度24,559，已使用额度0。
10. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（日元账户，卡片尾号：0792）。截至2025年05月，信用额
度16,584，已使用额度0。
11. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（香港元账户，卡片尾号：0792）。截至2025年05月，信用
额度20,965，已使用额度57。
第 1 页，共 7 页
12. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（欧元账户，卡片尾号：0792）。截至2025年05月，信用额
度19,671，已使用额度0。
13. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（英镑账户，卡片尾号：0792）。截至2025年05月，信用额
度20,782，已使用额度0。
14. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（新西兰元账户，卡片尾号：0792）。截至2025年05月，信
用额度17,587，已使用额度0。
15. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（加元账户，卡片尾号：0792）。截至2025年05月，信用额
度18,907，已使用额度0。
16. 2024年03月14日中国建设银行股份有限公司深圳深圳湾支行发放的贷记卡（人民币账户，卡片尾号：9345）。截至2025年
05月，信用额度0，余额230,750（含未出单的大额专项分期余额220,000）。
17. 2011年02月01日中国农业银行股份有限公司山东省分行发放的贷记卡（人民币账户，卡片尾号：3721），2022年10月销户。
18. 2017年07月31日上海浦东发展银行股份有限公司信用卡中心发放的贷记卡（人民币账户），2022年12月销户。
19. 2018年09月13日中国建设银行股份有限公司临沂新城支行发放的贷记卡（人民币账户，卡片尾号：2154），2022年09月销户。
20. 2018年09月13日中国建设银行股份有限公司临沂新城支行发放的贷记卡（美元账户，卡片尾号：2154），2022年09月销户。
21. 2018年10月18日中国建设银行股份有限公司临沂新城支行发放的贷记卡（人民币账户，卡片尾号：0929），2022年09月销户。
22. 2018年10月18日中国建设银行股份有限公司临沂新城支行发放的贷记卡（美元账户，卡片尾号：0929），2022年09月销户。
23. 2018年10月22日中国建设银行股份有限公司临沂新城支行发放的贷记卡（美元账户，卡片尾号：2539），2022年09月销户。
24. 2018年10月22日中国建设银行股份有限公司临沂新城支行发放的贷记卡（人民币账户，卡片尾号：2539），2022年09月销户。
25. 2018年10月23日中国建设银行股份有限公司临沂新城支行发放的贷记卡（美元账户，卡片尾号：4982），2022年09月销户。
26. 2018年10月23日中国建设银行股份有限公司临沂新城支行发放的贷记卡（人民币账户，卡片尾号：4982），2022年09月销户。
27. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（加元账户，卡片尾号：5529），2023年07月销户。
28. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（欧元账户，卡片尾号：5529），2023年07月销户。
29. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（人民币账户，卡片尾号：5529），2023年07月销户。
30. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（瑞士法郎账户，卡片尾号：5529），2023年07月销户。
31. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（新西兰元账户，卡片尾号：5529），2023年07月销户。
32. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（澳大利亚元账户，卡片尾号：5529），2023年07月销户。
33. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（新加坡元账户，卡片尾号：5529），2023年07月销户。
34. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（美元账户，卡片尾号：5529），2023年07月销户。
35. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（英镑账户，卡片尾号：5529），2023年07月销户。
36. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（香港元账户，卡片尾号：5529），2023年07月销户。
37. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（日元账户，卡片尾号：5529），2023年07月销户。
38. 2019年07月08日中国光大银行股份有限公司发放的贷记卡（人民币账户，卡片尾号：6876），2022年12月销户。
39. 2019年07月08日中国光大银行股份有限公司发放的贷记卡（美元账户，卡片尾号：6876），2022年12月销户。
40. 2019年07月08日中国光大银行股份有限公司发放的贷记卡（人民币账户，卡片尾号：2645），2022年12月销户。
41. 2020年08月26日中国建设银行股份有限公司临沂新城支行发放的贷记卡（人民币账户，卡片尾号：4977），2022年09月销户。
42. 2022年01月05日中国建设银行股份有限公司临沂新城支行发放的贷记卡（人民币账户，卡片尾号：4987），2022年09月销户。
43. 2023年02月02日交通银行股份有限公司太平洋信用卡中心发放的贷记卡（人民币账户，卡片尾号：6182），2023年03月销户。
44. 2023年02月02日交通银行股份有限公司太平洋信用卡中心发放的贷记卡（美元账户，卡片尾号：6182），2023年03月销户。
45. 2023年09月27日交通银行股份有限公司太平洋信用卡中心发放的贷记卡（人民币账户，卡片尾号：4965），2024年04月销户。
46. 2012年03月20日中国银行股份有限公司山东省分行发放的贷记卡（人民币账户，卡片尾号：1956）。截至2021年12月尚未激
活。
47. 2017年08月16日中国民生银行股份有限公司信用卡中心发放的贷记卡（人民币账户）。截至2025年05月尚未激活。
48. 2019年07月12日华夏银行股份有限公司信用卡中心发放的贷记卡（人民币账户）。截至2025年05月尚未激活。
 
贷款
发生过逾期的账户明细如下：
1. 2020年07月06日中国民生银行股份有限公司临沂兰陵支行发放的3,500,000元（人民币）个人经营性贷款，2021年07月已结清。最
近5年内有1个月处于逾期状态，没有发生过90天以上的逾期。
2. 2021年03月23日中国民生银行股份有限公司临沂兰陵支行发放的1,700,000元（人民币）个人经营性贷款，2022年03月已结清。最
近5年内有1个月处于逾期状态，没有发生过90天以上的逾期。
从未发生过逾期的账户明细如下：
3. 2025年03月05日山东临沂兰山农村商业银行股份有限公司发放的5,200,000元（人民币）个人经营性贷款，2026年02月22日到
期。截至2025年05月，余额5,200,000。
4. 2025年03月20日山东临沂兰山农村商业银行股份有限公司发放的2,800,000元（人民币）个人经营性贷款，2026年02月22日到
期。截至2025年05月，余额2,800,000。
第 2 页，共 7 页
5. 2024年03月28日中国邮政储蓄银行股份有限公司临沂市分行为个人经营性贷款授信，额度有效期至2028年03月28日，可循环使
用。截至2025年05月，信用额度3,000,000元（人民币），余额为3,000,000，当前无逾期。
6. 2010年11月16日国家开发银行山东省分行发放的6,000元（人民币）个人助学贷款，2015年12月已结清。
7. 2011年08月02日中国银行股份有限公司临沂分行发放的418,000元（人民币）个人住房商业贷款，2016年03月已结清。
8. 2011年11月16日国家开发银行山东省分行发放的6,000元（人民币）个人助学贷款，2015年12月已结清。
9. 2012年11月15日国家开发银行山东省分行发放的6,000元（人民币）个人助学贷款，2017年01月已结清。
10. 2017年01月16日浙江网商银行股份有限公司发放的19,000元（人民币）个人经营性贷款，2017年01月已结清。
11. 2017年02月16日浙江网商银行股份有限公司发放的20,000元（人民币）个人经营性贷款，2017年03月已结清。
12. 2017年06月11日浙江网商银行股份有限公司发放的5,000元（人民币）个人经营性贷款，2017年07月已结清。
13. 2017年06月28日梅赛德斯-奔驰汽车金融有限公司发放的249,900元（人民币）个人汽车消费贷款，2017年12月已结清。
14. 2017年07月24日浙江网商银行股份有限公司发放的5,000元（人民币）个人经营性贷款，2018年07月已结清。
15. 2017年08月03日日照银行股份有限公司发放的430,000元（人民币）个人住房商业贷款，2020年06月已结清。
16. 2017年11月01日中国工商银行股份有限公司临沂分行发放的645,000元（人民币）个人住房商业贷款，2023年02月已结清。
17. 2017年12月14日中国建设银行股份有限公司山东省分行发放的2,790,000元（人民币）个人住房商业贷款，2021年04月已结清。
18. 2018年10月25日四川新网银行股份有限公司发放的36,300元（人民币）其他个人消费贷款，2019年01月已结清。
19. 2019年02月02日浙江网商银行股份有限公司发放的2,000元（人民币）个人经营性贷款，2019年05月已结清。
20. 2019年06月05日浙江网商银行股份有限公司发放的321,000元（人民币）个人经营性贷款，2019年09月已结清。
21. 2019年07月09日中国民生银行股份有限公司临沂兰陵支行发放的3,500,000元（人民币）个人经营性贷款，2020年07月已结清。
22. 2019年08月11日重庆美团三快小额贷款有限公司发放的12,000元（人民币）其他个人消费贷款，2019年08月已结清。
23. 2019年09月22日重庆美团三快小额贷款有限公司发放的12,000元（人民币）其他个人消费贷款，2019年09月已结清。
24. 2019年10月08日浙江网商银行股份有限公司发放的224,000元（人民币）个人经营性贷款，2019年12月已结清。
25. 2019年12月17日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2020年12月已结清。
26. 2019年12月17日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2020年12月已结清。
27. 2019年12月25日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2020年12月已结清。
28. 2019年12月26日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2020年12月已结清。
29. 2020年01月01日中国邮政储蓄银行股份有限公司临沂市分行发放的400,000元（人民币）个人经营性贷款，2020年12月已结清。
30. 2020年02月17日中国建设银行股份有限公司山东省分行发放的150,000元（人民币）其他贷款，2020年12月已结清。
31. 2020年04月12日浙江网商银行股份有限公司发放的56,000元（人民币）个人经营性贷款，2020年04月已结清。
32. 2020年05月10日浙江网商银行股份有限公司发放的150,000元（人民币）个人经营性贷款，2020年05月已结清。
33. 2020年07月31日浙江网商银行股份有限公司发放的170,000元（人民币）个人经营性贷款，2020年11月已结清。
34. 2021年04月01日浙江网商银行股份有限公司发放的172,460元（人民币）个人经营性贷款，2021年04月已结清。
35. 2022年10月01日中国对外经济贸易信托有限公司发放的1,000,000元（人民币）个人经营性贷款，2024年07月已结清。
36. 2022年10月07日平安银行股份有限公司临沂分行发放的486,000元（人民币）个人经营性贷款，2023年05月已结清。
37. 2023年03月10日山东临沂兰山农村商业银行股份有限公司发放的2,800,000元（人民币）个人经营性贷款，2024年03月已结清。
38. 2024年03月07日山东临沂兰山农村商业银行股份有限公司发放的2,800,000元（人民币）个人经营性贷款，2025年03月已结清。
39. 2020年12月03日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2021年11月已结清。
40. 2020年12月04日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2021年11月已结清。
41. 2020年12月06日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2021年12月已结清。
42. 2020年12月07日中国邮政储蓄银行股份有限公司临沂市分行发放的400,000元（人民币）个人经营性贷款，2021年11月已结清。
43. 2020年12月08日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2021年12月已结清。
44. 2021年07月07日中国民生银行股份有限公司临沂兰陵支行发放的1,800,000元（人民币）个人经营性贷款，2022年04月已结清。
45. 2021年08月12日浙江网商银行股份有限公司发放的402,298元（人民币）个人经营性贷款，2022年02月已结清。
46. 2021年11月26日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2022年11月已结清。
47. 2021年11月29日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2022年11月已结清。
48. 2021年12月01日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2022年12月已结清。
49. 2021年12月02日中国邮政储蓄银行股份有限公司临沂市分行发放的400,000元（人民币）个人经营性贷款，2022年12月已结清。
50. 2021年12月03日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2022年12月已结清。
51. 2022年01月12日中国工商银行股份有限公司临沂分行发放的4,500,000元（人民币）其他贷款，2022年12月已结清。
52. 2022年11月27日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2023年11月已结清。
53. 2022年11月29日中国邮政储蓄银行股份有限公司临沂市分行发放的300,000元（人民币）个人经营性贷款，2023年11月已结清。
54. 2022年12月01日中国邮政储蓄银行股份有限公司临沂市分行发放的200,000元（人民币）个人经营性贷款，2023年11月已结清。
55. 2022年12月02日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2023年11月已结清。
56. 2022年12月03日中国邮政储蓄银行股份有限公司临沂市分行发放的400,000元（人民币）个人经营性贷款，2023年11月已结清。
57. 2022年12月04日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2023年11月已结清。
58. 2023年03月10日山东临沂兰山农村商业银行股份有限公司发放的5,200,000元（人民币）个人经营性贷款，2024年03月已结清。
59. 2023年11月30日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2024年11月已结清。
第 3 页，共 7 页
60. 2023年12月12日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2024年12月已结清。
61. 2023年12月13日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2024年12月已结清。
62. 2024年03月07日山东临沂兰山农村商业银行股份有限公司发放的5,200,000元（人民币）个人经营性贷款，2025年03月已结清。
63. 2021年11月11日浙江网商银行股份有限公司为个人经营性贷款授信，可循环使用。2022年02月已结清。
64. 2022年04月22日中国民生银行股份有限公司临沂兰陵支行为个人经营性贷款授信，可循环使用。2023年03月已结清。
65. 2022年10月28日中国民生银行股份有限公司临沂兰陵支行为个人经营性贷款授信，可循环使用。2023年03月已结清。
 
相关还款责任信息 
1. 2023年04月27日，为马景涛（证件类型：身份证，证件号码：371322198912203851）在临商银行股份有限公司成都路支行办理
的个人经营性贷款承担相关还款责任，责任人类型为保证人，相关还款责任金额2,000,000（保证合同编号：
D10014730H00072023042700000131）。截至2025年05月31日，个人经营性贷款余额0。
2. 2023年11月29日，为临沂竣豪电子商务有限责任公司（证件类型：中征码，证件号码：3713020005368380）在临沂市商业银行商
城支行办理的贷款承担相关还款责任，责任人类型为保证人，相关还款责任金额10,000,000（保证合同编号：
D10014730H00072023112900000132）。截至2024年11月30日，贷款余额0（人民币元）。
3. 2025年01月15日，为山东高新供应链管理服务有限公司（证件类型：中征码，证件号码：371300U6NRR64W37）在临商银行股
份有限公司高新区支行办理的贷款承担相关还款责任，责任人类型为保证人，相关还款责任金额300,000,000（保证合同编号：
D10014730H00072025011500000117）。截至2025年05月31日，贷款余额138,077,951（人民币元）。
4. 2025年03月25日，为山东高新供应链管理服务有限公司（证件类型：中征码，证件号码：371300U6NRR64W37）在临商银行股
份有限公司高新区支行办理的贷款承担相关还款责任，责任人类型为保证人，相关还款责任金额300,000,000（保证合同编号：
D10014730H00072025011500000117）。截至2025年05月31日，贷款余额48,549,571（人民币元）。
 
非信贷交易记录
系统中没有您最近5年内的非信贷交易记录。
公共记录
系统中没有您最近5年内的公共信息记录。
查询记录
这部分包含您的信用报告最近2年内被查询的记录。
机构查询记录明细
编号 查询日期 查询机构 查询原因
1 2025年06月04日 临商银行股份有限公司高新区支行 贷后管理
2 2025年05月23日 临商银行股份有限公司高新区支行 贷后管理
3 2025年05月07日 临商银行股份有限公司高新区支行 贷后管理
4 2025年04月25日 临商银行股份有限公司高新区支行 贷后管理
5 2025年04月19日 浙江网商银行股份有限公司 贷后管理
6 2025年04月14日 临商银行股份有限公司高新区支行 贷后管理
7 2025年04月03日 临商银行股份有限公司高新区支行 贷后管理
8 2025年03月24日 临商银行股份有限公司高新区支行 贷后管理
9 2025年03月19日 山东临沂兰山农村商业银行股份有 贷款审批
限公司
10 2025年03月17日 中国邮政储蓄银行股份有限公司临 贷后管理
沂市分行
第 4 页，共 7 页
11 2025年03月11日 临商银行股份有限公司高新区支行 贷后管理
12 2025年03月05日 交通银行股份有限公司太平洋信用 信用卡审批
卡中心
13 2025年02月28日 临商银行股份有限公司高新区支行 贷后管理
14 2025年02月07日 临商银行股份有限公司高新区支行 贷后管理
15 2025年01月24日 临商银行股份有限公司成都路支行 贷后管理
16 2025年01月17日 中国邮政储蓄银行股份有限公司山 贷款审批
东省分行
17 2025年01月14日 临商银行股份有限公司高新区支行 担保资格审查
18 2024年11月29日 狮桥融资租赁（中国）有限公司 资信审查
19 2024年11月29日 武汉众邦银行股份有限公司 法人代表、负责人、高管等资信审
查
20 2024年11月29日 深圳市大数融资担保有限公司 担保资格审查
21 2024年11月25日 临商银行股份有限公司成都路支行 贷后管理
22 2024年11月21日 浙江网商银行股份有限公司 贷款审批
23 2024年11月20日 中国邮政储蓄银行股份有限公司 贷后管理
24 2024年10月31日 临商银行股份有限公司成都路支行 贷后管理
25 2024年10月06日 中国邮政储蓄银行股份有限公司 贷后管理
26 2024年09月30日 平安银行股份有限公司 贷款审批
27 2024年09月03日 中国邮政储蓄银行股份有限公司 贷后管理
28 2024年08月03日 中国邮政储蓄银行股份有限公司 贷后管理
29 2024年07月31日 临商银行股份有限公司成都路支行 贷后管理
30 2024年07月05日 平安融易（江苏）融资担保有限公 担保资格审查
司
31 2024年07月01日 中国邮政储蓄银行股份有限公司 贷后管理
32 2024年05月17日 中国邮政储蓄银行股份有限公司 贷后管理
33 2024年04月16日 临商银行股份有限公司成都路支行 贷后管理
34 2024年04月12日 中国邮政储蓄银行股份有限公司 贷后管理
35 2024年04月02日 平安融易（江苏）融资担保有限公 担保资格审查
司
36 2024年03月13日 中国建设银行股份有限公司深圳华 信用卡审批
侨城支行
37 2024年03月07日 中国邮政储蓄银行股份有限公司山 贷款审批
东省分行
38 2024年03月07日 山东临沂兰山农村商业银行股份有 贷款审批
限公司
39 2024年03月06日 山东临沂兰山农村商业银行股份有 贷款审批
限公司
40 2024年03月02日 中国邮政储蓄银行股份有限公司 贷后管理
41 2024年02月19日 临商银行股份有限公司成都路支行 贷后管理
42 2024年01月29日 中国邮政储蓄银行股份有限公司 贷后管理
43 2024年01月02日 平安融易（江苏）融资担保有限公 担保资格审查
司
44 2023年11月28日 中国工商银行股份有限公司银行卡 贷后管理
业务部(牡丹卡中心)
第 5 页，共 7 页
45 2023年11月28日 临商银行股份有限公司商城支行营 担保资格审查
业部
46 2023年11月27日 中国邮政储蓄银行股份有限公司 贷款审批
47 2023年11月17日 中国邮政储蓄银行股份有限公司 贷后管理
48 2023年11月15日 临商银行股份有限公司成都路支行 贷后管理
49 2023年11月01日 临商银行股份有限公司商城支行营 担保资格审查
业部
50 2023年10月19日 临商银行股份有限公司高新区支行 担保资格审查
51 2023年10月02日 平安融易（江苏）融资担保有限公 担保资格审查
司
52 2023年09月27日 交通银行股份有限公司太平洋信用 信用卡审批
卡中心
53 2023年09月15日 中国邮政储蓄银行股份有限公司 贷后管理
54 2023年08月29日 中国工商银行股份有限公司银行卡 信用卡审批
业务部(牡丹卡中心)
55 2023年08月12日 临商银行股份有限公司成都路支行 贷后管理
56 2023年07月14日 中国邮政储蓄银行股份有限公司 贷后管理
57 2023年07月03日 平安融易（江苏）融资担保有限公 担保资格审查
司
个人查询记录明细
编号 查询日期 查询机构 查询原因
1 2025年03月19日 本人 本人查询（自助查询机）
2 2024年04月07日 本人 本人查询（自助查询机）
3 2024年04月01日 本人 本人查询（互联网个人信用信息服
务平台）
4 2023年09月21日 本人 本人查询（商业银行网上银行）
第 6 页，共 7 页
说  明
 
1.除查询记录外，本报告中的信息是依据截至报告时间个人征信系统记录的信息生成，征信中心不确保其真实性和准确性，但承
诺在信息汇总、加工、整合的全过程中保持客观、中立的地位。
2.本报告仅包含可能影响您信用评价的主要信息，如需获取您在个人征信系统中更详细的记录，请到当地信用报告查询网点查
询。信用报告查询网点的具体地址及联系方式可访问征信中心门户网站（www.pbccrc.org.cn）查询。
3.您有权对本报告中的内容提出异议。如有异议，可联系数据提供单位，也可到当地信用报告查询网点提出异议申请。
4.本报告仅供您了解自己的信用状况，请妥善保管。因保管不当造成个人隐私泄露的，征信中心将不承担相关责任。
5.更多咨询，请致电全国客户服务热线400-810-8866。
第 7 页，共 7 页

2025-09-03 11:16:01,288 [http-nio-8100-exec-8] ERROR [com.agentsflex.core.llm.client.HttpClient] HttpClient.java:136 - java.lang.IllegalArgumentException: Expected URL scheme 'http' or 'https' but no scheme was found for null/c...
java.lang.IllegalArgumentException: Expected URL scheme 'http' or 'https' but no scheme was found for null/c...
	at okhttp3.HttpUrl$Builder.parse$okhttp(HttpUrl.kt:1261)
	at okhttp3.HttpUrl$Companion.get(HttpUrl.kt:1634)
	at okhttp3.Request$Builder.url(Request.kt:184)
	at com.agentsflex.core.llm.client.HttpClient.execute0(HttpClient.java:157)
	at com.agentsflex.core.llm.client.HttpClient.executeString(HttpClient.java:130)
	at com.agentsflex.core.llm.client.HttpClient.post(HttpClient.java:57)
	at com.agentsflex.llm.qwen.QwenLlm.chat(QwenLlm.java:63)
	at com.agentsflex.core.chain.node.LlmNode.execute(LlmNode.java:121)
	at com.agentsflex.core.chain.Chain.doExecuteNode(Chain.java:501)
	at com.agentsflex.core.chain.Chain.doExecuteNodes(Chain.java:472)
	at com.agentsflex.core.chain.Chain.doExecuteNode(Chain.java:539)
	at com.agentsflex.core.chain.Chain.doExecuteNodes(Chain.java:472)
	at com.agentsflex.core.chain.Chain.doExecuteNode(Chain.java:539)
	at com.agentsflex.core.chain.Chain.doExecuteNodes(Chain.java:472)
	at com.agentsflex.core.chain.Chain.executeInternal(Chain.java:455)
	at com.agentsflex.core.chain.Chain.runInLifeCycle(Chain.java:603)
	at com.agentsflex.core.chain.Chain.executeForResult(Chain.java:353)
	at com.agentsflex.core.chain.Chain.executeForResult(Chain.java:346)
	at cn.tycoding.langchat.ai.core.service.impl.WorkflowRunningServiceImpl.workflowRunning(WorkflowRunningServiceImpl.java:170)
	at cn.tycoding.langchat.server.controller.AiWorkflowController.tryRunning(AiWorkflowController.java:136)
	at jdk.internal.reflect.GeneratedMethodAccessor177.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:259)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:192)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-03 11:16:49,858 [SpringApplicationShutdownHook] INFO  [o.dromara.x.file.storage.core.FileStorageService] FileStorageService.java:734 - 销毁存储平台 local 成功
2025-09-03 11:16:49,861 [SpringApplicationShutdownHook] INFO  [o.dromara.x.file.storage.core.FileStorageService] FileStorageService.java:734 - 销毁存储平台 aliyun-oss 成功
2025-09-03 11:16:49,866 [SpringApplicationShutdownHook] INFO  [com.alibaba.druid.pool.DruidDataSource] DruidDataSource.java:2204 - {dataSource-1} closing ...
2025-09-03 11:16:49,871 [SpringApplicationShutdownHook] INFO  [com.alibaba.druid.pool.DruidDataSource] DruidDataSource.java:2277 - {dataSource-1} closed
2025-09-03 11:16:55,785 [background-preinit] INFO  [org.hibernate.validator.internal.util.Version] Version.java:21 - HV000001: Hibernate Validator 8.0.1.Final
2025-09-03 11:16:55,977 [main] INFO  [cn.tycoding.langchat.LangChatApp] StartupInfoLogger.java:50 - Starting LangChatApp using Java 17.0.14 with PID 14124 (D:\awork\work-aigc\aigc-manage\langchat-server\target\classes started by Administrator in D:\awork\work-aigc)
2025-09-03 11:16:55,978 [main] INFO  [cn.tycoding.langchat.LangChatApp] SpringApplication.java:660 - The following 1 profile is active: "dev"
2025-09-03 11:16:58,311 [main] INFO  [o.s.d.r.config.RepositoryConfigurationDelegate] RepositoryConfigurationDelegate.java:292 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-03 11:16:58,316 [main] INFO  [o.s.d.r.config.RepositoryConfigurationDelegate] RepositoryConfigurationDelegate.java:139 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-03 11:16:58,419 [main] INFO  [o.s.d.r.config.RepositoryConfigurationDelegate] RepositoryConfigurationDelegate.java:208 - Finished Spring Data repository scanning in 81 ms. Found 0 Redis repository interfaces.
2025-09-03 11:17:00,267 [main] INFO  [o.s.boot.web.embedded.tomcat.TomcatWebServer] TomcatWebServer.java:109 - Tomcat initialized with port 8100 (http)
2025-09-03 11:17:00,286 [main] INFO  [org.apache.coyote.http11.Http11NioProtocol] DirectJDKLog.java:173 - Initializing ProtocolHandler ["http-nio-8100"]
2025-09-03 11:17:00,288 [main] INFO  [org.apache.catalina.core.StandardService] DirectJDKLog.java:173 - Starting service [Tomcat]
2025-09-03 11:17:00,288 [main] INFO  [org.apache.catalina.core.StandardEngine] DirectJDKLog.java:173 - Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-09-03 11:17:00,511 [main] INFO  [o.a.c.core.ContainerBase.[Tomcat].[localhost].[/]] DirectJDKLog.java:173 - Initializing Spring embedded WebApplicationContext
2025-09-03 11:17:00,511 [main] INFO  [o.s.b.w.s.c.ServletWebServerApplicationContext] ServletWebServerApplicationContext.java:296 - Root WebApplicationContext: initialization completed in 3972 ms
2025-09-03 11:17:00,698 [main] INFO  [c.a.d.s.b.a.DruidDataSourceAutoConfigure] DruidDataSourceAutoConfigure.java:68 - Init DruidDataSource
2025-09-03 11:17:00,837 [main] INFO  [com.alibaba.druid.pool.DruidDataSource] DruidDataSource.java:1002 - {dataSource-1} inited
2025-09-03 11:17:01,302 [main] WARN  [c.b.mybatisplus.core.metadata.TableInfoHelper] TableInfoHelper.java:376 - Can not find table primary key in Class: "cn.tycoding.langchat.upms.entity.SysUserRole".
2025-09-03 11:17:01,303 [main] WARN  [c.b.mybatisplus.core.injector.DefaultSqlInjector] DefaultSqlInjector.java:52 - class cn.tycoding.langchat.upms.entity.SysUserRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-09-03 11:17:02,879 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- EMBEDDING， 模型配置：AigcModel(id=23b21ee73bc3dd67ddcc4287aadb1697, type=EMBEDDING, model=text-embedding-v4, provider=Q_WEN, name=text-embedding-v4, responseLimit=null, temperature=0.2, topP=0.0, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=1024, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-03 11:17:02,892 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：ZHIPU -- CHAT， 模型配置：AigcModel(id=30c047625a22dd3cfdc8a21d13bd7d04, type=CHAT, model=glm-4-flash, provider=ZHIPU, name=glm-4-flash-250414, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=a4867e65138e4f259c5c692aef943164.6xPiD4YHC2krGJAV, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-03 11:17:02,892 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- CHAT， 模型配置：AigcModel(id=6b1cdbb07032a117d1b4f00b865e7f23, type=CHAT, model=deepseek-v3, provider=Q_WEN, name=阿里DeepSeek-V3, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=https://dashscope.aliyuncs.com, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-03 11:17:02,893 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- CHAT， 模型配置：AigcModel(id=765a327613f9c29b547ec9dac3d3b2b4, type=CHAT, model=qwen-max-longcontext, provider=Q_WEN, name=千问长token模型, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=https://dashscope.aliyuncs.com, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-03 11:17:02,893 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：ZHIPU -- CHAT， 模型配置：AigcModel(id=91c235219da4e2a92ae166e38d179b89, type=CHAT, model=glm-4, provider=ZHIPU, name=glm-4-plus, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=a4867e65138e4f259c5c692aef943164.6xPiD4YHC2krGJAV, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-03 11:17:02,893 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：SILICON -- CHAT， 模型配置：AigcModel(id=ac05866194643c5341a73d837b2017c8, type=CHAT, model=deepseek-ai/DeepSeek-V2.5, provider=SILICON, name=deepseek, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-univnrseuupvzarrjozodhcmnurfvvyrcujdssfggfckynxd, secretKey=null, baseUrl=https://api.siliconflow.cn/v1, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-03 11:17:02,894 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- EMBEDDING， 模型配置：AigcModel(id=b3ac64325c37094da12d199d1928a37b, type=EMBEDDING, model=text-embedding-v3, provider=Q_WEN, name=通用文本向量-v3, responseLimit=null, temperature=0.2, topP=0.0, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=1024, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-03 11:17:02,894 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：ZHIPU -- TEXT_IMAGE， 模型配置：AigcModel(id=d4f776f9baa847f9c5a975c9f078e049, type=TEXT_IMAGE, model=cogview-3, provider=ZHIPU, name=图片生成, responseLimit=null, temperature=0.2, topP=0.0, apiKey=a4867e65138e4f259c5c692aef943164.6xPiD4YHC2krGJAV, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-03 11:17:02,894 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：OLLAMA -- EMBEDDING， 模型配置：AigcModel(id=d61f28c3ced11a1abac3215d2154c882, type=EMBEDDING, model=bge-m3:latest, provider=OLLAMA, name=ollama, responseLimit=null, temperature=0.2, topP=0.0, apiKey=null, secretKey=null, baseUrl=http://localhost:11434/, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=1024, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-03 11:17:02,895 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- EMBEDDING， 模型配置：AigcModel(id=dc3858965af7b992c806ed38ddd0164b, type=EMBEDDING, model=multimodal-embedding-v1, provider=Q_WEN, name=多模态向量模型, responseLimit=null, temperature=0.2, topP=0.0, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=1024, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-03 11:17:02,895 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- CHAT， 模型配置：AigcModel(id=f664ddbdec3fecd2afc2ed7631c17578, type=CHAT, model=Qwen-Long, provider=Q_WEN, name=Qwen-Long, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-553d1b0396064e16af616b910a526b53, secretKey=null, baseUrl=https://dashscope.aliyuncs.com/compatible-mode/v1, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-03 11:17:13,648 [main] ERROR [io.milvus.client.AbstractMilvusGrpcClient] AbstractMilvusGrpcClient.java:3378 - DEADLINE_EXCEEDED: deadline exceeded after 9.889437900s. Name resolution delay 0.021756900 seconds. [closed=[], open=[[wait_for_ready, buffered_nanos=**********, waiting_for_connection]]]
2025-09-03 11:17:13,650 [main] ERROR [io.milvus.client.AbstractMilvusGrpcClient] AbstractMilvusGrpcClient.java:3378 - Failed to initialize connection. Error: DEADLINE_EXCEEDED: deadline exceeded after 9.889437900s. Name resolution delay 0.021756900 seconds. [closed=[], open=[[wait_for_ready, buffered_nanos=**********, waiting_for_connection]]]
2025-09-03 11:17:13,652 [main] ERROR [c.t.l.ai.core.provider.EmbeddingStoreFactory] EmbeddingStoreFactory.java:103 - 向量数据库初始化失败：[Milvus] --- [MILVUS]，数据库配置信息：[AigcEmbedStore(id=c92cfbaaff366b12bc87c1082149150a, name=Milvus, provider=MILVUS, host=************, port=19530, username=null, password=null, databaseName=default, tableName=ai, dimension=1024)]
2025-09-03 11:17:13,891 [main] INFO  [cn.tycoding.langchat.server.store.AppStore] AppStore.java:44 - initialize app config list...
2025-09-03 11:17:14,171 [main] INFO  [cn.tycoding.langchat.server.store.AppChannelStore] AppChannelStore.java:52 - initialize app channel config list...
2025-09-03 11:17:14,354 [main] WARN  [c.t.l.c.oss.config.FileStorageAutoConfiguration] FileStorageAutoConfiguration.java:65 - 没有找到 FileRecorder 的实现类，文件上传之外的部分功能无法正常使用，必须实现该接口才能使用完整功能！
2025-09-03 11:17:14,388 [main] INFO  [o.d.x.file.storage.core.FileStorageServiceBuilder] FileStorageServiceBuilder.java:291 - 加载本地升级版存储平台：local
2025-09-03 11:17:14,394 [main] INFO  [o.d.x.file.storage.core.FileStorageServiceBuilder] FileStorageServiceBuilder.java:325 - 加载阿里云 OSS 存储平台：aliyun-oss
2025-09-03 11:17:14,426 [main] INFO  [c.t.l.a.b.searchEngine.SearchEngineStrategyManager] SearchEngineStrategyManager.java:57 - 注册搜索引擎策略: BOCHAAI
2025-09-03 11:17:14,427 [main] INFO  [c.t.l.a.b.searchEngine.SearchEngineStrategyManager] SearchEngineStrategyManager.java:60 - 搜索引擎策略管理器初始化完成，共注册 1 个策略
2025-09-03 11:17:15,437 [main] WARN  [c.b.mybatisplus.core.metadata.TableInfoHelper] TableInfoHelper.java:376 - Can not find table primary key in Class: "cn.tycoding.langchat.upms.entity.SysRoleMenu".
2025-09-03 11:17:15,437 [main] WARN  [c.b.mybatisplus.core.injector.DefaultSqlInjector] DefaultSqlInjector.java:52 - class cn.tycoding.langchat.upms.entity.SysRoleMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-09-03 11:17:15,758 [main] INFO  [c.t.langchat.mcp.core.config.McpAutoConfiguration] McpAutoConfiguration.java:41 - LangChat MCP模块已启用
2025-09-03 11:17:15,775 [main] INFO  [c.t.l.m.c.o.impl.FlexibleMcpOrchestrator] FlexibleMcpOrchestrator.java:230 - 注册编排策略: CompositeStrategy (优先级: 90)
2025-09-03 11:17:15,775 [main] INFO  [c.t.l.m.c.orchestration.strategy.CompositeStrategy] CompositeStrategy.java:48 - 复合编排策略已注册
2025-09-03 11:17:15,777 [main] INFO  [c.t.l.m.c.o.impl.FlexibleMcpOrchestrator] FlexibleMcpOrchestrator.java:230 - 注册编排策略: EnhancedImageGenerationStrategy (优先级: 85)
2025-09-03 11:17:15,777 [main] INFO  [c.t.l.m.c.o.s.EnhancedImageGenerationStrategy] EnhancedImageGenerationStrategy.java:49 - 增强图片生成编排策略已注册
2025-09-03 11:17:15,779 [main] INFO  [c.t.l.m.c.o.impl.FlexibleMcpOrchestrator] FlexibleMcpOrchestrator.java:230 - 注册编排策略: ImageGenerationStrategy (优先级: 80)
2025-09-03 11:17:15,779 [main] INFO  [c.t.l.m.c.o.strategy.ImageGenerationStrategy] ImageGenerationStrategy.java:48 - 图片生成编排策略已注册
2025-09-03 11:17:15,780 [main] INFO  [c.t.l.m.c.o.impl.FlexibleMcpOrchestrator] FlexibleMcpOrchestrator.java:230 - 注册编排策略: WebSearchStrategy (优先级: 70)
2025-09-03 11:17:15,781 [main] INFO  [c.t.l.m.c.orchestration.strategy.WebSearchStrategy] WebSearchStrategy.java:47 - 网络搜索编排策略已注册
2025-09-03 11:17:17,413 [main] WARN  [o.s.b.a.g.template.GroovyTemplateAutoConfiguration] GroovyTemplateAutoConfiguration.java:84 - Cannot find template location: classpath:/templates/ (please add some templates, check your Groovy configuration, or set spring.groovy.template.check-template-location=false)
2025-09-03 11:17:18,060 [main] INFO  [org.apache.coyote.http11.Http11NioProtocol] DirectJDKLog.java:173 - Starting ProtocolHandler ["http-nio-8100"]
2025-09-03 11:17:18,076 [main] INFO  [o.s.boot.web.embedded.tomcat.TomcatWebServer] TomcatWebServer.java:241 - Tomcat started on port 8100 (http) with context path ''
2025-09-03 11:17:18,089 [main] INFO  [cn.tycoding.langchat.LangChatApp] StartupInfoLogger.java:56 - Started LangChatApp in 23.47 seconds (process running for 25.233)
2025-09-03 11:17:18,095 [main] INFO  [c.t.l.mcp.core.config.McpOrchestrationConfig] McpOrchestrationConfig.java:47 - 开始注册MCP编排策略...
2025-09-03 11:17:18,095 [main] INFO  [c.t.l.m.c.o.impl.FlexibleMcpOrchestrator] FlexibleMcpOrchestrator.java:230 - 注册编排策略: ImagePublishStrategy (优先级: 80)
2025-09-03 11:17:18,095 [main] INFO  [c.t.l.m.c.o.impl.FlexibleMcpOrchestrator] FlexibleMcpOrchestrator.java:230 - 注册编排策略: ContentCreationStrategy (优先级: 90)
2025-09-03 11:17:18,096 [main] INFO  [c.t.l.m.c.o.impl.FlexibleMcpOrchestrator] FlexibleMcpOrchestrator.java:230 - 注册编排策略: AiDrivenStrategy (优先级: 10)
2025-09-03 11:17:18,096 [main] INFO  [c.t.l.mcp.core.config.McpOrchestrationConfig] McpOrchestrationConfig.java:54 - MCP编排策略注册完成
2025-09-03 11:17:18,097 [main] INFO  [c.t.l.mcp.core.config.McpServiceAutoConfiguration] McpServiceAutoConfiguration.java:47 - 开始自动同步数据库中的MCP服务配置...
2025-09-03 11:17:18,125 [main] INFO  [cn.tycoding.langchat.mcp.core.protocol.McpClient] McpClient.java:54 - MCP服务已注册: ai-prompt-optimizer
2025-09-03 11:17:18,143 [main] INFO  [c.t.l.m.c.service.impl.AigcMcpServiceServiceImpl] AigcMcpServiceServiceImpl.java:217 - MCP服务同步成功: ai-prompt-optimizer
2025-09-03 11:17:18,214 [main] INFO  [cn.tycoding.langchat.mcp.core.protocol.McpClient] McpClient.java:54 - MCP服务已注册: builtin-api
2025-09-03 11:17:18,220 [main] INFO  [c.t.l.m.c.service.impl.AigcMcpServiceServiceImpl] AigcMcpServiceServiceImpl.java:217 - MCP服务同步成功: builtin-api
2025-09-03 11:17:18,222 [main] INFO  [cn.tycoding.langchat.mcp.core.protocol.McpClient] McpClient.java:54 - MCP服务已注册: builtin-file
2025-09-03 11:17:18,226 [main] INFO  [c.t.l.m.c.service.impl.AigcMcpServiceServiceImpl] AigcMcpServiceServiceImpl.java:217 - MCP服务同步成功: builtin-file
2025-09-03 11:17:18,230 [main] INFO  [cn.tycoding.langchat.mcp.core.protocol.McpClient] McpClient.java:54 - MCP服务已注册: builtin-email
2025-09-03 11:17:18,235 [main] INFO  [c.t.l.m.c.service.impl.AigcMcpServiceServiceImpl] AigcMcpServiceServiceImpl.java:217 - MCP服务同步成功: builtin-email
2025-09-03 11:17:18,239 [main] INFO  [cn.tycoding.langchat.mcp.core.protocol.McpClient] McpClient.java:54 - MCP服务已注册: wanx-image-generation
2025-09-03 11:17:18,244 [main] INFO  [c.t.l.m.c.service.impl.AigcMcpServiceServiceImpl] AigcMcpServiceServiceImpl.java:217 - MCP服务同步成功: wanx-image-generation
2025-09-03 11:17:18,246 [main] INFO  [cn.tycoding.langchat.mcp.core.protocol.McpClient] McpClient.java:54 - MCP服务已注册: builtin-search
2025-09-03 11:17:18,250 [main] INFO  [c.t.l.m.c.service.impl.AigcMcpServiceServiceImpl] AigcMcpServiceServiceImpl.java:217 - MCP服务同步成功: builtin-search
2025-09-03 11:17:18,251 [main] INFO  [c.t.l.mcp.core.config.McpServiceAutoConfiguration] McpServiceAutoConfiguration.java:52 - MCP服务配置自动同步完成
2025-09-03 11:17:18,252 [main] INFO  [c.t.l.common.core.component.CustomBannerPrinter] CustomBannerPrinter.java:34 - AIGC智能AI平台 启动完成...... 当前环境：dev
2025-09-03 11:20:05,611 [http-nio-8100-exec-1] INFO  [o.a.c.core.ContainerBase.[Tomcat].[localhost].[/]] DirectJDKLog.java:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-03 11:20:05,611 [http-nio-8100-exec-1] INFO  [org.springframework.web.servlet.DispatcherServlet] FrameworkServlet.java:532 - Initializing Servlet 'dispatcherServlet'
2025-09-03 11:20:05,613 [http-nio-8100-exec-1] INFO  [org.springframework.web.servlet.DispatcherServlet] FrameworkServlet.java:554 - Completed initialization in 1 ms
2025-09-03 11:20:27,838 [http-nio-8100-exec-10] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本： 
个人信用报告
 
报告编号：2025061016250556886580 报告时间：2025-06-10 16:25:05
姓名： 侯明 证件类型：身份证 证件号码：371322198905214915 已婚
 
信贷记录
这部分包含您的信用卡、贷款和其他信贷记录。金额类数据均以人民币计算，精确到元。
信 息概要
贷款
信用卡 其他业务 逾期记录可能影响对您的信用评价。
购房 其他
账户数 48 4 61 -- 购房贷款，包括个人住房贷款、个人商用
房（包括商住两用）贷款和个人住房公积
未结清/未销户账户数 19 -- 3 -- 金贷款。
发生过逾期的账户数 2 -- 2 -- 发生过逾期的信用卡账户，指曾经“未按
时还最低还款额”的贷记卡账户和“透支
发生过90天以上逾期的账户数 -- -- -- -- 超过60天”的准贷记卡账户。
为个人 为企业
相关还款责任账户数 1 3
信用卡
发生过逾期的贷记卡账户明细如下：
1. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（新加坡元账户，卡片尾号：0792）。截至2025年05月，信用
额度21,715，余额0，当前无逾期。最近5年内有1个月处于逾期状态，没有发生过90天以上逾期。
2. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（美元账户，卡片尾号：0792）。截至2025年05月，信用额度
21,060，余额695，当前有逾期。最近5年内有4个月处于逾期状态，没有发生过90天以上逾期。
从未逾期过的贷记卡及透支未超过60天的准贷记卡账户明细如下：
3. 2011年02月23日中国银行股份有限公司临沂分行发放的贷记卡（人民币账户，卡片尾号：4204）。截至2025年05月，信用额度
2,000，已使用额度0。
4. 2011年04月27日兴业银行股份有限公司发放的贷记卡（人民币账户）。截至2025年06月，信用额度8,000，已使用额度0。
5. 2018年12月06日中国工商银行股份有限公司临沂分行发放的贷记卡（人民币账户，卡片尾号：7133）。截至2025年05月，信用
额度0，已使用额度0。
6. 2020年08月18日中信银行股份有限公司信用卡中心发放的贷记卡（人民币账户）。截至2025年05月，信用额度50,000，已使用
额度0。
7. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（人民币账户，卡片尾号：0792）。截至2025年05月，信用
额度20,000，已使用额度0。
8. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（澳大利亚元账户，卡片尾号：0792）。截至2025年05月，
信用额度17,378，已使用额度0。
9. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（瑞士法郎账户，卡片尾号：0792）。截至2025年05月，信
用额度24,559，已使用额度0。
10. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（日元账户，卡片尾号：0792）。截至2025年05月，信用额
度16,584，已使用额度0。
11. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（香港元账户，卡片尾号：0792）。截至2025年05月，信用
额度20,965，已使用额度57。
第 1 页，共 7 页
12. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（欧元账户，卡片尾号：0792）。截至2025年05月，信用额
度19,671，已使用额度0。
13. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（英镑账户，卡片尾号：0792）。截至2025年05月，信用额
度20,782，已使用额度0。
14. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（新西兰元账户，卡片尾号：0792）。截至2025年05月，信
用额度17,587，已使用额度0。
15. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（加元账户，卡片尾号：0792）。截至2025年05月，信用额
度18,907，已使用额度0。
16. 2024年03月14日中国建设银行股份有限公司深圳深圳湾支行发放的贷记卡（人民币账户，卡片尾号：9345）。截至2025年
05月，信用额度0，余额230,750（含未出单的大额专项分期余额220,000）。
17. 2011年02月01日中国农业银行股份有限公司山东省分行发放的贷记卡（人民币账户，卡片尾号：3721），2022年10月销户。
18. 2017年07月31日上海浦东发展银行股份有限公司信用卡中心发放的贷记卡（人民币账户），2022年12月销户。
19. 2018年09月13日中国建设银行股份有限公司临沂新城支行发放的贷记卡（人民币账户，卡片尾号：2154），2022年09月销户。
20. 2018年09月13日中国建设银行股份有限公司临沂新城支行发放的贷记卡（美元账户，卡片尾号：2154），2022年09月销户。
21. 2018年10月18日中国建设银行股份有限公司临沂新城支行发放的贷记卡（人民币账户，卡片尾号：0929），2022年09月销户。
22. 2018年10月18日中国建设银行股份有限公司临沂新城支行发放的贷记卡（美元账户，卡片尾号：0929），2022年09月销户。
23. 2018年10月22日中国建设银行股份有限公司临沂新城支行发放的贷记卡（美元账户，卡片尾号：2539），2022年09月销户。
24. 2018年10月22日中国建设银行股份有限公司临沂新城支行发放的贷记卡（人民币账户，卡片尾号：2539），2022年09月销户。
25. 2018年10月23日中国建设银行股份有限公司临沂新城支行发放的贷记卡（美元账户，卡片尾号：4982），2022年09月销户。
26. 2018年10月23日中国建设银行股份有限公司临沂新城支行发放的贷记卡（人民币账户，卡片尾号：4982），2022年09月销户。
27. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（加元账户，卡片尾号：5529），2023年07月销户。
28. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（欧元账户，卡片尾号：5529），2023年07月销户。
29. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（人民币账户，卡片尾号：5529），2023年07月销户。
30. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（瑞士法郎账户，卡片尾号：5529），2023年07月销户。
31. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（新西兰元账户，卡片尾号：5529），2023年07月销户。
32. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（澳大利亚元账户，卡片尾号：5529），2023年07月销户。
33. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（新加坡元账户，卡片尾号：5529），2023年07月销户。
34. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（美元账户，卡片尾号：5529），2023年07月销户。
35. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（英镑账户，卡片尾号：5529），2023年07月销户。
36. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（香港元账户，卡片尾号：5529），2023年07月销户。
37. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（日元账户，卡片尾号：5529），2023年07月销户。
38. 2019年07月08日中国光大银行股份有限公司发放的贷记卡（人民币账户，卡片尾号：6876），2022年12月销户。
39. 2019年07月08日中国光大银行股份有限公司发放的贷记卡（美元账户，卡片尾号：6876），2022年12月销户。
40. 2019年07月08日中国光大银行股份有限公司发放的贷记卡（人民币账户，卡片尾号：2645），2022年12月销户。
41. 2020年08月26日中国建设银行股份有限公司临沂新城支行发放的贷记卡（人民币账户，卡片尾号：4977），2022年09月销户。
42. 2022年01月05日中国建设银行股份有限公司临沂新城支行发放的贷记卡（人民币账户，卡片尾号：4987），2022年09月销户。
43. 2023年02月02日交通银行股份有限公司太平洋信用卡中心发放的贷记卡（人民币账户，卡片尾号：6182），2023年03月销户。
44. 2023年02月02日交通银行股份有限公司太平洋信用卡中心发放的贷记卡（美元账户，卡片尾号：6182），2023年03月销户。
45. 2023年09月27日交通银行股份有限公司太平洋信用卡中心发放的贷记卡（人民币账户，卡片尾号：4965），2024年04月销户。
46. 2012年03月20日中国银行股份有限公司山东省分行发放的贷记卡（人民币账户，卡片尾号：1956）。截至2021年12月尚未激
活。
47. 2017年08月16日中国民生银行股份有限公司信用卡中心发放的贷记卡（人民币账户）。截至2025年05月尚未激活。
48. 2019年07月12日华夏银行股份有限公司信用卡中心发放的贷记卡（人民币账户）。截至2025年05月尚未激活。
 
贷款
发生过逾期的账户明细如下：
1. 2020年07月06日中国民生银行股份有限公司临沂兰陵支行发放的3,500,000元（人民币）个人经营性贷款，2021年07月已结清。最
近5年内有1个月处于逾期状态，没有发生过90天以上的逾期。
2. 2021年03月23日中国民生银行股份有限公司临沂兰陵支行发放的1,700,000元（人民币）个人经营性贷款，2022年03月已结清。最
近5年内有1个月处于逾期状态，没有发生过90天以上的逾期。
从未发生过逾期的账户明细如下：
3. 2025年03月05日山东临沂兰山农村商业银行股份有限公司发放的5,200,000元（人民币）个人经营性贷款，2026年02月22日到
期。截至2025年05月，余额5,200,000。
4. 2025年03月20日山东临沂兰山农村商业银行股份有限公司发放的2,800,000元（人民币）个人经营性贷款，2026年02月22日到
期。截至2025年05月，余额2,800,000。
第 2 页，共 7 页
5. 2024年03月28日中国邮政储蓄银行股份有限公司临沂市分行为个人经营性贷款授信，额度有效期至2028年03月28日，可循环使
用。截至2025年05月，信用额度3,000,000元（人民币），余额为3,000,000，当前无逾期。
6. 2010年11月16日国家开发银行山东省分行发放的6,000元（人民币）个人助学贷款，2015年12月已结清。
7. 2011年08月02日中国银行股份有限公司临沂分行发放的418,000元（人民币）个人住房商业贷款，2016年03月已结清。
8. 2011年11月16日国家开发银行山东省分行发放的6,000元（人民币）个人助学贷款，2015年12月已结清。
9. 2012年11月15日国家开发银行山东省分行发放的6,000元（人民币）个人助学贷款，2017年01月已结清。
10. 2017年01月16日浙江网商银行股份有限公司发放的19,000元（人民币）个人经营性贷款，2017年01月已结清。
11. 2017年02月16日浙江网商银行股份有限公司发放的20,000元（人民币）个人经营性贷款，2017年03月已结清。
12. 2017年06月11日浙江网商银行股份有限公司发放的5,000元（人民币）个人经营性贷款，2017年07月已结清。
13. 2017年06月28日梅赛德斯-奔驰汽车金融有限公司发放的249,900元（人民币）个人汽车消费贷款，2017年12月已结清。
14. 2017年07月24日浙江网商银行股份有限公司发放的5,000元（人民币）个人经营性贷款，2018年07月已结清。
15. 2017年08月03日日照银行股份有限公司发放的430,000元（人民币）个人住房商业贷款，2020年06月已结清。
16. 2017年11月01日中国工商银行股份有限公司临沂分行发放的645,000元（人民币）个人住房商业贷款，2023年02月已结清。
17. 2017年12月14日中国建设银行股份有限公司山东省分行发放的2,790,000元（人民币）个人住房商业贷款，2021年04月已结清。
18. 2018年10月25日四川新网银行股份有限公司发放的36,300元（人民币）其他个人消费贷款，2019年01月已结清。
19. 2019年02月02日浙江网商银行股份有限公司发放的2,000元（人民币）个人经营性贷款，2019年05月已结清。
20. 2019年06月05日浙江网商银行股份有限公司发放的321,000元（人民币）个人经营性贷款，2019年09月已结清。
21. 2019年07月09日中国民生银行股份有限公司临沂兰陵支行发放的3,500,000元（人民币）个人经营性贷款，2020年07月已结清。
22. 2019年08月11日重庆美团三快小额贷款有限公司发放的12,000元（人民币）其他个人消费贷款，2019年08月已结清。
23. 2019年09月22日重庆美团三快小额贷款有限公司发放的12,000元（人民币）其他个人消费贷款，2019年09月已结清。
24. 2019年10月08日浙江网商银行股份有限公司发放的224,000元（人民币）个人经营性贷款，2019年12月已结清。
25. 2019年12月17日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2020年12月已结清。
26. 2019年12月17日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2020年12月已结清。
27. 2019年12月25日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2020年12月已结清。
28. 2019年12月26日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2020年12月已结清。
29. 2020年01月01日中国邮政储蓄银行股份有限公司临沂市分行发放的400,000元（人民币）个人经营性贷款，2020年12月已结清。
30. 2020年02月17日中国建设银行股份有限公司山东省分行发放的150,000元（人民币）其他贷款，2020年12月已结清。
31. 2020年04月12日浙江网商银行股份有限公司发放的56,000元（人民币）个人经营性贷款，2020年04月已结清。
32. 2020年05月10日浙江网商银行股份有限公司发放的150,000元（人民币）个人经营性贷款，2020年05月已结清。
33. 2020年07月31日浙江网商银行股份有限公司发放的170,000元（人民币）个人经营性贷款，2020年11月已结清。
34. 2021年04月01日浙江网商银行股份有限公司发放的172,460元（人民币）个人经营性贷款，2021年04月已结清。
35. 2022年10月01日中国对外经济贸易信托有限公司发放的1,000,000元（人民币）个人经营性贷款，2024年07月已结清。
36. 2022年10月07日平安银行股份有限公司临沂分行发放的486,000元（人民币）个人经营性贷款，2023年05月已结清。
37. 2023年03月10日山东临沂兰山农村商业银行股份有限公司发放的2,800,000元（人民币）个人经营性贷款，2024年03月已结清。
38. 2024年03月07日山东临沂兰山农村商业银行股份有限公司发放的2,800,000元（人民币）个人经营性贷款，2025年03月已结清。
39. 2020年12月03日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2021年11月已结清。
40. 2020年12月04日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2021年11月已结清。
41. 2020年12月06日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2021年12月已结清。
42. 2020年12月07日中国邮政储蓄银行股份有限公司临沂市分行发放的400,000元（人民币）个人经营性贷款，2021年11月已结清。
43. 2020年12月08日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2021年12月已结清。
44. 2021年07月07日中国民生银行股份有限公司临沂兰陵支行发放的1,800,000元（人民币）个人经营性贷款，2022年04月已结清。
45. 2021年08月12日浙江网商银行股份有限公司发放的402,298元（人民币）个人经营性贷款，2022年02月已结清。
46. 2021年11月26日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2022年11月已结清。
47. 2021年11月29日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2022年11月已结清。
48. 2021年12月01日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2022年12月已结清。
49. 2021年12月02日中国邮政储蓄银行股份有限公司临沂市分行发放的400,000元（人民币）个人经营性贷款，2022年12月已结清。
50. 2021年12月03日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2022年12月已结清。
51. 2022年01月12日中国工商银行股份有限公司临沂分行发放的4,500,000元（人民币）其他贷款，2022年12月已结清。
52. 2022年11月27日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2023年11月已结清。
53. 2022年11月29日中国邮政储蓄银行股份有限公司临沂市分行发放的300,000元（人民币）个人经营性贷款，2023年11月已结清。
54. 2022年12月01日中国邮政储蓄银行股份有限公司临沂市分行发放的200,000元（人民币）个人经营性贷款，2023年11月已结清。
55. 2022年12月02日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2023年11月已结清。
56. 2022年12月03日中国邮政储蓄银行股份有限公司临沂市分行发放的400,000元（人民币）个人经营性贷款，2023年11月已结清。
57. 2022年12月04日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2023年11月已结清。
58. 2023年03月10日山东临沂兰山农村商业银行股份有限公司发放的5,200,000元（人民币）个人经营性贷款，2024年03月已结清。
59. 2023年11月30日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2024年11月已结清。
第 3 页，共 7 页
60. 2023年12月12日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2024年12月已结清。
61. 2023年12月13日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2024年12月已结清。
62. 2024年03月07日山东临沂兰山农村商业银行股份有限公司发放的5,200,000元（人民币）个人经营性贷款，2025年03月已结清。
63. 2021年11月11日浙江网商银行股份有限公司为个人经营性贷款授信，可循环使用。2022年02月已结清。
64. 2022年04月22日中国民生银行股份有限公司临沂兰陵支行为个人经营性贷款授信，可循环使用。2023年03月已结清。
65. 2022年10月28日中国民生银行股份有限公司临沂兰陵支行为个人经营性贷款授信，可循环使用。2023年03月已结清。
 
相关还款责任信息 
1. 2023年04月27日，为马景涛（证件类型：身份证，证件号码：371322198912203851）在临商银行股份有限公司成都路支行办理
的个人经营性贷款承担相关还款责任，责任人类型为保证人，相关还款责任金额2,000,000（保证合同编号：
D10014730H00072023042700000131）。截至2025年05月31日，个人经营性贷款余额0。
2. 2023年11月29日，为临沂竣豪电子商务有限责任公司（证件类型：中征码，证件号码：3713020005368380）在临沂市商业银行商
城支行办理的贷款承担相关还款责任，责任人类型为保证人，相关还款责任金额10,000,000（保证合同编号：
D10014730H00072023112900000132）。截至2024年11月30日，贷款余额0（人民币元）。
3. 2025年01月15日，为山东高新供应链管理服务有限公司（证件类型：中征码，证件号码：371300U6NRR64W37）在临商银行股
份有限公司高新区支行办理的贷款承担相关还款责任，责任人类型为保证人，相关还款责任金额300,000,000（保证合同编号：
D10014730H00072025011500000117）。截至2025年05月31日，贷款余额138,077,951（人民币元）。
4. 2025年03月25日，为山东高新供应链管理服务有限公司（证件类型：中征码，证件号码：371300U6NRR64W37）在临商银行股
份有限公司高新区支行办理的贷款承担相关还款责任，责任人类型为保证人，相关还款责任金额300,000,000（保证合同编号：
D10014730H00072025011500000117）。截至2025年05月31日，贷款余额48,549,571（人民币元）。
 
非信贷交易记录
系统中没有您最近5年内的非信贷交易记录。
公共记录
系统中没有您最近5年内的公共信息记录。
查询记录
这部分包含您的信用报告最近2年内被查询的记录。
机构查询记录明细
编号 查询日期 查询机构 查询原因
1 2025年06月04日 临商银行股份有限公司高新区支行 贷后管理
2 2025年05月23日 临商银行股份有限公司高新区支行 贷后管理
3 2025年05月07日 临商银行股份有限公司高新区支行 贷后管理
4 2025年04月25日 临商银行股份有限公司高新区支行 贷后管理
5 2025年04月19日 浙江网商银行股份有限公司 贷后管理
6 2025年04月14日 临商银行股份有限公司高新区支行 贷后管理
7 2025年04月03日 临商银行股份有限公司高新区支行 贷后管理
8 2025年03月24日 临商银行股份有限公司高新区支行 贷后管理
9 2025年03月19日 山东临沂兰山农村商业银行股份有 贷款审批
限公司
10 2025年03月17日 中国邮政储蓄银行股份有限公司临 贷后管理
沂市分行
第 4 页，共 7 页
11 2025年03月11日 临商银行股份有限公司高新区支行 贷后管理
12 2025年03月05日 交通银行股份有限公司太平洋信用 信用卡审批
卡中心
13 2025年02月28日 临商银行股份有限公司高新区支行 贷后管理
14 2025年02月07日 临商银行股份有限公司高新区支行 贷后管理
15 2025年01月24日 临商银行股份有限公司成都路支行 贷后管理
16 2025年01月17日 中国邮政储蓄银行股份有限公司山 贷款审批
东省分行
17 2025年01月14日 临商银行股份有限公司高新区支行 担保资格审查
18 2024年11月29日 狮桥融资租赁（中国）有限公司 资信审查
19 2024年11月29日 武汉众邦银行股份有限公司 法人代表、负责人、高管等资信审
查
20 2024年11月29日 深圳市大数融资担保有限公司 担保资格审查
21 2024年11月25日 临商银行股份有限公司成都路支行 贷后管理
22 2024年11月21日 浙江网商银行股份有限公司 贷款审批
23 2024年11月20日 中国邮政储蓄银行股份有限公司 贷后管理
24 2024年10月31日 临商银行股份有限公司成都路支行 贷后管理
25 2024年10月06日 中国邮政储蓄银行股份有限公司 贷后管理
26 2024年09月30日 平安银行股份有限公司 贷款审批
27 2024年09月03日 中国邮政储蓄银行股份有限公司 贷后管理
28 2024年08月03日 中国邮政储蓄银行股份有限公司 贷后管理
29 2024年07月31日 临商银行股份有限公司成都路支行 贷后管理
30 2024年07月05日 平安融易（江苏）融资担保有限公 担保资格审查
司
31 2024年07月01日 中国邮政储蓄银行股份有限公司 贷后管理
32 2024年05月17日 中国邮政储蓄银行股份有限公司 贷后管理
33 2024年04月16日 临商银行股份有限公司成都路支行 贷后管理
34 2024年04月12日 中国邮政储蓄银行股份有限公司 贷后管理
35 2024年04月02日 平安融易（江苏）融资担保有限公 担保资格审查
司
36 2024年03月13日 中国建设银行股份有限公司深圳华 信用卡审批
侨城支行
37 2024年03月07日 中国邮政储蓄银行股份有限公司山 贷款审批
东省分行
38 2024年03月07日 山东临沂兰山农村商业银行股份有 贷款审批
限公司
39 2024年03月06日 山东临沂兰山农村商业银行股份有 贷款审批
限公司
40 2024年03月02日 中国邮政储蓄银行股份有限公司 贷后管理
41 2024年02月19日 临商银行股份有限公司成都路支行 贷后管理
42 2024年01月29日 中国邮政储蓄银行股份有限公司 贷后管理
43 2024年01月02日 平安融易（江苏）融资担保有限公 担保资格审查
司
44 2023年11月28日 中国工商银行股份有限公司银行卡 贷后管理
业务部(牡丹卡中心)
第 5 页，共 7 页
45 2023年11月28日 临商银行股份有限公司商城支行营 担保资格审查
业部
46 2023年11月27日 中国邮政储蓄银行股份有限公司 贷款审批
47 2023年11月17日 中国邮政储蓄银行股份有限公司 贷后管理
48 2023年11月15日 临商银行股份有限公司成都路支行 贷后管理
49 2023年11月01日 临商银行股份有限公司商城支行营 担保资格审查
业部
50 2023年10月19日 临商银行股份有限公司高新区支行 担保资格审查
51 2023年10月02日 平安融易（江苏）融资担保有限公 担保资格审查
司
52 2023年09月27日 交通银行股份有限公司太平洋信用 信用卡审批
卡中心
53 2023年09月15日 中国邮政储蓄银行股份有限公司 贷后管理
54 2023年08月29日 中国工商银行股份有限公司银行卡 信用卡审批
业务部(牡丹卡中心)
55 2023年08月12日 临商银行股份有限公司成都路支行 贷后管理
56 2023年07月14日 中国邮政储蓄银行股份有限公司 贷后管理
57 2023年07月03日 平安融易（江苏）融资担保有限公 担保资格审查
司
个人查询记录明细
编号 查询日期 查询机构 查询原因
1 2025年03月19日 本人 本人查询（自助查询机）
2 2024年04月07日 本人 本人查询（自助查询机）
3 2024年04月01日 本人 本人查询（互联网个人信用信息服
务平台）
4 2023年09月21日 本人 本人查询（商业银行网上银行）
第 6 页，共 7 页
说  明
 
1.除查询记录外，本报告中的信息是依据截至报告时间个人征信系统记录的信息生成，征信中心不确保其真实性和准确性，但承
诺在信息汇总、加工、整合的全过程中保持客观、中立的地位。
2.本报告仅包含可能影响您信用评价的主要信息，如需获取您在个人征信系统中更详细的记录，请到当地信用报告查询网点查
询。信用报告查询网点的具体地址及联系方式可访问征信中心门户网站（www.pbccrc.org.cn）查询。
3.您有权对本报告中的内容提出异议。如有异议，可联系数据提供单位，也可到当地信用报告查询网点提出异议申请。
4.本报告仅供您了解自己的信用状况，请妥善保管。因保管不当造成个人隐私泄露的，征信中心将不承担相关责任。
5.更多咨询，请致电全国客户服务热线400-810-8866。
第 7 页，共 7 页

2025-09-03 11:20:27,880 [http-nio-8100-exec-9] ERROR [com.agentsflex.core.llm.client.HttpClient] HttpClient.java:136 - java.lang.IllegalArgumentException: Expected URL scheme 'http' or 'https' but no scheme was found for null/c...
java.lang.IllegalArgumentException: Expected URL scheme 'http' or 'https' but no scheme was found for null/c...
	at okhttp3.HttpUrl$Builder.parse$okhttp(HttpUrl.kt:1261)
	at okhttp3.HttpUrl$Companion.get(HttpUrl.kt:1634)
	at okhttp3.Request$Builder.url(Request.kt:184)
	at com.agentsflex.core.llm.client.HttpClient.execute0(HttpClient.java:157)
	at com.agentsflex.core.llm.client.HttpClient.executeString(HttpClient.java:130)
	at com.agentsflex.core.llm.client.HttpClient.post(HttpClient.java:57)
	at com.agentsflex.llm.qwen.QwenLlm.chat(QwenLlm.java:63)
	at com.agentsflex.core.chain.node.LlmNode.execute(LlmNode.java:121)
	at com.agentsflex.core.chain.Chain.doExecuteNode(Chain.java:501)
	at com.agentsflex.core.chain.Chain.doExecuteNodes(Chain.java:472)
	at com.agentsflex.core.chain.Chain.doExecuteNode(Chain.java:539)
	at com.agentsflex.core.chain.Chain.doExecuteNodes(Chain.java:472)
	at com.agentsflex.core.chain.Chain.doExecuteNode(Chain.java:539)
	at com.agentsflex.core.chain.Chain.doExecuteNodes(Chain.java:472)
	at com.agentsflex.core.chain.Chain.executeInternal(Chain.java:455)
	at com.agentsflex.core.chain.Chain.runInLifeCycle(Chain.java:603)
	at com.agentsflex.core.chain.Chain.executeForResult(Chain.java:353)
	at com.agentsflex.core.chain.Chain.executeForResult(Chain.java:346)
	at cn.tycoding.langchat.ai.core.service.impl.WorkflowRunningServiceImpl.workflowRunning(WorkflowRunningServiceImpl.java:170)
	at cn.tycoding.langchat.server.controller.AiWorkflowController.tryRunning(AiWorkflowController.java:136)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:259)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:192)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-03 11:20:36,974 [http-nio-8100-exec-4] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本： 
个人信用报告
 
报告编号：2025061016250556886580 报告时间：2025-06-10 16:25:05
姓名： 侯明 证件类型：身份证 证件号码：371322198905214915 已婚
 
信贷记录
这部分包含您的信用卡、贷款和其他信贷记录。金额类数据均以人民币计算，精确到元。
信 息概要
贷款
信用卡 其他业务 逾期记录可能影响对您的信用评价。
购房 其他
账户数 48 4 61 -- 购房贷款，包括个人住房贷款、个人商用
房（包括商住两用）贷款和个人住房公积
未结清/未销户账户数 19 -- 3 -- 金贷款。
发生过逾期的账户数 2 -- 2 -- 发生过逾期的信用卡账户，指曾经“未按
时还最低还款额”的贷记卡账户和“透支
发生过90天以上逾期的账户数 -- -- -- -- 超过60天”的准贷记卡账户。
为个人 为企业
相关还款责任账户数 1 3
信用卡
发生过逾期的贷记卡账户明细如下：
1. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（新加坡元账户，卡片尾号：0792）。截至2025年05月，信用
额度21,715，余额0，当前无逾期。最近5年内有1个月处于逾期状态，没有发生过90天以上逾期。
2. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（美元账户，卡片尾号：0792）。截至2025年05月，信用额度
21,060，余额695，当前有逾期。最近5年内有4个月处于逾期状态，没有发生过90天以上逾期。
从未逾期过的贷记卡及透支未超过60天的准贷记卡账户明细如下：
3. 2011年02月23日中国银行股份有限公司临沂分行发放的贷记卡（人民币账户，卡片尾号：4204）。截至2025年05月，信用额度
2,000，已使用额度0。
4. 2011年04月27日兴业银行股份有限公司发放的贷记卡（人民币账户）。截至2025年06月，信用额度8,000，已使用额度0。
5. 2018年12月06日中国工商银行股份有限公司临沂分行发放的贷记卡（人民币账户，卡片尾号：7133）。截至2025年05月，信用
额度0，已使用额度0。
6. 2020年08月18日中信银行股份有限公司信用卡中心发放的贷记卡（人民币账户）。截至2025年05月，信用额度50,000，已使用
额度0。
7. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（人民币账户，卡片尾号：0792）。截至2025年05月，信用
额度20,000，已使用额度0。
8. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（澳大利亚元账户，卡片尾号：0792）。截至2025年05月，
信用额度17,378，已使用额度0。
9. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（瑞士法郎账户，卡片尾号：0792）。截至2025年05月，信
用额度24,559，已使用额度0。
10. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（日元账户，卡片尾号：0792）。截至2025年05月，信用额
度16,584，已使用额度0。
11. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（香港元账户，卡片尾号：0792）。截至2025年05月，信用
额度20,965，已使用额度57。
第 1 页，共 7 页
12. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（欧元账户，卡片尾号：0792）。截至2025年05月，信用额
度19,671，已使用额度0。
13. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（英镑账户，卡片尾号：0792）。截至2025年05月，信用额
度20,782，已使用额度0。
14. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（新西兰元账户，卡片尾号：0792）。截至2025年05月，信
用额度17,587，已使用额度0。
15. 2023年08月30日中国工商银行股份有限公司临沂分行发放的贷记卡（加元账户，卡片尾号：0792）。截至2025年05月，信用额
度18,907，已使用额度0。
16. 2024年03月14日中国建设银行股份有限公司深圳深圳湾支行发放的贷记卡（人民币账户，卡片尾号：9345）。截至2025年
05月，信用额度0，余额230,750（含未出单的大额专项分期余额220,000）。
17. 2011年02月01日中国农业银行股份有限公司山东省分行发放的贷记卡（人民币账户，卡片尾号：3721），2022年10月销户。
18. 2017年07月31日上海浦东发展银行股份有限公司信用卡中心发放的贷记卡（人民币账户），2022年12月销户。
19. 2018年09月13日中国建设银行股份有限公司临沂新城支行发放的贷记卡（人民币账户，卡片尾号：2154），2022年09月销户。
20. 2018年09月13日中国建设银行股份有限公司临沂新城支行发放的贷记卡（美元账户，卡片尾号：2154），2022年09月销户。
21. 2018年10月18日中国建设银行股份有限公司临沂新城支行发放的贷记卡（人民币账户，卡片尾号：0929），2022年09月销户。
22. 2018年10月18日中国建设银行股份有限公司临沂新城支行发放的贷记卡（美元账户，卡片尾号：0929），2022年09月销户。
23. 2018年10月22日中国建设银行股份有限公司临沂新城支行发放的贷记卡（美元账户，卡片尾号：2539），2022年09月销户。
24. 2018年10月22日中国建设银行股份有限公司临沂新城支行发放的贷记卡（人民币账户，卡片尾号：2539），2022年09月销户。
25. 2018年10月23日中国建设银行股份有限公司临沂新城支行发放的贷记卡（美元账户，卡片尾号：4982），2022年09月销户。
26. 2018年10月23日中国建设银行股份有限公司临沂新城支行发放的贷记卡（人民币账户，卡片尾号：4982），2022年09月销户。
27. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（加元账户，卡片尾号：5529），2023年07月销户。
28. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（欧元账户，卡片尾号：5529），2023年07月销户。
29. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（人民币账户，卡片尾号：5529），2023年07月销户。
30. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（瑞士法郎账户，卡片尾号：5529），2023年07月销户。
31. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（新西兰元账户，卡片尾号：5529），2023年07月销户。
32. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（澳大利亚元账户，卡片尾号：5529），2023年07月销户。
33. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（新加坡元账户，卡片尾号：5529），2023年07月销户。
34. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（美元账户，卡片尾号：5529），2023年07月销户。
35. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（英镑账户，卡片尾号：5529），2023年07月销户。
36. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（香港元账户，卡片尾号：5529），2023年07月销户。
37. 2018年10月24日中国工商银行股份有限公司临沂分行发放的贷记卡（日元账户，卡片尾号：5529），2023年07月销户。
38. 2019年07月08日中国光大银行股份有限公司发放的贷记卡（人民币账户，卡片尾号：6876），2022年12月销户。
39. 2019年07月08日中国光大银行股份有限公司发放的贷记卡（美元账户，卡片尾号：6876），2022年12月销户。
40. 2019年07月08日中国光大银行股份有限公司发放的贷记卡（人民币账户，卡片尾号：2645），2022年12月销户。
41. 2020年08月26日中国建设银行股份有限公司临沂新城支行发放的贷记卡（人民币账户，卡片尾号：4977），2022年09月销户。
42. 2022年01月05日中国建设银行股份有限公司临沂新城支行发放的贷记卡（人民币账户，卡片尾号：4987），2022年09月销户。
43. 2023年02月02日交通银行股份有限公司太平洋信用卡中心发放的贷记卡（人民币账户，卡片尾号：6182），2023年03月销户。
44. 2023年02月02日交通银行股份有限公司太平洋信用卡中心发放的贷记卡（美元账户，卡片尾号：6182），2023年03月销户。
45. 2023年09月27日交通银行股份有限公司太平洋信用卡中心发放的贷记卡（人民币账户，卡片尾号：4965），2024年04月销户。
46. 2012年03月20日中国银行股份有限公司山东省分行发放的贷记卡（人民币账户，卡片尾号：1956）。截至2021年12月尚未激
活。
47. 2017年08月16日中国民生银行股份有限公司信用卡中心发放的贷记卡（人民币账户）。截至2025年05月尚未激活。
48. 2019年07月12日华夏银行股份有限公司信用卡中心发放的贷记卡（人民币账户）。截至2025年05月尚未激活。
 
贷款
发生过逾期的账户明细如下：
1. 2020年07月06日中国民生银行股份有限公司临沂兰陵支行发放的3,500,000元（人民币）个人经营性贷款，2021年07月已结清。最
近5年内有1个月处于逾期状态，没有发生过90天以上的逾期。
2. 2021年03月23日中国民生银行股份有限公司临沂兰陵支行发放的1,700,000元（人民币）个人经营性贷款，2022年03月已结清。最
近5年内有1个月处于逾期状态，没有发生过90天以上的逾期。
从未发生过逾期的账户明细如下：
3. 2025年03月05日山东临沂兰山农村商业银行股份有限公司发放的5,200,000元（人民币）个人经营性贷款，2026年02月22日到
期。截至2025年05月，余额5,200,000。
4. 2025年03月20日山东临沂兰山农村商业银行股份有限公司发放的2,800,000元（人民币）个人经营性贷款，2026年02月22日到
期。截至2025年05月，余额2,800,000。
第 2 页，共 7 页
5. 2024年03月28日中国邮政储蓄银行股份有限公司临沂市分行为个人经营性贷款授信，额度有效期至2028年03月28日，可循环使
用。截至2025年05月，信用额度3,000,000元（人民币），余额为3,000,000，当前无逾期。
6. 2010年11月16日国家开发银行山东省分行发放的6,000元（人民币）个人助学贷款，2015年12月已结清。
7. 2011年08月02日中国银行股份有限公司临沂分行发放的418,000元（人民币）个人住房商业贷款，2016年03月已结清。
8. 2011年11月16日国家开发银行山东省分行发放的6,000元（人民币）个人助学贷款，2015年12月已结清。
9. 2012年11月15日国家开发银行山东省分行发放的6,000元（人民币）个人助学贷款，2017年01月已结清。
10. 2017年01月16日浙江网商银行股份有限公司发放的19,000元（人民币）个人经营性贷款，2017年01月已结清。
11. 2017年02月16日浙江网商银行股份有限公司发放的20,000元（人民币）个人经营性贷款，2017年03月已结清。
12. 2017年06月11日浙江网商银行股份有限公司发放的5,000元（人民币）个人经营性贷款，2017年07月已结清。
13. 2017年06月28日梅赛德斯-奔驰汽车金融有限公司发放的249,900元（人民币）个人汽车消费贷款，2017年12月已结清。
14. 2017年07月24日浙江网商银行股份有限公司发放的5,000元（人民币）个人经营性贷款，2018年07月已结清。
15. 2017年08月03日日照银行股份有限公司发放的430,000元（人民币）个人住房商业贷款，2020年06月已结清。
16. 2017年11月01日中国工商银行股份有限公司临沂分行发放的645,000元（人民币）个人住房商业贷款，2023年02月已结清。
17. 2017年12月14日中国建设银行股份有限公司山东省分行发放的2,790,000元（人民币）个人住房商业贷款，2021年04月已结清。
18. 2018年10月25日四川新网银行股份有限公司发放的36,300元（人民币）其他个人消费贷款，2019年01月已结清。
19. 2019年02月02日浙江网商银行股份有限公司发放的2,000元（人民币）个人经营性贷款，2019年05月已结清。
20. 2019年06月05日浙江网商银行股份有限公司发放的321,000元（人民币）个人经营性贷款，2019年09月已结清。
21. 2019年07月09日中国民生银行股份有限公司临沂兰陵支行发放的3,500,000元（人民币）个人经营性贷款，2020年07月已结清。
22. 2019年08月11日重庆美团三快小额贷款有限公司发放的12,000元（人民币）其他个人消费贷款，2019年08月已结清。
23. 2019年09月22日重庆美团三快小额贷款有限公司发放的12,000元（人民币）其他个人消费贷款，2019年09月已结清。
24. 2019年10月08日浙江网商银行股份有限公司发放的224,000元（人民币）个人经营性贷款，2019年12月已结清。
25. 2019年12月17日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2020年12月已结清。
26. 2019年12月17日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2020年12月已结清。
27. 2019年12月25日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2020年12月已结清。
28. 2019年12月26日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2020年12月已结清。
29. 2020年01月01日中国邮政储蓄银行股份有限公司临沂市分行发放的400,000元（人民币）个人经营性贷款，2020年12月已结清。
30. 2020年02月17日中国建设银行股份有限公司山东省分行发放的150,000元（人民币）其他贷款，2020年12月已结清。
31. 2020年04月12日浙江网商银行股份有限公司发放的56,000元（人民币）个人经营性贷款，2020年04月已结清。
32. 2020年05月10日浙江网商银行股份有限公司发放的150,000元（人民币）个人经营性贷款，2020年05月已结清。
33. 2020年07月31日浙江网商银行股份有限公司发放的170,000元（人民币）个人经营性贷款，2020年11月已结清。
34. 2021年04月01日浙江网商银行股份有限公司发放的172,460元（人民币）个人经营性贷款，2021年04月已结清。
35. 2022年10月01日中国对外经济贸易信托有限公司发放的1,000,000元（人民币）个人经营性贷款，2024年07月已结清。
36. 2022年10月07日平安银行股份有限公司临沂分行发放的486,000元（人民币）个人经营性贷款，2023年05月已结清。
37. 2023年03月10日山东临沂兰山农村商业银行股份有限公司发放的2,800,000元（人民币）个人经营性贷款，2024年03月已结清。
38. 2024年03月07日山东临沂兰山农村商业银行股份有限公司发放的2,800,000元（人民币）个人经营性贷款，2025年03月已结清。
39. 2020年12月03日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2021年11月已结清。
40. 2020年12月04日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2021年11月已结清。
41. 2020年12月06日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2021年12月已结清。
42. 2020年12月07日中国邮政储蓄银行股份有限公司临沂市分行发放的400,000元（人民币）个人经营性贷款，2021年11月已结清。
43. 2020年12月08日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2021年12月已结清。
44. 2021年07月07日中国民生银行股份有限公司临沂兰陵支行发放的1,800,000元（人民币）个人经营性贷款，2022年04月已结清。
45. 2021年08月12日浙江网商银行股份有限公司发放的402,298元（人民币）个人经营性贷款，2022年02月已结清。
46. 2021年11月26日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2022年11月已结清。
47. 2021年11月29日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2022年11月已结清。
48. 2021年12月01日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2022年12月已结清。
49. 2021年12月02日中国邮政储蓄银行股份有限公司临沂市分行发放的400,000元（人民币）个人经营性贷款，2022年12月已结清。
50. 2021年12月03日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2022年12月已结清。
51. 2022年01月12日中国工商银行股份有限公司临沂分行发放的4,500,000元（人民币）其他贷款，2022年12月已结清。
52. 2022年11月27日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2023年11月已结清。
53. 2022年11月29日中国邮政储蓄银行股份有限公司临沂市分行发放的300,000元（人民币）个人经营性贷款，2023年11月已结清。
54. 2022年12月01日中国邮政储蓄银行股份有限公司临沂市分行发放的200,000元（人民币）个人经营性贷款，2023年11月已结清。
55. 2022年12月02日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2023年11月已结清。
56. 2022年12月03日中国邮政储蓄银行股份有限公司临沂市分行发放的400,000元（人民币）个人经营性贷款，2023年11月已结清。
57. 2022年12月04日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2023年11月已结清。
58. 2023年03月10日山东临沂兰山农村商业银行股份有限公司发放的5,200,000元（人民币）个人经营性贷款，2024年03月已结清。
59. 2023年11月30日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2024年11月已结清。
第 3 页，共 7 页
60. 2023年12月12日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2024年12月已结清。
61. 2023年12月13日中国邮政储蓄银行股份有限公司临沂市分行发放的500,000元（人民币）个人经营性贷款，2024年12月已结清。
62. 2024年03月07日山东临沂兰山农村商业银行股份有限公司发放的5,200,000元（人民币）个人经营性贷款，2025年03月已结清。
63. 2021年11月11日浙江网商银行股份有限公司为个人经营性贷款授信，可循环使用。2022年02月已结清。
64. 2022年04月22日中国民生银行股份有限公司临沂兰陵支行为个人经营性贷款授信，可循环使用。2023年03月已结清。
65. 2022年10月28日中国民生银行股份有限公司临沂兰陵支行为个人经营性贷款授信，可循环使用。2023年03月已结清。
 
相关还款责任信息 
1. 2023年04月27日，为马景涛（证件类型：身份证，证件号码：371322198912203851）在临商银行股份有限公司成都路支行办理
的个人经营性贷款承担相关还款责任，责任人类型为保证人，相关还款责任金额2,000,000（保证合同编号：
D10014730H00072023042700000131）。截至2025年05月31日，个人经营性贷款余额0。
2. 2023年11月29日，为临沂竣豪电子商务有限责任公司（证件类型：中征码，证件号码：3713020005368380）在临沂市商业银行商
城支行办理的贷款承担相关还款责任，责任人类型为保证人，相关还款责任金额10,000,000（保证合同编号：
D10014730H00072023112900000132）。截至2024年11月30日，贷款余额0（人民币元）。
3. 2025年01月15日，为山东高新供应链管理服务有限公司（证件类型：中征码，证件号码：371300U6NRR64W37）在临商银行股
份有限公司高新区支行办理的贷款承担相关还款责任，责任人类型为保证人，相关还款责任金额300,000,000（保证合同编号：
D10014730H00072025011500000117）。截至2025年05月31日，贷款余额138,077,951（人民币元）。
4. 2025年03月25日，为山东高新供应链管理服务有限公司（证件类型：中征码，证件号码：371300U6NRR64W37）在临商银行股
份有限公司高新区支行办理的贷款承担相关还款责任，责任人类型为保证人，相关还款责任金额300,000,000（保证合同编号：
D10014730H00072025011500000117）。截至2025年05月31日，贷款余额48,549,571（人民币元）。
 
非信贷交易记录
系统中没有您最近5年内的非信贷交易记录。
公共记录
系统中没有您最近5年内的公共信息记录。
查询记录
这部分包含您的信用报告最近2年内被查询的记录。
机构查询记录明细
编号 查询日期 查询机构 查询原因
1 2025年06月04日 临商银行股份有限公司高新区支行 贷后管理
2 2025年05月23日 临商银行股份有限公司高新区支行 贷后管理
3 2025年05月07日 临商银行股份有限公司高新区支行 贷后管理
4 2025年04月25日 临商银行股份有限公司高新区支行 贷后管理
5 2025年04月19日 浙江网商银行股份有限公司 贷后管理
6 2025年04月14日 临商银行股份有限公司高新区支行 贷后管理
7 2025年04月03日 临商银行股份有限公司高新区支行 贷后管理
8 2025年03月24日 临商银行股份有限公司高新区支行 贷后管理
9 2025年03月19日 山东临沂兰山农村商业银行股份有 贷款审批
限公司
10 2025年03月17日 中国邮政储蓄银行股份有限公司临 贷后管理
沂市分行
第 4 页，共 7 页
11 2025年03月11日 临商银行股份有限公司高新区支行 贷后管理
12 2025年03月05日 交通银行股份有限公司太平洋信用 信用卡审批
卡中心
13 2025年02月28日 临商银行股份有限公司高新区支行 贷后管理
14 2025年02月07日 临商银行股份有限公司高新区支行 贷后管理
15 2025年01月24日 临商银行股份有限公司成都路支行 贷后管理
16 2025年01月17日 中国邮政储蓄银行股份有限公司山 贷款审批
东省分行
17 2025年01月14日 临商银行股份有限公司高新区支行 担保资格审查
18 2024年11月29日 狮桥融资租赁（中国）有限公司 资信审查
19 2024年11月29日 武汉众邦银行股份有限公司 法人代表、负责人、高管等资信审
查
20 2024年11月29日 深圳市大数融资担保有限公司 担保资格审查
21 2024年11月25日 临商银行股份有限公司成都路支行 贷后管理
22 2024年11月21日 浙江网商银行股份有限公司 贷款审批
23 2024年11月20日 中国邮政储蓄银行股份有限公司 贷后管理
24 2024年10月31日 临商银行股份有限公司成都路支行 贷后管理
25 2024年10月06日 中国邮政储蓄银行股份有限公司 贷后管理
26 2024年09月30日 平安银行股份有限公司 贷款审批
27 2024年09月03日 中国邮政储蓄银行股份有限公司 贷后管理
28 2024年08月03日 中国邮政储蓄银行股份有限公司 贷后管理
29 2024年07月31日 临商银行股份有限公司成都路支行 贷后管理
30 2024年07月05日 平安融易（江苏）融资担保有限公 担保资格审查
司
31 2024年07月01日 中国邮政储蓄银行股份有限公司 贷后管理
32 2024年05月17日 中国邮政储蓄银行股份有限公司 贷后管理
33 2024年04月16日 临商银行股份有限公司成都路支行 贷后管理
34 2024年04月12日 中国邮政储蓄银行股份有限公司 贷后管理
35 2024年04月02日 平安融易（江苏）融资担保有限公 担保资格审查
司
36 2024年03月13日 中国建设银行股份有限公司深圳华 信用卡审批
侨城支行
37 2024年03月07日 中国邮政储蓄银行股份有限公司山 贷款审批
东省分行
38 2024年03月07日 山东临沂兰山农村商业银行股份有 贷款审批
限公司
39 2024年03月06日 山东临沂兰山农村商业银行股份有 贷款审批
限公司
40 2024年03月02日 中国邮政储蓄银行股份有限公司 贷后管理
41 2024年02月19日 临商银行股份有限公司成都路支行 贷后管理
42 2024年01月29日 中国邮政储蓄银行股份有限公司 贷后管理
43 2024年01月02日 平安融易（江苏）融资担保有限公 担保资格审查
司
44 2023年11月28日 中国工商银行股份有限公司银行卡 贷后管理
业务部(牡丹卡中心)
第 5 页，共 7 页
45 2023年11月28日 临商银行股份有限公司商城支行营 担保资格审查
业部
46 2023年11月27日 中国邮政储蓄银行股份有限公司 贷款审批
47 2023年11月17日 中国邮政储蓄银行股份有限公司 贷后管理
48 2023年11月15日 临商银行股份有限公司成都路支行 贷后管理
49 2023年11月01日 临商银行股份有限公司商城支行营 担保资格审查
业部
50 2023年10月19日 临商银行股份有限公司高新区支行 担保资格审查
51 2023年10月02日 平安融易（江苏）融资担保有限公 担保资格审查
司
52 2023年09月27日 交通银行股份有限公司太平洋信用 信用卡审批
卡中心
53 2023年09月15日 中国邮政储蓄银行股份有限公司 贷后管理
54 2023年08月29日 中国工商银行股份有限公司银行卡 信用卡审批
业务部(牡丹卡中心)
55 2023年08月12日 临商银行股份有限公司成都路支行 贷后管理
56 2023年07月14日 中国邮政储蓄银行股份有限公司 贷后管理
57 2023年07月03日 平安融易（江苏）融资担保有限公 担保资格审查
司
个人查询记录明细
编号 查询日期 查询机构 查询原因
1 2025年03月19日 本人 本人查询（自助查询机）
2 2024年04月07日 本人 本人查询（自助查询机）
3 2024年04月01日 本人 本人查询（互联网个人信用信息服
务平台）
4 2023年09月21日 本人 本人查询（商业银行网上银行）
第 6 页，共 7 页
说  明
 
1.除查询记录外，本报告中的信息是依据截至报告时间个人征信系统记录的信息生成，征信中心不确保其真实性和准确性，但承
诺在信息汇总、加工、整合的全过程中保持客观、中立的地位。
2.本报告仅包含可能影响您信用评价的主要信息，如需获取您在个人征信系统中更详细的记录，请到当地信用报告查询网点查
询。信用报告查询网点的具体地址及联系方式可访问征信中心门户网站（www.pbccrc.org.cn）查询。
3.您有权对本报告中的内容提出异议。如有异议，可联系数据提供单位，也可到当地信用报告查询网点提出异议申请。
4.本报告仅供您了解自己的信用状况，请妥善保管。因保管不当造成个人隐私泄露的，征信中心将不承担相关责任。
5.更多咨询，请致电全国客户服务热线400-810-8866。
第 7 页，共 7 页

2025-09-03 11:21:09,390 [http-nio-8100-exec-10] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本：《采购合同书》
                                                                                                     
 
合同编号： J1960880963642748930 下单日期：2025-08-28
需方：广东美特机械有限公司 供方：江苏日成橡胶有限公司
《中华人民共和国民法典》有关条款规定，经双方共同协商，达成以下条款，以资共同遵守。
一、采购信息：
序号 商品名称 数量 单位 单价 金额（元）
1 白砂糖 粗砂糖[ 40 吨 ￥1,000.00 ￥40,000.00
运费：￥160,000.00
总额：￥200,000.00
合计人民币（大写）：贰拾万元整
 
二、质量技术标准
质量标准：小麦(不含非瘟病毒)：出芽率不做强制要求，不纳入不完善指标籽粒整齐均匀、饱满，黄褐色或白色，无发酵、霉
变、虫蛙及异味、异臭、异物。水分≤13.5%；容重≥730G/L；杂质<1%；虫蛀<5%；不完善（包括病斑粒）s8%；黄曲霉毒素
<10ppb、赤霉烯酮≤150ppb、呕吐毒素≤1000ppb。
三、检验方式
以需方工厂检验为准。若供方对检验结果产生异议，可委托国家认可的检测部门检测。如检测结果达不到合同约定，检测费用
由供方承担，达到合同约定，检测费用则由需方承担。
四、交货地点
需方指定地点，运输费用由需方承担。
五、交货期限
在前交货，如供方不能按合同约定期限交货，需方有权单方变更或终止合同，并视为供方违约。
六、计量
以需方电子磅数量为准.
七、包装标准
散粮/包粮：编织袋包装，包装物由供方提供不计价，不返回，每条编织袋扣重 150克。
八、付款方式及期限
1、到检验合格，7天内凭发票以银行电汇付清货款。
2、供方必须提供增值税发票，如供方提供违规、虚假发票，需方有权对供方处于 20%-100%的货款处罚，并承担由此给需方造成
的一切经济损失和税务风险。（必须在发票备注栏注明纸制合同号）
九、违约责任
按《中华人民共和国民法典》有关规定执行；若发生违约纠纷，违约方按本合同金额 20%支付给守约方，由合同签订所在地人民
法院管辖。
十、其他条款
1、若货物质量达不到需方要求，供方自行处理，所发生的一切费用由供方承担；如果经过国家检验机构确定供方有掺假行为，
供方同意按不低于合同总价的 20%金额赔偿给需方。
2、物发出后 1个工作日内告之需方车号、数量等相关事宜；若火车运输需传真大票并寄小票到需方。
本合同一式两份(供需双方各执一份），须经双方代表签字盖章后有效，涂改无效，合同正本与传真件具有同等法律效力，未尽
事项双方协商解决。
需方（盖章）：广东美特机械有限公司 供方（盖章）：江苏日成橡胶有限公司
需方签名：          供方签名：          
签署日期： 签署日期：     

2025-09-03 11:24:41,465 [http-nio-8100-exec-4] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本：《采购合同书》
                                                                                                     
 
合同编号： J1960880963642748930 下单日期：2025-08-28
需方：广东美特机械有限公司 供方：江苏日成橡胶有限公司
《中华人民共和国民法典》有关条款规定，经双方共同协商，达成以下条款，以资共同遵守。
一、采购信息：
序号 商品名称 数量 单位 单价 金额（元）
1 白砂糖 粗砂糖[ 40 吨 ￥1,000.00 ￥40,000.00
运费：￥160,000.00
总额：￥200,000.00
合计人民币（大写）：贰拾万元整
 
二、质量技术标准
质量标准：小麦(不含非瘟病毒)：出芽率不做强制要求，不纳入不完善指标籽粒整齐均匀、饱满，黄褐色或白色，无发酵、霉
变、虫蛙及异味、异臭、异物。水分≤13.5%；容重≥730G/L；杂质<1%；虫蛀<5%；不完善（包括病斑粒）s8%；黄曲霉毒素
<10ppb、赤霉烯酮≤150ppb、呕吐毒素≤1000ppb。
三、检验方式
以需方工厂检验为准。若供方对检验结果产生异议，可委托国家认可的检测部门检测。如检测结果达不到合同约定，检测费用
由供方承担，达到合同约定，检测费用则由需方承担。
四、交货地点
需方指定地点，运输费用由需方承担。
五、交货期限
在前交货，如供方不能按合同约定期限交货，需方有权单方变更或终止合同，并视为供方违约。
六、计量
以需方电子磅数量为准.
七、包装标准
散粮/包粮：编织袋包装，包装物由供方提供不计价，不返回，每条编织袋扣重 150克。
八、付款方式及期限
1、到检验合格，7天内凭发票以银行电汇付清货款。
2、供方必须提供增值税发票，如供方提供违规、虚假发票，需方有权对供方处于 20%-100%的货款处罚，并承担由此给需方造成
的一切经济损失和税务风险。（必须在发票备注栏注明纸制合同号）
九、违约责任
按《中华人民共和国民法典》有关规定执行；若发生违约纠纷，违约方按本合同金额 20%支付给守约方，由合同签订所在地人民
法院管辖。
十、其他条款
1、若货物质量达不到需方要求，供方自行处理，所发生的一切费用由供方承担；如果经过国家检验机构确定供方有掺假行为，
供方同意按不低于合同总价的 20%金额赔偿给需方。
2、物发出后 1个工作日内告之需方车号、数量等相关事宜；若火车运输需传真大票并寄小票到需方。
本合同一式两份(供需双方各执一份），须经双方代表签字盖章后有效，涂改无效，合同正本与传真件具有同等法律效力，未尽
事项双方协商解决。
需方（盖章）：广东美特机械有限公司 供方（盖章）：江苏日成橡胶有限公司
需方签名：          供方签名：          
签署日期： 签署日期：     

2025-09-03 11:45:03,258 [http-nio-8100-exec-7] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本：《采购合同书》
                                                                                                     
 
合同编号： J1960880963642748930 下单日期：2025-08-28
需方：广东美特机械有限公司 供方：江苏日成橡胶有限公司
《中华人民共和国民法典》有关条款规定，经双方共同协商，达成以下条款，以资共同遵守。
一、采购信息：
序号 商品名称 数量 单位 单价 金额（元）
1 白砂糖 粗砂糖[ 40 吨 ￥1,000.00 ￥40,000.00
运费：￥160,000.00
总额：￥200,000.00
合计人民币（大写）：贰拾万元整
 
二、质量技术标准
质量标准：小麦(不含非瘟病毒)：出芽率不做强制要求，不纳入不完善指标籽粒整齐均匀、饱满，黄褐色或白色，无发酵、霉
变、虫蛙及异味、异臭、异物。水分≤13.5%；容重≥730G/L；杂质<1%；虫蛀<5%；不完善（包括病斑粒）s8%；黄曲霉毒素
<10ppb、赤霉烯酮≤150ppb、呕吐毒素≤1000ppb。
三、检验方式
以需方工厂检验为准。若供方对检验结果产生异议，可委托国家认可的检测部门检测。如检测结果达不到合同约定，检测费用
由供方承担，达到合同约定，检测费用则由需方承担。
四、交货地点
需方指定地点，运输费用由需方承担。
五、交货期限
在前交货，如供方不能按合同约定期限交货，需方有权单方变更或终止合同，并视为供方违约。
六、计量
以需方电子磅数量为准.
七、包装标准
散粮/包粮：编织袋包装，包装物由供方提供不计价，不返回，每条编织袋扣重 150克。
八、付款方式及期限
1、到检验合格，7天内凭发票以银行电汇付清货款。
2、供方必须提供增值税发票，如供方提供违规、虚假发票，需方有权对供方处于 20%-100%的货款处罚，并承担由此给需方造成
的一切经济损失和税务风险。（必须在发票备注栏注明纸制合同号）
九、违约责任
按《中华人民共和国民法典》有关规定执行；若发生违约纠纷，违约方按本合同金额 20%支付给守约方，由合同签订所在地人民
法院管辖。
十、其他条款
1、若货物质量达不到需方要求，供方自行处理，所发生的一切费用由供方承担；如果经过国家检验机构确定供方有掺假行为，
供方同意按不低于合同总价的 20%金额赔偿给需方。
2、物发出后 1个工作日内告之需方车号、数量等相关事宜；若火车运输需传真大票并寄小票到需方。
本合同一式两份(供需双方各执一份），须经双方代表签字盖章后有效，涂改无效，合同正本与传真件具有同等法律效力，未尽
事项双方协商解决。
需方（盖章）：广东美特机械有限公司 供方（盖章）：江苏日成橡胶有限公司
需方签名：          供方签名：          
签署日期： 签署日期：     

2025-09-03 11:46:58,911 [http-nio-8100-exec-8] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本：《采购合同书》
                                                                                                     
 
合同编号： J1960880963642748930 下单日期：2025-08-28
需方：广东美特机械有限公司 供方：江苏日成橡胶有限公司
《中华人民共和国民法典》有关条款规定，经双方共同协商，达成以下条款，以资共同遵守。
一、采购信息：
序号 商品名称 数量 单位 单价 金额（元）
1 白砂糖 粗砂糖[ 40 吨 ￥1,000.00 ￥40,000.00
运费：￥160,000.00
总额：￥200,000.00
合计人民币（大写）：贰拾万元整
 
二、质量技术标准
质量标准：小麦(不含非瘟病毒)：出芽率不做强制要求，不纳入不完善指标籽粒整齐均匀、饱满，黄褐色或白色，无发酵、霉
变、虫蛙及异味、异臭、异物。水分≤13.5%；容重≥730G/L；杂质<1%；虫蛀<5%；不完善（包括病斑粒）s8%；黄曲霉毒素
<10ppb、赤霉烯酮≤150ppb、呕吐毒素≤1000ppb。
三、检验方式
以需方工厂检验为准。若供方对检验结果产生异议，可委托国家认可的检测部门检测。如检测结果达不到合同约定，检测费用
由供方承担，达到合同约定，检测费用则由需方承担。
四、交货地点
需方指定地点，运输费用由需方承担。
五、交货期限
在前交货，如供方不能按合同约定期限交货，需方有权单方变更或终止合同，并视为供方违约。
六、计量
以需方电子磅数量为准.
七、包装标准
散粮/包粮：编织袋包装，包装物由供方提供不计价，不返回，每条编织袋扣重 150克。
八、付款方式及期限
1、到检验合格，7天内凭发票以银行电汇付清货款。
2、供方必须提供增值税发票，如供方提供违规、虚假发票，需方有权对供方处于 20%-100%的货款处罚，并承担由此给需方造成
的一切经济损失和税务风险。（必须在发票备注栏注明纸制合同号）
九、违约责任
按《中华人民共和国民法典》有关规定执行；若发生违约纠纷，违约方按本合同金额 20%支付给守约方，由合同签订所在地人民
法院管辖。
十、其他条款
1、若货物质量达不到需方要求，供方自行处理，所发生的一切费用由供方承担；如果经过国家检验机构确定供方有掺假行为，
供方同意按不低于合同总价的 20%金额赔偿给需方。
2、物发出后 1个工作日内告之需方车号、数量等相关事宜；若火车运输需传真大票并寄小票到需方。
本合同一式两份(供需双方各执一份），须经双方代表签字盖章后有效，涂改无效，合同正本与传真件具有同等法律效力，未尽
事项双方协商解决。
需方（盖章）：广东美特机械有限公司 供方（盖章）：江苏日成橡胶有限公司
需方签名：          供方签名：          
签署日期： 签署日期：     

2025-09-03 11:47:22,096 [http-nio-8100-exec-1] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本：《采购合同书》
                                                                                                     
 
合同编号： J1960880963642748930 下单日期：2025-08-28
需方：广东美特机械有限公司 供方：江苏日成橡胶有限公司
《中华人民共和国民法典》有关条款规定，经双方共同协商，达成以下条款，以资共同遵守。
一、采购信息：
序号 商品名称 数量 单位 单价 金额（元）
1 白砂糖 粗砂糖[ 40 吨 ￥1,000.00 ￥40,000.00
运费：￥160,000.00
总额：￥200,000.00
合计人民币（大写）：贰拾万元整
 
二、质量技术标准
质量标准：小麦(不含非瘟病毒)：出芽率不做强制要求，不纳入不完善指标籽粒整齐均匀、饱满，黄褐色或白色，无发酵、霉
变、虫蛙及异味、异臭、异物。水分≤13.5%；容重≥730G/L；杂质<1%；虫蛀<5%；不完善（包括病斑粒）s8%；黄曲霉毒素
<10ppb、赤霉烯酮≤150ppb、呕吐毒素≤1000ppb。
三、检验方式
以需方工厂检验为准。若供方对检验结果产生异议，可委托国家认可的检测部门检测。如检测结果达不到合同约定，检测费用
由供方承担，达到合同约定，检测费用则由需方承担。
四、交货地点
需方指定地点，运输费用由需方承担。
五、交货期限
在前交货，如供方不能按合同约定期限交货，需方有权单方变更或终止合同，并视为供方违约。
六、计量
以需方电子磅数量为准.
七、包装标准
散粮/包粮：编织袋包装，包装物由供方提供不计价，不返回，每条编织袋扣重 150克。
八、付款方式及期限
1、到检验合格，7天内凭发票以银行电汇付清货款。
2、供方必须提供增值税发票，如供方提供违规、虚假发票，需方有权对供方处于 20%-100%的货款处罚，并承担由此给需方造成
的一切经济损失和税务风险。（必须在发票备注栏注明纸制合同号）
九、违约责任
按《中华人民共和国民法典》有关规定执行；若发生违约纠纷，违约方按本合同金额 20%支付给守约方，由合同签订所在地人民
法院管辖。
十、其他条款
1、若货物质量达不到需方要求，供方自行处理，所发生的一切费用由供方承担；如果经过国家检验机构确定供方有掺假行为，
供方同意按不低于合同总价的 20%金额赔偿给需方。
2、物发出后 1个工作日内告之需方车号、数量等相关事宜；若火车运输需传真大票并寄小票到需方。
本合同一式两份(供需双方各执一份），须经双方代表签字盖章后有效，涂改无效，合同正本与传真件具有同等法律效力，未尽
事项双方协商解决。
需方（盖章）：广东美特机械有限公司 供方（盖章）：江苏日成橡胶有限公司
需方签名：          供方签名：          
签署日期： 签署日期：     

2025-09-03 13:52:43,190 [http-nio-8100-exec-7] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本：《采购合同书》
                                                                                                     
 
合同编号： J1960880963642748930 下单日期：2025-08-28
需方：广东美特机械有限公司 供方：江苏日成橡胶有限公司
《中华人民共和国民法典》有关条款规定，经双方共同协商，达成以下条款，以资共同遵守。
一、采购信息：
序号 商品名称 数量 单位 单价 金额（元）
1 白砂糖 粗砂糖[ 40 吨 ￥1,000.00 ￥40,000.00
运费：￥160,000.00
总额：￥200,000.00
合计人民币（大写）：贰拾万元整
 
二、质量技术标准
质量标准：小麦(不含非瘟病毒)：出芽率不做强制要求，不纳入不完善指标籽粒整齐均匀、饱满，黄褐色或白色，无发酵、霉
变、虫蛙及异味、异臭、异物。水分≤13.5%；容重≥730G/L；杂质<1%；虫蛀<5%；不完善（包括病斑粒）s8%；黄曲霉毒素
<10ppb、赤霉烯酮≤150ppb、呕吐毒素≤1000ppb。
三、检验方式
以需方工厂检验为准。若供方对检验结果产生异议，可委托国家认可的检测部门检测。如检测结果达不到合同约定，检测费用
由供方承担，达到合同约定，检测费用则由需方承担。
四、交货地点
需方指定地点，运输费用由需方承担。
五、交货期限
在前交货，如供方不能按合同约定期限交货，需方有权单方变更或终止合同，并视为供方违约。
六、计量
以需方电子磅数量为准.
七、包装标准
散粮/包粮：编织袋包装，包装物由供方提供不计价，不返回，每条编织袋扣重 150克。
八、付款方式及期限
1、到检验合格，7天内凭发票以银行电汇付清货款。
2、供方必须提供增值税发票，如供方提供违规、虚假发票，需方有权对供方处于 20%-100%的货款处罚，并承担由此给需方造成
的一切经济损失和税务风险。（必须在发票备注栏注明纸制合同号）
九、违约责任
按《中华人民共和国民法典》有关规定执行；若发生违约纠纷，违约方按本合同金额 20%支付给守约方，由合同签订所在地人民
法院管辖。
十、其他条款
1、若货物质量达不到需方要求，供方自行处理，所发生的一切费用由供方承担；如果经过国家检验机构确定供方有掺假行为，
供方同意按不低于合同总价的 20%金额赔偿给需方。
2、物发出后 1个工作日内告之需方车号、数量等相关事宜；若火车运输需传真大票并寄小票到需方。
本合同一式两份(供需双方各执一份），须经双方代表签字盖章后有效，涂改无效，合同正本与传真件具有同等法律效力，未尽
事项双方协商解决。
需方（盖章）：广东美特机械有限公司 供方（盖章）：江苏日成橡胶有限公司
需方签名：          供方签名：          
签署日期： 签署日期：     

2025-09-03 14:42:16,540 [http-nio-8100-exec-6] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本：

2025-09-03 15:10:52,260 [http-nio-8100-exec-6] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本：《采购合同书》
                                                                                                     
 
合同编号： J1960880963642748930 下单日期：2025-08-28
需方：广东美特机械有限公司 供方：江苏日成橡胶有限公司
《中华人民共和国民法典》有关条款规定，经双方共同协商，达成以下条款，以资共同遵守。
一、采购信息：
序号 商品名称 数量 单位 单价 金额（元）
1 白砂糖 粗砂糖[ 40 吨 ￥1,000.00 ￥40,000.00
运费：￥160,000.00
总额：￥200,000.00
合计人民币（大写）：贰拾万元整
 
二、质量技术标准
质量标准：小麦(不含非瘟病毒)：出芽率不做强制要求，不纳入不完善指标籽粒整齐均匀、饱满，黄褐色或白色，无发酵、霉
变、虫蛙及异味、异臭、异物。水分≤13.5%；容重≥730G/L；杂质<1%；虫蛀<5%；不完善（包括病斑粒）s8%；黄曲霉毒素
<10ppb、赤霉烯酮≤150ppb、呕吐毒素≤1000ppb。
三、检验方式
以需方工厂检验为准。若供方对检验结果产生异议，可委托国家认可的检测部门检测。如检测结果达不到合同约定，检测费用
由供方承担，达到合同约定，检测费用则由需方承担。
四、交货地点
需方指定地点，运输费用由需方承担。
五、交货期限
在前交货，如供方不能按合同约定期限交货，需方有权单方变更或终止合同，并视为供方违约。
六、计量
以需方电子磅数量为准.
七、包装标准
散粮/包粮：编织袋包装，包装物由供方提供不计价，不返回，每条编织袋扣重 150克。
八、付款方式及期限
1、到检验合格，7天内凭发票以银行电汇付清货款。
2、供方必须提供增值税发票，如供方提供违规、虚假发票，需方有权对供方处于 20%-100%的货款处罚，并承担由此给需方造成
的一切经济损失和税务风险。（必须在发票备注栏注明纸制合同号）
九、违约责任
按《中华人民共和国民法典》有关规定执行；若发生违约纠纷，违约方按本合同金额 20%支付给守约方，由合同签订所在地人民
法院管辖。
十、其他条款
1、若货物质量达不到需方要求，供方自行处理，所发生的一切费用由供方承担；如果经过国家检验机构确定供方有掺假行为，
供方同意按不低于合同总价的 20%金额赔偿给需方。
2、物发出后 1个工作日内告之需方车号、数量等相关事宜；若火车运输需传真大票并寄小票到需方。
本合同一式两份(供需双方各执一份），须经双方代表签字盖章后有效，涂改无效，合同正本与传真件具有同等法律效力，未尽
事项双方协商解决。
需方（盖章）：广东美特机械有限公司 供方（盖章）：江苏日成橡胶有限公司
需方签名：          供方签名：          
签署日期： 签署日期：     

2025-09-03 15:15:35,941 [http-nio-8100-exec-1] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本：《采购合同书》
                                                                                                     
 
合同编号： J1960880963642748930 下单日期：2025-08-28
需方：广东美特机械有限公司 供方：江苏日成橡胶有限公司
《中华人民共和国民法典》有关条款规定，经双方共同协商，达成以下条款，以资共同遵守。
一、采购信息：
序号 商品名称 数量 单位 单价 金额（元）
1 白砂糖 粗砂糖[ 40 吨 ￥1,000.00 ￥40,000.00
运费：￥160,000.00
总额：￥200,000.00
合计人民币（大写）：贰拾万元整
 
二、质量技术标准
质量标准：小麦(不含非瘟病毒)：出芽率不做强制要求，不纳入不完善指标籽粒整齐均匀、饱满，黄褐色或白色，无发酵、霉
变、虫蛙及异味、异臭、异物。水分≤13.5%；容重≥730G/L；杂质<1%；虫蛀<5%；不完善（包括病斑粒）s8%；黄曲霉毒素
<10ppb、赤霉烯酮≤150ppb、呕吐毒素≤1000ppb。
三、检验方式
以需方工厂检验为准。若供方对检验结果产生异议，可委托国家认可的检测部门检测。如检测结果达不到合同约定，检测费用
由供方承担，达到合同约定，检测费用则由需方承担。
四、交货地点
需方指定地点，运输费用由需方承担。
五、交货期限
在前交货，如供方不能按合同约定期限交货，需方有权单方变更或终止合同，并视为供方违约。
六、计量
以需方电子磅数量为准.
七、包装标准
散粮/包粮：编织袋包装，包装物由供方提供不计价，不返回，每条编织袋扣重 150克。
八、付款方式及期限
1、到检验合格，7天内凭发票以银行电汇付清货款。
2、供方必须提供增值税发票，如供方提供违规、虚假发票，需方有权对供方处于 20%-100%的货款处罚，并承担由此给需方造成
的一切经济损失和税务风险。（必须在发票备注栏注明纸制合同号）
九、违约责任
按《中华人民共和国民法典》有关规定执行；若发生违约纠纷，违约方按本合同金额 20%支付给守约方，由合同签订所在地人民
法院管辖。
十、其他条款
1、若货物质量达不到需方要求，供方自行处理，所发生的一切费用由供方承担；如果经过国家检验机构确定供方有掺假行为，
供方同意按不低于合同总价的 20%金额赔偿给需方。
2、物发出后 1个工作日内告之需方车号、数量等相关事宜；若火车运输需传真大票并寄小票到需方。
本合同一式两份(供需双方各执一份），须经双方代表签字盖章后有效，涂改无效，合同正本与传真件具有同等法律效力，未尽
事项双方协商解决。
需方（盖章）：广东美特机械有限公司 供方（盖章）：江苏日成橡胶有限公司
需方签名：          供方签名：          
签署日期： 签署日期：     

2025-09-03 15:19:34,168 [http-nio-8100-exec-7] ERROR [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:147 - OCR识别失败
java.lang.IllegalArgumentException: 无法读取图片数据
	at cn.tycoding.service.impl.DocumentRecognitionServiceImpl.processImage(DocumentRecognitionServiceImpl.java:136)
	at cn.tycoding.service.impl.DocumentRecognitionServiceImpl.processImageAsync(DocumentRecognitionServiceImpl.java:126)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at cn.tycoding.service.impl.DocumentRecognitionServiceImpl.recognizeIdCard(DocumentRecognitionServiceImpl.java:108)
	at cn.tycoding.service.impl.DocumentRecognitionServiceImpl.recognizeDocument(DocumentRecognitionServiceImpl.java:49)
	at cn.tycoding.langchat.server.controller.AiTestController.testImageData(AiTestController.java:47)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:259)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:192)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-03 15:19:34,171 [http-nio-8100-exec-7] ERROR [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:128 - 图片处理失败
java.lang.RuntimeException: OCR识别失败
	at cn.tycoding.service.impl.DocumentRecognitionServiceImpl.processImage(DocumentRecognitionServiceImpl.java:148)
	at cn.tycoding.service.impl.DocumentRecognitionServiceImpl.processImageAsync(DocumentRecognitionServiceImpl.java:126)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at cn.tycoding.service.impl.DocumentRecognitionServiceImpl.recognizeIdCard(DocumentRecognitionServiceImpl.java:108)
	at cn.tycoding.service.impl.DocumentRecognitionServiceImpl.recognizeDocument(DocumentRecognitionServiceImpl.java:49)
	at cn.tycoding.langchat.server.controller.AiTestController.testImageData(AiTestController.java:47)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:259)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:192)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.IllegalArgumentException: 无法读取图片数据
	at cn.tycoding.service.impl.DocumentRecognitionServiceImpl.processImage(DocumentRecognitionServiceImpl.java:136)
	... 67 common frames omitted
2025-09-03 15:19:34,173 [http-nio-8100-exec-7] ERROR [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:118 - 身份证识别失败
java.util.concurrent.CompletionException: java.lang.RuntimeException: OCR识别失败
	at java.base/java.util.concurrent.CompletableFuture.reportJoin(CompletableFuture.java:413)
	at java.base/java.util.concurrent.CompletableFuture.join(CompletableFuture.java:2118)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at cn.tycoding.service.impl.DocumentRecognitionServiceImpl.recognizeIdCard(DocumentRecognitionServiceImpl.java:113)
	at cn.tycoding.service.impl.DocumentRecognitionServiceImpl.recognizeDocument(DocumentRecognitionServiceImpl.java:49)
	at cn.tycoding.langchat.server.controller.AiTestController.testImageData(AiTestController.java:47)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:259)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:192)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.RuntimeException: OCR识别失败
	at cn.tycoding.service.impl.DocumentRecognitionServiceImpl.processImage(DocumentRecognitionServiceImpl.java:148)
	at cn.tycoding.service.impl.DocumentRecognitionServiceImpl.processImageAsync(DocumentRecognitionServiceImpl.java:126)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at cn.tycoding.service.impl.DocumentRecognitionServiceImpl.recognizeIdCard(DocumentRecognitionServiceImpl.java:108)
	... 58 common frames omitted
Caused by: java.lang.IllegalArgumentException: 无法读取图片数据
	at cn.tycoding.service.impl.DocumentRecognitionServiceImpl.processImage(DocumentRecognitionServiceImpl.java:136)
	... 67 common frames omitted
2025-09-03 15:19:54,387 [http-nio-8100-exec-6] ERROR [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:147 - OCR识别失败
java.lang.IllegalArgumentException: 无法读取图片数据
	at cn.tycoding.service.impl.DocumentRecognitionServiceImpl.processImage(DocumentRecognitionServiceImpl.java:136)
	at cn.tycoding.service.impl.DocumentRecognitionServiceImpl.processImageAsync(DocumentRecognitionServiceImpl.java:126)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at cn.tycoding.service.impl.DocumentRecognitionServiceImpl.recognizeIdCard(DocumentRecognitionServiceImpl.java:108)
	at cn.tycoding.service.impl.DocumentRecognitionServiceImpl.recognizeDocument(DocumentRecognitionServiceImpl.java:49)
	at cn.tycoding.langchat.server.controller.AiTestController.testImageData(AiTestController.java:47)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:259)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:192)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-03 15:19:54,388 [http-nio-8100-exec-6] ERROR [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:128 - 图片处理失败
java.lang.RuntimeException: OCR识别失败
	at cn.tycoding.service.impl.DocumentRecognitionServiceImpl.processImage(DocumentRecognitionServiceImpl.java:148)
	at cn.tycoding.service.impl.DocumentRecognitionServiceImpl.processImageAsync(DocumentRecognitionServiceImpl.java:126)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at cn.tycoding.service.impl.DocumentRecognitionServiceImpl.recognizeIdCard(DocumentRecognitionServiceImpl.java:108)
	at cn.tycoding.service.impl.DocumentRecognitionServiceImpl.recognizeDocument(DocumentRecognitionServiceImpl.java:49)
	at cn.tycoding.langchat.server.controller.AiTestController.testImageData(AiTestController.java:47)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:259)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:192)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.IllegalArgumentException: 无法读取图片数据
	at cn.tycoding.service.impl.DocumentRecognitionServiceImpl.processImage(DocumentRecognitionServiceImpl.java:136)
	... 67 common frames omitted
2025-09-03 15:19:54,389 [http-nio-8100-exec-6] ERROR [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:118 - 身份证识别失败
java.util.concurrent.CompletionException: java.lang.RuntimeException: OCR识别失败
	at java.base/java.util.concurrent.CompletableFuture.reportJoin(CompletableFuture.java:413)
	at java.base/java.util.concurrent.CompletableFuture.join(CompletableFuture.java:2118)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at cn.tycoding.service.impl.DocumentRecognitionServiceImpl.recognizeIdCard(DocumentRecognitionServiceImpl.java:113)
	at cn.tycoding.service.impl.DocumentRecognitionServiceImpl.recognizeDocument(DocumentRecognitionServiceImpl.java:49)
	at cn.tycoding.langchat.server.controller.AiTestController.testImageData(AiTestController.java:47)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:259)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:192)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.RuntimeException: OCR识别失败
	at cn.tycoding.service.impl.DocumentRecognitionServiceImpl.processImage(DocumentRecognitionServiceImpl.java:148)
	at cn.tycoding.service.impl.DocumentRecognitionServiceImpl.processImageAsync(DocumentRecognitionServiceImpl.java:126)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at cn.tycoding.service.impl.DocumentRecognitionServiceImpl.recognizeIdCard(DocumentRecognitionServiceImpl.java:108)
	... 58 common frames omitted
Caused by: java.lang.IllegalArgumentException: 无法读取图片数据
	at cn.tycoding.service.impl.DocumentRecognitionServiceImpl.processImage(DocumentRecognitionServiceImpl.java:136)
	... 67 common frames omitted
2025-09-03 15:25:37,696 [http-nio-8100-exec-10] INFO  [cn.tycoding.langchat.auth.endpoint.AuthEndpoint] AuthEndpoint.java:93 - ====> login success，token=47dfec6d-d4ae-4a08-8f6b-132ad2a7a3e6
2025-09-03 15:31:57,974 [http-nio-8100-exec-2] INFO  [c.t.l.a.c.service.impl.PersistentChatMemoryStore] PersistentChatMemoryStore.java:61 - initialize message memory store to: 91b4524a46a949601e7f3b004ed76034
2025-09-03 15:32:06,016 [http-nio-8100-exec-8] INFO  [c.t.l.a.c.service.impl.PersistentChatMemoryStore] PersistentChatMemoryStore.java:61 - initialize message memory store to: app_9be4417e7529ed84a01e9ac574fcdd23
2025-09-03 15:32:20,405 [http-nio-8100-exec-7] INFO  [c.t.l.a.c.service.impl.PersistentChatMemoryStore] PersistentChatMemoryStore.java:61 - initialize message memory store to: app_69bce36e71cad6fb7e697dc4fcf35699
2025-09-03 15:32:33,674 [http-nio-8100-exec-9] INFO  [c.t.l.a.c.service.impl.PersistentChatMemoryStore] PersistentChatMemoryStore.java:61 - initialize message memory store to: app_ef50e8606c64180c8e8ecf3af67363e8
2025-09-03 15:32:39,947 [http-nio-8100-exec-10] INFO  [c.t.l.a.c.service.impl.PersistentChatMemoryStore] PersistentChatMemoryStore.java:61 - initialize message memory store to: app_ef50e8606c64180c8e8ecf3af67363e8
2025-09-03 15:57:38,755 [SpringApplicationShutdownHook] INFO  [o.dromara.x.file.storage.core.FileStorageService] FileStorageService.java:734 - 销毁存储平台 local 成功
2025-09-03 15:57:38,756 [SpringApplicationShutdownHook] INFO  [o.dromara.x.file.storage.core.FileStorageService] FileStorageService.java:734 - 销毁存储平台 aliyun-oss 成功
2025-09-03 15:57:38,762 [SpringApplicationShutdownHook] INFO  [com.alibaba.druid.pool.DruidDataSource] DruidDataSource.java:2204 - {dataSource-1} closing ...
2025-09-03 15:57:38,767 [SpringApplicationShutdownHook] INFO  [com.alibaba.druid.pool.DruidDataSource] DruidDataSource.java:2277 - {dataSource-1} closed
2025-09-03 15:57:44,201 [background-preinit] INFO  [org.hibernate.validator.internal.util.Version] Version.java:21 - HV000001: Hibernate Validator 8.0.1.Final
2025-09-03 15:57:44,390 [main] INFO  [cn.tycoding.langchat.LangChatApp] StartupInfoLogger.java:50 - Starting LangChatApp using Java 17.0.14 with PID 27976 (D:\awork\work-aigc\aigc-manage\langchat-server\target\classes started by Administrator in D:\awork\work-aigc)
2025-09-03 15:57:44,391 [main] INFO  [cn.tycoding.langchat.LangChatApp] SpringApplication.java:660 - The following 1 profile is active: "dev"
2025-09-03 15:57:46,701 [main] INFO  [o.s.d.r.config.RepositoryConfigurationDelegate] RepositoryConfigurationDelegate.java:292 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-03 15:57:46,705 [main] INFO  [o.s.d.r.config.RepositoryConfigurationDelegate] RepositoryConfigurationDelegate.java:139 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-03 15:57:46,805 [main] INFO  [o.s.d.r.config.RepositoryConfigurationDelegate] RepositoryConfigurationDelegate.java:208 - Finished Spring Data repository scanning in 79 ms. Found 0 Redis repository interfaces.
2025-09-03 15:57:48,614 [main] INFO  [o.s.boot.web.embedded.tomcat.TomcatWebServer] TomcatWebServer.java:109 - Tomcat initialized with port 8100 (http)
2025-09-03 15:57:48,633 [main] INFO  [org.apache.coyote.http11.Http11NioProtocol] DirectJDKLog.java:173 - Initializing ProtocolHandler ["http-nio-8100"]
2025-09-03 15:57:48,636 [main] INFO  [org.apache.catalina.core.StandardService] DirectJDKLog.java:173 - Starting service [Tomcat]
2025-09-03 15:57:48,636 [main] INFO  [org.apache.catalina.core.StandardEngine] DirectJDKLog.java:173 - Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-09-03 15:57:48,852 [main] INFO  [o.a.c.core.ContainerBase.[Tomcat].[localhost].[/]] DirectJDKLog.java:173 - Initializing Spring embedded WebApplicationContext
2025-09-03 15:57:48,852 [main] INFO  [o.s.b.w.s.c.ServletWebServerApplicationContext] ServletWebServerApplicationContext.java:296 - Root WebApplicationContext: initialization completed in 3895 ms
2025-09-03 15:57:49,037 [main] INFO  [c.a.d.s.b.a.DruidDataSourceAutoConfigure] DruidDataSourceAutoConfigure.java:68 - Init DruidDataSource
2025-09-03 15:57:49,175 [main] INFO  [com.alibaba.druid.pool.DruidDataSource] DruidDataSource.java:1002 - {dataSource-1} inited
2025-09-03 15:57:49,636 [main] WARN  [c.b.mybatisplus.core.metadata.TableInfoHelper] TableInfoHelper.java:376 - Can not find table primary key in Class: "cn.tycoding.langchat.upms.entity.SysUserRole".
2025-09-03 15:57:49,637 [main] WARN  [c.b.mybatisplus.core.injector.DefaultSqlInjector] DefaultSqlInjector.java:52 - class cn.tycoding.langchat.upms.entity.SysUserRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-09-03 15:57:51,207 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- EMBEDDING， 模型配置：AigcModel(id=23b21ee73bc3dd67ddcc4287aadb1697, type=EMBEDDING, model=text-embedding-v4, provider=Q_WEN, name=text-embedding-v4, responseLimit=null, temperature=0.2, topP=0.0, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=1024, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-03 15:57:51,220 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：ZHIPU -- CHAT， 模型配置：AigcModel(id=30c047625a22dd3cfdc8a21d13bd7d04, type=CHAT, model=glm-4-flash, provider=ZHIPU, name=glm-4-flash-250414, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=a4867e65138e4f259c5c692aef943164.6xPiD4YHC2krGJAV, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-03 15:57:51,221 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- CHAT， 模型配置：AigcModel(id=6b1cdbb07032a117d1b4f00b865e7f23, type=CHAT, model=deepseek-v3, provider=Q_WEN, name=阿里DeepSeek-V3, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=https://dashscope.aliyuncs.com, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-03 15:57:51,221 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- CHAT， 模型配置：AigcModel(id=765a327613f9c29b547ec9dac3d3b2b4, type=CHAT, model=qwen-max-longcontext, provider=Q_WEN, name=千问长token模型, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=https://dashscope.aliyuncs.com, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-03 15:57:51,221 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：ZHIPU -- CHAT， 模型配置：AigcModel(id=91c235219da4e2a92ae166e38d179b89, type=CHAT, model=glm-4, provider=ZHIPU, name=glm-4-plus, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=a4867e65138e4f259c5c692aef943164.6xPiD4YHC2krGJAV, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-03 15:57:51,222 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：SILICON -- CHAT， 模型配置：AigcModel(id=ac05866194643c5341a73d837b2017c8, type=CHAT, model=deepseek-ai/DeepSeek-V2.5, provider=SILICON, name=deepseek, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-univnrseuupvzarrjozodhcmnurfvvyrcujdssfggfckynxd, secretKey=null, baseUrl=https://api.siliconflow.cn/v1, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-03 15:57:51,222 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- EMBEDDING， 模型配置：AigcModel(id=b3ac64325c37094da12d199d1928a37b, type=EMBEDDING, model=text-embedding-v3, provider=Q_WEN, name=通用文本向量-v3, responseLimit=null, temperature=0.2, topP=0.0, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=1024, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-03 15:57:51,223 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：ZHIPU -- TEXT_IMAGE， 模型配置：AigcModel(id=d4f776f9baa847f9c5a975c9f078e049, type=TEXT_IMAGE, model=cogview-3, provider=ZHIPU, name=图片生成, responseLimit=null, temperature=0.2, topP=0.0, apiKey=a4867e65138e4f259c5c692aef943164.6xPiD4YHC2krGJAV, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-03 15:57:51,223 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：OLLAMA -- EMBEDDING， 模型配置：AigcModel(id=d61f28c3ced11a1abac3215d2154c882, type=EMBEDDING, model=bge-m3:latest, provider=OLLAMA, name=ollama, responseLimit=null, temperature=0.2, topP=0.0, apiKey=null, secretKey=null, baseUrl=http://localhost:11434/, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=1024, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-03 15:57:51,223 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- EMBEDDING， 模型配置：AigcModel(id=dc3858965af7b992c806ed38ddd0164b, type=EMBEDDING, model=multimodal-embedding-v1, provider=Q_WEN, name=多模态向量模型, responseLimit=null, temperature=0.2, topP=0.0, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=1024, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-03 15:57:51,224 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- CHAT， 模型配置：AigcModel(id=f664ddbdec3fecd2afc2ed7631c17578, type=CHAT, model=Qwen-Long, provider=Q_WEN, name=Qwen-Long, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-553d1b0396064e16af616b910a526b53, secretKey=null, baseUrl=https://dashscope.aliyuncs.com/compatible-mode/v1, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-03 15:58:01,955 [main] ERROR [io.milvus.client.AbstractMilvusGrpcClient] AbstractMilvusGrpcClient.java:3378 - DEADLINE_EXCEEDED: deadline exceeded after 9.882739000s. Name resolution delay 0.015093700 seconds. [closed=[], open=[[wait_for_ready, buffered_nanos=**********, waiting_for_connection]]]
2025-09-03 15:58:01,958 [main] ERROR [io.milvus.client.AbstractMilvusGrpcClient] AbstractMilvusGrpcClient.java:3378 - Failed to initialize connection. Error: DEADLINE_EXCEEDED: deadline exceeded after 9.882739000s. Name resolution delay 0.015093700 seconds. [closed=[], open=[[wait_for_ready, buffered_nanos=**********, waiting_for_connection]]]
2025-09-03 15:58:01,960 [main] ERROR [c.t.l.ai.core.provider.EmbeddingStoreFactory] EmbeddingStoreFactory.java:103 - 向量数据库初始化失败：[Milvus] --- [MILVUS]，数据库配置信息：[AigcEmbedStore(id=c92cfbaaff366b12bc87c1082149150a, name=Milvus, provider=MILVUS, host=************, port=19530, username=null, password=null, databaseName=default, tableName=ai, dimension=1024)]
2025-09-03 15:58:02,193 [main] INFO  [cn.tycoding.langchat.server.store.AppStore] AppStore.java:44 - initialize app config list...
2025-09-03 15:58:02,477 [main] INFO  [cn.tycoding.langchat.server.store.AppChannelStore] AppChannelStore.java:52 - initialize app channel config list...
2025-09-03 15:58:02,663 [main] WARN  [c.t.l.c.oss.config.FileStorageAutoConfiguration] FileStorageAutoConfiguration.java:65 - 没有找到 FileRecorder 的实现类，文件上传之外的部分功能无法正常使用，必须实现该接口才能使用完整功能！
2025-09-03 15:58:02,698 [main] INFO  [o.d.x.file.storage.core.FileStorageServiceBuilder] FileStorageServiceBuilder.java:291 - 加载本地升级版存储平台：local
2025-09-03 15:58:02,703 [main] INFO  [o.d.x.file.storage.core.FileStorageServiceBuilder] FileStorageServiceBuilder.java:325 - 加载阿里云 OSS 存储平台：aliyun-oss
2025-09-03 15:58:02,730 [main] INFO  [c.t.l.a.b.searchEngine.SearchEngineStrategyManager] SearchEngineStrategyManager.java:57 - 注册搜索引擎策略: BOCHAAI
2025-09-03 15:58:02,731 [main] INFO  [c.t.l.a.b.searchEngine.SearchEngineStrategyManager] SearchEngineStrategyManager.java:60 - 搜索引擎策略管理器初始化完成，共注册 1 个策略
2025-09-03 15:58:03,785 [main] WARN  [c.b.mybatisplus.core.metadata.TableInfoHelper] TableInfoHelper.java:376 - Can not find table primary key in Class: "cn.tycoding.langchat.upms.entity.SysRoleMenu".
2025-09-03 15:58:03,785 [main] WARN  [c.b.mybatisplus.core.injector.DefaultSqlInjector] DefaultSqlInjector.java:52 - class cn.tycoding.langchat.upms.entity.SysRoleMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-09-03 15:58:04,106 [main] INFO  [c.t.langchat.mcp.core.config.McpAutoConfiguration] McpAutoConfiguration.java:41 - LangChat MCP模块已启用
2025-09-03 15:58:04,123 [main] INFO  [c.t.l.m.c.o.impl.FlexibleMcpOrchestrator] FlexibleMcpOrchestrator.java:230 - 注册编排策略: CompositeStrategy (优先级: 90)
2025-09-03 15:58:04,123 [main] INFO  [c.t.l.m.c.orchestration.strategy.CompositeStrategy] CompositeStrategy.java:48 - 复合编排策略已注册
2025-09-03 15:58:04,125 [main] INFO  [c.t.l.m.c.o.impl.FlexibleMcpOrchestrator] FlexibleMcpOrchestrator.java:230 - 注册编排策略: EnhancedImageGenerationStrategy (优先级: 85)
2025-09-03 15:58:04,125 [main] INFO  [c.t.l.m.c.o.s.EnhancedImageGenerationStrategy] EnhancedImageGenerationStrategy.java:49 - 增强图片生成编排策略已注册
2025-09-03 15:58:04,126 [main] INFO  [c.t.l.m.c.o.impl.FlexibleMcpOrchestrator] FlexibleMcpOrchestrator.java:230 - 注册编排策略: ImageGenerationStrategy (优先级: 80)
2025-09-03 15:58:04,127 [main] INFO  [c.t.l.m.c.o.strategy.ImageGenerationStrategy] ImageGenerationStrategy.java:48 - 图片生成编排策略已注册
2025-09-03 15:58:04,128 [main] INFO  [c.t.l.m.c.o.impl.FlexibleMcpOrchestrator] FlexibleMcpOrchestrator.java:230 - 注册编排策略: WebSearchStrategy (优先级: 70)
2025-09-03 15:58:04,128 [main] INFO  [c.t.l.m.c.orchestration.strategy.WebSearchStrategy] WebSearchStrategy.java:47 - 网络搜索编排策略已注册
2025-09-03 15:58:05,779 [main] WARN  [o.s.b.a.g.template.GroovyTemplateAutoConfiguration] GroovyTemplateAutoConfiguration.java:84 - Cannot find template location: classpath:/templates/ (please add some templates, check your Groovy configuration, or set spring.groovy.template.check-template-location=false)
2025-09-03 15:58:06,418 [main] INFO  [org.apache.coyote.http11.Http11NioProtocol] DirectJDKLog.java:173 - Starting ProtocolHandler ["http-nio-8100"]
2025-09-03 15:58:06,434 [main] INFO  [o.s.boot.web.embedded.tomcat.TomcatWebServer] TomcatWebServer.java:241 - Tomcat started on port 8100 (http) with context path ''
2025-09-03 15:58:06,447 [main] INFO  [cn.tycoding.langchat.LangChatApp] StartupInfoLogger.java:56 - Started LangChatApp in 23.356 seconds (process running for 25.68)
2025-09-03 15:58:06,452 [main] INFO  [c.t.l.mcp.core.config.McpOrchestrationConfig] McpOrchestrationConfig.java:47 - 开始注册MCP编排策略...
2025-09-03 15:58:06,453 [main] INFO  [c.t.l.m.c.o.impl.FlexibleMcpOrchestrator] FlexibleMcpOrchestrator.java:230 - 注册编排策略: ImagePublishStrategy (优先级: 80)
2025-09-03 15:58:06,453 [main] INFO  [c.t.l.m.c.o.impl.FlexibleMcpOrchestrator] FlexibleMcpOrchestrator.java:230 - 注册编排策略: ContentCreationStrategy (优先级: 90)
2025-09-03 15:58:06,453 [main] INFO  [c.t.l.m.c.o.impl.FlexibleMcpOrchestrator] FlexibleMcpOrchestrator.java:230 - 注册编排策略: AiDrivenStrategy (优先级: 10)
2025-09-03 15:58:06,454 [main] INFO  [c.t.l.mcp.core.config.McpOrchestrationConfig] McpOrchestrationConfig.java:54 - MCP编排策略注册完成
2025-09-03 15:58:06,454 [main] INFO  [c.t.l.mcp.core.config.McpServiceAutoConfiguration] McpServiceAutoConfiguration.java:47 - 开始自动同步数据库中的MCP服务配置...
2025-09-03 15:58:06,482 [main] INFO  [cn.tycoding.langchat.mcp.core.protocol.McpClient] McpClient.java:54 - MCP服务已注册: ai-prompt-optimizer
2025-09-03 15:58:06,500 [main] INFO  [c.t.l.m.c.service.impl.AigcMcpServiceServiceImpl] AigcMcpServiceServiceImpl.java:217 - MCP服务同步成功: ai-prompt-optimizer
2025-09-03 15:58:06,571 [main] INFO  [cn.tycoding.langchat.mcp.core.protocol.McpClient] McpClient.java:54 - MCP服务已注册: builtin-api
2025-09-03 15:58:06,577 [main] INFO  [c.t.l.m.c.service.impl.AigcMcpServiceServiceImpl] AigcMcpServiceServiceImpl.java:217 - MCP服务同步成功: builtin-api
2025-09-03 15:58:06,579 [main] INFO  [cn.tycoding.langchat.mcp.core.protocol.McpClient] McpClient.java:54 - MCP服务已注册: builtin-file
2025-09-03 15:58:06,584 [main] INFO  [c.t.l.m.c.service.impl.AigcMcpServiceServiceImpl] AigcMcpServiceServiceImpl.java:217 - MCP服务同步成功: builtin-file
2025-09-03 15:58:06,587 [main] INFO  [cn.tycoding.langchat.mcp.core.protocol.McpClient] McpClient.java:54 - MCP服务已注册: builtin-email
2025-09-03 15:58:06,592 [main] INFO  [c.t.l.m.c.service.impl.AigcMcpServiceServiceImpl] AigcMcpServiceServiceImpl.java:217 - MCP服务同步成功: builtin-email
2025-09-03 15:58:06,597 [main] INFO  [cn.tycoding.langchat.mcp.core.protocol.McpClient] McpClient.java:54 - MCP服务已注册: wanx-image-generation
2025-09-03 15:58:06,602 [main] INFO  [c.t.l.m.c.service.impl.AigcMcpServiceServiceImpl] AigcMcpServiceServiceImpl.java:217 - MCP服务同步成功: wanx-image-generation
2025-09-03 15:58:06,603 [main] INFO  [cn.tycoding.langchat.mcp.core.protocol.McpClient] McpClient.java:54 - MCP服务已注册: builtin-search
2025-09-03 15:58:06,608 [main] INFO  [c.t.l.m.c.service.impl.AigcMcpServiceServiceImpl] AigcMcpServiceServiceImpl.java:217 - MCP服务同步成功: builtin-search
2025-09-03 15:58:06,608 [main] INFO  [c.t.l.mcp.core.config.McpServiceAutoConfiguration] McpServiceAutoConfiguration.java:52 - MCP服务配置自动同步完成
2025-09-03 15:58:06,610 [main] INFO  [c.t.l.common.core.component.CustomBannerPrinter] CustomBannerPrinter.java:34 - AIGC智能AI平台 启动完成...... 当前环境：dev
2025-09-03 16:00:57,453 [http-nio-8100-exec-1] INFO  [o.a.c.core.ContainerBase.[Tomcat].[localhost].[/]] DirectJDKLog.java:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-03 16:00:57,453 [http-nio-8100-exec-1] INFO  [org.springframework.web.servlet.DispatcherServlet] FrameworkServlet.java:532 - Initializing Servlet 'dispatcherServlet'
2025-09-03 16:00:57,455 [http-nio-8100-exec-1] INFO  [org.springframework.web.servlet.DispatcherServlet] FrameworkServlet.java:554 - Completed initialization in 2 ms
2025-09-03 16:01:33,469 [http-nio-8100-exec-7] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:208 - 开始处理扫描版PDF，总页数: 1
2025-09-03 16:01:47,101 [http-nio-8100-exec-7] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:222 - PDF文本内容不足，开始OCR识别
2025-09-03 16:02:02,132 [http-nio-8100-exec-7] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:243 - 正在处理第 1 页，共 1 页
2025-09-03 16:02:21,517 [http-nio-8100-exec-7] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:29 - 推理引擎初始化完成，当前使用的推理引擎为：onnx-v1.2.2
2025-09-03 16:02:21,519 [http-nio-8100-exec-7] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:30 - 初始化时模型配置为：Model.ONNX_PPOCR_V4(modelsDir=/models, modelType=onnx, detName=ch_PP-OCRv4_det_infer.onnx, clsName=ch_ppocr_mobile_v2.0_cls_infer.onnx, recName=ch_PP-OCRv4_rec_infer.onnx, keysName=ppocr_keys_v1.txt)， 硬件配置为：HardwareConfig(numThread=12, gpuIndex=-1)
2025-09-03 16:02:21,522 [http-nio-8100-exec-7] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\pdf_page_1_10560404815416789810.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-03 16:02:34,295 [http-nio-8100-exec-7] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：采购合同合同编号：QJSBT-20230726云南瑞和四季科技有限公司供方：签订时间：2023-07-24签订地点：云南省开远市开远市粮油购销有限公司需方：《中华人民共和国民法典》有关条款规定，经双方共同协商，达成以下条款，以资共同遵守。一、品种、规格、数量、单价、总金额金额（元）订单单位数量单价（元/吨）产品名称及规格2833000.00吨2833.00芽麦\猪料用1000.00合计人民币（大写）：贰佰捌拾叁万叁仟元整（含税价）二、质量技术标准质量标准：小麦（不含非瘟病毒）：出芽率不做强制要求，不纳入不完善指标籽粒整齐均匀、饱满，黄褐色或白色，无发酵、霉变、虫蛙及异味、异臭、异物。水分≤13.5%；容重≥730G/L；杂质s1%；虫蛀<5%：不完善（包括病斑粒）≤8%；黄曲霉毒素≤10ppb、赤霉烯酮<150ppb、呕吐毒素<1000ppb.三、检验方式以需方工厂检验为准。若供方对检验结果产生异议，可委托国家认可的检测部门检测。如检测结果达不到合同约定，检测费用由供方承担，达到合同约定，检测费用则由需方承担。四、交货地点需方指定地点，运输费用由需方承担。五、交货期限在2023年8月31日（星期四）前交货，如供方不能按合同约定期限交货，需方有权单方变更或终止合同，并视为供方违约。六、计量以需方电子磅数量为准七、包装标准散粮/包粮：编织袋包装包装物由供方提供不计价，不返回，每条编织袋扣重150克八、付款方式及期限1、货到检验合格，7天内凭发票以银行电汇付清货款2、供方必须提供增值税发票，如供方提供违规、虚假发票，需方有权对供方处于20%-100%的货款处罚，并承担由此给需方造成的一切经济损失和税务风险。（必须在发票备注栏注明纸制合同号）九、违约责任按《中华人民共和国民法典》有关规定执行；若发生违约纠纷，违约方按本合同金额20%支付给守约方，由合同签订所在地人民法院管辖。十、其它条款1、供方在2023年6月27日17：00前盖章回传有效，逾期该采购要约失效。2、若货物质量达不到需方要求，供方自行处理，所发生的一切费用由供方承担；如果经过国家检验机构确定供方有掺假行为，供方同意按不低于合同总价的20%金额赔偿给需方。3、货物发出后1个工作日内告之需方车号、数量等相关事宜；若火车运输需传真大票并寄小票到需方。本合同一式两份（供需双方各执一份），须经双方代表签字盖章后有效，涂改无效，合同正本与传真件具有同等法律效力，未尽事项双方协商解决。油购销和四秀需方（签章）：供方（签章）：云南瑞和四季科技有限公司王融云地址：云南省红河州开远市开文路108号地址：云南昆明滇池度假区滇池路金成财郡商业中心9栋602开世合同专用章法人代表/委托代理人：法人代表/委托代理电话：0873-7124297电话：0871-65330028传真：传真：CS扫描全能王，耗时12569.516199991107ms
2025-09-03 16:03:00,849 [http-nio-8100-exec-7] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本：=== 第1页 ===
采购合同
合同编号：QJSBT-20230726
云南瑞和四季科技有限公司
供方：
签订时间：2023-07-24
签订地点：云南省开远市
开远市粮油购销有限公司
需方：
《中华人民共和国民法典》有关条款规定，经双方共同协商，达成以下条款，以资共同遵守。
一、品种、规格、数量、单价、总金额
金额（元）
订单单位
数量
单价（元/吨）
产品名称及规格
2833000.00
吨
2833.00
芽麦\猪料用
1000.00
合计人民币（大写）：贰佰捌拾叁万叁仟元整（含税价）
二、质量技术标准
质量标准：小麦（不含非瘟病毒）：出芽率不做强制要求，不纳入不完善指标籽粒整齐均匀、饱满，黄褐色或白色，无发酵、霉变、虫蛙及异味、异臭、
异物。水分≤13.5%；容重≥730G/L；杂质s1%；虫蛀<5%：不完善（包括病斑粒）≤8%；黄曲霉毒素≤10ppb、赤霉烯酮<150ppb、呕吐毒素<
1000ppb.
三、检验方式
以需方工厂检验为准。若供方对检验结果产生异议，可委托国家认可的检测部门检测。如检测结果达不到合同约定，检测费用由供方承担，达到合同
约定，检测费用则由需方承担。
四、交货地点
需方指定地点，运输费用由需方承担。
五、交货期限
在2023年8月31日（星期四）前交货，如供方不能按合同约定期限交货，需方有权单方变更或终止合同，并视为供方违约。
六、计量
以需方电子磅数量为准
七、包装标准
散粮/包粮：编织袋包装包装物由供方提供不计价，不返回，每条编织袋扣重150克
八、付款方式及期限
1、货到检验合格，7天内凭发票以银行电汇付清货款
2、供方必须提供增值税发票，如供方提供违规、虚假发票，需方有权对供方处于20%-100%的货款处罚，并承担由此给需方造成的一切经济损失和税
务风险。（必须在发票备注栏注明纸制合同号）
九、违约责任
按《中华人民共和国民法典》有关规定执行；若发生违约纠纷，违约方按本合同金额20%支付给守约方，由合同签订所在地人民法院管辖。
十、其它条款
1、供方在2023年6月27日17：00前盖章回传有效，逾期该采购要约失效。
2、若货物质量达不到需方要求，供方自行处理，所发生的一切费用由供方承担；如果经过国家检验机构确定供方有掺假行为，供方同意按不低于合同
总价的20%金额赔偿给需方。
3、货物发出后1个工作日内告之需方车号、数量等相关事宜；若火车运输需传真大票并寄小票到需方。
本合同一式两份（供需双方各执一份），须经双方代表签字盖章后有效，涂改无效，合同正本与传真件具有同等法律效力，未尽事项双方协商解决。
油购销
和四秀
需方（签章）：
供方（签章）：云南瑞和四季科技有限公司
王融
云

地址：云南省红河州开远市开文路108号
地址：云南昆明滇池度假区滇池路金成财郡商业中心9栋602
开
世
合同专用章
法人代表/委托代理人：
法人代表/委托代理
电话：0873-7124297
电话：0871-65330028
传真：
传真：
CS
扫描全能王


2025-09-03 16:03:31,082 [http-nio-8100-exec-10] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:208 - 开始处理扫描版PDF，总页数: 1
2025-09-03 16:03:31,084 [http-nio-8100-exec-10] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:222 - PDF文本内容不足，开始OCR识别
2025-09-03 16:03:31,084 [http-nio-8100-exec-10] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:243 - 正在处理第 1 页，共 1 页
2025-09-03 16:03:32,022 [http-nio-8100-exec-10] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:38 - 当前使用的推理引擎为：onnx-v1.2.2
2025-09-03 16:03:32,022 [http-nio-8100-exec-10] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\pdf_page_1_15052370272212844806.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-03 16:03:44,259 [http-nio-8100-exec-10] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：采购合同合同编号：QJSBT-20230726云南瑞和四季科技有限公司供方：签订时间：2023-07-24签订地点：云南省开远市开远市粮油购销有限公司需方：《中华人民共和国民法典》有关条款规定，经双方共同协商，达成以下条款，以资共同遵守。一、品种、规格、数量、单价、总金额金额（元）订单单位数量单价（元/吨）产品名称及规格2833000.00吨2833.00芽麦\猪料用1000.00合计人民币（大写）：贰佰捌拾叁万叁仟元整（含税价）二、质量技术标准质量标准：小麦（不含非瘟病毒）：出芽率不做强制要求，不纳入不完善指标籽粒整齐均匀、饱满，黄褐色或白色，无发酵、霉变、虫蛙及异味、异臭、异物。水分≤13.5%；容重≥730G/L；杂质s1%；虫蛀<5%：不完善（包括病斑粒）≤8%；黄曲霉毒素≤10ppb、赤霉烯酮<150ppb、呕吐毒素<1000ppb.三、检验方式以需方工厂检验为准。若供方对检验结果产生异议，可委托国家认可的检测部门检测。如检测结果达不到合同约定，检测费用由供方承担，达到合同约定，检测费用则由需方承担。四、交货地点需方指定地点，运输费用由需方承担。五、交货期限在2023年8月31日（星期四）前交货，如供方不能按合同约定期限交货，需方有权单方变更或终止合同，并视为供方违约。六、计量以需方电子磅数量为准七、包装标准散粮/包粮：编织袋包装包装物由供方提供不计价，不返回，每条编织袋扣重150克八、付款方式及期限1、货到检验合格，7天内凭发票以银行电汇付清货款2、供方必须提供增值税发票，如供方提供违规、虚假发票，需方有权对供方处于20%-100%的货款处罚，并承担由此给需方造成的一切经济损失和税务风险。（必须在发票备注栏注明纸制合同号）九、违约责任按《中华人民共和国民法典》有关规定执行；若发生违约纠纷，违约方按本合同金额20%支付给守约方，由合同签订所在地人民法院管辖。十、其它条款1、供方在2023年6月27日17：00前盖章回传有效，逾期该采购要约失效。2、若货物质量达不到需方要求，供方自行处理，所发生的一切费用由供方承担；如果经过国家检验机构确定供方有掺假行为，供方同意按不低于合同总价的20%金额赔偿给需方。3、货物发出后1个工作日内告之需方车号、数量等相关事宜；若火车运输需传真大票并寄小票到需方。本合同一式两份（供需双方各执一份），须经双方代表签字盖章后有效，涂改无效，合同正本与传真件具有同等法律效力，未尽事项双方协商解决。油购销和四秀需方（签章）：供方（签章）：云南瑞和四季科技有限公司王融云地址：云南省红河州开远市开文路108号地址：云南昆明滇池度假区滇池路金成财郡商业中心9栋602开世合同专用章法人代表/委托代理人：法人代表/委托代理电话：0873-7124297电话：0871-65330028传真：传真：CS扫描全能王，耗时12100.729699999094ms
2025-09-03 16:03:44,261 [http-nio-8100-exec-10] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本：=== 第1页 ===
采购合同
合同编号：QJSBT-20230726
云南瑞和四季科技有限公司
供方：
签订时间：2023-07-24
签订地点：云南省开远市
开远市粮油购销有限公司
需方：
《中华人民共和国民法典》有关条款规定，经双方共同协商，达成以下条款，以资共同遵守。
一、品种、规格、数量、单价、总金额
金额（元）
订单单位
数量
单价（元/吨）
产品名称及规格
2833000.00
吨
2833.00
芽麦\猪料用
1000.00
合计人民币（大写）：贰佰捌拾叁万叁仟元整（含税价）
二、质量技术标准
质量标准：小麦（不含非瘟病毒）：出芽率不做强制要求，不纳入不完善指标籽粒整齐均匀、饱满，黄褐色或白色，无发酵、霉变、虫蛙及异味、异臭、
异物。水分≤13.5%；容重≥730G/L；杂质s1%；虫蛀<5%：不完善（包括病斑粒）≤8%；黄曲霉毒素≤10ppb、赤霉烯酮<150ppb、呕吐毒素<
1000ppb.
三、检验方式
以需方工厂检验为准。若供方对检验结果产生异议，可委托国家认可的检测部门检测。如检测结果达不到合同约定，检测费用由供方承担，达到合同
约定，检测费用则由需方承担。
四、交货地点
需方指定地点，运输费用由需方承担。
五、交货期限
在2023年8月31日（星期四）前交货，如供方不能按合同约定期限交货，需方有权单方变更或终止合同，并视为供方违约。
六、计量
以需方电子磅数量为准
七、包装标准
散粮/包粮：编织袋包装包装物由供方提供不计价，不返回，每条编织袋扣重150克
八、付款方式及期限
1、货到检验合格，7天内凭发票以银行电汇付清货款
2、供方必须提供增值税发票，如供方提供违规、虚假发票，需方有权对供方处于20%-100%的货款处罚，并承担由此给需方造成的一切经济损失和税
务风险。（必须在发票备注栏注明纸制合同号）
九、违约责任
按《中华人民共和国民法典》有关规定执行；若发生违约纠纷，违约方按本合同金额20%支付给守约方，由合同签订所在地人民法院管辖。
十、其它条款
1、供方在2023年6月27日17：00前盖章回传有效，逾期该采购要约失效。
2、若货物质量达不到需方要求，供方自行处理，所发生的一切费用由供方承担；如果经过国家检验机构确定供方有掺假行为，供方同意按不低于合同
总价的20%金额赔偿给需方。
3、货物发出后1个工作日内告之需方车号、数量等相关事宜；若火车运输需传真大票并寄小票到需方。
本合同一式两份（供需双方各执一份），须经双方代表签字盖章后有效，涂改无效，合同正本与传真件具有同等法律效力，未尽事项双方协商解决。
油购销
和四秀
需方（签章）：
供方（签章）：云南瑞和四季科技有限公司
王融
云

地址：云南省红河州开远市开文路108号
地址：云南昆明滇池度假区滇池路金成财郡商业中心9栋602
开
世
合同专用章
法人代表/委托代理人：
法人代表/委托代理
电话：0873-7124297
电话：0871-65330028
传真：
传真：
CS
扫描全能王


