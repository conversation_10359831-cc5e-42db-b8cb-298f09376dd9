2025-09-02 15:15:04,094 [background-preinit] INFO  [org.hibernate.validator.internal.util.Version] Version.java:21 - HV000001: Hibernate Validator 8.0.1.Final
2025-09-02 15:15:04,301 [main] INFO  [cn.tycoding.langchat.LangChatApp] StartupInfoLogger.java:50 - Starting LangChatApp using Java 17.0.14 with PID 16324 (D:\awork\work-aigc\aigc-manage\langchat-server\target\classes started by Administrator in D:\awork\work-aigc)
2025-09-02 15:15:04,303 [main] INFO  [cn.tycoding.langchat.LangChatApp] SpringApplication.java:660 - The following 1 profile is active: "dev"
2025-09-02 15:15:06,978 [main] INFO  [o.s.d.r.config.RepositoryConfigurationDelegate] RepositoryConfigurationDelegate.java:292 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-02 15:15:06,983 [main] INFO  [o.s.d.r.config.RepositoryConfigurationDelegate] RepositoryConfigurationDelegate.java:139 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-02 15:15:07,090 [main] INFO  [o.s.d.r.config.RepositoryConfigurationDelegate] RepositoryConfigurationDelegate.java:208 - Finished Spring Data repository scanning in 79 ms. Found 0 Redis repository interfaces.
2025-09-02 15:15:09,101 [main] INFO  [o.s.boot.web.embedded.tomcat.TomcatWebServer] TomcatWebServer.java:109 - Tomcat initialized with port 8100 (http)
2025-09-02 15:15:09,123 [main] INFO  [org.apache.coyote.http11.Http11NioProtocol] DirectJDKLog.java:173 - Initializing ProtocolHandler ["http-nio-8100"]
2025-09-02 15:15:09,125 [main] INFO  [org.apache.catalina.core.StandardService] DirectJDKLog.java:173 - Starting service [Tomcat]
2025-09-02 15:15:09,126 [main] INFO  [org.apache.catalina.core.StandardEngine] DirectJDKLog.java:173 - Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-09-02 15:15:09,446 [main] INFO  [o.a.c.core.ContainerBase.[Tomcat].[localhost].[/]] DirectJDKLog.java:173 - Initializing Spring embedded WebApplicationContext
2025-09-02 15:15:09,446 [main] INFO  [o.s.b.w.s.c.ServletWebServerApplicationContext] ServletWebServerApplicationContext.java:296 - Root WebApplicationContext: initialization completed in 4489 ms
2025-09-02 15:15:09,682 [main] INFO  [c.a.d.s.b.a.DruidDataSourceAutoConfigure] DruidDataSourceAutoConfigure.java:68 - Init DruidDataSource
2025-09-02 15:15:09,835 [main] INFO  [com.alibaba.druid.pool.DruidDataSource] DruidDataSource.java:1002 - {dataSource-1} inited
2025-09-02 15:15:10,332 [main] WARN  [c.b.mybatisplus.core.metadata.TableInfoHelper] TableInfoHelper.java:376 - Can not find table primary key in Class: "cn.tycoding.langchat.upms.entity.SysUserRole".
2025-09-02 15:15:10,333 [main] WARN  [c.b.mybatisplus.core.injector.DefaultSqlInjector] DefaultSqlInjector.java:52 - class cn.tycoding.langchat.upms.entity.SysUserRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-09-02 15:15:11,993 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- EMBEDDING， 模型配置：AigcModel(id=23b21ee73bc3dd67ddcc4287aadb1697, type=EMBEDDING, model=text-embedding-v4, provider=Q_WEN, name=text-embedding-v4, responseLimit=null, temperature=0.2, topP=0.0, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=1024, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-02 15:15:12,005 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：ZHIPU -- CHAT， 模型配置：AigcModel(id=30c047625a22dd3cfdc8a21d13bd7d04, type=CHAT, model=glm-4-flash, provider=ZHIPU, name=glm-4-flash-250414, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=a4867e65138e4f259c5c692aef943164.6xPiD4YHC2krGJAV, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-02 15:15:12,006 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- CHAT， 模型配置：AigcModel(id=6b1cdbb07032a117d1b4f00b865e7f23, type=CHAT, model=deepseek-v3, provider=Q_WEN, name=阿里DeepSeek-V3, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=https://dashscope.aliyuncs.com, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-02 15:15:12,006 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- CHAT， 模型配置：AigcModel(id=765a327613f9c29b547ec9dac3d3b2b4, type=CHAT, model=qwen-max-longcontext, provider=Q_WEN, name=千问长token模型, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=https://dashscope.aliyuncs.com, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-02 15:15:12,007 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：ZHIPU -- CHAT， 模型配置：AigcModel(id=91c235219da4e2a92ae166e38d179b89, type=CHAT, model=glm-4, provider=ZHIPU, name=glm-4-plus, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=a4867e65138e4f259c5c692aef943164.6xPiD4YHC2krGJAV, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-02 15:15:12,007 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：SILICON -- CHAT， 模型配置：AigcModel(id=ac05866194643c5341a73d837b2017c8, type=CHAT, model=deepseek-ai/DeepSeek-V2.5, provider=SILICON, name=deepseek, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-univnrseuupvzarrjozodhcmnurfvvyrcujdssfggfckynxd, secretKey=null, baseUrl=https://api.siliconflow.cn/v1, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-02 15:15:12,007 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- EMBEDDING， 模型配置：AigcModel(id=b3ac64325c37094da12d199d1928a37b, type=EMBEDDING, model=text-embedding-v3, provider=Q_WEN, name=通用文本向量-v3, responseLimit=null, temperature=0.2, topP=0.0, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=1024, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-02 15:15:12,007 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：ZHIPU -- TEXT_IMAGE， 模型配置：AigcModel(id=d4f776f9baa847f9c5a975c9f078e049, type=TEXT_IMAGE, model=cogview-3, provider=ZHIPU, name=图片生成, responseLimit=null, temperature=0.2, topP=0.0, apiKey=a4867e65138e4f259c5c692aef943164.6xPiD4YHC2krGJAV, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-02 15:15:12,008 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：OLLAMA -- EMBEDDING， 模型配置：AigcModel(id=d61f28c3ced11a1abac3215d2154c882, type=EMBEDDING, model=bge-m3:latest, provider=OLLAMA, name=ollama, responseLimit=null, temperature=0.2, topP=0.0, apiKey=null, secretKey=null, baseUrl=http://localhost:11434/, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=1024, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-02 15:15:12,008 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- EMBEDDING， 模型配置：AigcModel(id=dc3858965af7b992c806ed38ddd0164b, type=EMBEDDING, model=multimodal-embedding-v1, provider=Q_WEN, name=多模态向量模型, responseLimit=null, temperature=0.2, topP=0.0, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=1024, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-02 15:15:12,008 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- CHAT， 模型配置：AigcModel(id=f664ddbdec3fecd2afc2ed7631c17578, type=CHAT, model=Qwen-Long, provider=Q_WEN, name=Qwen-Long, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-553d1b0396064e16af616b910a526b53, secretKey=null, baseUrl=https://dashscope.aliyuncs.com/compatible-mode/v1, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-02 15:15:22,942 [main] ERROR [io.milvus.client.AbstractMilvusGrpcClient] AbstractMilvusGrpcClient.java:3378 - DEADLINE_EXCEEDED: deadline exceeded after 9.878994800s. Name resolution delay 0.017552200 seconds. [closed=[], open=[[wait_for_ready, buffered_nanos=**********, waiting_for_connection]]]
2025-09-02 15:15:22,948 [main] ERROR [io.milvus.client.AbstractMilvusGrpcClient] AbstractMilvusGrpcClient.java:3378 - Failed to initialize connection. Error: DEADLINE_EXCEEDED: deadline exceeded after 9.878994800s. Name resolution delay 0.017552200 seconds. [closed=[], open=[[wait_for_ready, buffered_nanos=**********, waiting_for_connection]]]
2025-09-02 15:15:22,950 [main] ERROR [c.t.l.ai.core.provider.EmbeddingStoreFactory] EmbeddingStoreFactory.java:103 - 向量数据库初始化失败：[Milvus] --- [MILVUS]，数据库配置信息：[AigcEmbedStore(id=c92cfbaaff366b12bc87c1082149150a, name=Milvus, provider=MILVUS, host=************, port=19530, username=null, password=null, databaseName=default, tableName=ai, dimension=1024)]
2025-09-02 15:15:23,185 [main] INFO  [cn.tycoding.langchat.server.store.AppStore] AppStore.java:44 - initialize app config list...
2025-09-02 15:15:23,509 [main] INFO  [cn.tycoding.langchat.server.store.AppChannelStore] AppChannelStore.java:52 - initialize app channel config list...
2025-09-02 15:15:23,738 [main] WARN  [c.t.l.c.oss.config.FileStorageAutoConfiguration] FileStorageAutoConfiguration.java:65 - 没有找到 FileRecorder 的实现类，文件上传之外的部分功能无法正常使用，必须实现该接口才能使用完整功能！
2025-09-02 15:15:23,774 [main] INFO  [o.d.x.file.storage.core.FileStorageServiceBuilder] FileStorageServiceBuilder.java:291 - 加载本地升级版存储平台：local
2025-09-02 15:15:23,781 [main] INFO  [o.d.x.file.storage.core.FileStorageServiceBuilder] FileStorageServiceBuilder.java:325 - 加载阿里云 OSS 存储平台：aliyun-oss
2025-09-02 15:15:23,806 [main] INFO  [c.t.l.a.b.searchEngine.SearchEngineStrategyManager] SearchEngineStrategyManager.java:57 - 注册搜索引擎策略: BOCHAAI
2025-09-02 15:15:23,807 [main] INFO  [c.t.l.a.b.searchEngine.SearchEngineStrategyManager] SearchEngineStrategyManager.java:60 - 搜索引擎策略管理器初始化完成，共注册 1 个策略
2025-09-02 15:15:24,963 [main] WARN  [c.b.mybatisplus.core.metadata.TableInfoHelper] TableInfoHelper.java:376 - Can not find table primary key in Class: "cn.tycoding.langchat.upms.entity.SysRoleMenu".
2025-09-02 15:15:24,963 [main] WARN  [c.b.mybatisplus.core.injector.DefaultSqlInjector] DefaultSqlInjector.java:52 - class cn.tycoding.langchat.upms.entity.SysRoleMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-09-02 15:15:25,296 [main] INFO  [c.t.langchat.mcp.core.config.McpAutoConfiguration] McpAutoConfiguration.java:41 - LangChat MCP模块已启用
2025-09-02 15:15:25,313 [main] INFO  [c.t.l.m.c.o.impl.FlexibleMcpOrchestrator] FlexibleMcpOrchestrator.java:230 - 注册编排策略: CompositeStrategy (优先级: 90)
2025-09-02 15:15:25,313 [main] INFO  [c.t.l.m.c.orchestration.strategy.CompositeStrategy] CompositeStrategy.java:48 - 复合编排策略已注册
2025-09-02 15:15:25,315 [main] INFO  [c.t.l.m.c.o.impl.FlexibleMcpOrchestrator] FlexibleMcpOrchestrator.java:230 - 注册编排策略: EnhancedImageGenerationStrategy (优先级: 85)
2025-09-02 15:15:25,315 [main] INFO  [c.t.l.m.c.o.s.EnhancedImageGenerationStrategy] EnhancedImageGenerationStrategy.java:49 - 增强图片生成编排策略已注册
2025-09-02 15:15:25,317 [main] INFO  [c.t.l.m.c.o.impl.FlexibleMcpOrchestrator] FlexibleMcpOrchestrator.java:230 - 注册编排策略: ImageGenerationStrategy (优先级: 80)
2025-09-02 15:15:25,317 [main] INFO  [c.t.l.m.c.o.strategy.ImageGenerationStrategy] ImageGenerationStrategy.java:48 - 图片生成编排策略已注册
2025-09-02 15:15:25,319 [main] INFO  [c.t.l.m.c.o.impl.FlexibleMcpOrchestrator] FlexibleMcpOrchestrator.java:230 - 注册编排策略: WebSearchStrategy (优先级: 70)
2025-09-02 15:15:25,319 [main] INFO  [c.t.l.m.c.orchestration.strategy.WebSearchStrategy] WebSearchStrategy.java:47 - 网络搜索编排策略已注册
2025-09-02 15:15:27,162 [main] WARN  [o.s.b.a.g.template.GroovyTemplateAutoConfiguration] GroovyTemplateAutoConfiguration.java:84 - Cannot find template location: classpath:/templates/ (please add some templates, check your Groovy configuration, or set spring.groovy.template.check-template-location=false)
2025-09-02 15:15:27,989 [main] INFO  [org.apache.coyote.http11.Http11NioProtocol] DirectJDKLog.java:173 - Starting ProtocolHandler ["http-nio-8100"]
2025-09-02 15:15:28,006 [main] INFO  [o.s.boot.web.embedded.tomcat.TomcatWebServer] TomcatWebServer.java:241 - Tomcat started on port 8100 (http) with context path ''
2025-09-02 15:15:28,021 [main] INFO  [cn.tycoding.langchat.LangChatApp] StartupInfoLogger.java:56 - Started LangChatApp in 25.16 seconds (process running for 27.495)
2025-09-02 15:15:28,027 [main] INFO  [c.t.l.mcp.core.config.McpServiceAutoConfiguration] McpServiceAutoConfiguration.java:47 - 开始自动同步数据库中的MCP服务配置...
2025-09-02 15:15:28,080 [main] INFO  [cn.tycoding.langchat.mcp.core.protocol.McpClient] McpClient.java:54 - MCP服务已注册: ai-prompt-optimizer
2025-09-02 15:15:28,120 [main] INFO  [c.t.l.m.c.service.impl.AigcMcpServiceServiceImpl] AigcMcpServiceServiceImpl.java:217 - MCP服务同步成功: ai-prompt-optimizer
2025-09-02 15:15:28,311 [main] INFO  [cn.tycoding.langchat.mcp.core.protocol.McpClient] McpClient.java:54 - MCP服务已注册: builtin-api
2025-09-02 15:15:28,318 [main] INFO  [c.t.l.m.c.service.impl.AigcMcpServiceServiceImpl] AigcMcpServiceServiceImpl.java:217 - MCP服务同步成功: builtin-api
2025-09-02 15:15:28,322 [main] INFO  [cn.tycoding.langchat.mcp.core.protocol.McpClient] McpClient.java:54 - MCP服务已注册: builtin-file
2025-09-02 15:15:28,329 [main] INFO  [c.t.l.m.c.service.impl.AigcMcpServiceServiceImpl] AigcMcpServiceServiceImpl.java:217 - MCP服务同步成功: builtin-file
2025-09-02 15:15:28,334 [main] INFO  [cn.tycoding.langchat.mcp.core.protocol.McpClient] McpClient.java:54 - MCP服务已注册: builtin-email
2025-09-02 15:15:28,341 [main] INFO  [c.t.l.m.c.service.impl.AigcMcpServiceServiceImpl] AigcMcpServiceServiceImpl.java:217 - MCP服务同步成功: builtin-email
2025-09-02 15:15:28,347 [main] INFO  [cn.tycoding.langchat.mcp.core.protocol.McpClient] McpClient.java:54 - MCP服务已注册: wanx-image-generation
2025-09-02 15:15:28,353 [main] INFO  [c.t.l.m.c.service.impl.AigcMcpServiceServiceImpl] AigcMcpServiceServiceImpl.java:217 - MCP服务同步成功: wanx-image-generation
2025-09-02 15:15:28,355 [main] INFO  [cn.tycoding.langchat.mcp.core.protocol.McpClient] McpClient.java:54 - MCP服务已注册: builtin-search
2025-09-02 15:15:28,365 [main] INFO  [c.t.l.m.c.service.impl.AigcMcpServiceServiceImpl] AigcMcpServiceServiceImpl.java:217 - MCP服务同步成功: builtin-search
2025-09-02 15:15:28,366 [main] INFO  [c.t.l.mcp.core.config.McpServiceAutoConfiguration] McpServiceAutoConfiguration.java:52 - MCP服务配置自动同步完成
2025-09-02 15:15:28,367 [main] INFO  [c.t.l.mcp.core.config.McpOrchestrationConfig] McpOrchestrationConfig.java:47 - 开始注册MCP编排策略...
2025-09-02 15:15:28,367 [main] INFO  [c.t.l.m.c.o.impl.FlexibleMcpOrchestrator] FlexibleMcpOrchestrator.java:230 - 注册编排策略: ImagePublishStrategy (优先级: 80)
2025-09-02 15:15:28,368 [main] INFO  [c.t.l.m.c.o.impl.FlexibleMcpOrchestrator] FlexibleMcpOrchestrator.java:230 - 注册编排策略: ContentCreationStrategy (优先级: 90)
2025-09-02 15:15:28,368 [main] INFO  [c.t.l.m.c.o.impl.FlexibleMcpOrchestrator] FlexibleMcpOrchestrator.java:230 - 注册编排策略: AiDrivenStrategy (优先级: 10)
2025-09-02 15:15:28,368 [main] INFO  [c.t.l.mcp.core.config.McpOrchestrationConfig] McpOrchestrationConfig.java:54 - MCP编排策略注册完成
2025-09-02 15:15:28,369 [main] INFO  [c.t.l.common.core.component.CustomBannerPrinter] CustomBannerPrinter.java:34 - AIGC智能AI平台 启动完成...... 当前环境：dev
