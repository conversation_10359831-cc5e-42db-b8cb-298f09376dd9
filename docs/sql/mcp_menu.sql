-- MCP服务管理菜单脚本
-- 插入MCP服务管理相关的菜单项

-- 1. 插入MCP服务管理主菜单
INSERT INTO `sys_menu` (`id`, `pid`, `title`, `name`, `path`, `component`, `type`, `icon`, `order_no`, `is_hidden`, `is_keepalive`, `is_affix`, `is_iframe`, `permission`, `status`, `create_time`, `update_time`, `remark`) VALUES
('mcp_management', 'aigc_management', 'MCP服务管理', 'McpManagement', '/aigc/mcp', 'Layout', 1, 'CloudOutline', 35, 0, 1, 0, 0, NULL, 1, NOW(), NOW(), 'MCP服务管理模块');

-- 2. 插入MCP服务管理子菜单
INSERT INTO `sys_menu` (`id`, `pid`, `title`, `name`, `path`, `component`, `type`, `icon`, `order_no`, `is_hidden`, `is_keepalive`, `is_affix`, `is_iframe`, `permission`, `status`, `create_time`, `update_time`, `remark`) VALUES
('mcp_service_management', 'mcp_management', 'MCP服务管理', 'McpServiceManagement', '/aigc/mcp/service', '/aigc/mcp/service/index', 2, 'SettingsOutline', 1, 0, 1, 0, 0, 'aigc:mcp:service:list', 1, NOW(), NOW(), 'MCP服务配置和管理'),
('mcp_tools_management', 'mcp_management', 'MCP工具管理', 'McpToolsManagement', '/aigc/mcp/tools', '/aigc/mcp/tools/index', 2, 'BuildOutline', 2, 0, 1, 0, 0, 'aigc:mcp:tools:list', 1, NOW(), NOW(), 'MCP工具浏览和测试'),
('mcp_logs_management', 'mcp_management', 'MCP调用日志', 'McpLogsManagement', '/aigc/mcp/logs', '/aigc/mcp/logs/index', 2, 'DocumentTextOutline', 3, 0, 1, 0, 0, 'aigc:mcp:logs:list', 1, NOW(), NOW(), 'MCP服务调用日志查看');

-- 3. 插入MCP服务管理相关权限
INSERT INTO `sys_menu` (`id`, `pid`, `title`, `name`, `path`, `component`, `type`, `icon`, `order_no`, `is_hidden`, `is_keepalive`, `is_affix`, `is_iframe`, `permission`, `status`, `create_time`, `update_time`, `remark`) VALUES
-- MCP服务管理权限
('mcp_service_list', 'mcp_service_management', '查看服务列表', NULL, NULL, NULL, 3, NULL, 1, 0, 0, 0, 0, 'aigc:mcp:service:list', 1, NOW(), NOW(), '查看MCP服务列表权限'),
('mcp_service_info', 'mcp_service_management', '查看服务详情', NULL, NULL, NULL, 3, NULL, 2, 0, 0, 0, 0, 'aigc:mcp:service:info', 1, NOW(), NOW(), '查看MCP服务详情权限'),
('mcp_service_add', 'mcp_service_management', '新增服务', NULL, NULL, NULL, 3, NULL, 3, 0, 0, 0, 0, 'aigc:mcp:service:add', 1, NOW(), NOW(), '新增MCP服务权限'),
('mcp_service_update', 'mcp_service_management', '修改服务', NULL, NULL, NULL, 3, NULL, 4, 0, 0, 0, 0, 'aigc:mcp:service:update', 1, NOW(), NOW(), '修改MCP服务权限'),
('mcp_service_delete', 'mcp_service_management', '删除服务', NULL, NULL, NULL, 3, NULL, 5, 0, 0, 0, 0, 'aigc:mcp:service:delete', 1, NOW(), NOW(), '删除MCP服务权限'),
('mcp_service_toggle', 'mcp_service_management', '启用/禁用服务', NULL, NULL, NULL, 3, NULL, 6, 0, 0, 0, 0, 'aigc:mcp:service:toggle', 1, NOW(), NOW(), '启用/禁用MCP服务权限'),
('mcp_service_test', 'mcp_service_management', '测试服务连接', NULL, NULL, NULL, 3, NULL, 7, 0, 0, 0, 0, 'aigc:mcp:service:test', 1, NOW(), NOW(), '测试MCP服务连接权限'),
('mcp_service_sync', 'mcp_service_management', '同步服务', NULL, NULL, NULL, 3, NULL, 8, 0, 0, 0, 0, 'aigc:mcp:service:sync', 1, NOW(), NOW(), '同步MCP服务权限'),
('mcp_service_health', 'mcp_service_management', '查看服务健康状态', NULL, NULL, NULL, 3, NULL, 9, 0, 0, 0, 0, 'aigc:mcp:service:health', 1, NOW(), NOW(), '查看MCP服务健康状态权限'),
('mcp_service_refresh', 'mcp_service_management', '刷新工具列表', NULL, NULL, NULL, 3, NULL, 10, 0, 0, 0, 0, 'aigc:mcp:service:refresh', 1, NOW(), NOW(), '刷新MCP服务工具列表权限'),
('mcp_service_stats', 'mcp_service_management', '查看统计信息', NULL, NULL, NULL, 3, NULL, 11, 0, 0, 0, 0, 'aigc:mcp:service:stats', 1, NOW(), NOW(), '查看MCP服务统计信息权限'),

-- MCP工具管理权限
('mcp_tools_list', 'mcp_tools_management', '查看工具列表', NULL, NULL, NULL, 3, NULL, 1, 0, 0, 0, 0, 'aigc:mcp:tools:list', 1, NOW(), NOW(), '查看MCP工具列表权限'),
('mcp_tools_detail', 'mcp_tools_management', '查看工具详情', NULL, NULL, NULL, 3, NULL, 2, 0, 0, 0, 0, 'aigc:mcp:tools:detail', 1, NOW(), NOW(), '查看MCP工具详情权限'),
('mcp_tools_test', 'mcp_tools_management', '测试工具', NULL, NULL, NULL, 3, NULL, 3, 0, 0, 0, 0, 'aigc:mcp:tools:test', 1, NOW(), NOW(), '测试MCP工具权限'),

-- MCP调用日志权限
('mcp_logs_list', 'mcp_logs_management', '查看调用日志', NULL, NULL, NULL, 3, NULL, 1, 0, 0, 0, 0, 'aigc:mcp:logs:list', 1, NOW(), NOW(), '查看MCP调用日志权限'),
('mcp_logs_detail', 'mcp_logs_management', '查看日志详情', NULL, NULL, NULL, 3, NULL, 2, 0, 0, 0, 0, 'aigc:mcp:logs:detail', 1, NOW(), NOW(), '查看MCP调用日志详情权限'),
('mcp_logs_export', 'mcp_logs_management', '导出日志', NULL, NULL, NULL, 3, NULL, 3, 0, 0, 0, 0, 'aigc:mcp:logs:export', 1, NOW(), NOW(), '导出MCP调用日志权限');

-- 4. 为管理员角色分配MCP管理权限
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) 
SELECT 'admin', `id` FROM `sys_menu` WHERE `id` IN (
    'mcp_management',
    'mcp_service_management', 'mcp_tools_management', 'mcp_logs_management',
    'mcp_service_list', 'mcp_service_info', 'mcp_service_add', 'mcp_service_update', 'mcp_service_delete',
    'mcp_service_toggle', 'mcp_service_test', 'mcp_service_sync', 'mcp_service_health', 'mcp_service_refresh', 'mcp_service_stats',
    'mcp_tools_list', 'mcp_tools_detail', 'mcp_tools_test',
    'mcp_logs_list', 'mcp_logs_detail', 'mcp_logs_export'
);

-- 5. 为运维角色分配部分MCP权限（如果存在运维角色）
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) 
SELECT 'operator', `id` FROM `sys_menu` WHERE `id` IN (
    'mcp_management',
    'mcp_service_management', 'mcp_tools_management', 'mcp_logs_management',
    'mcp_service_list', 'mcp_service_info', 'mcp_service_test', 'mcp_service_health', 'mcp_service_stats',
    'mcp_tools_list', 'mcp_tools_detail', 'mcp_tools_test',
    'mcp_logs_list', 'mcp_logs_detail'
) AND EXISTS (SELECT 1 FROM `sys_role` WHERE `id` = 'operator');

-- 6. 为普通用户角色分配查看权限（如果存在用户角色）
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) 
SELECT 'user', `id` FROM `sys_menu` WHERE `id` IN (
    'mcp_tools_management',
    'mcp_tools_list', 'mcp_tools_detail', 'mcp_tools_test'
) AND EXISTS (SELECT 1 FROM `sys_role` WHERE `id` = 'user');

-- 7. 更新菜单排序（如果需要调整AIGC管理模块下的菜单顺序）
UPDATE `sys_menu` SET `order_no` = 30 WHERE `id` = 'model_management';
UPDATE `sys_menu` SET `order_no` = 35 WHERE `id` = 'mcp_management';
UPDATE `sys_menu` SET `order_no` = 40 WHERE `id` = 'knowledge_management';
UPDATE `sys_menu` SET `order_no` = 45 WHERE `id` = 'prompt_management';

-- 查询验证菜单是否插入成功
SELECT 
    m.id,
    m.pid,
    m.title,
    m.path,
    m.permission,
    m.order_no,
    CASE m.type 
        WHEN 1 THEN '目录'
        WHEN 2 THEN '菜单'
        WHEN 3 THEN '按钮'
        ELSE '未知'
    END as menu_type
FROM `sys_menu` m 
WHERE m.id LIKE 'mcp_%' OR m.pid LIKE 'mcp_%'
ORDER BY m.pid, m.order_no;
