-- 应用MCP服务参数配置 - 数据库迁移脚本
-- 在aigc_app表中添加MCP服务参数配置字段

-- 1. 添加MCP服务参数配置字段
ALTER TABLE `aigc_app` 
ADD COLUMN `mcp_service_params` longtext COMMENT 'MCP服务参数配置JSON' AFTER `mcp_service_ids`;

-- 2. 验证字段添加
DESCRIBE `aigc_app`;

-- 3. 查看现有应用数据
SELECT 
  `id`,
  `name`,
  `mcp_service_ids`,
  CASE 
    WHEN `mcp_service_params` IS NOT NULL THEN '已配置'
    ELSE '未配置'
  END as `参数配置状态`
FROM `aigc_app`
WHERE `mcp_service_ids` IS NOT NULL AND `mcp_service_ids` != '[]'
ORDER BY `create_time` DESC;

-- 4. 备注说明
/*
mcp_service_params字段说明：
- 存储应用中每个MCP服务的参数配置
- JSON格式：{"serviceId1": {"param1": "value1", "param2": "value2"}, "serviceId2": {...}}
- 当用户在应用中配置MCP服务参数时，参数值会保存到这个字段
- 在聊天时，系统会读取这些参数值并传递给MCP服务

示例数据结构：
{
  "service-123": {
    "base_url": "https://api.example.com",
    "api_key": "sk-1234567890abcdef",
    "timeout": 30
  },
  "service-456": {
    "search_engine": "google",
    "max_results": 10,
    "safe_search": true
  }
}
*/
