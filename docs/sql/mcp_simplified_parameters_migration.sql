-- MCP服务动态参数 - 简化方案数据库迁移脚本
-- 在现有的aigc_mcp_service表中添加参数相关字段

-- 1. 添加参数定义和参数值字段
ALTER TABLE `aigc_mcp_service` 
ADD COLUMN `parameters` longtext COMMENT '服务参数定义JSON' AFTER `tools`,
ADD COLUMN `parameter_values` longtext COMMENT '服务参数值JSON' AFTER `parameters`;

-- 2. 更新现有服务的参数定义示例

-- 内置API服务参数定义
UPDATE `aigc_mcp_service` 
SET `parameters` = '[
  {
    "name": "base_url",
    "displayName": "API基础URL",
    "description": "API服务的基础URL地址",
    "type": "string",
    "required": true,
    "sensitive": false,
    "group": "connection",
    "order": 1
  },
  {
    "name": "api_key",
    "displayName": "API密钥",
    "description": "用于认证的API密钥",
    "type": "string",
    "required": false,
    "sensitive": true,
    "group": "auth",
    "order": 2
  },
  {
    "name": "timeout",
    "displayName": "请求超时时间",
    "description": "HTTP请求的超时时间（秒）",
    "type": "number",
    "required": false,
    "defaultValue": 30,
    "group": "advanced",
    "order": 3
  },
  {
    "name": "max_retries",
    "displayName": "最大重试次数",
    "description": "请求失败时的最大重试次数",
    "type": "number",
    "required": false,
    "defaultValue": 3,
    "group": "advanced",
    "order": 4
  }
]'
WHERE `name` = 'builtin-api';

-- 内置搜索服务参数定义
UPDATE `aigc_mcp_service` 
SET `parameters` = '[
  {
    "name": "search_engine",
    "displayName": "搜索引擎",
    "description": "选择要使用的搜索引擎",
    "type": "string",
    "required": true,
    "defaultValue": "google",
    "group": "basic",
    "order": 1
  },
  {
    "name": "max_results",
    "displayName": "最大结果数",
    "description": "单次搜索返回的最大结果数量",
    "type": "number",
    "required": false,
    "defaultValue": 10,
    "group": "basic",
    "order": 2
  },
  {
    "name": "safe_search",
    "displayName": "安全搜索",
    "description": "是否启用安全搜索过滤",
    "type": "boolean",
    "required": false,
    "defaultValue": true,
    "group": "basic",
    "order": 3
  }
]'
WHERE `name` = 'builtin-search';

-- Wanx文生图服务参数定义
UPDATE `aigc_mcp_service` 
SET `parameters` = '[
  {
    "name": "api_key",
    "displayName": "DashScope API Key",
    "description": "阿里云DashScope服务的API密钥",
    "type": "string",
    "required": true,
    "sensitive": true,
    "group": "auth",
    "order": 1
  },
  {
    "name": "model",
    "displayName": "模型名称",
    "description": "使用的图像生成模型",
    "type": "string",
    "required": false,
    "defaultValue": "wanx-v1",
    "group": "basic",
    "order": 2
  },
  {
    "name": "size",
    "displayName": "图片尺寸",
    "description": "生成图片的尺寸",
    "type": "string",
    "required": false,
    "defaultValue": "1024*1024",
    "group": "basic",
    "order": 3
  },
  {
    "name": "n",
    "displayName": "生成数量",
    "description": "一次生成的图片数量",
    "type": "number",
    "required": false,
    "defaultValue": 1,
    "group": "basic",
    "order": 4
  }
]'
WHERE `name` = 'wanx-image-generation';

-- GitHub MCP服务参数定义
UPDATE `aigc_mcp_service` 
SET `parameters` = '[
  {
    "name": "github_token",
    "displayName": "GitHub Token",
    "description": "GitHub个人访问令牌",
    "type": "string",
    "required": true,
    "sensitive": true,
    "group": "auth",
    "order": 1
  },
  {
    "name": "default_owner",
    "displayName": "默认用户/组织",
    "description": "默认的GitHub用户名或组织名",
    "type": "string",
    "required": false,
    "group": "basic",
    "order": 2
  },
  {
    "name": "max_results",
    "displayName": "最大结果数",
    "description": "搜索时返回的最大结果数量",
    "type": "number",
    "required": false,
    "defaultValue": 20,
    "group": "basic",
    "order": 3
  }
]'
WHERE `name` = 'github-mcp';

-- 3. 设置默认参数值示例（用户可以在界面中修改）

-- 内置搜索服务默认参数值
UPDATE `aigc_mcp_service` 
SET `parameter_values` = '{
  "search_engine": "google",
  "max_results": 10,
  "safe_search": true
}'
WHERE `name` = 'builtin-search';

-- 4. 创建参数配置索引（可选，提升查询性能）
-- ALTER TABLE `aigc_mcp_service` ADD INDEX `idx_parameters` (`parameters`(255));

-- 5. 验证数据
SELECT 
  `name`,
  `display_name`,
  CASE 
    WHEN `parameters` IS NOT NULL THEN '已配置'
    ELSE '未配置'
  END as `参数定义状态`,
  CASE 
    WHEN `parameter_values` IS NOT NULL THEN '已配置'
    ELSE '未配置'
  END as `参数值状态`
FROM `aigc_mcp_service`
WHERE `enabled` = 1
ORDER BY `priority` DESC;

-- 6. 备注说明
/*
参数定义字段说明：
- name: 参数名称（英文，用于代码中引用）
- displayName: 显示名称（中文，用于界面显示）
- description: 参数描述
- type: 参数类型（string, number, boolean, object）
- required: 是否必填
- sensitive: 是否敏感信息（如密码、API Key）
- defaultValue: 默认值
- group: 参数分组（用于界面分组显示）
- order: 显示顺序

参数值字段说明：
- 存储用户配置的具体参数值
- JSON格式，key为参数名称，value为参数值
- 敏感信息在存储时会进行加密处理
*/
