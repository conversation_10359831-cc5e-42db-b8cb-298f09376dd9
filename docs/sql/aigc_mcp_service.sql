-- MCP服务管理表
CREATE TABLE `aigc_mcp_service` (
  `id` varchar(64) NOT NULL COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '服务名称',
  `display_name` varchar(200) DEFAULT NULL COMMENT '服务显示名称',
  `description` text COMMENT '服务描述',
  `endpoint` varchar(500) DEFAULT NULL COMMENT '服务端点URL',
  `version` varchar(50) DEFAULT NULL COMMENT '服务版本',
  `type` varchar(20) NOT NULL DEFAULT 'HTTP' COMMENT '服务类型：HTTP, WEBSOCKET, GRPC',
  `status` varchar(20) NOT NULL DEFAULT 'UNKNOWN' COMMENT '服务状态：ACTIVE, INACTIVE, ERROR, UNKNOWN',
  `category` varchar(20) NOT NULL DEFAULT 'external' COMMENT '服务分类：builtin(内置), external(外部)',
  `auth_type` varchar(20) DEFAULT 'none' COMMENT '认证类型：bearer, basic, api_key, none',
  `auth_config` text COMMENT '认证配置JSON',
  `config` text COMMENT '服务配置JSON',
  `tools` longtext COMMENT '支持的工具列表JSON',
  `icon` varchar(200) DEFAULT NULL COMMENT '服务图标',
  `tags` varchar(500) DEFAULT NULL COMMENT '服务标签',
  `priority` int DEFAULT '5' COMMENT '优先级',
  `enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `requires_confirmation` tinyint(1) DEFAULT '0' COMMENT '是否需要确认',
  `timeout` int DEFAULT '30' COMMENT '超时时间（秒）',
  `max_retries` int DEFAULT '3' COMMENT '最大重试次数',
  `health_check_url` varchar(500) DEFAULT NULL COMMENT '健康检查URL',
  `health_check_interval` int DEFAULT '30' COMMENT '健康检查间隔（秒）',
  `last_health_check` datetime DEFAULT NULL COMMENT '最后健康检查时间',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`),
  KEY `idx_category` (`category`),
  KEY `idx_status` (`status`),
  KEY `idx_enabled` (`enabled`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='MCP服务管理表';

-- 插入内置服务数据
INSERT INTO `aigc_mcp_service` (`id`, `name`, `display_name`, `description`, `endpoint`, `version`, `type`, `status`, `category`, `auth_type`, `tools`, `icon`, `tags`, `priority`, `enabled`, `timeout`, `max_retries`, `create_time`, `update_time`) VALUES
('builtin-search-001', 'builtin-search', '内置搜索服务', '提供基本的网络搜索功能', 'http://localhost:8080', '1.0.0', 'HTTP', 'ACTIVE', 'builtin', 'none', '[{"name":"web_search","description":"搜索网络信息","category":"search"}]', '🔍', 'search,builtin', 10, 1, 30, 3, NOW(), NOW()),
('builtin-email-001', 'builtin-email', '内置邮件服务', '提供电子邮件发送功能', 'http://localhost:8080', '1.0.0', 'HTTP', 'ACTIVE', 'builtin', 'none', '[{"name":"send_email","description":"发送电子邮件","category":"email"}]', '📧', 'email,builtin', 8, 1, 30, 3, NOW(), NOW()),
('builtin-file-001', 'builtin-file', '内置文件服务', '提供文件读写操作功能', 'http://localhost:8080', '1.0.0', 'HTTP', 'ACTIVE', 'builtin', 'none', '[{"name":"file_operation","description":"文件读写操作","category":"file"}]', '📁', 'file,builtin', 7, 1, 30, 3, NOW(), NOW()),
('builtin-api-001', 'builtin-api', '内置API服务', '提供HTTP请求调用功能', 'http://localhost:8080', '1.0.0', 'HTTP', 'ACTIVE', 'builtin', 'none', '[{"name":"http_request","description":"发送HTTP请求","category":"api"}]', '🌐', 'api,builtin', 6, 1, 30, 3, NOW(), NOW());

-- 插入外部服务数据
INSERT INTO `aigc_mcp_service` (`id`, `name`, `display_name`, `description`, `endpoint`, `version`, `type`, `status`, `category`, `auth_type`, `auth_config`, `tools`, `icon`, `tags`, `priority`, `enabled`, `timeout`, `max_retries`, `create_time`, `update_time`) VALUES
('wanx-image-001', 'wanx-image-generation', 'Wanx文生图', '阿里巴巴通义万相文生图服务', 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis', '1.0.0', 'HTTP', 'UNKNOWN', 'external', 'api_key', '{"type":"api_key","apiKey":"${DASHSCOPE_API_KEY}"}', '[{"name":"text_to_image","description":"根据文本描述生成图片","category":"image"}]', '🎨', 'image,ai,alibaba', 9, 0, 60, 3, NOW(), NOW()),
('sequential-thinking-001', 'sequential-thinking', 'Sequential Thinking', '序列化思维推理服务', 'http://localhost:3001', '1.0.0', 'HTTP', 'UNKNOWN', 'external', 'none', NULL, '[{"name":"sequential_think","description":"通过序列化思维过程解决复杂问题","category":"reasoning"}]', '🧠', 'reasoning,thinking', 8, 0, 30, 3, NOW(), NOW()),
('github-mcp-001', 'github-mcp', 'GitHub MCP', 'GitHub仓库搜索和管理服务', 'http://localhost:3002', '1.0.0', 'HTTP', 'UNKNOWN', 'external', 'bearer', '{"type":"bearer","token":"${GITHUB_TOKEN}"}', '[{"name":"search_repositories","description":"搜索GitHub仓库","category":"code"},{"name":"get_repository_info","description":"获取仓库详细信息","category":"code"}]', '🐙', 'github,code,git', 7, 0, 30, 3, NOW(), NOW()),
('brave-search-001', 'brave-search', 'Brave Search', 'Brave搜索引擎服务', 'https://api.search.brave.com/res/v1/web/search', '1.0.0', 'HTTP', 'UNKNOWN', 'external', 'api_key', '{"type":"api_key","apiKey":"${BRAVE_SEARCH_API_KEY}"}', '[{"name":"web_search","description":"使用Brave搜索引擎搜索网络内容","category":"search"}]', '🦁', 'search,brave,web', 6, 0, 30, 3, NOW(), NOW());

-- 创建MCP服务调用日志表
CREATE TABLE `aigc_mcp_service_log` (
  `id` varchar(64) NOT NULL COMMENT '主键ID',
  `service_id` varchar(64) NOT NULL COMMENT '服务ID',
  `service_name` varchar(100) NOT NULL COMMENT '服务名称',
  `tool_name` varchar(100) NOT NULL COMMENT '工具名称',
  `request_params` text COMMENT '请求参数JSON',
  `response_data` longtext COMMENT '响应数据JSON',
  `success` tinyint(1) NOT NULL COMMENT '是否成功',
  `error_message` text COMMENT '错误信息',
  `execution_time` int DEFAULT NULL COMMENT '执行时间（毫秒）',
  `user_id` varchar(64) DEFAULT NULL COMMENT '用户ID',
  `conversation_id` varchar(64) DEFAULT NULL COMMENT '对话ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_service_id` (`service_id`),
  KEY `idx_service_name` (`service_name`),
  KEY `idx_success` (`success`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='MCP服务调用日志表';
