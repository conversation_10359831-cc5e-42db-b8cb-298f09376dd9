-- 完整的MCP功能数据库迁移脚本
-- 包含MCP服务表和应用MCP配置字段

-- 1. 检查并添加aigc_app表的MCP相关字段
-- 检查mcp_service_ids字段是否存在
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'aigc_app'
    AND COLUMN_NAME = 'mcp_service_ids'
);

-- 如果mcp_service_ids字段不存在，则添加
SET @sql = IF(@column_exists = 0,
    'ALTER TABLE `aigc_app` ADD COLUMN `mcp_service_ids` longtext COMMENT ''MCP服务ID列表JSON'' AFTER `knowledge_ids`',
    'SELECT ''mcp_service_ids字段已存在'' as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查mcp_service_params字段是否存在
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'aigc_app'
    AND COLUMN_NAME = 'mcp_service_params'
);

-- 如果mcp_service_params字段不存在，则添加
SET @sql = IF(@column_exists = 0,
    'ALTER TABLE `aigc_app` ADD COLUMN `mcp_service_params` longtext COMMENT ''MCP服务参数配置JSON'' AFTER `mcp_service_ids`',
    'SELECT ''mcp_service_params字段已存在'' as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 检查并创建aigc_mcp_service表
CREATE TABLE IF NOT EXISTS `aigc_mcp_service` (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '服务名称',
  `display_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '显示名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '服务描述',
  `endpoint` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '服务端点',
  `version` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '1.0.0' COMMENT '服务版本',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT 'HTTP' COMMENT '服务类型',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT 'external' COMMENT '服务分类',
  `auth_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT 'none' COMMENT '认证类型',
  `auth_config` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '认证配置JSON',
  `icon` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '服务图标',
  `tags` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '标签',
  `priority` int DEFAULT 5 COMMENT '优先级',
  `enabled` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `requires_confirmation` tinyint(1) DEFAULT 0 COMMENT '是否需要确认',
  `timeout` int DEFAULT 30 COMMENT '超时时间(秒)',
  `max_retries` int DEFAULT 3 COMMENT '最大重试次数',
  `health_check_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '健康检查URL',
  `health_check_interval` int DEFAULT 30 COMMENT '健康检查间隔(秒)',
  `config` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '服务配置JSON',
  `tools` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '支持的工具列表JSON',
  `parameters` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '服务参数定义JSON',
  `parameter_values` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '服务参数值JSON',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_name` (`name`) USING BTREE,
  KEY `idx_category` (`category`) USING BTREE,
  KEY `idx_enabled` (`enabled`) USING BTREE,
  KEY `idx_priority` (`priority`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='MCP服务配置表';

-- 3. 插入内置MCP服务数据（如果不存在）
INSERT IGNORE INTO `aigc_mcp_service` (
  `id`, `name`, `display_name`, `description`, `category`, `type`, `enabled`, `priority`,
  `tools`, `parameters`, `parameter_values`, `create_time`
) VALUES 
(
  'builtin-search-001',
  'builtin-search',
  '内置搜索服务',
  '提供网络搜索功能',
  'builtin',
  'HTTP',
  1,
  10,
  '[{"name":"web_search","description":"网络搜索","parameters":{"query":{"type":"string","description":"搜索关键词","required":true},"count":{"type":"number","description":"结果数量","default":10}}}]',
  '[{"name":"search_engine","displayName":"搜索引擎","description":"选择搜索引擎","type":"string","required":false,"defaultValue":"google","group":"basic","order":1},{"name":"max_results","displayName":"最大结果数","description":"搜索返回的最大结果数量","type":"number","required":false,"defaultValue":10,"group":"basic","order":2},{"name":"safe_search","displayName":"安全搜索","description":"是否启用安全搜索过滤","type":"boolean","required":false,"defaultValue":true,"group":"basic","order":3}]',
  '{"search_engine":"google","max_results":10,"safe_search":true}',
  NOW()
),
(
  'builtin-api-001',
  'builtin-api',
  '内置API服务',
  '调用外部API接口',
  'builtin',
  'HTTP',
  1,
  10,
  '[{"name":"http_request","description":"HTTP请求","parameters":{"url":{"type":"string","description":"请求URL","required":true},"method":{"type":"string","description":"请求方法","default":"GET"},"headers":{"type":"object","description":"请求头"},"body":{"type":"string","description":"请求体"}}}]',
  '[{"name":"base_url","displayName":"API基础URL","description":"API服务的基础URL地址","type":"string","required":true,"sensitive":false,"group":"connection","order":1},{"name":"api_key","displayName":"API密钥","description":"用于认证的API密钥","type":"string","required":false,"sensitive":true,"group":"auth","order":2},{"name":"timeout","displayName":"请求超时时间","description":"HTTP请求的超时时间（秒）","type":"number","required":false,"defaultValue":30,"group":"advanced","order":3},{"name":"max_retries","displayName":"最大重试次数","description":"请求失败时的最大重试次数","type":"number","required":false,"defaultValue":3,"group":"advanced","order":4}]',
  NULL,
  NOW()
),
(
  'builtin-email-001',
  'builtin-email',
  '内置邮件服务',
  '发送邮件功能',
  'builtin',
  'SMTP',
  1,
  8,
  '[{"name":"send_email","description":"发送邮件","parameters":{"to":{"type":"string","description":"收件人","required":true},"subject":{"type":"string","description":"邮件主题","required":true},"content":{"type":"string","description":"邮件内容","required":true}}}]',
  '[{"name":"smtp_host","displayName":"SMTP服务器","description":"邮件服务器地址","type":"string","required":true,"group":"connection","order":1},{"name":"smtp_port","displayName":"SMTP端口","description":"邮件服务器端口","type":"number","required":true,"defaultValue":587,"group":"connection","order":2},{"name":"username","displayName":"用户名","description":"邮箱用户名","type":"string","required":true,"group":"auth","order":3},{"name":"password","displayName":"密码","description":"邮箱密码或授权码","type":"string","required":true,"sensitive":true,"group":"auth","order":4}]',
  NULL,
  NOW()
),
(
  'builtin-company-search-001',
  'builtin-company-search',
  '内置企业查询服务',
  '提供企业基本信息、财务状况、法律风险等多维度数据查询服务',
  'builtin',
  'BUILTIN',
  1,
  8,
  '[{"name":"company_search","description":"根据企业名称搜索企业基本信息","parameters":{"companyName":{"type":"string","description":"企业名称或关键词","required":true},"searchType":{"type":"string","description":"搜索类型","default":"fuzzy"},"region":{"type":"string","description":"查询地区","default":"全国"}}},{"name":"company_detail","description":"获取企业详细信息","parameters":{"companyId":{"type":"string","description":"企业统一社会信用代码","required":true},"infoType":{"type":"string","description":"信息类型","default":"basic"}}},{"name":"company_financial","description":"查询企业财务数据","parameters":{"companyId":{"type":"string","description":"企业统一社会信用代码","required":true},"year":{"type":"number","description":"查询年份","default":2023}}},{"name":"company_risk_assessment","description":"企业风险评估","parameters":{"companyId":{"type":"string","description":"企业统一社会信用代码","required":true},"riskTypes":{"type":"array","description":"风险类型","default":["legal","financial","operational"]}}}]',
  '[{"name":"companyName","displayName":"企业名称","description":"要查询的企业名称或关键词","type":"string","required":true,"sensitive":false,"defaultValue":null,"group":"basic","order":1,"validation":{"extractFromInput":true,"extractionPrompt":"请从用户输入中提取企业或公司的完整名称，包括有限公司、股份有限公司、集团等后缀。保留企业的完整全称。如果用户提到多个企业，返回第一个提到的企业名称。","keywords":["企业","公司","集团","有限公司","股份公司","查询","搜索","找","了解","调研","信息","资料","数据","详情","情况"],"pattern":"(?:查询|搜索|找|了解|调研)?\\\\s*(?:企业|公司|集团)?[:：]?\\\\s*([\\\\u4e00-\\\\u9fa5a-zA-Z0-9\\\\(\\\\)（）\\\\s]+?(?:有限公司|股份有限公司|有限责任公司|股份公司|集团|公司|企业))(?:的|信息|资料|数据|详情|情况)?","defaultValue":null}},{"name":"queryType","displayName":"查询类型","description":"要查询的信息类型","type":"string","required":false,"sensitive":false,"defaultValue":"basic","group":"basic","order":2,"validation":{"extractFromInput":true,"extractionPrompt":"请判断用户想要查询企业的什么类型信息。根据用户的描述返回以下之一：basic(基本信息)、financial(财务信息)、legal(法律信息)、risk(风险信息)、all(全部信息)。如果用户没有明确说明，返回basic。","keywords":["基本信息","基本","概况","简介","财务","财务信息","财务状况","财务数据","年报","财报","法律","法律信息","法律风险","诉讼","违法","处罚","风险","风险评估","风险信息","信用","评级","全部","所有","详细","完整"],"pattern":"(?:查询|了解|要|想要|需要)\\\\s*(?:企业|公司)?\\\\s*(?:的)?\\\\s*(基本信息|基本|概况|简介|财务|财务信息|财务状况|财务数据|年报|财报|法律|法律信息|法律风险|诉讼|违法|处罚|风险|风险评估|风险信息|信用|评级|全部|所有|详细|完整)(?:信息|数据|情况|状况)?","defaultValue":"basic"}},{"name":"searchScope","displayName":"搜索范围","description":"搜索的地理范围","type":"string","required":false,"sensitive":false,"defaultValue":"全国","group":"advanced","order":3,"validation":{"extractFromInput":true,"extractionPrompt":"请从用户输入中提取地理范围信息，如省份、城市等。如果没有明确提到地理范围，返回全国。","keywords":["北京","上海","广州","深圳","杭州","南京","成都","重庆","省","市","区","县","地区","全国","国内"],"pattern":"([\\\\u4e00-\\\\u9fa5]+(?:省|市|区|县|地区)?)(?:的|范围内|内)?","defaultValue":"全国"}},{"name":"resultLimit","displayName":"结果数量限制","description":"返回结果的最大数量","type":"number","required":false,"sensitive":false,"defaultValue":10,"group":"advanced","order":4,"validation":{"extractFromInput":true,"extractionPrompt":"请从用户输入中提取用户想要的结果数量。如果用户提到前几个、几家、几条等，提取对应的数字。如果没有明确说明，返回10。","keywords":["前","个","家","条","项","最多","限制","数量"],"pattern":"(?:前|最多|限制)?\\\\s*(\\\\d+)\\\\s*(?:个|家|条|项|家公司|个企业)","defaultValue":"10"}}]',
  NULL,
  NOW()
);

-- 4. 验证迁移结果
SELECT 'aigc_app表结构检查' as '检查项目';
DESCRIBE `aigc_app`;

SELECT 'aigc_mcp_service表记录检查' as '检查项目';
SELECT `id`, `name`, `display_name`, `category`, `enabled` FROM `aigc_mcp_service` ORDER BY `priority` DESC;

SELECT '迁移完成' as '状态', NOW() as '完成时间';
