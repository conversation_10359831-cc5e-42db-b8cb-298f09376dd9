/*
 Navicat Premium Data Transfer

 Source Server         : 智能AI平台
 Source Server Type    : MySQL
 Source Server Version : 80031
 Source Host           : ************:3306
 Source Schema         : jrzh_aigc

 Target Server Type    : MySQL
 Target Server Version : 80031
 File Encoding         : 65001

 Date: 13/05/2025 19:07:59
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for aigc_app
-- ----------------------------
DROP TABLE IF EXISTS `aigc_app`;
CREATE TABLE `aigc_app` (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `model_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '关联模型',
  `knowledge_ids` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '关联知识库',
  `cover` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '封面',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '名称',
  `prompt` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '提示词',
  `des` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '描述',
  `save_time` datetime DEFAULT NULL COMMENT '保存时间',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='提示词表';

-- ----------------------------
-- Records of aigc_app
-- ----------------------------
BEGIN;
INSERT INTO `aigc_app` (`id`, `model_id`, `knowledge_ids`, `cover`, `name`, `prompt`, `des`, `save_time`, `create_time`) VALUES ('9be4417e7529ed84a01e9ac574fcdd23', 'ac05866194643c5341a73d837b2017c8', '[]', NULL, '身份证OCR应用', '请从{image_data}精确提取以下字段，按示例格式输出纯JSON（无注释）：\n         必须输出完整JSON结构，缺失字段保留空字符串：\n         {\n         \"personal_infos\": [\n         {\n         \"name\": \"\",        // 姓名\n         \"gender\": \"\",      // 性别\n         \"ethnic\": \"\",      // 民族\n         \"birth_date\": \"\",  // 出生日期yyyy-MM-dd\n         \"id_number\": \"\",   // 身份证号码\n         \"residence\": \"\",   // 住址\n         \"mailing_address\": \"\",   // 通讯地址\n         \"issuing_authority\": \"\", // 签发机关\n         \"validity_period\": {     // 有效期限\n         \"start\": \"\",   \n         \"end\": \"\"\n         },\n         \"postal_code\": \"\"  // 邮政编码\n         }\n         ]\n         }', '身份证OCR应用', '2025-05-13 18:31:11', '2025-05-12 12:13:20');
INSERT INTO `aigc_app` (`id`, `model_id`, `knowledge_ids`, `cover`, `name`, `prompt`, `des`, `save_time`, `create_time`) VALUES ('e16a582b47d3041cf14074d5451dff7a', '0c21c2f8ebd3aa3757ef1bae81154cc4', '[\"393704ac13f67fde5da674ddd0742b03\"]', 'http://cdn.tycoding.cn/tycoding.jpg', 'LangChat官方应用', '你是一个专业的文档分析师，你擅长从文档中提取关键内容并总结分析含义，下面你需要根据用户的问题做出解答。\n\n## 限制\n不要回答和文档无关的内容', '快速解答LangChat项目相关的内容，LangChat官方助手', '2025-03-07 20:24:01', '2024-08-04 17:49:24');
INSERT INTO `aigc_app` (`id`, `model_id`, `knowledge_ids`, `cover`, `name`, `prompt`, `des`, `save_time`, `create_time`) VALUES ('ef50e8606c64180c8e8ecf3af67363e8', 'ac05866194643c5341a73d837b2017c8', '[\"311e15c8058c118cf0fd88f051458358\"]', 'http://127.0.0.1:8100/langchat/2025042268076b3622524337a2640ebd.jpg', '风控应用', '请根据以下财务数据和财务指标知识库，全面分析企业的财务状况，并生成一份详细的财务分析报告：\n\n财务数据：\n{financial_data}\n\n财务指标知识库：\n{knowledge}\n\n报告要求：\n1. 计算关键财务指标，包括但不限于流动比率、速动比率、资产负债率、毛利率、净利率等。\n2. 根据计算结果，评估企业的短期偿债能力、长期偿债能力、盈利能力和运营效率。\n3. 指出企业在财务健康方面存在的潜在问题或风险。\n4. 提供基于数据是否可以进行融资放款建议。\n5. 报告应结构清晰，包括摘要、详细分析、结论和建议等部分。\n\n请确保报告内容准确、专业，并基于提供的数据和知识库内容。', NULL, '2025-05-12 17:46:10', '2025-04-22 18:11:13');
COMMIT;

-- ----------------------------
-- Table structure for aigc_app_api
-- ----------------------------
DROP TABLE IF EXISTS `aigc_app_api`;
CREATE TABLE `aigc_app_api` (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `app_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '应用ID',
  `channel` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '应用渠道',
  `api_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'Key',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='应用';

-- ----------------------------
-- Records of aigc_app_api
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for aigc_conversation
-- ----------------------------
DROP TABLE IF EXISTS `aigc_conversation`;
CREATE TABLE `aigc_conversation` (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `user_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '用户ID',
  `prompt_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '提示词ID',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '标题',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='对话窗口表';

-- ----------------------------
-- Records of aigc_conversation
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for aigc_docs
-- ----------------------------
DROP TABLE IF EXISTS `aigc_docs`;
CREATE TABLE `aigc_docs` (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `knowledge_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '知识库ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '名称',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '类型',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `origin` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '来源',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '内容或链接',
  `size` int DEFAULT NULL COMMENT '文件大小',
  `slice_num` int DEFAULT NULL COMMENT '切片数量',
  `slice_status` tinyint(1) DEFAULT NULL COMMENT '切片状态',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='文档表';

-- ----------------------------
-- Records of aigc_docs
-- ----------------------------
BEGIN;
INSERT INTO `aigc_docs` (`id`, `knowledge_id`, `name`, `type`, `url`, `origin`, `content`, `size`, `slice_num`, `slice_status`, `create_time`) VALUES ('0540a75a46e3ae8a81d02ad0c5c9bb7a', '5324bdc7b88a1ea5c01d2878b10d0461', 'B2B交易系统产品文档.docx', 'UPLOAD', 'http://127.0.0.1:8100/langchat/2025030767cabc8f8a5204f581136867.docx', NULL, NULL, 22580, 8, 1, '2025-03-07 17:29:51');
INSERT INTO `aigc_docs` (`id`, `knowledge_id`, `name`, `type`, `url`, `origin`, `content`, `size`, `slice_num`, `slice_status`, `create_time`) VALUES ('0c45ad15fc3cc4791a1157a1b19bbcb1', '918f6399b7d0278c33a0affbb201a481', 'B2B交易系统产品文档.docx', 'UPLOAD', 'http://127.0.0.1:8100/langchat/2025030867cbae418a529490d99e0ca9.docx', NULL, NULL, 22580, 8, 1, '2025-03-08 10:41:06');
INSERT INTO `aigc_docs` (`id`, `knowledge_id`, `name`, `type`, `url`, `origin`, `content`, `size`, `slice_num`, `slice_status`, `create_time`) VALUES ('1c17f8aaf048ef86a8d691d4d1b6830e', '311e15c8058c118cf0fd88f051458358', '财务数据指标.pdf', 'UPLOAD', 'http://127.0.0.1:8100/langchat/2025042268072f1612522b7b67605b71.pdf', NULL, NULL, 95537, 4, 1, '2025-04-22 13:54:30');
INSERT INTO `aigc_docs` (`id`, `knowledge_id`, `name`, `type`, `url`, `origin`, `content`, `size`, `slice_num`, `slice_status`, `create_time`) VALUES ('2bff6278a4a9a13dafa56131239930eb', '5324bdc7b88a1ea5c01d2878b10d0461', 'B2B交易系统产品文档.docx', 'UPLOAD', 'http://127.0.0.1:8100/langchat/2025030767cad6a18a52eabc42a26577.docx', NULL, NULL, 22580, 8, 1, '2025-03-07 19:21:06');
INSERT INTO `aigc_docs` (`id`, `knowledge_id`, `name`, `type`, `url`, `origin`, `content`, `size`, `slice_num`, `slice_status`, `create_time`) VALUES ('2ec9977819eeb9f7eeabdb6c7f0aaee2', '918f6399b7d0278c33a0affbb201a481', 'B2B交易系统产品文档.docx', 'UPLOAD', 'http://127.0.0.1:8100/langchat/2025030867cbabb38a529490d99e0ca7.docx', NULL, NULL, 22580, 8, 1, '2025-03-08 10:30:11');
INSERT INTO `aigc_docs` (`id`, `knowledge_id`, `name`, `type`, `url`, `origin`, `content`, `size`, `slice_num`, `slice_status`, `create_time`) VALUES ('31924ac7113f41d176b3c2a573ad5c84', '5324bdc7b88a1ea5c01d2878b10d0461', 'B2B交易系统产品文档.docx', 'UPLOAD', 'http://127.0.0.1:8100/langchat/2025030767cad6ea8a52eabc42a2657a.docx', NULL, NULL, 22580, 8, 1, '2025-03-07 19:22:19');
INSERT INTO `aigc_docs` (`id`, `knowledge_id`, `name`, `type`, `url`, `origin`, `content`, `size`, `slice_num`, `slice_status`, `create_time`) VALUES ('3275c6be019aba229baa0a4a09ffbb0e', '311e15c8058c118cf0fd88f051458358', '财务数据指标.pdf', 'UPLOAD', 'http://127.0.0.1:8100/langchat/2025042268073ed722522b1a675b39ba.pdf', NULL, NULL, 95537, 4, 1, '2025-04-22 15:01:44');
INSERT INTO `aigc_docs` (`id`, `knowledge_id`, `name`, `type`, `url`, `origin`, `content`, `size`, `slice_num`, `slice_status`, `create_time`) VALUES ('354d9bfb76f3408a6263dff5ce32bb36', '918f6399b7d0278c33a0affbb201a481', 'B2B交易系统产品文档.docx', 'UPLOAD', 'http://127.0.0.1:8100/langchat/2025030867cbace88a529490d99e0ca8.docx', NULL, NULL, 22580, 8, 1, '2025-03-08 10:35:21');
INSERT INTO `aigc_docs` (`id`, `knowledge_id`, `name`, `type`, `url`, `origin`, `content`, `size`, `slice_num`, `slice_status`, `create_time`) VALUES ('51ae6d7356eec12b30dceb7975846c4e', '393704ac13f67fde5da674ddd0742b03', 'story-about-happy-carrot.pdf', 'UPLOAD', NULL, NULL, NULL, 35359, NULL, 0, '2024-08-08 17:02:41');
INSERT INTO `aigc_docs` (`id`, `knowledge_id`, `name`, `type`, `url`, `origin`, `content`, `size`, `slice_num`, `slice_status`, `create_time`) VALUES ('5abd4ecc9e6ec783a75af8a53411751f', 'f2b593661ba4dfc2c7657c636d5b1be9', '副本元鼎供应链协同系统技术方案.docx', 'UPLOAD', 'http://127.0.0.1:8100/langchat/2025040867f4dbcfaa528ddd53ec2499.docx', NULL, NULL, 391175, 0, 1, '2025-04-08 16:18:24');
INSERT INTO `aigc_docs` (`id`, `knowledge_id`, `name`, `type`, `url`, `origin`, `content`, `size`, `slice_num`, `slice_status`, `create_time`) VALUES ('5f2c704a82a8548fe5a43c4a78cd71f4', '5324bdc7b88a1ea5c01d2878b10d0461', 'B2B交易系统产品文档.docx', 'UPLOAD', 'http://127.0.0.1:8100/langchat/2025030767cae2168a524b997bebd037.docx', NULL, NULL, 22580, 8, 1, '2025-03-07 20:09:59');
INSERT INTO `aigc_docs` (`id`, `knowledge_id`, `name`, `type`, `url`, `origin`, `content`, `size`, `slice_num`, `slice_status`, `create_time`) VALUES ('658755f8518fb05556008fbfb6dc936a', 'f2b593661ba4dfc2c7657c636d5b1be9', '副本元鼎供应链协同系统技术方案.docx', 'UPLOAD', 'http://127.0.0.1:8100/langchat/2025040867f4dc31aa528ddd53ec249c.docx', NULL, NULL, 391175, 0, 1, '2025-04-08 16:20:01');
INSERT INTO `aigc_docs` (`id`, `knowledge_id`, `name`, `type`, `url`, `origin`, `content`, `size`, `slice_num`, `slice_status`, `create_time`) VALUES ('66059cca32c3fafb62905a707b4042c8', '5324bdc7b88a1ea5c01d2878b10d0461', 'B2B交易系统产品文档.docx', 'UPLOAD', 'http://127.0.0.1:8100/langchat/2025030767cad6b88a52eabc42a26578.docx', NULL, NULL, 22580, 8, 1, '2025-03-07 19:21:28');
INSERT INTO `aigc_docs` (`id`, `knowledge_id`, `name`, `type`, `url`, `origin`, `content`, `size`, `slice_num`, `slice_status`, `create_time`) VALUES ('7f74b072f7cdd0b68b992f6717120b60', 'f2b593661ba4dfc2c7657c636d5b1be9', '副本元鼎供应链协同系统技术方案.docx', 'UPLOAD', 'http://127.0.0.1:8100/langchat/2025040867f4dc5daa528ddd53ec249d.docx', NULL, NULL, 391175, 0, 1, '2025-04-08 16:20:46');
INSERT INTO `aigc_docs` (`id`, `knowledge_id`, `name`, `type`, `url`, `origin`, `content`, `size`, `slice_num`, `slice_status`, `create_time`) VALUES ('8933fc0e6b449a153adc1789a4e1781c', '393704ac13f67fde5da674ddd0742b03', 'guide1', 'INPUT', NULL, NULL, 'LangChat 是一个基于Java生态的企业AI知识库和大模型应用解决方案，帮助企业快速搭建AI大模型应用。 同时，LangChat也集成了RBAC权限体系，为企业提供开箱即用的AI大模型产品解决方案。\n\nLangChat 使用Java生态，前后端分离，并采用最新的技术栈开发。后端基于SpringBoot3，前端基于Vue3。 LangChat不仅为企业提供AI领域的产品解决方案，也是一个完整的Java企业级应用案例。这个系统带你全面了解SpringBoot3和Vue3的前后端开发流程、业务模块化，以及AI应用集成方案。 无论是企业开发，还是个人学习，LangChat都为你提供丰富的学习案例', NULL, 1, 1, '2024-08-04 18:18:46');
INSERT INTO `aigc_docs` (`id`, `knowledge_id`, `name`, `type`, `url`, `origin`, `content`, `size`, `slice_num`, `slice_status`, `create_time`) VALUES ('8ece11892d6ff0e4b0023d8c71dde027', '918f6399b7d0278c33a0affbb201a481', 'B2B交易系统产品文档.docx', 'UPLOAD', 'http://127.0.0.1:8100/langchat/2025030867cbae518a529490d99e0caa.docx', NULL, NULL, 22580, 8, 1, '2025-03-08 10:41:21');
INSERT INTO `aigc_docs` (`id`, `knowledge_id`, `name`, `type`, `url`, `origin`, `content`, `size`, `slice_num`, `slice_status`, `create_time`) VALUES ('95439d1b557864321c29ea700823ba6c', '2048ae8b18d76621ddfa7f5ca309e6aa', '副本元鼎供应链协同系统技术方案.docx', 'UPLOAD', 'http://127.0.0.1:8100/langchat/2025040867f4dc13aa528ddd53ec249b.docx', NULL, NULL, 391175, 17, 1, '2025-04-08 16:19:31');
INSERT INTO `aigc_docs` (`id`, `knowledge_id`, `name`, `type`, `url`, `origin`, `content`, `size`, `slice_num`, `slice_status`, `create_time`) VALUES ('ae21afee18f5c05572ef10c710a70fb9', '311e15c8058c118cf0fd88f051458358', '财务数据指标.pdf', 'UPLOAD', 'http://127.0.0.1:8100/langchat/2025042268073bc322522b1a675b39b9.pdf', NULL, NULL, 95537, 4, 1, '2025-04-22 14:48:35');
INSERT INTO `aigc_docs` (`id`, `knowledge_id`, `name`, `type`, `url`, `origin`, `content`, `size`, `slice_num`, `slice_status`, `create_time`) VALUES ('af98cb1d598a57e8f44a42ab13a7798d', 'f2b593661ba4dfc2c7657c636d5b1be9', '副本元鼎供应链协同系统技术方案.docx', 'UPLOAD', 'http://127.0.0.1:8100/langchat/2025040867f4dce7aa528ddd53ec249e.docx', NULL, NULL, 391175, 0, 1, '2025-04-08 16:23:03');
INSERT INTO `aigc_docs` (`id`, `knowledge_id`, `name`, `type`, `url`, `origin`, `content`, `size`, `slice_num`, `slice_status`, `create_time`) VALUES ('b4db970826417986525f08579dbaf000', 'f2b593661ba4dfc2c7657c636d5b1be9', '副本元鼎供应链协同系统技术方案.docx', 'UPLOAD', 'http://127.0.0.1:8100/langchat/2025040867f4df1faa526e8ab128f7b8.docx', NULL, NULL, 391175, 0, 1, '2025-04-08 16:32:31');
INSERT INTO `aigc_docs` (`id`, `knowledge_id`, `name`, `type`, `url`, `origin`, `content`, `size`, `slice_num`, `slice_status`, `create_time`) VALUES ('c016395611390bab37bddaf7aae1d9a5', '5324bdc7b88a1ea5c01d2878b10d0461', 'B2B交易系统产品文档.docx', 'UPLOAD', 'http://127.0.0.1:8100/langchat/2025030767cad7108a524b997bebd036.docx', NULL, NULL, 22580, 8, 1, '2025-03-07 19:23:25');
INSERT INTO `aigc_docs` (`id`, `knowledge_id`, `name`, `type`, `url`, `origin`, `content`, `size`, `slice_num`, `slice_status`, `create_time`) VALUES ('d2451102b9066b4d468bf6531a2cd002', '05d871aa19bb79ec72ce84848f31d8c8', 'B2B交易系统产品文档.docx', 'UPLOAD', 'http://127.0.0.1:8100/langchat/2025030867cbf6fc8a52450b6ffb8b13.docx', NULL, NULL, 22580, 8, 1, '2025-03-08 15:51:24');
INSERT INTO `aigc_docs` (`id`, `knowledge_id`, `name`, `type`, `url`, `origin`, `content`, `size`, `slice_num`, `slice_status`, `create_time`) VALUES ('d490723a1e138ef6446c9780de737922', '5324bdc7b88a1ea5c01d2878b10d0461', 'B2B交易系统产品文档.docx', 'UPLOAD', 'http://127.0.0.1:8100/langchat/2025030767cae70a8a524b997bebd039.docx', NULL, NULL, 22580, 8, 1, '2025-03-07 20:31:06');
INSERT INTO `aigc_docs` (`id`, `knowledge_id`, `name`, `type`, `url`, `origin`, `content`, `size`, `slice_num`, `slice_status`, `create_time`) VALUES ('dafd284d9358c69723275e9700b85a8a', '5324bdc7b88a1ea5c01d2878b10d0461', 'B2B交易系统产品文档.docx', 'UPLOAD', 'http://127.0.0.1:8100/langchat/2025030767cacb808a52c1a2d66a08d7.docx', NULL, NULL, 22580, 8, 1, '2025-03-07 18:33:37');
INSERT INTO `aigc_docs` (`id`, `knowledge_id`, `name`, `type`, `url`, `origin`, `content`, `size`, `slice_num`, `slice_status`, `create_time`) VALUES ('ec0c960461a615bb7c7648d7ee5801b5', '393704ac13f67fde5da674ddd0742b03', 'story-about-happy-carrot.pdf', 'UPLOAD', 'http://127.0.0.1/langchat/2024080866b4b069cdb262aeea8da409.pdf', NULL, NULL, 35359, 37, 1, '2024-08-08 19:47:54');
INSERT INTO `aigc_docs` (`id`, `knowledge_id`, `name`, `type`, `url`, `origin`, `content`, `size`, `slice_num`, `slice_status`, `create_time`) VALUES ('f12ba086fdc65b88708516a7c0b388a0', '5324bdc7b88a1ea5c01d2878b10d0461', 'B2B交易系统产品文档.docx', 'UPLOAD', 'http://127.0.0.1:8100/langchat/2025030767cae2f98a524b997bebd038.docx', NULL, NULL, 22580, 8, 1, '2025-03-07 20:13:46');
INSERT INTO `aigc_docs` (`id`, `knowledge_id`, `name`, `type`, `url`, `origin`, `content`, `size`, `slice_num`, `slice_status`, `create_time`) VALUES ('f19396ef3639b20c11866b73b48d8909', 'f2b593661ba4dfc2c7657c636d5b1be9', 'B2B交易系统产品文档.docx', 'UPLOAD', 'http://127.0.0.1:8100/langchat/2025040867f4dbefaa528ddd53ec249a.docx', NULL, NULL, 22580, 0, 1, '2025-04-08 16:18:55');
INSERT INTO `aigc_docs` (`id`, `knowledge_id`, `name`, `type`, `url`, `origin`, `content`, `size`, `slice_num`, `slice_status`, `create_time`) VALUES ('f3268cc12828add284c03f7140aae4a9', '5324bdc7b88a1ea5c01d2878b10d0461', 'B2B交易系统产品文档.docx', 'UPLOAD', 'http://127.0.0.1:8100/langchat/2025030767cad6d18a52eabc42a26579.docx', NULL, NULL, 22580, 8, 1, '2025-03-07 19:21:54');
INSERT INTO `aigc_docs` (`id`, `knowledge_id`, `name`, `type`, `url`, `origin`, `content`, `size`, `slice_num`, `slice_status`, `create_time`) VALUES ('f4a465ea6bfc25c34707f1e132356192', '393704ac13f67fde5da674ddd0742b03', 'story-about-happy-carrot.pdf', 'UPLOAD', NULL, NULL, NULL, 35359, NULL, 0, '2024-08-06 22:57:32');
INSERT INTO `aigc_docs` (`id`, `knowledge_id`, `name`, `type`, `url`, `origin`, `content`, `size`, `slice_num`, `slice_status`, `create_time`) VALUES ('f5f5e6ed42992d3f6bed40822ea74a58', '2048ae8b18d76621ddfa7f5ca309e6aa', '未命名.txt', 'UPLOAD', 'http://127.0.0.1:8100/langchat/2025030867cbfaef8a5242a2cac81f4b.txt', NULL, NULL, 6, 1, 1, '2025-03-08 16:08:16');
COMMIT;

-- ----------------------------
-- Table structure for aigc_docs_slice
-- ----------------------------
DROP TABLE IF EXISTS `aigc_docs_slice`;
CREATE TABLE `aigc_docs_slice` (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `vector_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '向量库的ID',
  `docs_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '文档ID',
  `knowledge_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '知识库ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '文档名称',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '切片内容',
  `word_num` int DEFAULT NULL COMMENT '字符数',
  `status` tinyint(1) DEFAULT NULL COMMENT '状态',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='文档切片表';

-- ----------------------------
-- Records of aigc_docs_slice
-- ----------------------------
BEGIN;
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('037c05510f7784a76f6e3dc61171c569', '86fe6170-a81a-4bf6-890c-597fb7d90b4b', '3275c6be019aba229baa0a4a09ffbb0e', '311e15c8058c118cf0fd88f051458358', '财务数据指标.pdf', ' 利息保障倍数（Interest Coverage Ratio）\n\n（净利润+利息费用+所得税费用 ）/ 利息费用，衡量偿还利息的能\n\n力（≥3 倍较安全，否则标红）。\n\n3. 盈利能力（持续经营能力）\n\n 毛利率（Gross Profit Margin）\n\n（营业收入-营业成本）/ 营业收入，反映核心业务盈利能力。\n\n 净利率（Net Profit Margin）\n\n（利润总额-所得税费用）/ 营业收入，综合盈利能力指标。\n\n 净资产收益率（ROE）\n\n（利润总额-所得税费用） / （资产总计-负债合计），衡量资本利用\n\n效率（需与行业对比）。\n\n4. 运营效率（供应链稳定性）', 297, 1, '2025-04-22 15:41:55');
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('065fe25fdd83897b117a10ae683e6048', 'e480112c-fe8a-4372-8caf-c171756e9771', '95439d1b557864321c29ea700823ba6c', '2048ae8b18d76621ddfa7f5ca309e6aa', '副本元鼎供应链协同系统技术方案.docx', '如ERP有管理，则从ERP同步供应商的应付账款清单，同步策略分为定时同步和手动同步。\n否则需要提供应付账款的解决方案。\n7. 工程资料管理\n· BOM管理\n· BOM数据同步\n手动或者自动从ERP同步BOM数据。\n· BOM版本管理\n针对BOM进行版本管理。\n· 图纸文档管理\n设计图纸等文档与BOM绑定，并上传。\n· BOM与SKU协同\n建立或同步SKU，而后部分BOM数据与SKU绑定，解决BOM与SKU不关联的问题。\n8. 代工厂管理\n· 委外订单管理\n· 生产工单\n向指定供应商下发生产工单，另外可以分解、合并生产工单。\n· 生产跟踪\n· 备料管理', 278, 1, '2025-04-08 16:19:36');
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('1207744d24c0d21f192b64b961258cce', 'b1f19afd-a870-45e6-8d8b-4b201d0d64ff', '1c17f8aaf048ef86a8d691d4d1b6830e', '311e15c8058c118cf0fd88f051458358', '财务数据指标.pdf', ' 利息保障倍数（Interest Coverage Ratio）\n\n（净利润+利息费用+所得税费用 ）/ 利息费用，衡量偿还利息的能\n\n力（≥3 倍较安全，否则标红）。\n\n3. 盈利能力（持续经营能力）\n\n 毛利率（Gross Profit Margin）\n\n（营业收入-营业成本）/ 营业收入，反映核心业务盈利能力。\n\n 净利率（Net Profit Margin）\n\n（利润总额-所得税费用）/ 营业收入，综合盈利能力指标。\n\n 净资产收益率（ROE）\n\n（利润总额-所得税费用） / （资产总计-负债合计），衡量资本利用\n\n效率（需与行业对比）。\n\n4. 运营效率（供应链稳定性）', 297, 1, '2025-04-22 13:54:32');
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('1f99c001af974891ea77050cbe5a8fcf', '144d9880-921d-419a-81ef-b3736b637f0f', '95439d1b557864321c29ea700823ba6c', '2048ae8b18d76621ddfa7f5ca309e6aa', '副本元鼎供应链协同系统技术方案.docx', '6. 采购订单协同管理\n· 采购订单同步\n代工厂的采购订单-导进来。\n· 送货单开具\n供应商确认采购订单后出具送货单，并在系统中查看送货单情况。\n· 采购订单执行分析\n交货达成率（OTD）、交货准时率、交货数量准确率、验收合格率等，其余可在下一步沟通中进行深入讨论。\n7. 首页\n需要在深入讨论之后得出首页需要提取、展示哪些数据。\n供应商端\n1. 报价\n该模块在接收到元鼎、代工厂询价单以后，向元鼎、代工厂提交报价信息。\n2. 工装模具管理\n· 工装模具清单\n展示从元鼎领用的工装模具的清单。\n· 工装模具保养\n上传工装模具保养的结果。\n· 工装模具保养\n上传工装模具盘点的结果。\n3. 采购单确认', 299, 1, '2025-04-08 16:19:36');
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('215a1c1a914592661b9a08c71d03e495', '22951481-11aa-4b3c-a893-262516f9ceed', 'ae21afee18f5c05572ef10c710a70fb9', '311e15c8058c118cf0fd88f051458358', '财务数据指标.pdf', '经营活动产生的现金流量净额 -资本支出，其中，资本支出=购建固\n\n定资产、无形资产和其他长期资产所支付的现金-处置固定资产、无\n\n形资产和其他长期资产所收回的现金净额，衡量可自由支配的现金流。\n\n6. 规模与增长潜力\n\n 营业收入增长率\n\n营业收入增长率=当期营业收入/去年同期营业收入-1。\n\n 净利润增长率\n\n净利润增长率=当期净利润/去年同期净利润-1\n\n是否与营收同步增长，避免“增收不增利”。', 203, 1, '2025-04-22 14:48:39');
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('2b8fba89e4ae0198a03f31a7d80065cb', '02a9f938-c6a3-4fdf-becd-cdb8102c27fe', 'f5f5e6ed42992d3f6bed40822ea74a58', '2048ae8b18d76621ddfa7f5ca309e6aa', '未命名.txt', '卖家', 2, 1, '2025-03-08 16:08:16');
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('324549dae79b01638341e926f67669be', '8a725c1e-acc1-4f21-9c65-3501007fec60', '1c17f8aaf048ef86a8d691d4d1b6830e', '311e15c8058c118cf0fd88f051458358', '财务数据指标.pdf', '经营活动产生的现金流量净额 -资本支出，其中，资本支出=购建固\n\n定资产、无形资产和其他长期资产所支付的现金-处置固定资产、无\n\n形资产和其他长期资产所收回的现金净额，衡量可自由支配的现金流。\n\n6. 规模与增长潜力\n\n 营业收入增长率\n\n营业收入增长率=当期营业收入/去年同期营业收入-1。\n\n 净利润增长率\n\n净利润增长率=当期净利润/去年同期净利润-1\n\n是否与营收同步增长，避免“增收不增利”。', 203, 1, '2025-04-22 13:54:32');
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('33a1e75b8f89437fe2a1117bfd090465', 'f03341b5-3844-4d42-b55e-6394709a0528', 'ae21afee18f5c05572ef10c710a70fb9', '311e15c8058c118cf0fd88f051458358', '财务数据指标.pdf', ' 利息保障倍数（Interest Coverage Ratio）\n\n（净利润+利息费用+所得税费用 ）/ 利息费用，衡量偿还利息的能\n\n力（≥3 倍较安全，否则标红）。\n\n3. 盈利能力（持续经营能力）\n\n 毛利率（Gross Profit Margin）\n\n（营业收入-营业成本）/ 营业收入，反映核心业务盈利能力。\n\n 净利率（Net Profit Margin）\n\n（利润总额-所得税费用）/ 营业收入，综合盈利能力指标。\n\n 净资产收益率（ROE）\n\n（利润总额-所得税费用） / （资产总计-负债合计），衡量资本利用\n\n效率（需与行业对比）。\n\n4. 运营效率（供应链稳定性）', 297, 1, '2025-04-22 14:48:39');
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('3db1d24df03c96722246798dd9deac83', '69d40104-c7a6-43d9-8c3a-1c1b5be97d27', '95439d1b557864321c29ea700823ba6c', '2048ae8b18d76621ddfa7f5ca309e6aa', '副本元鼎供应链协同系统技术方案.docx', '业务在线化：打通ERP、生产系统数据壁垒，实现销售订单、工单分解、采购协同等全流程在线流转。\n协同透明化：通过多角色（供应商、代工厂）账号体系与权限隔离，确保关键节点数据实时可视，提升跨组织协作效率。\n决策智能化：基于订单完成率、交货达成率、模具健康度等核心指标的多维分析，为供应链优化提供数据支撑。\n管理标准化：固化模具保养、生产检验、BOM版本管理等业务流程规则，降低人为操作风险。\n\n本系统的建设将显著提升元鼎智能在复杂供应链环境下的敏捷响应能力，助力其在新一轮产业升级中巩固核心竞争力，为全球化业务拓展奠定数字化基石。\n2、 功能分解图', 272, 1, '2025-04-08 16:19:36');
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('449a3757b46d51cc113f2d945b40b1d3', '47a9443b-a9c2-4f73-998c-b64727ae765f', 'd2451102b9066b4d468bf6531a2cd002', '05d871aa19bb79ec72ce84848f31d8c8', 'B2B交易系统产品文档.docx', '3.3 供应商端\n核心功能\n· \n供应链协同\n· \n2. \n实时接收卖家采购需求\n2. \n2. \n库存水位预警与自动补货建议\n2. \n· \n物流管理\n· \n4. \n多仓库发货优先级设置\n4. \n4. \n物流时效承诺（T+1/T+3）\n4. \n· \n对账结算\n· \n6. \n与卖家的账单自动核对\n6. \n6. \n结算周期配置（月结/货到付款）\n6. \n集成需求\n· \n支持通过EDI/API与卖家ERP系统对接\n·', 206, 1, '2025-03-08 15:51:25');
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('4be93f5ea1a457e0c7a987541c3cc984', '51fcd296-733f-4a26-bf31-fcb322bfb64d', '95439d1b557864321c29ea700823ba6c', '2048ae8b18d76621ddfa7f5ca309e6aa', '副本元鼎供应链协同系统技术方案.docx', '在供应商未完成审核之前针对供应商的基础数据进行录入。部分数据在供应商完成审核之后进行补录。\n如ERP对供应商进行了管理，那么此系统应当是拉取或者写入。待确认。\n· 供应商审核\n在供应商数据录入完成之后进行集团内部流程化审批。\n· 供应商账号管理\n在供应商完成审核之后，为供应商创建指定的供应商账号。而后才可以参与询价、报价等操作。\n· 价格管理\n· 询价\n针对产品向供应商发起询价，发起之后供应商通过消息提醒收到询价通知。\n· 比价\n在供应商报价之后针对指定的询价内容，进行价格对比。\n· 价目表\n查询供应商的报价价目清单。\n· 应付账款管理', 271, 1, '2025-04-08 16:19:36');
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('4d3016e85fcc3800756ef2bfba6b8749', '2929300f-9d9e-489c-9d81-26ac8a3b0a23', 'd2451102b9066b4d468bf6531a2cd002', '05d871aa19bb79ec72ce84848f31d8c8', 'B2B交易系统产品文档.docx', '安全\n	符合等保三级，敏感数据加密存储\n\n扩展性\n	支持插件化扩展（如新增支付方式）\n\n兼容性\n	适配主流浏览器及iOS/Android系统\n\n5. 附录\n· \n术语表（如：RFQ、SKU、EDI等）\n· \n· \n版本迭代计划（分阶段上线核心功能）\n· \n· \n接口文档索引（指向具体API文档链接）\n·\n\n备注：实际文档需补充以下内容：\n1. \n详细的业务流程泳道图\n2. \n3. \n各功能点的原型图或UI设计稿链接\n4. \n5. \n数据字典及表结构设计\n6. \n7. \n测试用例与验收标准\n8. \n可根据团队需求扩展或精简部分模块。', 268, 1, '2025-03-08 15:51:25');
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('4d9aa3852d70fe8bee448e9cf0a564e9', '8e5f2b90-231a-49f1-b00c-7c85da846ba4', 'd2451102b9066b4d468bf6531a2cd002', '05d871aa19bb79ec72ce84848f31d8c8', 'B2B交易系统产品文档.docx', 'B2B交易系统产品文档\n1. 系统概述\n1.1 目标与定位\n目标：为企业用户提供高效、安全、可扩展的在线交易平台，支持大宗商品采购、供应链协同、订单管理、支付结算等功能。\n定位：服务于制造业、批发零售、跨境贸易等行业的B2B交易场景，连接买家、卖家、供应商与平台方。\n· \n1.2 用户角色\n	角色\n	描述\n\n买家\n	企业采购方，发起询价、下单、管理合同及支付\n\n卖家\n	直接销售商品/服务的企业，管理商品、订单及客户关系\n\n供应商\n	为卖家提供原材料或商品的供应链上游企业，支持库存同步与物流协同\n\n平台方\n	系统管理员，负责用户审核、规则配置、纠纷仲裁及数据分析', 283, 1, '2025-03-08 15:51:25');
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('4eb7ba79911df1dca3efda26a8361241', '80fb7bc9-b650-4a2f-ab60-c5188ccf7217', '95439d1b557864321c29ea700823ba6c', '2048ae8b18d76621ddfa7f5ca309e6aa', '副本元鼎供应链协同系统技术方案.docx', '元鼎智能供应链协同系统技术方案\n\n版本号：V1.0.0\n开发人：张建民\n最后更新日期：2025/02/24\n\n甲方：深圳市元鼎智能创新有限公司\n乙方：东莞市东智创新科技有限公司\n\n1、 项目背景\n在全球制造业智能化转型与供应链协同升级的背景下，深圳市元鼎智能创新有限公司作为一家专注于智能硬件研发与生产的高新技术企业，面临供应链管理复杂度持续攀升的挑战。随着业务规模的扩大及全球化布局的深化，现有供应链体系在跨企业协同、数据实时互通、流程可视化等方面逐渐暴露出以下核心问题：', 237, 1, '2025-04-08 16:19:36');
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('578a40d8994f9d56d383528e50db6f85', 'f5bea21e-f5d7-4a7d-91ed-d3b00bbf7555', '95439d1b557864321c29ea700823ba6c', '2048ae8b18d76621ddfa7f5ca309e6aa', '副本元鼎供应链协同系统技术方案.docx', '信息孤岛与流程割裂\n供应链上下游（供应商、代工厂、内部生产部门）数据分散于不同系统（如ERP、生产管理系统等），信息传递依赖人工导出导入，导致订单状态、库存数据、生产进度等关键信息滞后，影响决策效率与执行准确性。\n\n协同效率低下\n工装模具管理、采购订单执行、生产工单跟踪等环节缺乏统一平台支撑，代工厂与供应商的协作依赖线下沟通，流程审批、异常反馈响应周期长，难以满足快速交付需求。\n\n生产质量与追溯能力不足\n生产过程中的备料、投料、检验数据未能实现全链路数字化，品质异常处理依赖事后追溯，测试数据与BOM版本管理松散，影响产品一致性及质量风险管控。', 274, 1, '2025-04-08 16:19:36');
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('59981d66c32bbd4678c0ac7b7314830d', '16b4d406-2b3e-4637-85e8-ee4ca9ed42d7', '95439d1b557864321c29ea700823ba6c', '2048ae8b18d76621ddfa7f5ca309e6aa', '副本元鼎供应链协同系统技术方案.docx', '工装模具在制定了保养计划之后，应该按照对应的计划规则，去生成对应的任务、以及任务明细，而后完成保养，并填写保养结果。\n任务生成之后应该提前通知（短信or站内信or邮件）到指定的公司或者相关管理员。\n· 盘点管理\n· 盘点任务\n工装模具需要进行定期不定期的盘点，此时就需要创建盘点任务，任务创建并确定以后，需要及时下发到指定的责任人（包括供应商、代工厂）。\n· 盘点执行\n指定的责任人（包括供应商、代工厂）在接收到盘点任务后，需在规定的时间内完成盘点任务，并完成上传或者填写。\n6. 供应商管理\n· 供应商基础资料\n· 基础数据录入', 265, 1, '2025-04-08 16:19:36');
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('5addc351964f8bef07253a90d4e62c2c', '19376bbc-b970-4f7e-ae45-cc30e0731983', '95439d1b557864321c29ea700823ba6c', '2048ae8b18d76621ddfa7f5ca309e6aa', '副本元鼎供应链协同系统技术方案.docx', 'image1.png\n\nimage2.png', 22, 1, '2025-04-08 16:19:36');
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('77f6e208e534536a966d1adacf9ce2ad', '1d85e02a-452f-4616-86b7-3d203e5aaab1', '95439d1b557864321c29ea700823ba6c', '2048ae8b18d76621ddfa7f5ca309e6aa', '副本元鼎供应链协同系统技术方案.docx', '自动从ERP同步销售订单功能，具体策略需要跟IT以及相关部门讨论之后再做决定。\n· 销售订单数据分析\n· 成品转换周期分析：\n入库到结算、结算到出货\n· 生产数据跟踪：\n· 订单完成率\n· 其它分析（具体有待敲定）\n需要讨论具体的分析内容、取数逻辑，展示方法等。\n5. 工装模具管理\n· 开模立项管理\n· 开模立项\n如在后续的生产工作当中需要新模具时需要对新模具进行数据录入、填入最终完成立项，而后进入到审批阶段，进行流程审批。\n· 立项审批\n工装模具在立项以后需要审流去做控制相关负责人进行逐级确认。审批完成之后进入采购流程。\n· 采购管理\n在模具完成立项审批后，进入采购，而后走采购流程。', 296, 1, '2025-04-08 16:19:36');
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('7bec5b0670ec0de91979c4afe49eb4f9', 'cc938bbf-aa4a-4f5a-b87f-c9dfce38f97e', '95439d1b557864321c29ea700823ba6c', '2048ae8b18d76621ddfa7f5ca309e6aa', '副本元鼎供应链协同系统技术方案.docx', '资源调度与成本控制压力\n模具借用归还记录不透明、保养计划执行难监管，导致资产利用率低；供应商报价与采购执行缺乏动态比价机制，成本优化空间受限。\n\n为应对上述挑战，元鼎智能联合东莞东智创新科技有限公司，基于工业互联网与大数据技术，共同打造元鼎智能供应链协同系统。该系统旨在构建覆盖\"核心企业-代工厂-供应商\"的端到端数字化协同网络，实现以下目标：', 172, 1, '2025-04-08 16:19:36');
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('86640d2c35284b4961a8cab6309083de', '15987ed1-bf4d-4def-bec7-28c5acf3859d', '95439d1b557864321c29ea700823ba6c', '2048ae8b18d76621ddfa7f5ca309e6aa', '副本元鼎供应链协同系统技术方案.docx', '3、 功能说明\n管理端（元鼎智能）\n1. 基础数据管理\n一些基础数据的管理，例如：业务字典等等，需要在具体业务敲定之后进行具体开发。\n2. 系统管理\n系统级的一些能力，此处不做赘述。\n3. 基础设施\n系统的一些基础建设设施。系统的基础能力之一，此处不做赘述。\n4. 销售订单管理\n因暂无预测订单，因此暂无预测订单相关的内容。如后续讨论和沟通中有此栏目，再做设计。\n· 销售订单管理\n主要展示销售订单的信息列表，以分页的形式展示。\n点击订单号进入订单明细界面：查看订单明细\n是否需要数据权限隔离？\n· 销售订单同步\n手动从ERP同步销售订单功能。', 272, 1, '2025-04-08 16:19:36');
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('88267ea4caec4e884c0ff2458443bcfa', '0c4fc817-fe89-4aeb-b2ec-775dc4ab47fb', '95439d1b557864321c29ea700823ba6c', '2048ae8b18d76621ddfa7f5ca309e6aa', '副本元鼎供应链协同系统技术方案.docx', '该模块在接收到元鼎、代工厂的采购订单之后进行确认。\n4. 送货单管理\n在供应商发货以后，填写、导入送货单信息，绑定指定的采购单。\n5. 基础数据管理\n需要在深入讨论之后得出基础数据的信息。\n6. 送货单管理\n需要在深入讨论之后得出首页需要提取、展示哪些数据。', 129, 1, '2025-04-08 16:19:36');
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('8e7795b5e6b0f5b5ae318b2831379715', '2c4e98fe-351e-47fa-823f-1ac4c1cb70ea', 'ae21afee18f5c05572ef10c710a70fb9', '311e15c8058c118cf0fd88f051458358', '财务数据指标.pdf', ' 应收账款周转天数（DSO）\n\n应收账款 / 日均销售额，其中，日均销售额=营业收入/当期天数，\n\n反映回款速度（越短越好）。\n\n 应付账款周转天数（DPO）\n\n应付账款 / 日均采购额，其中，日均采购额=（营业成本 + 存货期\n\n末余额 - 存货期初余额）/当期天数，观察供应商对上游的付款能力。\n\n 存货周转天数（DIO）\n\n存货 / 日均销售成本，其中，日均销售成本=营业成本/当期天数，\n\n评估库存管理效率。\n\n5. 现金流（抗风险能力）\n\n 经营活动产生的现金流量净额（CFO）\n\n是否持续为正，反映主营业务造血能力。\n\n 自由现金流（FCF）', 282, 1, '2025-04-22 14:48:39');
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('abb214104bc3ba1f4505be6b7bfa6bbb', '1bd52de2-9f6b-4b51-83aa-e80d864ab438', '1c17f8aaf048ef86a8d691d4d1b6830e', '311e15c8058c118cf0fd88f051458358', '财务数据指标.pdf', '1. 流动性指标（短期偿债能力）\n\n 流动比率（Current Ratio）\n\n流动资产 / 流动负债，反映短期偿债能力（一般≥1.5视为安全，否\n\n则标红）。\n\n 速动比率（Quick Ratio）\n\n（流动资产 - 存货）/ 流动负债，衡量紧急情况下的偿债能力（一\n\n般≥1，否则标红）。\n\n 现金比率（Cash Ratio）\n\n现金及现金等价物净增加额/ 流动负债，极端情况下的流动性保障能\n\n力。\n\n2. 偿债能力（长期稳定性）\n\n 资产负债率（Debt-to-Asset Ratio）\n\n负债合计 / 资产总计，评估长期债务风险（通常≤60%为宜，否则\n\n标红）。', 291, 1, '2025-04-22 13:54:32');
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('ae032f9134f780b5c25e7f3549684764', '2b94efd7-b76b-4e56-9939-803b8d7a70c7', '95439d1b557864321c29ea700823ba6c', '2048ae8b18d76621ddfa7f5ca309e6aa', '副本元鼎供应链协同系统技术方案.docx', '代工厂在产品完成生产之后，进行出库，出库的数据需要按照制定模板导入到系统，而后元鼎相关负责人可以查看出货情况。\n分析如果有ERP是否考虑写入ERP当中去。\n· 代工厂账号管理\n对操作本系统的代工厂的账号进行集中管控。\n9. 采购订单协同管理\n· 采购订单同步\n元鼎对于生产物料的采购，由ERP同步到系统当中去。\n代工厂的采购订单-导进来。\n· 送货单开具\n代工厂的供应商、元鼎的供应商确认采购订单后出具送货单，并在系统中查看送货单情况。\n· 采购订单执行分析\n交货达成率（OTD）、交货准时率、交货数量准确率、验收合格率等，其余可在下一步沟通中进行深入讨论。\n10. 首页\n· 数据提取\n· 图表展示', 300, 1, '2025-04-08 16:19:36');
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('b0c7d1083f686304ffb8ea5cc12885f6', 'c414d4ae-b8a1-4d9e-a3ab-a9e7604a9ebf', '3275c6be019aba229baa0a4a09ffbb0e', '311e15c8058c118cf0fd88f051458358', '财务数据指标.pdf', '1. 流动性指标（短期偿债能力）\n\n 流动比率（Current Ratio）\n\n流动资产 / 流动负债，反映短期偿债能力（一般≥1.5视为安全，否\n\n则标红）。\n\n 速动比率（Quick Ratio）\n\n（流动资产 - 存货）/ 流动负债，衡量紧急情况下的偿债能力（一\n\n般≥1，否则标红）。\n\n 现金比率（Cash Ratio）\n\n现金及现金等价物净增加额/ 流动负债，极端情况下的流动性保障能\n\n力。\n\n2. 偿债能力（长期稳定性）\n\n 资产负债率（Debt-to-Asset Ratio）\n\n负债合计 / 资产总计，评估长期债务风险（通常≤60%为宜，否则\n\n标红）。', 291, 1, '2025-04-22 15:41:55');
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('b4df12024121b201ebac13957c114549', 'dd674898-0523-4a53-bf89-c18db8f02d2a', '95439d1b557864321c29ea700823ba6c', '2048ae8b18d76621ddfa7f5ca309e6aa', '副本元鼎供应链协同系统技术方案.docx', '首页\n· 数据提取\n· 图表展示\n后续需要做讨论，究竟怎么显示、显示哪些内容。\n代工厂端\n1. 工装模具管理\n· 工装模具清单\n展示从元鼎领用的工装木模具的清单。\n· 工装模具保养\n上传工装模具保养的结果。\n2. 生产管理\n· 备料管理\n根据元鼎制定的备料清单模板上传备料信息。\n· 投料管理\n根据元鼎制定的投料清单模板上传投料信息。\n3. 出货管理\n根据元鼎制定的出货数据导入模板，导入出货数据。\n4. 基础数据管理\n需要在深入讨论之后得出基础数据的信息。\n5. 价格管理\n· 询价\n向元鼎体系内的指定供应商发起询价单。\n· 比价\n通过元鼎体系内供应商的询价单反馈的信息进行比价。', 291, 1, '2025-04-08 16:19:36');
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('bd5a973e4b5e38e9c3e8cef0eb27842e', '2ea0c854-d7cc-4084-aebf-6871c3767fa8', 'ae21afee18f5c05572ef10c710a70fb9', '311e15c8058c118cf0fd88f051458358', '财务数据指标.pdf', '1. 流动性指标（短期偿债能力）\n\n 流动比率（Current Ratio）\n\n流动资产 / 流动负债，反映短期偿债能力（一般≥1.5视为安全，否\n\n则标红）。\n\n 速动比率（Quick Ratio）\n\n（流动资产 - 存货）/ 流动负债，衡量紧急情况下的偿债能力（一\n\n般≥1，否则标红）。\n\n 现金比率（Cash Ratio）\n\n现金及现金等价物净增加额/ 流动负债，极端情况下的流动性保障能\n\n力。\n\n2. 偿债能力（长期稳定性）\n\n 资产负债率（Debt-to-Asset Ratio）\n\n负债合计 / 资产总计，评估长期债务风险（通常≤60%为宜，否则\n\n标红）。', 291, 1, '2025-04-22 14:48:39');
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('bda06d1d871cedf446566a0cba3d7a65', '5066c2b4-bc24-453c-af6e-e1cf4d9fcf66', 'd2451102b9066b4d468bf6531a2cd002', '05d871aa19bb79ec72ce84848f31d8c8', 'B2B交易系统产品文档.docx', '3.4 平台端\n核心功能\n· \n用户管理\n· \n2. \n企业实名认证（三证合一验证）\n2. \n2. \n角色权限分配（如：买家企业子账号权限）\n2. \n· \n交易规则配置\n· \n4. \n佣金规则（按品类/交易额阶梯收费）\n4. \n4. \n纠纷处理流程（退货、质量争议）\n4. \n· \n数据监控\n· \n6. \n实时交易大盘（GMV、订单量、热销品类）\n6. \n6. \n风险控制（反欺诈、异常订单预警）\n6. \n运营工具\n· \n站内信通知模板管理\n· \n· \n平台活动报名系统（如：行业展会）\n·\n\n4. 非功能性需求\n	类别\n	要求\n\n性能\n	支持每秒500+并发订单，响应时间<2s', 290, 1, '2025-03-08 15:51:25');
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('c74258d4f0f07621f47cca3839e21e8d', '26722c30-f214-40e9-8308-e25ada310a9a', '1c17f8aaf048ef86a8d691d4d1b6830e', '311e15c8058c118cf0fd88f051458358', '财务数据指标.pdf', ' 应收账款周转天数（DSO）\n\n应收账款 / 日均销售额，其中，日均销售额=营业收入/当期天数，\n\n反映回款速度（越短越好）。\n\n 应付账款周转天数（DPO）\n\n应付账款 / 日均采购额，其中，日均采购额=（营业成本 + 存货期\n\n末余额 - 存货期初余额）/当期天数，观察供应商对上游的付款能力。\n\n 存货周转天数（DIO）\n\n存货 / 日均销售成本，其中，日均销售成本=营业成本/当期天数，\n\n评估库存管理效率。\n\n5. 现金流（抗风险能力）\n\n 经营活动产生的现金流量净额（CFO）\n\n是否持续为正，反映主营业务造血能力。\n\n 自由现金流（FCF）', 282, 1, '2025-04-22 13:54:32');
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('c9975808c114479bcac59609319c96ec', '2e9a7e18-8fbb-4fed-addb-b38b0cb92102', 'd2451102b9066b4d468bf6531a2cd002', '05d871aa19bb79ec72ce84848f31d8c8', 'B2B交易系统产品文档.docx', '3. 功能需求详述\n3.1 买家端\n核心功能\n· \n商品搜索与询价\n· \n2. \n支持按分类/关键词/供应商筛选商品\n2. \n2. \n批量询价（RFQ）及比价功能\n2. \n· \n合同与订单管理\n· \n4. \n在线签订电子合同（集成CA认证）\n4. \n4. \n订单状态跟踪（待支付、发货中、验收完成）\n4. \n· \n支付与结算\n· \n· \n支持对公账户支付、信用证、账期支付\n· \n· \n发票自动匹配与下载\n· \n· \n数据分析\n· \n8. \n采购成本分析、供应商履约评分\n8. \n权限控制\n· \n多级审批流程（例如：采购员→部门经理→财务）\n·', 272, 1, '2025-03-08 15:51:25');
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('cc669b5f1079a3ee69240255e9805786', '2a511744-59cd-4d1e-a8c9-12be3a7ffbcb', '95439d1b557864321c29ea700823ba6c', '2048ae8b18d76621ddfa7f5ca309e6aa', '副本元鼎供应链协同系统技术方案.docx', '· 模具调度\n· 借出管理\n工装模具在使用当中可能会有供应商、代工厂需要使用，此时就需要记录借出的相关数据。\n· 归还管理\n工装模具在供应商、代工厂使用完成之后，走归还的流程，并做好记录。\n· 保养管理\n· 保养方案\n工装模具需要指定对应的保养方案，例如：检查机油是否需要补充、检查关键部位是否磨损。\n· 保养计划\n工装模具在制定了保养方案之后，还需设定保养计划，例如：多长时间保养一次等。\n需要注意的是，不管是元鼎智能，还是代工厂、供应商，在借调之后都需要遵守保养计划的原则，来保障模具的健康。\n· 保养执行', 255, 1, '2025-04-08 16:19:36');
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('d745a418154eed6558fad75b70f26ebd', '83b7a3ca-0bdd-4c13-95d4-c23c76ba88b3', '3275c6be019aba229baa0a4a09ffbb0e', '311e15c8058c118cf0fd88f051458358', '财务数据指标.pdf', ' 应收账款周转天数（DSO）\n\n应收账款 / 日均销售额，其中，日均销售额=营业收入/当期天数，\n\n反映回款速度（越短越好）。\n\n 应付账款周转天数（DPO）\n\n应付账款 / 日均采购额，其中，日均采购额=（营业成本 + 存货期\n\n末余额 - 存货期初余额）/当期天数，观察供应商对上游的付款能力。\n\n 存货周转天数（DIO）\n\n存货 / 日均销售成本，其中，日均销售成本=营业成本/当期天数，\n\n评估库存管理效率。\n\n5. 现金流（抗风险能力）\n\n 经营活动产生的现金流量净额（CFO）\n\n是否持续为正，反映主营业务造血能力。\n\n 自由现金流（FCF）', 282, 1, '2025-04-22 15:41:55');
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('d80ed8b9f73c16ec909f83532ec31185', 'f3ea942c-df13-47d1-87fc-37a88f2611a2', '95439d1b557864321c29ea700823ba6c', '2048ae8b18d76621ddfa7f5ca309e6aa', '副本元鼎供应链协同系统技术方案.docx', '代工厂按照指定的模板将备料的数据导入系统，而后通过导入的数据分析查看对应的生产情况。\n在数据导入之后进行备料齐套分析，监督供应商的齐套信息，或者遇到了什么问题。\n· 投料管理\n代工厂将投料的数据按照投料模板导入系统后，工单负责人对投料信息进行查看。\n· 入库管理\n代工厂在生产完成之后将入库数据通过指定的模板导入后进行数据分析，检查和分析工单完成情况。\n· 品质跟踪\n· 来料检验\n代工厂、元鼎在接收供应商的物料时，进行来料检验，检验后的结果数据导入到系统后进行查看和分析。\n· IPQC巡检\n代工厂在生产过程中进行IPQC的巡检后，将IPQC的数据导入到系统中去而后，对数据进行分析和查看。', 296, 1, '2025-04-08 16:19:36');
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('d8ed876afc88e7b328d664c77ecbb299', '144039bf-d0aa-4608-b51f-7dab3adcac6f', 'd2451102b9066b4d468bf6531a2cd002', '05d871aa19bb79ec72ce84848f31d8c8', 'B2B交易系统产品文档.docx', '2. 系统架构\n2.1 技术架构（示例）\n· \n前端：Web端 + 移动端（React/Vue + 小程序）\n· \n· \n后端：微服务架构（Spring Cloud/Dubbo）\n· \n· \n数据库：MySQL（交易数据） + MongoDB（日志/行为数据）\n· \n· \n第三方集成：支付网关（支付宝/银联）、物流API、电子签章、ERP系统对接\n· \n2.2 核心功能模块\n\n（此处可插入模块图，说明四端交互逻辑）', 209, 1, '2025-03-08 15:51:25');
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('e538b6dca1b5b32c33ad0f16ae74c13a', '27187624-59c9-4a02-950e-4a927a3bc91e', '95439d1b557864321c29ea700823ba6c', '2048ae8b18d76621ddfa7f5ca309e6aa', '副本元鼎供应链协同系统技术方案.docx', '· OQC检验\n代工厂在生产过程中进行OQC检查后，将OQC的数据导入到系统中去而后，对数据进行分析和查看。\n· 生产异常填报\n代工厂在生产过程中发现有品质异常时，按照指定的模板进行异常填报导入操作，而后元鼎相关负责人在收到品质异常的信息之后，进行对应的处理。\n此处需要消息提醒功能，但是目前还没确定是哪一种。\n· 测试数据\n· 测试数据查看\n代工厂按照规定，对产品的测试数据进行导入，而后由元鼎的相关负责人查看。\n· 测试数据分析\n代工厂按照规定对产品的测试数据进导入后，而后由元鼎的相关负责人查看分析结果。\n分析结果以及业务公式需要在下一步讨论中得到结果。\n· 出货管理', 287, 1, '2025-04-08 16:19:36');
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('e87caecc6b608738635028078336cac1', '6f5691d8-0e3a-42cf-b6dd-3b9d37ab1ebd', '3275c6be019aba229baa0a4a09ffbb0e', '311e15c8058c118cf0fd88f051458358', '财务数据指标.pdf', '经营活动产生的现金流量净额 -资本支出，其中，资本支出=购建固\n\n定资产、无形资产和其他长期资产所支付的现金-处置固定资产、无\n\n形资产和其他长期资产所收回的现金净额，衡量可自由支配的现金流。\n\n6. 规模与增长潜力\n\n 营业收入增长率\n\n营业收入增长率=当期营业收入/去年同期营业收入-1。\n\n 净利润增长率\n\n净利润增长率=当期净利润/去年同期净利润-1\n\n是否与营收同步增长，避免“增收不增利”。', 203, 1, '2025-04-22 15:41:55');
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('f57d89433a6aba9bf128cefe15ee7b19', '2982ba49-e47b-4767-988a-1857055d68a4', 'd2451102b9066b4d468bf6531a2cd002', '05d871aa19bb79ec72ce84848f31d8c8', 'B2B交易系统产品文档.docx', 'D --> E[供应商发货至卖家仓库]\nE --> C', 27, 1, '2025-03-08 15:51:25');
INSERT INTO `aigc_docs_slice` (`id`, `vector_id`, `docs_id`, `knowledge_id`, `name`, `content`, `word_num`, `status`, `create_time`) VALUES ('f5aef22d97a6306afa8846d90b47f608', '89311f59-56d0-4027-8d17-f1303d77d73b', 'd2451102b9066b4d468bf6531a2cd002', '05d871aa19bb79ec72ce84848f31d8c8', 'B2B交易系统产品文档.docx', '3.2 卖家端\n核心功能\n·\n商品管理\n·\n·\nSKU批量上传（支持Excel模板）\n·\n·\n动态库存同步（对接供应商端）\n·\n·\n订单处理\n·\n4.\n自动拆单（按仓库/供应商分配）\n4.\n4.\n物流单号批量导入与通知\n4.\n·\n客户管理\n·\n6.\n企业资质审核（营业执照、行业认证）\n6.\n6.\n分层定价（针对不同客户等级设置折扣）\n6.\n·\n营销工具\n·\n8.\n定向发放优惠券、满减活动\n8.\n典型流程\nmermaid\n复制\ngraph TD\nA[买家下单] --> B{库存充足?}\nB -->|是| C[直接发货]\nB -->|否| D[向供应商发起采购]', 282, 1, '2025-03-08 15:51:25');
COMMIT;

-- ----------------------------
-- Table structure for aigc_embed_store
-- ----------------------------
DROP TABLE IF EXISTS `aigc_embed_store`;
CREATE TABLE `aigc_embed_store` (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '别名',
  `provider` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '供应商',
  `host` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '地址',
  `port` int DEFAULT NULL COMMENT '端口',
  `username` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '用户名',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '密码',
  `database_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '数据库名称',
  `table_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '表名称',
  `dimension` int DEFAULT NULL COMMENT '向量维数',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Embedding向量数据库配置表';

-- ----------------------------
-- Records of aigc_embed_store
-- ----------------------------
BEGIN;
INSERT INTO `aigc_embed_store` (`id`, `name`, `provider`, `host`, `port`, `username`, `password`, `database_name`, `table_name`, `dimension`) VALUES ('c92cfbaaff366b12bc87c1082149150a', 'Milvus', 'MILVUS', '************', 19530, NULL, NULL, 'default', 'ai', 1024);
COMMIT;

-- ----------------------------
-- Table structure for aigc_knowledge
-- ----------------------------
DROP TABLE IF EXISTS `aigc_knowledge`;
CREATE TABLE `aigc_knowledge` (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `user_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '用户ID',
  `embed_store_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '向量数据库ID',
  `embed_model_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '向量模型ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '知识库名称',
  `des` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '描述',
  `cover` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '封面',
  `create_time` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='知识库表';

-- ----------------------------
-- Records of aigc_knowledge
-- ----------------------------
BEGIN;
INSERT INTO `aigc_knowledge` (`id`, `user_id`, `embed_store_id`, `embed_model_id`, `name`, `des`, `cover`, `create_time`) VALUES ('05d871aa19bb79ec72ce84848f31d8c8', NULL, '1556f1ace07266958db4919a092201d1', 'd61f28c3ced11a1abac3215d2154c882', 'test', 'test', NULL, '1741404104463');
INSERT INTO `aigc_knowledge` (`id`, `user_id`, `embed_store_id`, `embed_model_id`, `name`, `des`, `cover`, `create_time`) VALUES ('2048ae8b18d76621ddfa7f5ca309e6aa', NULL, '1556f1ace07266958db4919a092201d1', 'd61f28c3ced11a1abac3215d2154c882', 'aaaa', 'aaaaa', NULL, '1741421291144');
INSERT INTO `aigc_knowledge` (`id`, `user_id`, `embed_store_id`, `embed_model_id`, `name`, `des`, `cover`, `create_time`) VALUES ('311e15c8058c118cf0fd88f051458358', NULL, 'c92cfbaaff366b12bc87c1082149150a', 'd61f28c3ced11a1abac3215d2154c882', 'Milvus', 'Milvus', NULL, '1744101228525');
COMMIT;

-- ----------------------------
-- Table structure for aigc_message
-- ----------------------------
DROP TABLE IF EXISTS `aigc_message`;
CREATE TABLE `aigc_message` (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `user_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '用户ID',
  `conversation_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '会话ID',
  `chat_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '消息的ID',
  `username` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '用户名',
  `ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'IP地址',
  `role` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '角色，user和assistant',
  `model` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '模型名称',
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '消息内容',
  `tokens` int DEFAULT NULL,
  `prompt_tokens` int DEFAULT NULL,
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `conversation_id` (`conversation_id`) USING BTREE,
  KEY `role` (`role`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='对话消息表';

-- ----------------------------
-- Records of aigc_message
-- ----------------------------
BEGIN;
INSERT INTO `aigc_message` (`id`, `user_id`, `conversation_id`, `chat_id`, `username`, `ip`, `role`, `model`, `message`, `tokens`, `prompt_tokens`, `create_time`) VALUES ('2131c844b145cef933e69a6a0f675196', '91b4524a46a949601e7f3b004ed76034', '91b4524a46a949601e7f3b004ed76034', '4b54dd44-2945-47a7-9cb3-0dd12748336b', 'administrator', 'unknown', 'user', NULL, '公民身份号码430221198410137130\n住址\n出生1984年10月13日\n性别男\n姓名\n广东省深圳市龙华区欢远\n业2层208A\n中路深圳北站西广场A1物\n吴俊明\n民旅汉', 0, 0, '2025-05-13 11:43:10');
INSERT INTO `aigc_message` (`id`, `user_id`, `conversation_id`, `chat_id`, `username`, `ip`, `role`, `model`, `message`, `tokens`, `prompt_tokens`, `create_time`) VALUES ('891f118aa6af1872f334f78eb1628589', '91b4524a46a949601e7f3b004ed76034', '91b4524a46a949601e7f3b004ed76034', '8377c683-e693-4b68-a8cf-d15066b34015', 'administrator', 'unknown', 'user', NULL, '流动资产: 150,328.1; 流动负债: 207,858.27; 存货: 12,629; 营业收入: 0; 营业成本: 0; 利润总额: -1,924.69; 所得税费用: 0; 负债合计: 207,858.27; 资产总计: 270,432.1 风控评估', 0, 0, '2025-04-22 19:19:38');
INSERT INTO `aigc_message` (`id`, `user_id`, `conversation_id`, `chat_id`, `username`, `ip`, `role`, `model`, `message`, `tokens`, `prompt_tokens`, `create_time`) VALUES ('a862dbd415be4549b7f33ffb6d6a85c5', '91b4524a46a949601e7f3b004ed76034', '91b4524a46a949601e7f3b004ed76034', '8377c683-e693-4b68-a8cf-d15066b34015', 'administrator', 'unknown', 'assistant', NULL, '### 财务分析报告\n\n#### 摘要\n本报告基于提供的财务数据，对企业当前的财务状况进行了全面分析。通过计算关键财务指标，评估了企业的短期偿债能力、长期偿债能力、盈利能力和运营效率。报告指出企业在财务健康方面存在的潜在问题，并提供了基于数据的融资放款建议。\n\n#### 详细分析\n\n##### 1. 关键财务指标计算\n- **流动比率** = 流动资产 / 流动负债 = 150,328.1 / 207,858.27 ≈ 0.72\n- **速动比率** = (流动资产 - 存货) / 流动负债 = (150,328.1 - 12,629) / 207,858.27 ≈ 0.66\n- **资产负债率** = 负债合计 / 资产总计 = 207,858.27 / 270,432.1 ≈ 0.77\n- **毛利率** = (营业收入 - 营业成本) / 营业收入 = (0 - 0) / 0 = 0%\n- **净利率** = 利润总额 / 营业收入 = -1,924.69 / 0 = 不可计算（因营业收入为0）\n\n##### 2. 财务能力评估\n- **短期偿债能力**：流动比率和速动比率均低于1，表明企业短期偿债能力较弱，可能面临流动性风险。\n- **长期偿债能力**：资产负债率高达77%，显示企业负债水平较高，长期偿债压力较大。\n- **盈利能力**：营业收入和营业成本均为0，利润总额为负，表明企业当前无盈利能力。\n- **运营效率**：由于营业收入为0，无法计算运营效率相关指标。\n\n##### 3. 潜在问题或风险\n- 企业短期和长期偿债能力均较弱，可能面临资金链断裂的风险。\n- 企业当前无营业收入，且利润总额为负，表明企业经营状况不佳。\n- 高资产负债率增加了企业的财务风险，可能影响其未来的融资能力。\n\n#### 结论\n企业当前的财务状况不佳，短期和长期偿债能力较弱，盈利能力缺失，存在较高的财务风险。\n\n#### 建议\n基于当前的财务数据，不建议进行融资放款。企业应首先改善其财务状况，增强偿债能力和盈利能力，降低财务风险，然后再考虑融资需求。同时，企业应加强内部管理，提高运营效率，寻找新的收入来源，以改善其财务健康状况。', 593, 686, '2025-04-22 19:20:56');
INSERT INTO `aigc_message` (`id`, `user_id`, `conversation_id`, `chat_id`, `username`, `ip`, `role`, `model`, `message`, `tokens`, `prompt_tokens`, `create_time`) VALUES ('bb9cdb53bff9ba0b98555f4d4ba9bb96', '91b4524a46a949601e7f3b004ed76034', '91b4524a46a949601e7f3b004ed76034', '01d98bb1-f663-4ea5-83b4-62c3301eb58c', 'administrator', 'unknown', 'assistant', NULL, '```json\n{\n  \"personal_infos\": [\n    {\n      \"name\": \"吴俊明\",\n      \"gender\": \"男\",\n      \"ethnic\": \"汉\",\n      \"birth_date\": \"1984年10月13日\",\n      \"id_number\": \"430221198410137130\",\n      \"residence\": \"广东省深圳市龙华区欢远中路深圳北站西广场A1物业2层208A\",\n      \"mailing_address\": \"\",\n      \"issuing_authority\": \"\",\n      \"validity_period\": {\n        \"start\": \"\",\n        \"end\": \"\"\n      },\n      \"postal_code\": \"\"\n    }\n  ]\n}\n```', 179, 1856, '2025-05-13 11:41:34');
INSERT INTO `aigc_message` (`id`, `user_id`, `conversation_id`, `chat_id`, `username`, `ip`, `role`, `model`, `message`, `tokens`, `prompt_tokens`, `create_time`) VALUES ('bf5d689318d332f26d45c94097b24af5', '91b4524a46a949601e7f3b004ed76034', '91b4524a46a949601e7f3b004ed76034', 'b8897dd1-58d6-4760-87a0-9fd95ed61666', 'administrator', 'unknown', 'assistant', NULL, '### 风控评估报告\n\n#### 1. **财务健康状况分析**\n\n- **流动比率** = 流动资产 / 流动负债 = 150,328.1 / 207,858.27 ≈ 0.72  \n  - 流动比率低于1，表明企业的短期偿债能力较弱，可能面临流动性风险。\n  \n- **速动比率** = (流动资产 - 存货) / 流动负债 = (150,328.1 - 12,629) / 207,858.27 ≈ 0.66  \n  - 速动比率同样低于1，进一步说明企业短期内可能难以偿还债务，尤其是剔除存货后，流动性问题更加明显。\n\n- **资产负债率** = 负债合计 / 资产总计 = 207,858.27 / 270,432.1 ≈ 77%  \n  - 资产负债率高达77%，表明企业的负债水平较高，财务杠杆较大，长期偿债压力较大。\n\n- **盈利能力**：  \n  - 营业收入和营业成本均为0，利润总额为-1,924.69，表明企业当前无营业收入，且处于亏损状态，盈利能力严重不足。\n\n- **所得税费用为0**：  \n  - 由于利润总额为负，所得税费用为0，符合逻辑，但进一步说明企业当前无盈利能力。\n\n#### 2. **风险点分析**\n\n- **流动性风险**：  \n  - 流动比率和速动比率均低于1，表明企业短期内可能无法偿还债务，存在流动性风险。\n\n- **偿债能力风险**：  \n  - 资产负债率高达77%，表明企业负债水平较高，长期偿债能力较弱，可能面临较大的财务压力。\n\n- **盈利能力风险**：  \n  - 企业当前无营业收入，且利润总额为负，表明企业经营状况不佳，盈利能力缺失，可能难以维持正常运营。\n\n- **运营风险**：  \n  - 营业收入和营业成本均为0，表明企业可能处于停业或未开展业务的状态，运营效率低下。\n\n#### 3. **风控建议**\n\n- **谨慎放贷**：  \n  - 基于企业当前的财务状况，尤其是流动性和盈利能力问题，建议谨慎考虑放贷。企业短期内可能无法偿还债务，且长期偿债能力较弱，放贷风险较高。\n\n- **要求提供担保或抵押**：  \n  - 如果必须放贷，建议要求企业提供足够的担保或抵押物，以降低风险。\n\n- **监控企业经营状况**：  \n  - 建议定期监控企业的经营状况，尤其是营业收入和利润的变化情况，确保企业能够逐步恢复盈利能力。\n\n- **限制贷款额度**：  \n  - 如果放贷，建议限制贷款额度，避免一次性提供大额贷款，以降低潜在的违约风险。\n\n#### 4. **总结**\n\n企业当前的财务状况较差，流动性和偿债能力较弱，盈利能力缺失，存在较高的财务风险。建议谨慎放贷，并采取相应的风控措施，如要求担保、限制贷款额度等，以降低潜在风险。', 750, 819, '2025-05-08 19:28:54');
INSERT INTO `aigc_message` (`id`, `user_id`, `conversation_id`, `chat_id`, `username`, `ip`, `role`, `model`, `message`, `tokens`, `prompt_tokens`, `create_time`) VALUES ('d6097588d60054590798d1792884060f', '91b4524a46a949601e7f3b004ed76034', '91b4524a46a949601e7f3b004ed76034', 'b8897dd1-58d6-4760-87a0-9fd95ed61666', 'administrator', 'unknown', 'user', NULL, '流动资产: 150,328.1; 流动负债: 207,858.27; 存货: 12,629; 营业收入: 0; 营业成本: 0; 利润总额: -1,924.69; 所得税费用: 0; 负债合计: 207,858.27; 资产总计: 270,432.1 风控评估', 0, 0, '2025-05-08 19:28:16');
INSERT INTO `aigc_message` (`id`, `user_id`, `conversation_id`, `chat_id`, `username`, `ip`, `role`, `model`, `message`, `tokens`, `prompt_tokens`, `create_time`) VALUES ('e6cb0d2dc559efbf1ef20c01405f748f', '91b4524a46a949601e7f3b004ed76034', '91b4524a46a949601e7f3b004ed76034', '01d98bb1-f663-4ea5-83b4-62c3301eb58c', 'administrator', 'unknown', 'user', NULL, '公民身份号码430221198410137130\n住址\n出生1984年10月13日\n性别男\n姓名\n广东省深圳市龙华区欢远\n业2层208A\n中路深圳北站西广场A1物\n吴俊明\n民旅汉', 0, 0, '2025-05-13 11:41:21');
INSERT INTO `aigc_message` (`id`, `user_id`, `conversation_id`, `chat_id`, `username`, `ip`, `role`, `model`, `message`, `tokens`, `prompt_tokens`, `create_time`) VALUES ('ea95cda4e531f09c3cd70dc8f83ad979', '91b4524a46a949601e7f3b004ed76034', '91b4524a46a949601e7f3b004ed76034', '4b54dd44-2945-47a7-9cb3-0dd12748336b', 'administrator', 'unknown', 'assistant', NULL, '```json\n{\n  \"personal_infos\": [\n    {\n      \"name\": \"吴俊明\",\n      \"gender\": \"男\",\n      \"ethnic\": \"汉\",\n      \"birth_date\": \"1984-10-13\",\n      \"id_number\": \"430221198410137130\",\n      \"residence\": \"广东省深圳市龙华区欢远中路深圳北站西广场A1物业2层208A\",\n      \"mailing_address\": \"\",\n      \"issuing_authority\": \"\",\n      \"validity_period\": {\n        \"start\": \"\",\n        \"end\": \"\"\n      },\n      \"postal_code\": \"\"\n    }\n  ]\n}\n```', 178, 2119, '2025-05-13 11:43:20');
COMMIT;

-- ----------------------------
-- Table structure for aigc_model
-- ----------------------------
DROP TABLE IF EXISTS `aigc_model`;
CREATE TABLE `aigc_model` (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '类型: CHAT、Embedding、Image',
  `model` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '模型名称',
  `provider` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '供应商',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '别名',
  `response_limit` int DEFAULT NULL COMMENT '响应长度',
  `temperature` double DEFAULT NULL COMMENT '温度',
  `top_p` double DEFAULT NULL,
  `api_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `base_url` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `secret_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `endpoint` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `azure_deployment_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'azure模型参数',
  `gemini_project` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'gemini模型参数',
  `gemini_location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'gemini模型参数',
  `image_size` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '图片大小',
  `image_quality` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '图片质量',
  `image_style` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '图片风格',
  `dimension` int DEFAULT NULL COMMENT '向量维数',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='LLM模型配置表';

-- ----------------------------
-- Records of aigc_model
-- ----------------------------
BEGIN;
INSERT INTO `aigc_model` (`id`, `type`, `model`, `provider`, `name`, `response_limit`, `temperature`, `top_p`, `api_key`, `base_url`, `secret_key`, `endpoint`, `azure_deployment_name`, `gemini_project`, `gemini_location`, `image_size`, `image_quality`, `image_style`, `dimension`) VALUES ('ac05866194643c5341a73d837b2017c8', 'CHAT', 'deepseek-ai/DeepSeek-V2.5', 'SILICON', 'deepseek', 2000, 0.2, 0.8, 'sk-univnrseuupvzarrjozodhcmnurfvvyrcujdssfggfckynxd', 'https://api.siliconflow.cn/v1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `aigc_model` (`id`, `type`, `model`, `provider`, `name`, `response_limit`, `temperature`, `top_p`, `api_key`, `base_url`, `secret_key`, `endpoint`, `azure_deployment_name`, `gemini_project`, `gemini_location`, `image_size`, `image_quality`, `image_style`, `dimension`) VALUES ('b3ac64325c37094da12d199d1928a37b', 'EMBEDDING', 'text-embedding-v3', 'Q_WEN', '通用文本向量-v3', NULL, 0.2, 0, 'sk-92f32b2264e24dfbabf3ec3c53e6cdae', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1024);
INSERT INTO `aigc_model` (`id`, `type`, `model`, `provider`, `name`, `response_limit`, `temperature`, `top_p`, `api_key`, `base_url`, `secret_key`, `endpoint`, `azure_deployment_name`, `gemini_project`, `gemini_location`, `image_size`, `image_quality`, `image_style`, `dimension`) VALUES ('d61f28c3ced11a1abac3215d2154c882', 'EMBEDDING', 'bge-m3:latest', 'OLLAMA', 'ollama', NULL, 0.2, 0, NULL, 'http://localhost:11434/', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1024);
COMMIT;

-- ----------------------------
-- Table structure for aigc_oss
-- ----------------------------
DROP TABLE IF EXISTS `aigc_oss`;
CREATE TABLE `aigc_oss` (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `user_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '用户ID',
  `oss_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `original_filename` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '原始文件名称',
  `filename` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '文件存储名称',
  `url` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '文件地址',
  `base_path` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '桶路径',
  `path` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '文件的绝对路径',
  `size` int DEFAULT NULL COMMENT '文件大小',
  `ext` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '文件后缀',
  `content_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '文件头',
  `platform` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '平台',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='资源文件表';

-- ----------------------------
-- Records of aigc_oss
-- ----------------------------
BEGIN;
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('0c5f3815dfbf66fadf7a3d0aaf5a6c77', '91b4524a46a949601e7f3b004ed76034', NULL, 'B2B交易系统产品文档.docx', '67cad6d18a52eabc42a26579.docx', 'http://127.0.0.1:8100/langchat/2025030767cad6d18a52eabc42a26579.docx', 'langchat/', '20250307', 22580, 'docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'local', '2025-03-07 19:21:54');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('1bcf7fe1c18312b5ace0f36bc7f439cb', '91b4524a46a949601e7f3b004ed76034', NULL, '财务数据指标.pdf', '68073ed722522b1a675b39ba.pdf', 'http://127.0.0.1:8100/langchat/2025042268073ed722522b1a675b39ba.pdf', 'langchat/', '20250422', 95537, 'pdf', 'application/pdf', 'local', '2025-04-22 15:01:44');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('1ee3f46c35e3d4a2d6582f4ab4ff913a', '91b4524a46a949601e7f3b004ed76034', NULL, 'Ai-supply.md', '67c81b71aa526a595a4f3d79.md', 'http://127.0.0.1:8100/langchat/2025030567c81b71aa526a595a4f3d79.md', 'langchat/', '20250305', 6828070, 'md', 'text/markdown', 'local', '2025-03-05 17:37:54');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('1f42cb705a2e24defa025da362128fea', '91b4524a46a949601e7f3b004ed76034', NULL, '副本元鼎供应链协同系统技术方案.docx', '67f4dce7aa528ddd53ec249e.docx', 'http://127.0.0.1:8100/langchat/2025040867f4dce7aa528ddd53ec249e.docx', 'langchat/', '20250408', 391175, 'docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'local', '2025-04-08 16:23:03');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('257624728dda107fda4a1395feb6d08b', '91b4524a46a949601e7f3b004ed76034', NULL, 'B2B交易系统产品文档.docx', '67cae2f98a524b997bebd038.docx', 'http://127.0.0.1:8100/langchat/2025030767cae2f98a524b997bebd038.docx', 'langchat/', '20250307', 22580, 'docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'local', '2025-03-07 20:13:46');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('2624405fd5384283cdaf8fdbd72f4eb9', '91b4524a46a949601e7f3b004ed76034', NULL, '未命名.txt', '67cbfaef8a5242a2cac81f4b.txt', 'http://127.0.0.1:8100/langchat/2025030867cbfaef8a5242a2cac81f4b.txt', 'langchat/', '20250308', 6, 'txt', 'text/plain', 'local', '2025-03-08 16:08:15');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('2ed70a53ec67e9ca23db6ce469c047b4', '91b4524a46a949601e7f3b004ed76034', NULL, '财务数据指标.pdf', '68073bc322522b1a675b39b9.pdf', 'http://127.0.0.1:8100/langchat/2025042268073bc322522b1a675b39b9.pdf', 'langchat/', '20250422', 95537, 'pdf', 'application/pdf', 'local', '2025-04-22 14:48:35');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('413e25a4c7089fd1ecc65b4c7478a16e', '91b4524a46a949601e7f3b004ed76034', NULL, 'B2B交易系统产品文档.docx', '67cbb7cc8a522e5ea86c5d3c.docx', 'http://127.0.0.1:8100/langchat/2025030867cbb7cc8a522e5ea86c5d3c.docx', 'langchat/', '20250308', 22580, 'docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'local', '2025-03-08 11:21:49');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('4189fdfb745c403bef043966d3374dfa', '91b4524a46a949601e7f3b004ed76034', NULL, '副本元鼎供应链协同系统技术方案.docx', '67f4dc13aa528ddd53ec249b.docx', 'http://127.0.0.1:8100/langchat/2025040867f4dc13aa528ddd53ec249b.docx', 'langchat/', '20250408', 391175, 'docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'local', '2025-04-08 16:19:31');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('47ef0de9377f8d1954e74b9322db6c2a', '91b4524a46a949601e7f3b004ed76034', NULL, '财务数据指标.pdf', '68072f1612522b7b67605b71.pdf', 'http://127.0.0.1:8100/langchat/2025042268072f1612522b7b67605b71.pdf', 'langchat/', '20250422', 95537, 'pdf', 'application/pdf', 'local', '2025-04-22 13:54:30');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('48c236de62d2dd48ce49109f6e5a0872', '91b4524a46a949601e7f3b004ed76034', NULL, 'B2B交易系统产品文档.docx', '67f4dbefaa528ddd53ec249a.docx', 'http://127.0.0.1:8100/langchat/2025040867f4dbefaa528ddd53ec249a.docx', 'langchat/', '20250408', 22580, 'docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'local', '2025-04-08 16:18:55');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('496a1c3a6798e6b9f52e071d533753d1', '91b4524a46a949601e7f3b004ed76034', NULL, '36946717.JPEG', '66b6df5ecdb26cd406afc109.JPEG', 'http://127.0.0.1/langchat/2024081066b6df5ecdb26cd406afc109.JPEG', 'langchat/', '20240810', 11744, 'JPEG', 'image/jpeg', 'local', '2024-08-10 11:32:47');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('4cbd17eef14b70d987837525550922e8', '91b4524a46a949601e7f3b004ed76034', NULL, 'B2B交易系统产品文档.docx', '67cae2168a524b997bebd037.docx', 'http://127.0.0.1:8100/langchat/2025030767cae2168a524b997bebd037.docx', 'langchat/', '20250307', 22580, 'docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'local', '2025-03-07 20:09:59');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('555e91c0ee85dc3adb67b1a9b697401b', '91b4524a46a949601e7f3b004ed76034', NULL, '副本元鼎供应链协同系统技术方案.docx', '67f4dc31aa528ddd53ec249c.docx', 'http://127.0.0.1:8100/langchat/2025040867f4dc31aa528ddd53ec249c.docx', 'langchat/', '20250408', 391175, 'docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'local', '2025-04-08 16:20:01');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('55b5b75061c0a229ec0114fc62853a0c', '91b4524a46a949601e7f3b004ed76034', NULL, 'story-about-happy-carrot.pdf', '66b4afeecdb2c038a2624532.pdf', 'http://cdn.tycoding.cn/langchat/2024080866b4afeecdb2c038a2624532.pdf', 'langchat/', '20240808', 35359, 'pdf', 'application/pdf', 'qiniu', '2024-08-08 19:45:51');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('6149f19c08f8d488e6302cb904fff76d', '91b4524a46a949601e7f3b004ed76034', NULL, 'B2B交易系统产品文档.docx', '67cbae518a529490d99e0caa.docx', 'http://127.0.0.1:8100/langchat/2025030867cbae518a529490d99e0caa.docx', 'langchat/', '20250308', 22580, 'docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'local', '2025-03-08 10:41:21');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('6493b797b811c45a6fd50c9d070a8459', '91b4524a46a949601e7f3b004ed76034', NULL, '副本元鼎供应链协同系统技术方案.docx', '67f4df1faa526e8ab128f7b8.docx', 'http://127.0.0.1:8100/langchat/2025040867f4df1faa526e8ab128f7b8.docx', 'langchat/', '20250408', 391175, 'docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'local', '2025-04-08 16:32:31');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('6955454ab3316178cef61b245bdd6891', '91b4524a46a949601e7f3b004ed76034', NULL, 'B2B交易系统产品文档.docx', '67cbf6fc8a52450b6ffb8b13.docx', 'http://127.0.0.1:8100/langchat/2025030867cbf6fc8a52450b6ffb8b13.docx', 'langchat/', '20250308', 22580, 'docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'local', '2025-03-08 15:51:24');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('6a91df3d44a2fdfe6c8fcc83844757c8', '91b4524a46a949601e7f3b004ed76034', NULL, 'story-about-happy-carrot.pdf', '66b239dbcdb2ff916a0a092c.pdf', 'http://cdn.tycoding.cn/langchat/2024080666b239dbcdb2ff916a0a092c.pdf', 'langchat/', '20240806', 35359, 'pdf', 'application/pdf', 'qiniu', '2024-08-06 22:57:32');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('6ff4dc90dac77095ddeede50a42b06cc', '91b4524a46a949601e7f3b004ed76034', NULL, 'B2B交易系统产品文档.docx', '67cabc8f8a5204f581136867.docx', 'http://127.0.0.1:8100/langchat/2025030767cabc8f8a5204f581136867.docx', 'langchat/', '20250307', 22580, 'docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'local', '2025-03-07 17:29:51');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('726bc0a42f0753c78672bedb8529c2c4', '91b4524a46a949601e7f3b004ed76034', NULL, 'story-about-happy-carrot.pdf', '66b4b069cdb262aeea8da409.pdf', 'http://127.0.0.1/langchat/2024080866b4b069cdb262aeea8da409.pdf', 'langchat/', '20240808', 35359, 'pdf', 'application/pdf', 'local', '2024-08-08 19:47:54');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('754b857bcf269b1022490d32b2090f6a', '91b4524a46a949601e7f3b004ed76034', NULL, 'Ai-supply.md', '67c6b2e18a52d2a3a5b6ae92.md', 'http://127.0.0.1:8100/langchat/2025030467c6b2e18a52d2a3a5b6ae92.md', 'langchat/', '20250304', 6828070, 'md', 'text/markdown', 'local', '2025-03-04 15:59:29');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('7c0346f63c197b70d5a31cee7ac17df2', '91b4524a46a949601e7f3b004ed76034', NULL, 'B2B交易系统产品文档.docx', '67cae70a8a524b997bebd039.docx', 'http://127.0.0.1:8100/langchat/2025030767cae70a8a524b997bebd039.docx', 'langchat/', '20250307', 22580, 'docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'local', '2025-03-07 20:31:06');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('7ef543675e89ef3fea19563b667c1454', '91b4524a46a949601e7f3b004ed76034', NULL, 'story-about-happy-carrot.pdf', '66b489b0cdb2a4b1a529719f.pdf', 'http://cdn.tycoding.cn/langchat/2024080866b489b0cdb2a4b1a529719f.pdf', 'langchat/', '20240808', 35359, 'pdf', 'application/pdf', 'qiniu', '2024-08-08 17:02:41');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('7fe31ec6ab2799764afbca526c0e8cc3', '91b4524a46a949601e7f3b004ed76034', NULL, 'B2B交易系统产品文档.docx', '67cad6b88a52eabc42a26578.docx', 'http://127.0.0.1:8100/langchat/2025030767cad6b88a52eabc42a26578.docx', 'langchat/', '20250307', 22580, 'docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'local', '2025-03-07 19:21:28');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('9c18c83f878e321fa588ce0ff3ba6d09', '91b4524a46a949601e7f3b004ed76034', NULL, 'B2B交易系统产品文档.docx', '67cad7108a524b997bebd036.docx', 'http://127.0.0.1:8100/langchat/2025030767cad7108a524b997bebd036.docx', 'langchat/', '20250307', 22580, 'docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'local', '2025-03-07 19:22:57');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('a554815004e8b715f3d58517c04b47a2', '91b4524a46a949601e7f3b004ed76034', NULL, '1b6c07a7dea87caf28b8bc176564d7c3 (1).jpg', '68076b3622524337a2640ebd.jpg', 'http://127.0.0.1:8100/langchat/2025042268076b3622524337a2640ebd.jpg', 'langchat/', '20250422', 173265, 'jpg', 'image/jpeg', 'local', '2025-04-22 18:11:02');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('aaceec3a6dc5131178a09acbf7376902', '91b4524a46a949601e7f3b004ed76034', NULL, 'B2B交易系统产品文档.docx', '67cbace88a529490d99e0ca8.docx', 'http://127.0.0.1:8100/langchat/2025030867cbace88a529490d99e0ca8.docx', 'langchat/', '20250308', 22580, 'docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'local', '2025-03-08 10:35:20');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('b32d6b15234c4c29fa03dc3990d12ac1', '91b4524a46a949601e7f3b004ed76034', NULL, 'B2B交易系统产品文档.docx', '67cad6ea8a52eabc42a2657a.docx', 'http://127.0.0.1:8100/langchat/2025030767cad6ea8a52eabc42a2657a.docx', 'langchat/', '20250307', 22580, 'docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'local', '2025-03-07 19:22:19');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('b572ec6532f03530b8c2b45c93a26141', '91b4524a46a949601e7f3b004ed76034', NULL, '36946717.JPEG', '66b6e0fbcdb220c420fe6bae.JPEG', 'http://127.0.0.1/langchat/2024081066b6e0fbcdb220c420fe6bae.JPEG', 'langchat/', '20240810', 11744, 'JPEG', 'image/jpeg', 'local', '2024-08-10 11:39:40');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('b63bc6310584c1df28d77b890b0204f6', '91b4524a46a949601e7f3b004ed76034', NULL, '副本元鼎供应链协同系统技术方案.docx', '67f4dc5daa528ddd53ec249d.docx', 'http://127.0.0.1:8100/langchat/2025040867f4dc5daa528ddd53ec249d.docx', 'langchat/', '20250408', 391175, 'docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'local', '2025-04-08 16:20:46');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('bb20a3b8c4329c21bb871267d0b1efe4', '91b4524a46a949601e7f3b004ed76034', NULL, '副本元鼎供应链协同系统技术方案.docx', '67f4dbcfaa528ddd53ec2499.docx', 'http://127.0.0.1:8100/langchat/2025040867f4dbcfaa528ddd53ec2499.docx', 'langchat/', '20250408', 391175, 'docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'local', '2025-04-08 16:18:24');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('cc5bd4fffb8da1296bc87cc40ececb66', '91b4524a46a949601e7f3b004ed76034', NULL, '36946717.JPEG', '66b6e0a2cdb26cd406afc10a.JPEG', 'http://127.0.0.1/langchat/2024081066b6e0a2cdb26cd406afc10a.JPEG', 'langchat/', '20240810', 11744, 'JPEG', 'image/jpeg', 'local', '2024-08-10 11:38:10');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('d44881298d85d6e240bcd1638ef43194', '91b4524a46a949601e7f3b004ed76034', NULL, 'B2B交易系统产品文档.docx', '67cbae418a529490d99e0ca9.docx', 'http://127.0.0.1:8100/langchat/2025030867cbae418a529490d99e0ca9.docx', 'langchat/', '20250308', 22580, 'docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'local', '2025-03-08 10:41:06');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('d6336bdf867e811bc19b9efab97226f6', '91b4524a46a949601e7f3b004ed76034', NULL, '副本元鼎供应链协同系统技术方案.docx', '67f4e058aa525803cd47a48f.docx', 'http://127.0.0.1:8100/langchat/2025040867f4e058aa525803cd47a48f.docx', 'langchat/', '20250408', 391175, 'docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'local', '2025-04-08 16:37:45');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('d7e1933a3918e509a801905a191eeb30', '91b4524a46a949601e7f3b004ed76034', NULL, 'B2B交易系统产品文档.docx', '67cad6a18a52eabc42a26577.docx', 'http://127.0.0.1:8100/langchat/2025030767cad6a18a52eabc42a26577.docx', 'langchat/', '20250307', 22580, 'docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'local', '2025-03-07 19:21:05');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('e6410c83178268586dbd6cf0e8b2d994', '91b4524a46a949601e7f3b004ed76034', NULL, 'Ai-supply.md', '67caaa498a52120ec717e933.md', 'http://127.0.0.1:8100/langchat/2025030767caaa498a52120ec717e933.md', 'langchat/', '20250307', 6828070, 'md', 'text/markdown', 'local', '2025-03-07 16:11:53');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('eaf773844bb840ddb824a25aab430773', '91b4524a46a949601e7f3b004ed76034', NULL, 'B2B交易系统产品文档.docx', '67cbabb38a529490d99e0ca7.docx', 'http://127.0.0.1:8100/langchat/2025030867cbabb38a529490d99e0ca7.docx', 'langchat/', '20250308', 22580, 'docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'local', '2025-03-08 10:30:11');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('f2803e0b9e0dc86c0c838db0c26a5217', '91b4524a46a949601e7f3b004ed76034', NULL, 'Ai-supply.md', '67cabb7b8a523b4befd0a0cf.md', 'http://127.0.0.1:8100/langchat/2025030767cabb7b8a523b4befd0a0cf.md', 'langchat/', '20250307', 6828070, 'md', 'text/markdown', 'local', '2025-03-07 17:25:15');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('f5ee4c24a4024530069d3b8dd980572a', '91b4524a46a949601e7f3b004ed76034', NULL, 'B2B交易系统产品文档.docx', '67cacb808a52c1a2d66a08d7.docx', 'http://127.0.0.1:8100/langchat/2025030767cacb808a52c1a2d66a08d7.docx', 'langchat/', '20250307', 22580, 'docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'local', '2025-03-07 18:33:37');
INSERT INTO `aigc_oss` (`id`, `user_id`, `oss_id`, `original_filename`, `filename`, `url`, `base_path`, `path`, `size`, `ext`, `content_type`, `platform`, `create_time`) VALUES ('f912f02b5548b7b8e6262d6c356629f0', '91b4524a46a949601e7f3b004ed76034', NULL, '副本元鼎供应链协同系统技术方案.docx', '67f4df70aa526e8ab128f7b9.docx', 'http://127.0.0.1:8100/langchat/2025040867f4df70aa526e8ab128f7b9.docx', 'langchat/', '20250408', 391175, 'docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'local', '2025-04-08 16:33:52');
COMMIT;

-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_dept`;
CREATE TABLE `sys_dept` (
  `id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '部门ID',
  `parent_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '上级部门ID',
  `name` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '部门名称',
  `order_no` int DEFAULT NULL COMMENT '排序',
  `des` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '描述',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='部门表';

-- ----------------------------
-- Records of sys_dept
-- ----------------------------
BEGIN;
INSERT INTO `sys_dept` (`id`, `parent_id`, `name`, `order_no`, `des`) VALUES ('14b300858a898c6dcfd3dc95dde6df81', 'ece0a14ab891e775ff9f6252731130b7', '事业部', NULL, '事业部');
INSERT INTO `sys_dept` (`id`, `parent_id`, `name`, `order_no`, `des`) VALUES ('16794f488aa3b6f77012749a8160f45e', 'e8017fb290f576f5e1f60be4ab4f166a', '前端研发团队', NULL, '前端研发团队');
INSERT INTO `sys_dept` (`id`, `parent_id`, `name`, `order_no`, `des`) VALUES ('3f7ed841ec5e92ee039fd83bf3fd0ee4', '14b300858a898c6dcfd3dc95dde6df81', '北区事业部', NULL, '北区事业部');
INSERT INTO `sys_dept` (`id`, `parent_id`, `name`, `order_no`, `des`) VALUES ('87388f69e48e53c3771bbd2a56256374', '14b300858a898c6dcfd3dc95dde6df81', '销售团队', NULL, '销售团队');
INSERT INTO `sys_dept` (`id`, `parent_id`, `name`, `order_no`, `des`) VALUES ('da6b0029262feb514ab8c70d7f72c2c7', 'e8017fb290f576f5e1f60be4ab4f166a', '后端研发团队', NULL, '后端研发团队');
INSERT INTO `sys_dept` (`id`, `parent_id`, `name`, `order_no`, `des`) VALUES ('e8017fb290f576f5e1f60be4ab4f166a', 'ece0a14ab891e775ff9f6252731130b7', '产品研发部', NULL, '产品研发部');
INSERT INTO `sys_dept` (`id`, `parent_id`, `name`, `order_no`, `des`) VALUES ('ece0a14ab891e775ff9f6252731130b7', '0', '组织架构', 1, NULL);
COMMIT;

-- ----------------------------
-- Table structure for sys_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_log`;
CREATE TABLE `sys_log` (
  `id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '编号',
  `type` int DEFAULT NULL COMMENT '日志类型，1正常 2异常 ',
  `username` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '操作用户',
  `operation` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '操作描述',
  `url` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '请求URL',
  `time` bigint DEFAULT NULL COMMENT '耗时(毫秒)',
  `method` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '操作方法',
  `params` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '操作参数',
  `ip` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '用户代理',
  `create_time` datetime DEFAULT NULL COMMENT '操作时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='日志表';

-- ----------------------------
-- Records of sys_log
-- ----------------------------
BEGIN;
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('00716adaa3f578732b5ecead84cea014', 1, 'administrator', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-06 19:01:14');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('0ca8428240e46ad5e3b85cd264d21957', 1, 'administrator', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-04-22 18:10:28');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('0ed4917e921a710a2a53e23ace169ed6', 1, 'administrator', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-08 10:56:05');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('1197d0239e4c3e5f9ff992b5fe34ab2b', 1, 'administrator', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-04-18 17:57:42');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('1401bb516b1774d7792cca7213c440b1', 1, 'administrator', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-09 11:55:06');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('15097318f9202e4f4467d2f52b9197b7', 1, 'administrator', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-04-08 16:04:58');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('1c9fef55383e799d85a2dd9860cbb675', 1, 'administrator', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-04-28 11:31:49');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('2b5b81513f86cba2fbc794888d460ff3', 1, 'administrator', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-06 19:45:40');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('2d02cb43e60d49138fd134b52cfa6976', 1, 'administrator', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-13 18:28:51');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('3026a034e8ce48756ef5c430790bf65e', 1, 'administrator', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-09 11:55:50');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('348f8cf3d4fe47278b09a1360b9db261', 1, 'administrator', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-06 19:12:04');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('37d3014ebbe65e05322912e94468be1a', 1, 'administrator', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-06 19:44:40');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('3bbe0ce97c7ac472d6c60e4176b254fd', 1, 'langchat', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-06 19:07:44');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('********************************', 1, 'administrator', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-06 19:12:26');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('3d083576f99549973b8596e3e7a8ee0c', 1, 'administrator', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-06 19:05:36');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('3e786c63aa129292cc794cae0e65c931', 1, 'administrator', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-03-05 17:36:40');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('3fce1bc6f15b04d9ae64c2f8bfba2dcb', 1, 'administrator', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-06 19:37:13');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('48812f24490a9d1f4bf18d5504c7d387', 1, 'administrator', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-06 19:40:49');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('49119b95c8f78d8ef93f1a8181c9b19e', 1, 'administrator', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-06 19:11:36');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('4aa18ff5032c569302dd0eb7ecb43217', 1, 'angelwjm', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-13 18:29:24');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('4b78664f9bc9c3442eb4c912edefb00c', 1, 'administrator', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-03-08 15:51:02');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('67b0a4525e2dd0b7eeb2f2bab105f991', 1, 'administrator', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-06 19:44:51');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('6ba153bc04ae9c3ff42220435edbd2ed', 1, 'administrator', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-04-22 13:54:02');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('6dc605f559e702c4a4b415e0919c7dcf', 1, 'administrator', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-06 19:29:01');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('7902450e41be0cb3f05917c7551ccb5e', 1, 'administrator', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-03-04 15:56:30');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('7a9f841e7d0ae519aac7cb97e7e5c3ad', 1, 'administrator', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-03-08 15:33:50');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('9c7b3ebb24b249d354f9785e5859046f', 1, 'wujm', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-06 19:12:15');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('aec937af67729d1e26eeadf094db4955', 1, 'administrator', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-06 19:11:44');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('afa23aded3aae16fde6c929caf4e4398', 1, 'administrator', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-06 18:54:58');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('b811645217a0b5ea2bd1240149b1e2e6', 1, 'administrator', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-03-08 14:02:49');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('b95771a7092e6b0575a7e46f416756f3', 1, 'wujm', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-06 19:08:03');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('ba5c3bf552dc9940e2e69af543ed325e', 1, 'administrator', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-03-08 14:25:11');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('be36835d62158128c198a807c021e2fd', 1, 'administrator', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-06 19:47:15');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('c3382aaaf4747a51b5b041ab33de1a79', 1, 'angelwjm', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-09 11:27:25');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('c657794798d0712a3292b691c90d910b', 1, 'langchat', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-06 19:01:49');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('d411b04f4d714d56d5d97ebc973d0eeb', 1, 'administrator', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-03-07 13:51:48');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('de18e7a69bb4fca9fc93c7f63b3a1f4b', 1, 'administrator', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-12 16:19:22');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('df299bc71a8e8c5363a350b0b921446f', 1, 'administrator', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-13 16:25:56');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('e3fbd0f2fc34aa9048aca8c4fb3e7886', 1, 'administrator', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-12 11:59:22');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('f14fabe48a36e8c4f94fb41ae7c940f8', 1, 'wujm', '服务端登录', '/auth/login', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-06 19:29:18');
INSERT INTO `sys_log` (`id`, `type`, `username`, `operation`, `url`, `time`, `method`, `params`, `ip`, `user_agent`, `create_time`) VALUES ('f40c4a612fb75d99363bba0ebd4bc24f', 1, 'angelwjm', '服务端注册', '/auth/register', NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-09 11:27:16');
COMMIT;

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu` (
  `id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键',
  `name` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '菜单名称',
  `parent_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '父级ID',
  `path` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '菜单路径',
  `perms` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '权限标识',
  `type` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '菜单类型',
  `order_no` int DEFAULT NULL COMMENT '排序',
  `icon` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '菜单图标',
  `component` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '组件路径',
  `is_disabled` tinyint(1) DEFAULT NULL COMMENT '是否禁用',
  `is_ext` tinyint(1) DEFAULT NULL COMMENT '是否外链',
  `is_keepalive` tinyint(1) DEFAULT NULL COMMENT '是否缓存',
  `is_show` tinyint(1) DEFAULT NULL COMMENT '是否显示',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='菜单表';

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
BEGIN;
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('03917f40dfafba8c7ecb2b8843522a9e', '新增文档', '97a5eac3bfeeabe4013d828b919786f7', NULL, 'aigc:docs:add', 'button', 10, NULL, NULL, 0, 0, 1, NULL);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('04d3d71c4f0fe1416a8dd642eecbf230', '流程列表', 'a5e69f57426539250644a1580af58030', 'list', 'list', 'menu', 0, 'AppsOutline', '/workflow/index', 0, 0, 0, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('0597ccbb7b98b2d443bffb3f1785ce1c', '新增知识库', '97a5eac3bfeeabe4013d828b919786f7', NULL, 'aigc:knowledge:add', 'button', 2, NULL, NULL, 0, 0, 1, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('0976afe16e7b328886408f3e117733c1', '新增角色', '6f8aff1f2c458e5add9adb6d284fb451', NULL, 'upms:role:add', 'button', 2, NULL, NULL, 0, 0, 1, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('0adfa2c3c4d278aedd88019236c1425e', '向量库管理', '374409ab56141b311ccb0f1847dd724a', 'aigc/embed-store', 'embed-store', 'menu', 1, '', '/aigc/embed-store/index', 0, 0, 0, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('0f37f45fb15c38de948b17b8a24e431b', '修改菜单', 'b1df787d8af5b728181a4b9acf38da93', NULL, 'upms:menu:update', 'button', 3, NULL, NULL, 0, 0, 1, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('1440895f54ccae1c1e2706e3dbcf6f5d', '文本向量化', '43563b039d30b990f87af37783115ff4', NULL, 'aigc:embedding:text', 'button', 4, NULL, NULL, 0, 0, 0, NULL);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('1c1fa2c50ff306144a0ea2528dcec96b', '重置密码', 'b29de942eeabc9419185951f57be11f3', NULL, 'upms:user:reset', 'button', 5, NULL, NULL, 0, 0, 1, NULL);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('2dc3a6e16351901710060fd846ee9f19', '新增菜单', 'b1df787d8af5b728181a4b9acf38da93', NULL, 'upms:menu:add', 'button', 2, NULL, NULL, 0, 0, 1, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('2f5735d125b4537076893a4b4a37a188', '系统管理', '0', 'system', 'system', 'menu', 4, 'SettingsOutline', 'Layout', 0, 0, 1, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('35dcd70c8a4008b554b71bf02ab07b61', '删除聊天记录', 'bdd70f2c1ee068c13bd3288eff07c8e2', NULL, 'chat:messages:clean', 'button', 3, NULL, NULL, 0, 0, 0, NULL);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('374409ab56141b311ccb0f1847dd724a', 'AIGC平台', '0', 'aigc', 'aigc', 'menu', 2, 'CubeOutline', 'Layout', 0, 0, 1, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('3d1700109ece0187ba5e76217cd71995', '删除对话数据', 'f1ad3c056ac91fa5292a99f223155afc', NULL, 'aigc:message:delete', 'button', 2, NULL, NULL, 0, 0, 0, NULL);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('43563b039d30b990f87af37783115ff4', 'AI应用管理', 'a2ccfe694cd91cf159ad35626e4ea202', 'list', 'aigc:app', 'menu', 2, '', '/app/index', 0, 0, 1, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('4488cb5271b1220647d4a83cfbcb7b15', '文档向量化', '43563b039d30b990f87af37783115ff4', NULL, 'aigc:embedding:docs', 'button', 5, NULL, NULL, 0, 0, 0, NULL);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('510a89f01571d7eaa3b1393c8534ab6f', '删除应用', '43563b039d30b990f87af37783115ff4', NULL, 'aigc:app:delete', 'button', 3, NULL, NULL, 0, 0, 0, NULL);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('51ed9b1f27acc4695667821eac5f35cb', '删除文档', '97a5eac3bfeeabe4013d828b919786f7', NULL, 'aigc:docs:delete', 'button', 12, NULL, NULL, 0, 0, 1, NULL);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('5514605bae6ffdad3e4acff3e9e9742c', '新增应用', '43563b039d30b990f87af37783115ff4', NULL, 'aigc:app:add', 'button', 1, NULL, NULL, 0, 0, 0, NULL);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('5ce2349dc38a84cfbf0f5b260b41a2b6', '模型管理', '374409ab56141b311ccb0f1847dd724a', 'model', 'model', 'menu', 0, '', '/aigc/model/index', 0, 0, 1, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('62beffe9252934b4adeeef3125cab584', '新增模型', '5ce2349dc38a84cfbf0f5b260b41a2b6', NULL, 'aigc:model:add', 'button', 2, NULL, NULL, 0, 0, 1, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('64a1109e89e060bd7018806c62c8e7d3', '修改向量库', '0adfa2c3c4d278aedd88019236c1425e', NULL, 'aigc:embed-store:update', 'button', 2, NULL, NULL, 0, 0, 0, NULL);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('65deeb7aedec5490425ad2572d536ea9', 'Chat权限', '43563b039d30b990f87af37783115ff4', NULL, 'chat:completions', 'button', 6, NULL, NULL, 0, 0, 0, NULL);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('67435b96a82c494b48fc6458b7103d4d', '页面预览', '43563b039d30b990f87af37783115ff4', NULL, 'chat-docs:view', 'button', 1, NULL, NULL, 0, 0, 0, NULL);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('6c27a1ddba0ce10d7e242cb7e568bfc0', '删除模型', '5ce2349dc38a84cfbf0f5b260b41a2b6', NULL, 'aigc:model:delete', 'button', 4, NULL, NULL, 0, 0, 1, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('6cb25c77d3087d47a26c08d904a442fa', '新增部门', '8fb8756a4587cc4c76401a63ea194568', NULL, 'upms:dept:add', 'button', 2, NULL, NULL, 0, 0, 1, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('6f8aff1f2c458e5add9adb6d284fb451', '角色管理', '7c411c7d41034d6708103c8e0da19ced', 'role', 'role', 'menu', 2, NULL, '/upms/role/index', 0, 0, 1, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('72215ec9609e546cd56bacf4c29e482d', '修改部门', '8fb8756a4587cc4c76401a63ea194568', NULL, 'upms:dept:update', 'button', 3, NULL, NULL, 0, 0, 1, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('757e0f3fb5e153c15f3355a97f731f1e', '删除向量库', '0adfa2c3c4d278aedd88019236c1425e', NULL, 'aigc:embed-store:delete', 'button', 3, NULL, NULL, 0, 0, 0, NULL);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('7b3e324f4470bbd4b8363b379fd3ed3c', '删除部门', '8fb8756a4587cc4c76401a63ea194568', NULL, 'upms:dept:delete', 'button', 4, NULL, NULL, 0, 0, 1, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('7c411c7d41034d6708103c8e0da19ced', '权限管理', '0', 'upms', 'upms', 'menu', 3, 'KeyOutline', 'Layout', 0, 0, 1, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('7d225cd8d60da156e17e341f86304970', '删除知识库', '97a5eac3bfeeabe4013d828b919786f7', NULL, 'aigc:knowledge:delete', 'button', 4, NULL, NULL, 0, 0, 1, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('80c1246cff10a470f67b4a58b0fe257e', '修改知识库', '97a5eac3bfeeabe4013d828b919786f7', NULL, 'aigc:knowledge:update', 'button', 3, NULL, NULL, 0, 0, 1, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('89f1ba9a70e8bf72961f321156361fe6', '删除角色', '6f8aff1f2c458e5add9adb6d284fb451', NULL, 'upms:role:delete', 'button', 4, NULL, NULL, 0, 0, 1, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('8b2924d753d4e2c1932e1f17e30d0c52', '修改模型', '5ce2349dc38a84cfbf0f5b260b41a2b6', NULL, 'aigc:model:update', 'button', 3, NULL, NULL, 0, 0, 1, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('8c0eb60ccef367ce7048e5d486aaa3a9', '日志管理', '2f5735d125b4537076893a4b4a37a188', 'log', 'log', 'menu', 1, NULL, '/system/log/index', 0, 0, 1, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('8fb8756a4587cc4c76401a63ea194568', '部门管理', '7c411c7d41034d6708103c8e0da19ced', 'dept', 'dept', 'menu', 3, NULL, '/upms/dept/index', 0, 0, 1, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('979631c0fae847a8dd59321b1da7d5e7', '新增用户', 'b29de942eeabc9419185951f57be11f3', NULL, 'upms:user:add', 'button', 2, NULL, NULL, 0, 0, 1, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('97a5eac3bfeeabe4013d828b919786f7', '知识库管理', '374409ab56141b311ccb0f1847dd724a', 'knowledge', 'knowledge', 'menu', 1, 'alert', '/aigc/knowledge/index', 0, 0, 1, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('9e526a34052ca76cf4f1ec685187e84a', '删除菜单', 'b1df787d8af5b728181a4b9acf38da93', NULL, 'upms:menu:delete', 'button', 4, NULL, NULL, 0, 0, 1, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('a00ca2926f617715b236c113b2ea14b9', '删除令牌', 'abb7e994494b96797b262cc2c72ea620', NULL, 'system:token:delete', 'button', 2, NULL, NULL, 0, 0, 1, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('a2ccfe694cd91cf159ad35626e4ea202', 'AIGC应用', '0', 'app', 'app', 'menu', 1, 'PaperPlaneOutline', 'Layout', 0, 0, 1, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('a5e69f57426539250644a1580af58030', '工作流', '0', '/workflow', 'workflow', 'menu', 0, 'AnalyticsOutline', 'Layout', 0, 0, 0, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('a985c800d102da822b59dacc77ee6c9d', '修改用户', 'b29de942eeabc9419185951f57be11f3', NULL, 'upms:user:update', 'button', 3, NULL, NULL, 0, 0, 1, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('abb7e994494b96797b262cc2c72ea620', '令牌管理', '2f5735d125b4537076893a4b4a37a188', 'token', 'token', 'menu', 2, NULL, '/system/token/index', 0, 0, 1, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('af8e11cdd57a935bbcf36f8e53cc889f', '新增向量库', '0adfa2c3c4d278aedd88019236c1425e', NULL, 'aigc:embed-store:add', 'button', 1, NULL, NULL, 0, 0, 0, NULL);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('b1df787d8af5b728181a4b9acf38da93', '菜单管理', '7c411c7d41034d6708103c8e0da19ced', 'menu', 'menu', 'menu', 4, NULL, '/upms/menu/index', 0, 0, 1, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('b29de942eeabc9419185951f57be11f3', '用户管理', '7c411c7d41034d6708103c8e0da19ced', 'user', 'user', 'menu', 1, NULL, '/upms/user/index', 0, 0, 1, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('b3331acdd06227088f3fb4b92b8b0365', '删除日志', '8c0eb60ccef367ce7048e5d486aaa3a9', NULL, 'system:log:delete', 'button', 2, NULL, NULL, 0, 0, 1, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('bdd70f2c1ee068c13bd3288eff07c8e2', 'AI聊天助手', 'a2ccfe694cd91cf159ad35626e4ea202', 'chat', 'aigc:chat', 'menu', 1, '', '/chat/index', 0, 0, 1, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('c212381ae7a2333416a18e486f044777', '账单统计', '374409ab56141b311ccb0f1847dd724a', 'order', 'order', 'menu', 5, NULL, '/aigc/order/index', 0, 0, 1, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('cac8d8f2f35bd872dcc3652add9bbd08', '修改角色', '6f8aff1f2c458e5add9adb6d284fb451', NULL, 'upms:role:update', 'button', 3, NULL, NULL, 0, 0, 1, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('d99e460bd02a18eaf15206b09f709bfb', '修改应用', '43563b039d30b990f87af37783115ff4', NULL, 'aigc:app:update', 'button', 2, NULL, NULL, 0, 0, 0, NULL);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('f1ad3c056ac91fa5292a99f223155afc', '对话数据', '374409ab56141b311ccb0f1847dd724a', 'message', 'message', 'menu', 4, NULL, '/aigc/message/index', 0, 0, 1, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('f5031ca9ca645316c6eb94f4ea8684f8', '修改文档', '97a5eac3bfeeabe4013d828b919786f7', NULL, 'aigc:docs:update', 'button', 11, NULL, NULL, 0, 0, 1, NULL);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('f5d6cbc1e97e2a87149598f86c1bdbbe', '删除用户', 'b29de942eeabc9419185951f57be11f3', NULL, 'upms:user:delete', 'button', 4, NULL, NULL, 0, 0, 1, 1);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('fadaa37669c31316d8addac152f1f0ff', '聊天权限', 'bdd70f2c1ee068c13bd3288eff07c8e2', NULL, 'chat:completions', 'button', 2, NULL, NULL, 0, 0, 0, NULL);
INSERT INTO `sys_menu` (`id`, `name`, `parent_id`, `path`, `perms`, `type`, `order_no`, `icon`, `component`, `is_disabled`, `is_ext`, `is_keepalive`, `is_show`) VALUES ('ffca98852cd6faea6b20e2a339578f13', '删除令牌', 'abb7e994494b96797b262cc2c72ea620', NULL, 'system:token:delete', 'button', 2, NULL, NULL, 0, 0, 1, 1);
COMMIT;

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role` (
  `id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键',
  `name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '角色名称',
  `code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '角色别名',
  `des` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '描述',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='角色表';

-- ----------------------------
-- Records of sys_role
-- ----------------------------
BEGIN;
INSERT INTO `sys_role` (`id`, `name`, `code`, `des`) VALUES ('2827e950043adf67b7fe10306d3e94e4', '超级管理员角色', 'administrator', '超级管理员管理员，不受权限控制，不可编辑');
INSERT INTO `sys_role` (`id`, `name`, `code`, `des`) VALUES ('bbe1863be68ad07347b1dee0e358f18a', '默认人员角色', 'default_env', '后台新用户注册角色，不可删除');
INSERT INTO `sys_role` (`id`, `name`, `code`, `des`) VALUES ('d0d0cab7c147d865d35e70fc62f2f19e', '演示环境角色', 'demo_env', '演示环境使用角色，拥有页面预览权限，没有操作权限');
COMMIT;

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu` (
  `role_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '角色ID',
  `menu_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '菜单/按钮ID',
  PRIMARY KEY (`role_id`,`menu_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='角色资源关联表';

-- ----------------------------
-- Records of sys_role_menu
-- ----------------------------
BEGIN;
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('bbe1863be68ad07347b1dee0e358f18a', '03917f40dfafba8c7ecb2b8843522a9e');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('bbe1863be68ad07347b1dee0e358f18a', '04d3d71c4f0fe1416a8dd642eecbf230');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('bbe1863be68ad07347b1dee0e358f18a', '0597ccbb7b98b2d443bffb3f1785ce1c');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('bbe1863be68ad07347b1dee0e358f18a', '0adfa2c3c4d278aedd88019236c1425e');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('bbe1863be68ad07347b1dee0e358f18a', '1440895f54ccae1c1e2706e3dbcf6f5d');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('bbe1863be68ad07347b1dee0e358f18a', '35dcd70c8a4008b554b71bf02ab07b61');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('bbe1863be68ad07347b1dee0e358f18a', '374409ab56141b311ccb0f1847dd724a');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('bbe1863be68ad07347b1dee0e358f18a', '3d1700109ece0187ba5e76217cd71995');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('bbe1863be68ad07347b1dee0e358f18a', '43563b039d30b990f87af37783115ff4');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('bbe1863be68ad07347b1dee0e358f18a', '4488cb5271b1220647d4a83cfbcb7b15');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('bbe1863be68ad07347b1dee0e358f18a', '510a89f01571d7eaa3b1393c8534ab6f');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('bbe1863be68ad07347b1dee0e358f18a', '51ed9b1f27acc4695667821eac5f35cb');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('bbe1863be68ad07347b1dee0e358f18a', '5514605bae6ffdad3e4acff3e9e9742c');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('bbe1863be68ad07347b1dee0e358f18a', '5ce2349dc38a84cfbf0f5b260b41a2b6');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('bbe1863be68ad07347b1dee0e358f18a', '62beffe9252934b4adeeef3125cab584');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('bbe1863be68ad07347b1dee0e358f18a', '64a1109e89e060bd7018806c62c8e7d3');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('bbe1863be68ad07347b1dee0e358f18a', '65deeb7aedec5490425ad2572d536ea9');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('bbe1863be68ad07347b1dee0e358f18a', '67435b96a82c494b48fc6458b7103d4d');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('bbe1863be68ad07347b1dee0e358f18a', '6c27a1ddba0ce10d7e242cb7e568bfc0');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('bbe1863be68ad07347b1dee0e358f18a', '757e0f3fb5e153c15f3355a97f731f1e');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('bbe1863be68ad07347b1dee0e358f18a', '7d225cd8d60da156e17e341f86304970');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('bbe1863be68ad07347b1dee0e358f18a', '80c1246cff10a470f67b4a58b0fe257e');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('bbe1863be68ad07347b1dee0e358f18a', '8b2924d753d4e2c1932e1f17e30d0c52');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('bbe1863be68ad07347b1dee0e358f18a', '97a5eac3bfeeabe4013d828b919786f7');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('bbe1863be68ad07347b1dee0e358f18a', 'a2ccfe694cd91cf159ad35626e4ea202');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('bbe1863be68ad07347b1dee0e358f18a', 'a5e69f57426539250644a1580af58030');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('bbe1863be68ad07347b1dee0e358f18a', 'af8e11cdd57a935bbcf36f8e53cc889f');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('bbe1863be68ad07347b1dee0e358f18a', 'bdd70f2c1ee068c13bd3288eff07c8e2');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('bbe1863be68ad07347b1dee0e358f18a', 'd99e460bd02a18eaf15206b09f709bfb');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('bbe1863be68ad07347b1dee0e358f18a', 'f1ad3c056ac91fa5292a99f223155afc');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('bbe1863be68ad07347b1dee0e358f18a', 'f5031ca9ca645316c6eb94f4ea8684f8');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('bbe1863be68ad07347b1dee0e358f18a', 'fadaa37669c31316d8addac152f1f0ff');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('d0d0cab7c147d865d35e70fc62f2f19e', '0825f18b3860f8c01a9b0d8221280e3b');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('d0d0cab7c147d865d35e70fc62f2f19e', '0adfa2c3c4d278aedd88019236c1425e');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('d0d0cab7c147d865d35e70fc62f2f19e', '2f5735d125b4537076893a4b4a37a188');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('d0d0cab7c147d865d35e70fc62f2f19e', '374409ab56141b311ccb0f1847dd724a');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('d0d0cab7c147d865d35e70fc62f2f19e', '43563b039d30b990f87af37783115ff4');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('d0d0cab7c147d865d35e70fc62f2f19e', '5ce2349dc38a84cfbf0f5b260b41a2b6');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('d0d0cab7c147d865d35e70fc62f2f19e', '6f8aff1f2c458e5add9adb6d284fb451');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('d0d0cab7c147d865d35e70fc62f2f19e', '7c411c7d41034d6708103c8e0da19ced');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('d0d0cab7c147d865d35e70fc62f2f19e', '8c0eb60ccef367ce7048e5d486aaa3a9');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('d0d0cab7c147d865d35e70fc62f2f19e', '8fb8756a4587cc4c76401a63ea194568');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('d0d0cab7c147d865d35e70fc62f2f19e', '97a5eac3bfeeabe4013d828b919786f7');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('d0d0cab7c147d865d35e70fc62f2f19e', 'a2ccfe694cd91cf159ad35626e4ea202');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('d0d0cab7c147d865d35e70fc62f2f19e', 'abb7e994494b96797b262cc2c72ea620');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('d0d0cab7c147d865d35e70fc62f2f19e', 'b1df787d8af5b728181a4b9acf38da93');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('d0d0cab7c147d865d35e70fc62f2f19e', 'b29de942eeabc9419185951f57be11f3');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('d0d0cab7c147d865d35e70fc62f2f19e', 'bdd70f2c1ee068c13bd3288eff07c8e2');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('d0d0cab7c147d865d35e70fc62f2f19e', 'c212381ae7a2333416a18e486f044777');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('d0d0cab7c147d865d35e70fc62f2f19e', 'f1ad3c056ac91fa5292a99f223155afc');
COMMIT;

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user` (
  `id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '用户ID',
  `username` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '用户名',
  `password` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '密码',
  `real_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '真实姓名',
  `sex` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '性别',
  `phone` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '手机',
  `email` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '邮箱',
  `dept_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '部门ID',
  `avatar` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '头像',
  `status` tinyint(1) DEFAULT '0' COMMENT '状态 0锁定 1有效',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='用户表';

-- ----------------------------
-- Records of sys_user
-- ----------------------------
BEGIN;
INSERT INTO `sys_user` (`id`, `username`, `password`, `real_name`, `sex`, `phone`, `email`, `dept_id`, `avatar`, `status`, `create_time`) VALUES ('71e6a4f2f253442094177b7d82f13436', 'angelwjm', '48kQD0O/A69LENSbk/+FxA==', 'angelwjm', NULL, '18520820096', NULL, NULL, NULL, 1, '2025-05-09 11:27:16');
INSERT INTO `sys_user` (`id`, `username`, `password`, `real_name`, `sex`, `phone`, `email`, `dept_id`, `avatar`, `status`, `create_time`) VALUES ('827450c4a39b3c4c14fdfb06f454bfb3', 'wujm', '48kQD0O/A69LENSbk/+FxA==', '吴俊明8', '男', '18520820096', '<EMAIL>', '14b300858a898c6dcfd3dc95dde6df81', NULL, 1, '2024-08-04 13:55:35');
INSERT INTO `sys_user` (`id`, `username`, `password`, `real_name`, `sex`, `phone`, `email`, `dept_id`, `avatar`, `status`, `create_time`) VALUES ('91b4524a46a949601e7f3b004ed76034', 'administrator', 'U3lnYOIEGN38KKy0h3KUSA==', '超级管理员', '男', '19809587831', '<EMAIL>', NULL, NULL, 0, '2024-08-04 13:55:35');
COMMIT;

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role` (
  `user_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '用户ID',
  `role_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_id`,`role_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='用户角色关联表';

-- ----------------------------
-- Records of sys_user_role
-- ----------------------------
BEGIN;
INSERT INTO `sys_user_role` (`user_id`, `role_id`) VALUES ('71e6a4f2f253442094177b7d82f13436', 'bbe1863be68ad07347b1dee0e358f18a');
INSERT INTO `sys_user_role` (`user_id`, `role_id`) VALUES ('827450c4a39b3c4c14fdfb06f454bfb3', 'bbe1863be68ad07347b1dee0e358f18a');
INSERT INTO `sys_user_role` (`user_id`, `role_id`) VALUES ('91b4524a46a949601e7f3b004ed76034', '2827e950043adf67b7fe10306d3e94e4');
COMMIT;

-- ----------------------------
-- Table structure for workflows
-- ----------------------------
DROP TABLE IF EXISTS `workflows`;
CREATE TABLE `workflows` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `app_id` int DEFAULT NULL,
  `graph` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Records of workflows
-- ----------------------------
BEGIN;
INSERT INTO `workflows` (`id`, `name`, `app_id`, `graph`) VALUES (3, '通过http请求获取数据再执行财务指标分析', 1, '{\"nodes\":[{\"id\":\"node_82BjuWLD2POIHUF2\",\"position\":{\"x\":-186.79163213695304,\"y\":-168.63457218666775},\"data\":{\"title\":\"开始节点\",\"description\":\"开始定义输入参数\",\"expand\":true,\"parameters\":[{\"id\":\"g3qfdWpJAsAiHoZx\",\"name\":\"test\",\"required\":true},{\"id\":\"4yGuWCCRLSR7IU9o\",\"name\":\"a\",\"required\":true}]},\"type\":\"startNode\",\"selected\":false,\"measured\":{\"width\":306,\"height\":239},\"dragging\":false},{\"id\":\"node_XUwEBFWaJT6inhm3\",\"position\":{\"x\":897.4090033640784,\"y\":-12.462057106784844},\"data\":{\"title\":\"结束节点\",\"description\":\"结束定义输出参数\",\"expand\":true,\"outputDefs\":[{\"id\":\"CNZ8dn2u62EqAGGP\",\"ref\":\"node_JadG7YfjKLqvwi2l.body\",\"name\":\"body\",\"refType\":\"ref\"},{\"id\":\"xLxQamoVX18u5MGi\",\"refType\":\"ref\",\"name\":\"headers\",\"ref\":\"node_JadG7YfjKLqvwi2l.headers\"},{\"id\":\"sDpB6M4iQHxj64QZ\",\"refType\":\"ref\",\"name\":\"statusCode\",\"ref\":\"node_JadG7YfjKLqvwi2l.statusCode\"}]},\"type\":\"endNode\",\"selected\":true,\"measured\":{\"width\":379,\"height\":273},\"dragging\":false},{\"id\":\"node_JadG7YfjKLqvwi2l\",\"position\":{\"x\":326.11336034923585,\"y\":-130.7515136296053},\"data\":{\"title\":\"Http 请求\",\"description\":\"通过 HTTP 请求获取数据\",\"method\":\"get\",\"expand\":true,\"url\":\"http://127.0.0.1:8100/aigc/workflow/test/company\",\"headers\":[{\"id\":\"nSMkIWyUE0bDFTq1\",\"refType\":\"fixed\",\"name\":\"authorization\",\"value\":\"0adeae5c-61ce-442d-a464-bfae23fc21e5\"}],\"urlParameters\":[{\"id\":\"CXza7DqZr9zCflmJ\",\"refType\":\"fixed\",\"name\":\"companyName\",\"value\":\"潍坊盛清包装制品有限公司\"}],\"outputDefs\":[{\"id\":\"CmvHeIPFyw0XE3O1\",\"name\":\"body\",\"dataType\":\"Object\",\"children\":[]},{\"id\":\"k8VoEkkJwPUO7BDy\",\"name\":\"headers\"},{\"id\":\"DpnM3HGdKTe5Pzoh\",\"name\":\"statusCode\"}],\"bodyType\":\"\"},\"type\":\"httpNode\",\"selected\":false,\"measured\":{\"width\":404,\"height\":581},\"dragging\":false}],\"edges\":[{\"markerEnd\":{\"type\":\"arrowclosed\",\"width\":20,\"height\":20},\"source\":\"node_82BjuWLD2POIHUF2\",\"target\":\"node_JadG7YfjKLqvwi2l\",\"id\":\"xy-edge__node_82BjuWLD2POIHUF2-node_JadG7YfjKLqvwi2l\",\"selected\":false},{\"markerEnd\":{\"type\":\"arrowclosed\",\"width\":20,\"height\":20},\"source\":\"node_JadG7YfjKLqvwi2l\",\"target\":\"node_XUwEBFWaJT6inhm3\",\"id\":\"xy-edge__node_JadG7YfjKLqvwi2l-node_XUwEBFWaJT6inhm3\",\"selected\":false}],\"viewport\":{\"x\":156.64295366052318,\"y\":115.59775796317831,\"zoom\":0.6517111971750342}}');
INSERT INTO `workflows` (`id`, `name`, `app_id`, `graph`) VALUES (18, '财务指标', NULL, '{\"nodes\":[{\"id\":\"node_E8GChfalJrTJLOsX\",\"position\":{\"x\":-1571.851360065194,\"y\":-1742.7102447428915},\"data\":{\"title\":\"开始节点\",\"description\":\"开始定义输入参数\",\"expand\":false,\"parameters\":[{\"id\":\"Hvql2AC8Io8t2Zcv\",\"name\":\"companyName\"},{\"id\":\"MWR1veLkgklxQxDt\",\"name\":\"knowledge\"}]},\"type\":\"startNode\",\"selected\":false,\"measured\":{\"width\":306,\"height\":120},\"dragging\":false},{\"id\":\"node_JkvvbOxhsOc4djru\",\"position\":{\"x\":351.81275455411367,\"y\":-1438.9546330948226},\"data\":{\"title\":\"结束节点\",\"description\":\"结束定义输出参数\",\"expand\":true,\"outputDefs\":[{\"id\":\"C0CXAV1Uy3xvApuu\",\"refType\":\"ref\",\"name\":\"result\",\"ref\":\"node_ECjFDgU9zAHZYC3v.output\"}]},\"type\":\"endNode\",\"selected\":false,\"measured\":{\"width\":352,\"height\":206},\"dragging\":false},{\"id\":\"node_ECjFDgU9zAHZYC3v\",\"position\":{\"x\":-455.777683514411,\"y\":-1768.6622536430375},\"data\":{\"title\":\"大模型\",\"description\":\"使用大模型处理问题\",\"outType\":\"text\",\"outputDefs\":[{\"name\":\"output\",\"dataType\":\"String\",\"dataTypeDisabled\":true,\"deleteDisabled\":true}],\"expand\":true,\"llmId\":\"ac05866194643c5341a73d837b2017c8\",\"systemPrompt\":\"请根据以下财务数据和财务指标知识库，全面分析企业的财务状况，并生成一份详细的财务分析报告：\\n\\n财务数据：\\n{financial_data}\\n\\n财务指标知识库：\\n{knowledge}\\n\\n报告要求：\\n1. 计算关键财务指标，包括但不限于流动比率、速动比率、资产负债率、毛利率、净利率等。\\n2. 根据计算结果，评估企业的短期偿债能力、长期偿债能力、盈利能力和运营效率。\\n3. 指出企业在财务健康方面存在的潜在问题或风险。\\n4. 提供基于数据是否可以进行融资放款建议。\\n5. 报告应结构清晰，包括摘要、详细分析、结论和建议等部分。\\n\\n请确保报告内容准确、专业，并基于提供的数据和知识库内容。\",\"parameters\":[{\"id\":\"MpzdG2Jr8CI7aKGL\",\"refType\":\"ref\",\"ref\":\"node_jIrN4JD2bhikQICO.body\",\"name\":\"financial_data\"},{\"id\":\"mrVkh3vKiniBupjL\",\"refType\":\"ref\",\"name\":\"knowledge\",\"ref\":\"node_E8GChfalJrTJLOsX.knowledge\"}],\"userPrompt\":\"{financial_data} 风控评估\",\"topP\":0.8,\"temperature\":0.2},\"type\":\"llmNode\",\"selected\":false,\"measured\":{\"width\":375,\"height\":815},\"dragging\":false},{\"id\":\"node_jIrN4JD2bhikQICO\",\"position\":{\"x\":-1129.7617538818702,\"y\":-1412.2790130616206},\"data\":{\"title\":\"Http 请求\",\"description\":\"通过 HTTP 请求获取数据\",\"method\":\"get\",\"expand\":true,\"url\":\"http://127.0.0.1:8100/aigc/workflow/test/company\",\"headers\":[{\"id\":\"GlkHqZCBP3NT7mjC\",\"refType\":\"fixed\",\"name\":\"authorization\",\"defaultValue\":\"0adeae5c-61ce-442d-a464-bfae23fc21e5\",\"value\":\"05c0bd8b-8f2a-40ca-a3fe-3e8df810bd5d\"}],\"urlParameters\":[{\"id\":\"IExoTMk2BhHAMR3A\",\"refType\":\"ref\",\"name\":\"companyName\",\"ref\":\"node_E8GChfalJrTJLOsX.companyName\"}],\"outputDefs\":[{\"id\":\"qV9pgos08T5D5HIA\",\"name\":\"body\",\"dataType\":\"String\",\"children\":[]}],\"bodyType\":\"\"},\"type\":\"httpNode\",\"selected\":true,\"measured\":{\"width\":404,\"height\":514},\"dragging\":false}],\"edges\":[{\"markerEnd\":{\"type\":\"arrowclosed\",\"width\":20,\"height\":20},\"source\":\"node_ECjFDgU9zAHZYC3v\",\"target\":\"node_JkvvbOxhsOc4djru\",\"id\":\"xy-edge__node_ECjFDgU9zAHZYC3v-node_JkvvbOxhsOc4djru\",\"selected\":false},{\"markerEnd\":{\"type\":\"arrowclosed\",\"width\":20,\"height\":20},\"source\":\"node_E8GChfalJrTJLOsX\",\"target\":\"node_jIrN4JD2bhikQICO\",\"id\":\"xy-edge__node_E8GChfalJrTJLOsX-node_jIrN4JD2bhikQICO\",\"selected\":false},{\"markerEnd\":{\"type\":\"arrowclosed\",\"width\":20,\"height\":20},\"source\":\"node_jIrN4JD2bhikQICO\",\"target\":\"node_ECjFDgU9zAHZYC3v\",\"id\":\"xy-edge__node_jIrN4JD2bhikQICO-node_ECjFDgU9zAHZYC3v\",\"selected\":false}],\"viewport\":{\"x\":1140.8195862979433,\"y\":1313.138076132696,\"zoom\":0.8144236256633043}}');
INSERT INTO `workflows` (`id`, `name`, `app_id`, `graph`) VALUES (19, '身份证识别OCR', NULL, '{\"nodes\":[{\"id\":\"node_vV5117sFVhNu4mw5\",\"position\":{\"x\":-344.7948817895956,\"y\":-75.98579800930189},\"data\":{\"title\":\"开始节点\",\"description\":\"开始定义输入参数\",\"expand\":true,\"parameters\":[{\"id\":\"Ya2BLfJrXJlF9bcn\",\"name\":\"templateType\",\"defaultValue\":\"ID_CARD\",\"description\":\"识别类型\"},{\"id\":\"qEuAUzdUj39fvuFi\",\"name\":\"imageUlr\",\"description\":\"图片地址\"}]},\"type\":\"startNode\",\"selected\":false,\"measured\":{\"width\":306,\"height\":239},\"dragging\":false},{\"id\":\"node_ci52IpvgslywbWzz\",\"position\":{\"x\":610.043509642299,\"y\":-196.5267298554042},\"data\":{\"title\":\"大模型\",\"description\":\"使用大模型处理问题\",\"outType\":\"text\",\"outputDefs\":[{\"name\":\"output\",\"dataType\":\"String\",\"dataTypeDisabled\":true,\"deleteDisabled\":true}],\"expand\":true,\"llmId\":\"ac05866194643c5341a73d837b2017c8\",\"parameters\":[{\"id\":\"Rlnewx9OGA6jFOg4\",\"refType\":\"ref\",\"name\":\"image_data\",\"ref\":\"node_XhifUKCpSNVpt1J7.body\"}],\"userPrompt\":\"{image_data}\",\"systemPrompt\":\"请从{image_data}精确提取以下字段，按示例格式输出纯JSON（无注释）：\\n         必须输出完整JSON结构，缺失字段保留空字符串：\\n         {\\n         \\\"personal_infos\\\": [\\n         {\\n         \\\"name\\\": \\\"\\\",        // 姓名\\n         \\\"gender\\\": \\\"\\\",      // 性别\\n         \\\"ethnic\\\": \\\"\\\",      // 民族\\n         \\\"birth_date\\\": \\\"\\\",  // 出生日期yyyy-MM-dd\\n         \\\"id_number\\\": \\\"\\\",   // 身份证号码\\n         \\\"residence\\\": \\\"\\\",   // 住址\\n         \\\"mailing_address\\\": \\\"\\\",   // 通讯地址\\n         \\\"issuing_authority\\\": \\\"\\\", // 签发机关\\n         \\\"validity_period\\\": {     // 有效期限\\n         \\\"start\\\": \\\"\\\",   \\n         \\\"end\\\": \\\"\\\"\\n         },\\n         \\\"postal_code\\\": \\\"\\\"  // 邮政编码\\n         }\\n         ]\\n         }\"},\"type\":\"llmNode\",\"selected\":false,\"measured\":{\"width\":343,\"height\":781},\"dragging\":false},{\"id\":\"node_XhifUKCpSNVpt1J7\",\"position\":{\"x\":71.5519831978857,\"y\":-180.49299545904694},\"data\":{\"title\":\"Http 请求\",\"description\":\"通过 HTTP 请求获取数据\",\"method\":\"get\",\"expand\":true,\"url\":\"http://127.0.0.1:8100/aigc/workflow/test/image\",\"headers\":[{\"id\":\"MVbFUwHXlMhdzekF\",\"refType\":\"fixed\",\"name\":\"authorization\",\"defaultValue\":\"333ae17b-e5f3-4315-92e2-bff952bf9cbe\",\"value\":\"05c0bd8b-8f2a-40ca-a3fe-3e8df810bd5d\"}],\"urlParameters\":[{\"id\":\"fTnTWH91HAZhfxBb\",\"refType\":\"ref\",\"name\":\"templateType\",\"ref\":\"node_vV5117sFVhNu4mw5.templateType\"},{\"id\":\"szN8jYxfsoRP7yyb\",\"refType\":\"ref\",\"name\":\"imageUlr\",\"ref\":\"node_vV5117sFVhNu4mw5.imageUlr\"}],\"outputDefs\":[{\"id\":\"Q7TCvD8MS2DskbMp\",\"name\":\"body\",\"dataType\":\"String\"}]},\"type\":\"httpNode\",\"selected\":true,\"measured\":{\"width\":404,\"height\":548},\"dragging\":false},{\"id\":\"node_raTlw41qROAOTem0\",\"position\":{\"x\":1123.5573942388385,\"y\":-121.35282871412039},\"data\":{\"title\":\"结束节点\",\"description\":\"结束定义输出参数\",\"expand\":true,\"outputDefs\":[{\"id\":\"HKx5TFkkHh4U6Vwg\",\"refType\":\"ref\",\"name\":\"output\",\"ref\":\"node_ci52IpvgslywbWzz.output\"}]},\"type\":\"endNode\",\"selected\":false,\"measured\":{\"width\":352,\"height\":206},\"dragging\":false}],\"edges\":[{\"markerEnd\":{\"type\":\"arrowclosed\",\"width\":20,\"height\":20},\"source\":\"node_vV5117sFVhNu4mw5\",\"target\":\"node_XhifUKCpSNVpt1J7\",\"id\":\"xy-edge__node_vV5117sFVhNu4mw5-node_XhifUKCpSNVpt1J7\",\"selected\":false},{\"markerEnd\":{\"type\":\"arrowclosed\",\"width\":20,\"height\":20},\"source\":\"node_XhifUKCpSNVpt1J7\",\"target\":\"node_ci52IpvgslywbWzz\",\"id\":\"xy-edge__node_XhifUKCpSNVpt1J7-node_ci52IpvgslywbWzz\",\"selected\":false},{\"markerEnd\":{\"type\":\"arrowclosed\",\"width\":20,\"height\":20},\"source\":\"node_ci52IpvgslywbWzz\",\"target\":\"node_raTlw41qROAOTem0\",\"id\":\"xy-edge__node_ci52IpvgslywbWzz-node_raTlw41qROAOTem0\",\"selected\":false}],\"viewport\":{\"x\":250,\"y\":100,\"zoom\":1}}');
INSERT INTO `workflows` (`id`, `name`, `app_id`, `graph`) VALUES (21, '合同OCR识别', NULL, '{\"nodes\":[{\"id\":\"node_J6GAkNDARIN7z3KE\",\"position\":{\"x\":-665.3645523296029,\"y\":-1372.0344304772714},\"data\":{\"title\":\"开始节点\",\"description\":\"开始定义输入参数\",\"expand\":true,\"parameters\":[{\"id\":\"jqtAGeFESHcxGhfo\",\"name\":\"类型\"},{\"id\":\"s7Oomo2xYJA691la\",\"name\":\"证件图片\"}]},\"type\":\"startNode\",\"selected\":false,\"measured\":{\"width\":306,\"height\":239},\"dragging\":false},{\"id\":\"node_T3nYIH8FXwRFcfMX\",\"position\":{\"x\":-159.25613656087583,\"y\":-1623.565293175387},\"data\":{\"title\":\"Http 请求\",\"description\":\"通过 HTTP 请求获取数据\",\"method\":\"get\",\"expand\":true,\"url\":\"http://127.0.0.1:8100/aigc/workflow/test/image\",\"headers\":[{\"id\":\"2FqcxzB1J9psQy7I\",\"refType\":\"fixed\",\"name\":\"authorization\",\"defaultValue\":\"333ae17b-e5f3-4315-92e2-bff952bf9cbe\",\"value\":\"05c0bd8b-8f2a-40ca-a3fe-3e8df810bd5d\"}],\"urlParameters\":[{\"id\":\"np9QT3eKWO8G3n0e\",\"refType\":\"ref\",\"name\":\"templateType\",\"ref\":\"node_J6GAkNDARIN7z3KE.类型\"},{\"id\":\"xgQZjSwRRoppmYAa\",\"refType\":\"ref\",\"name\":\"imageUlr\",\"ref\":\"node_J6GAkNDARIN7z3KE.证件图片\"}],\"outputDefs\":[{\"id\":\"d8RSi4KBpLAVMbvp\",\"name\":\"body\"}]},\"type\":\"httpNode\",\"selected\":false,\"measured\":{\"width\":404,\"height\":548},\"dragging\":false},{\"id\":\"node_iUuqEAZRhFhFnyG0\",\"position\":{\"x\":570.************,\"y\":-1656.*************},\"data\":{\"title\":\"大模型\",\"description\":\"使用大模型处理问题\",\"outType\":\"text\",\"outputDefs\":[{\"name\":\"output\",\"dataType\":\"String\",\"dataTypeDisabled\":true,\"deleteDisabled\":true}],\"expand\":true,\"systemPrompt\":\"请从{pdf_info} 文本中提取关键信息，甲方(first_party)、乙方(second_party)、甲方地址（first_address）\\n、乙方地址（second_address）、联系方式（contact_information）、户名（account_name）、账号（account_number）\\n、开户行（bank_name），按示例格式输出纯JSON（无注释）：\\n请严格按照下方JSON结构进行输出，缺失字段保留空字符串返回：\\n\\t\\t{\\n\\t\\t\\\"contact_info\\\": [\\n\\t\\t{\\n\\t\\t\\\"first_party\\\":\\\"\\\",        //甲方\\n\\t\\t\\\"second_party\\\":\\\"\\\",      //乙方\\n\\t\\t\\\"first_address\\\":\\\"\\\",  //地址/甲方地址\\n\\t\\t\\\"second_address\\\":\\\"\\\",  //地址/乙方地址\\n\\t\\t\\\"contact_information\\\":\\\"\\\",   //联系方式\\n\\t\\t\\\"registered_capital\\\":\\\"\\\",   //注册资本\\n\\t\\t\\\"account_name\\\":\\\"\\\",   //户名\\n\\t\\t\\\"account_number\\\":\\\"\\\", //账号\\n\\t\\t\\\"bank_name\\\":\\\"\\\"    //开户行\\n\\t\\t}\\n\\t\\t]\\n\\t\\t}\\n不要添加```json\",\"userPrompt\":\"{pdf_info} \",\"llmId\":\"ac05866194643c5341a73d837b2017c8\",\"parameters\":[{\"id\":\"KOsBqT19W9rV9V8t\",\"refType\":\"ref\",\"name\":\"pdf_info\",\"ref\":\"node_T3nYIH8FXwRFcfMX.body\"}],\"temperature\":0.6,\"topP\":0.8},\"type\":\"llmNode\",\"selected\":true,\"measured\":{\"width\":343,\"height\":781},\"dragging\":false},{\"id\":\"node_7z6ypq0FJHjZaIvD\",\"position\":{\"x\":1115.*************,\"y\":-1497.*************},\"data\":{\"title\":\"结束节点\",\"description\":\"结束定义输出参数\",\"expand\":true,\"outputDefs\":[{\"id\":\"IR7BoECmd7vntid9\",\"refType\":\"ref\",\"name\":\"result\",\"ref\":\"node_iUuqEAZRhFhFnyG0.output\"}]},\"type\":\"endNode\",\"selected\":false,\"measured\":{\"width\":352,\"height\":206},\"dragging\":false}],\"edges\":[{\"markerEnd\":{\"type\":\"arrowclosed\",\"width\":20,\"height\":20},\"source\":\"node_J6GAkNDARIN7z3KE\",\"target\":\"node_T3nYIH8FXwRFcfMX\",\"id\":\"xy-edge__node_J6GAkNDARIN7z3KE-node_T3nYIH8FXwRFcfMX\",\"selected\":false},{\"markerEnd\":{\"type\":\"arrowclosed\",\"width\":20,\"height\":20},\"source\":\"node_T3nYIH8FXwRFcfMX\",\"target\":\"node_iUuqEAZRhFhFnyG0\",\"id\":\"xy-edge__node_T3nYIH8FXwRFcfMX-node_iUuqEAZRhFhFnyG0\",\"selected\":false},{\"markerEnd\":{\"type\":\"arrowclosed\",\"width\":20,\"height\":20},\"source\":\"node_iUuqEAZRhFhFnyG0\",\"target\":\"node_7z6ypq0FJHjZaIvD\",\"id\":\"xy-edge__node_iUuqEAZRhFhFnyG0-node_7z6ypq0FJHjZaIvD\",\"selected\":false}],\"viewport\":{\"x\":-285.2048973096928,\"y\":2094.599792647752,\"zoom\":1.4267928377964656}}');
INSERT INTO `workflows` (`id`, `name`, `app_id`, `graph`) VALUES (22, '营业执照识别OCR', NULL, '{\"nodes\":[{\"id\":\"node_J6GAkNDARIN7z3KE\",\"position\":{\"x\":-946.0722612611521,\"y\":-1406.2884671966478},\"data\":{\"title\":\"开始节点\",\"description\":\"开始定义输入参数\",\"expand\":true,\"parameters\":[{\"id\":\"jqtAGeFESHcxGhfo\",\"name\":\"类型\"},{\"id\":\"s7Oomo2xYJA691la\",\"name\":\"证件图片\"}]},\"type\":\"startNode\",\"selected\":false,\"measured\":{\"width\":306,\"height\":239},\"dragging\":false},{\"id\":\"node_T3nYIH8FXwRFcfMX\",\"position\":{\"x\":-452.5358324443755,\"y\":-1700.4948903917086},\"data\":{\"title\":\"Http 请求\",\"description\":\"通过 HTTP 请求获取数据\",\"method\":\"get\",\"expand\":true,\"url\":\"http://127.0.0.1:8100/aigc/workflow/test/image\",\"headers\":[{\"id\":\"2FqcxzB1J9psQy7I\",\"refType\":\"fixed\",\"name\":\"authorization\",\"defaultValue\":\"333ae17b-e5f3-4315-92e2-bff952bf9cbe\",\"value\":\"05c0bd8b-8f2a-40ca-a3fe-3e8df810bd5d\"}],\"urlParameters\":[{\"id\":\"np9QT3eKWO8G3n0e\",\"refType\":\"ref\",\"name\":\"templateType\",\"ref\":\"node_J6GAkNDARIN7z3KE.类型\"},{\"id\":\"xgQZjSwRRoppmYAa\",\"refType\":\"ref\",\"name\":\"imageUlr\",\"ref\":\"node_J6GAkNDARIN7z3KE.证件图片\"}],\"outputDefs\":[{\"id\":\"d8RSi4KBpLAVMbvp\",\"name\":\"body\"}]},\"type\":\"httpNode\",\"selected\":false,\"measured\":{\"width\":404,\"height\":548},\"dragging\":false},{\"id\":\"node_iUuqEAZRhFhFnyG0\",\"position\":{\"x\":228.41367864219245,\"y\":-1679.0049216860236},\"data\":{\"title\":\"大模型\",\"description\":\"使用大模型处理问题\",\"outType\":\"text\",\"outputDefs\":[{\"name\":\"output\",\"dataType\":\"String\",\"dataTypeDisabled\":true,\"deleteDisabled\":true}],\"expand\":true,\"systemPrompt\":\"请从{result_data}文本中提取关键信息，注册码(registration_number)、企业名称(name)、公司/企业类型/主体类型（type）\\n\\t\\t、住所/营业场所/企业住所（address）、法定代表人/负责人（legal_representative）、注册资本（registered_capital）、\\n成立日期（found_date）、营业期限（business_term）、发照日期（issueDate），按示例格式输出纯JSON（无注释）：\\n必须输出完整JSON结构，缺失字段保留空字符串：\\n\\t\\t{\\n\\t\\t\\\"business_license\\\": [\\n\\t\\t{\\n\\t\\t\\\"registration_number\\\": \\\"\\\",        // 注册码\\n\\t\\t\\\"name\\\": \\\"\\\",      // 企业名称\\n\\t\\t\\\"type\\\": \\\"\\\",      // 公司/企业类型/主体类型\\n\\t\\t\\\"address\\\": \\\"\\\",  // 住所/营业场所/企业住所\\n\\t\\t\\\"legal_representative\\\": \\\"\\\",   // 法定代表人/负责人\\n\\t\\t\\\"registered_capital\\\": \\\"\\\",   // 注册资本\\n\\t\\t\\\"found_date\\\": \\\"\\\",   // 成立日期\\n\\t\\t\\\"business_term\\\": \\\"\\\", // 营业期限\\n\\t\\t\\\"business_scope\\\": {     // 经营范围\\n\\t\\t\\\"issueDate\\\": \\\"\\\"     //发照日期\\n\\t\\t}\\n\\t\\t]\\n\\t\\t}\\n请确保所有字段都使用英文作为key值。如果信息缺失，对应的值设为null、返回结果不要加```json。\",\"parameters\":[{\"id\":\"ySVmK4hYaX8X6TKW\",\"refType\":\"ref\",\"name\":\"result_data\",\"ref\":\"node_T3nYIH8FXwRFcfMX.body\"}],\"llmId\":\"ac05866194643c5341a73d837b2017c8\",\"userPrompt\":\"{result_data}\"},\"type\":\"llmNode\",\"selected\":true,\"measured\":{\"width\":343,\"height\":781},\"dragging\":false},{\"id\":\"node_sTtSJtpWzJbU3Cxq\",\"position\":{\"x\":693.4135055751434,\"y\":-1382.0119846376524},\"data\":{\"title\":\"结束节点\",\"description\":\"结束定义输出参数\",\"expand\":true,\"outputDefs\":[{\"id\":\"kBstPZMppK6DEdeB\",\"refType\":\"ref\",\"name\":\"out_result\",\"ref\":\"node_iUuqEAZRhFhFnyG0.output\"}]},\"type\":\"endNode\",\"selected\":false,\"measured\":{\"width\":352,\"height\":206},\"dragging\":false}],\"edges\":[{\"markerEnd\":{\"type\":\"arrowclosed\",\"width\":20,\"height\":20},\"source\":\"node_J6GAkNDARIN7z3KE\",\"target\":\"node_T3nYIH8FXwRFcfMX\",\"id\":\"xy-edge__node_J6GAkNDARIN7z3KE-node_T3nYIH8FXwRFcfMX\",\"selected\":false},{\"markerEnd\":{\"type\":\"arrowclosed\",\"width\":20,\"height\":20},\"source\":\"node_T3nYIH8FXwRFcfMX\",\"target\":\"node_iUuqEAZRhFhFnyG0\",\"id\":\"xy-edge__node_T3nYIH8FXwRFcfMX-node_iUuqEAZRhFhFnyG0\",\"selected\":false},{\"markerEnd\":{\"type\":\"arrowclosed\",\"width\":20,\"height\":20},\"source\":\"node_iUuqEAZRhFhFnyG0\",\"target\":\"node_sTtSJtpWzJbU3Cxq\",\"id\":\"xy-edge__node_iUuqEAZRhFhFnyG0-node_sTtSJtpWzJbU3Cxq\",\"selected\":false}],\"viewport\":{\"x\":630.4776260559723,\"y\":1142.5907413662196,\"zoom\":0.577495688240166}}');
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;
