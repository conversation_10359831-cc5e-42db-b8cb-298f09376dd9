# Compose a postgres database together with the extension pgvector
services:
  pgvector:
    image: registry.cn-beijing.aliyuncs.com/langchat/pgvector
#    image: docker.m.daocloud.io/pgvector/pgvector:0.7.2-pg16
    ports:
     - 5432:5432
    restart: always
    environment:
      - POSTGRES_DB=langchat
      - POSTGRES_USER=root
      - POSTGRES_PASSWORD=root
    volumes:
      - ./pgdata:/var/lib/postgresql/data
