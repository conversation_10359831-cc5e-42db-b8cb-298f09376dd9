{"title": "企业名称提取测试用例", "description": "测试从用户输入中提取完整企业名称的各种场景", "test_cases": [{"category": "标准企业名称提取", "cases": [{"input": "我想查询阿里巴巴集团控股有限公司的信息", "expected": "阿里巴巴集团控股有限公司", "description": "完整的集团控股有限公司名称"}, {"input": "查询腾讯控股有限公司的财务状况", "expected": "腾讯控股有限公司", "description": "控股有限公司名称"}, {"input": "了解华为技术有限公司的基本信息", "expected": "华为技术有限公司", "description": "技术有限公司名称"}, {"input": "搜索比亚迪股份有限公司", "expected": "比亚迪股份有限公司", "description": "股份有限公司名称"}, {"input": "查找小米集团的详细资料", "expected": "小米集团", "description": "集团名称"}]}, {"category": "复杂企业名称提取", "cases": [{"input": "我要了解中国石油化工股份有限公司的情况", "expected": "中国石油化工股份有限公司", "description": "长企业名称"}, {"input": "查询招商银行股份有限公司的年报", "expected": "招商银行股份有限公司", "description": "银行股份有限公司"}, {"input": "搜索中国建筑集团有限公司", "expected": "中国建筑集团有限公司", "description": "国企集团有限公司"}, {"input": "了解万科企业股份有限公司", "expected": "万科企业股份有限公司", "description": "企业股份有限公司"}]}, {"category": "带括号的企业名称", "cases": [{"input": "查询苹果公司(Apple Inc.)的信息", "expected": "苹果公司", "description": "带英文括号的企业名称"}, {"input": "了解谷歌（Google）公司的情况", "expected": "谷歌公司", "description": "带中文括号的企业名称"}, {"input": "搜索微软(Microsoft Corporation)公司", "expected": "微软公司", "description": "复杂括号企业名称"}]}, {"category": "简称和全称混合", "cases": [{"input": "查询工商银行的信息", "expected": "工商银行", "description": "银行简称（无标准后缀）"}, {"input": "了解中国工商银行股份有限公司", "expected": "中国工商银行股份有限公司", "description": "银行全称"}, {"input": "搜索建设银行的财务数据", "expected": "建设银行", "description": "银行简称"}, {"input": "查询中国建设银行股份有限公司", "expected": "中国建设银行股份有限公司", "description": "银行全称"}]}, {"category": "特殊格式企业名称", "cases": [{"input": "查询字节跳动有限公司的信息", "expected": "字节跳动有限公司", "description": "互联网公司"}, {"input": "了解滴滴出行科技有限公司", "expected": "滴滴出行科技有限公司", "description": "科技有限公司"}, {"input": "搜索美团点评集团", "expected": "美团点评集团", "description": "点评集团"}, {"input": "查询拼多多集团控股有限公司", "expected": "拼多多集团控股有限公司", "description": "集团控股有限公司"}]}, {"category": "多企业名称提取", "cases": [{"input": "比较阿里巴巴集团和腾讯控股有限公司的财务状况", "expected": "阿里巴巴集团", "description": "提取第一个企业名称"}, {"input": "分析华为技术有限公司和小米集团的市场表现", "expected": "华为技术有限公司", "description": "提取第一个完整企业名称"}, {"input": "对比苹果公司、谷歌公司和微软公司", "expected": "苹果公司", "description": "多个企业名称中提取第一个"}]}, {"category": "边界情况", "cases": [{"input": "查询公司信息", "expected": null, "description": "只有关键词，无具体企业名称"}, {"input": "了解企业的基本情况", "expected": null, "description": "只有通用词汇，无具体企业"}, {"input": "搜索一家有限公司的资料", "expected": null, "description": "模糊描述，无具体企业名称"}, {"input": "查询：阿里巴巴集团控股有限公司", "expected": "阿里巴巴集团控股有限公司", "description": "冒号分隔的企业名称"}]}], "extraction_rules": {"priority": ["1. 正则表达式匹配（最高优先级）", "2. 关键词匹配提取", "3. AI智能提取", "4. 默认值（最低优先级）"], "company_suffixes": ["有限责任公司", "股份有限公司", "集团有限公司", "集团股份有限公司", "有限公司", "股份公司", "集团", "公司", "企业"], "extraction_strategy": {"keyword_matching": "在用户输入中查找企业相关关键词，然后提取相邻的完整企业名称", "regex_pattern": "使用正则表达式精确匹配企业名称模式，包括各种后缀", "ai_extraction": "使用AI模型理解语义，提取完整的企业名称"}}, "validation_criteria": {"completeness": "提取的企业名称应包含完整的后缀（如有限公司、股份有限公司等）", "accuracy": "提取的名称应与用户输入中的企业名称完全一致", "uniqueness": "当输入包含多个企业名称时，应提取第一个出现的企业名称", "format": "保持原有的格式，包括括号、空格等特殊字符"}, "regex_patterns": {"standard_pattern": "(?:查询|搜索|找|了解|调研)?\\s*(?:企业|公司|集团)?[:：]?\\s*([\\u4e00-\\u9fa5a-zA-Z0-9\\(\\)（）\\s]+?(?:有限公司|股份有限公司|有限责任公司|股份公司|集团|公司|企业))(?:的|信息|资料|数据|详情|情况)?", "description": "匹配包含完整企业后缀的企业名称", "capture_group": "第一个捕获组包含完整的企业名称，包括后缀"}, "ai_prompt_template": "请从用户输入中提取企业或公司的完整名称，包括有限公司、股份有限公司、集团等后缀。保留企业的完整全称。如果用户提到多个企业，返回第一个提到的企业名称。"}