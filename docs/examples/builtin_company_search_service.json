{"title": "内置企业数据查询服务配置", "description": "builtin-company-search 服务的完整配置示例，支持智能参数提取", "service_config": {"id": "builtin-company-search-001", "name": "builtin-company-search", "displayName": "内置企业数据查询", "description": "提供企业基本信息、财务状况、法律风险等多维度数据查询服务", "endpoint": "internal://builtin-company-search", "version": "1.0.0", "type": "BUILTIN", "category": "builtin", "authType": "none", "authConfig": null, "icon": "🏢", "tags": "企业查询,工商信息,财务数据,风险评估", "priority": 8, "enabled": true, "requiresConfirmation": false, "timeout": 30, "maxRetries": 3, "healthCheckUrl": null, "healthCheckInterval": 60, "config": {"supportedRegions": ["中国大陆", "香港", "台湾"], "dataSource": "国家企业信用信息公示系统", "updateFrequency": "daily", "maxResultsPerQuery": 50}, "tools": [{"name": "company_search", "description": "根据企业名称搜索企业基本信息", "category": "search", "priority": 10, "parameters": {"companyName": {"type": "string", "description": "企业名称或关键词", "required": true}, "searchType": {"type": "string", "description": "搜索类型：exact(精确匹配)、fuzzy(模糊匹配)", "required": false, "default": "fuzzy"}, "region": {"type": "string", "description": "查询地区", "required": false, "default": "中国大陆"}}}, {"name": "company_detail", "description": "获取企业详细信息", "category": "query", "priority": 9, "parameters": {"companyId": {"type": "string", "description": "企业统一社会信用代码或注册号", "required": true}, "infoType": {"type": "string", "description": "信息类型：basic(基本信息)、financial(财务信息)、legal(法律信息)、risk(风险信息)", "required": false, "default": "basic"}}}, {"name": "company_financial", "description": "查询企业财务数据", "category": "financial", "priority": 7, "parameters": {"companyId": {"type": "string", "description": "企业统一社会信用代码", "required": true}, "year": {"type": "number", "description": "查询年份", "required": false, "default": 2023}, "reportType": {"type": "string", "description": "报告类型：annual(年报)、quarterly(季报)", "required": false, "default": "annual"}}}, {"name": "company_risk_assessment", "description": "企业风险评估", "category": "risk", "priority": 6, "parameters": {"companyId": {"type": "string", "description": "企业统一社会信用代码", "required": true}, "riskTypes": {"type": "array", "description": "风险类型：legal(法律风险)、financial(财务风险)、operational(经营风险)", "required": false, "default": ["legal", "financial", "operational"]}}}], "parameters": [{"name": "companyName", "displayName": "企业名称", "description": "要查询的企业名称或关键词", "type": "string", "required": true, "sensitive": false, "defaultValue": null, "group": "basic", "order": 1, "validation": {"extractFromInput": true, "extractionPrompt": "请从用户输入中提取企业或公司的完整名称，包括有限公司、股份有限公司、集团等后缀。保留企业的完整全称。如果用户提到多个企业，返回第一个提到的企业名称。", "keywords": ["企业", "公司", "集团", "有限公司", "股份公司", "查询", "搜索", "找", "了解", "调研", "信息", "资料", "数据", "详情", "情况"], "pattern": "(?:查询|搜索|找|了解|调研)?\\s*(?:企业|公司|集团)?[:：]?\\s*([\\u4e00-\\u9fa5a-zA-Z0-9\\(\\)（）\\s]+?(?:有限公司|股份有限公司|有限责任公司|股份公司|集团|公司|企业))(?:的|信息|资料|数据|详情|情况)?", "defaultValue": null}}, {"name": "queryType", "displayName": "查询类型", "description": "要查询的信息类型", "type": "string", "required": false, "sensitive": false, "defaultValue": "basic", "group": "basic", "order": 2, "validation": {"extractFromInput": true, "extractionPrompt": "请判断用户想要查询企业的什么类型信息。根据用户的描述返回以下之一：basic(基本信息)、financial(财务信息)、legal(法律信息)、risk(风险信息)、all(全部信息)。如果用户没有明确说明，返回'basic'。", "keywords": ["基本信息", "基本", "概况", "简介", "财务", "财务信息", "财务状况", "财务数据", "年报", "财报", "法律", "法律信息", "法律风险", "诉讼", "违法", "处罚", "风险", "风险评估", "风险信息", "信用", "评级", "全部", "所有", "详细", "完整"], "pattern": "(?:查询|了解|要|想要|需要)\\s*(?:企业|公司)?\\s*(?:的)?\\s*(基本信息|基本|概况|简介|财务|财务信息|财务状况|财务数据|年报|财报|法律|法律信息|法律风险|诉讼|违法|处罚|风险|风险评估|风险信息|信用|评级|全部|所有|详细|完整)(?:信息|数据|情况|状况)?", "defaultValue": "basic"}}, {"name": "searchScope", "displayName": "搜索范围", "description": "搜索的地理范围", "type": "string", "required": false, "sensitive": false, "defaultValue": "全国", "group": "advanced", "order": 3, "validation": {"extractFromInput": true, "extractionPrompt": "请从用户输入中提取地理范围信息，如省份、城市等。如果没有明确提到地理范围，返回'全国'。", "keywords": ["北京", "上海", "广州", "深圳", "杭州", "南京", "成都", "重庆", "省", "市", "区", "县", "地区", "全国", "国内"], "pattern": "([\\u4e00-\\u9fa5]+(?:省|市|区|县|地区)?)(?:的|范围内|内)?", "defaultValue": "全国"}}, {"name": "resultLimit", "displayName": "结果数量限制", "description": "返回结果的最大数量", "type": "number", "required": false, "sensitive": false, "defaultValue": 10, "group": "advanced", "order": 4, "validation": {"extractFromInput": true, "extractionPrompt": "请从用户输入中提取用户想要的结果数量。如果用户提到'前几个'、'几家'、'几条'等，提取对应的数字。如果没有明确说明，返回10。", "keywords": ["前", "个", "家", "条", "项", "最多", "限制", "数量"], "pattern": "(?:前|最多|限制)?\\s*(\\d+)\\s*(?:个|家|条|项|家公司|个企业)", "defaultValue": "10"}}], "parameterValues": {"apiEndpoint": "https://api.company-data.gov.cn", "apiKey": "${COMPANY_DATA_API_KEY}", "timeout": 30, "retryCount": 3, "cacheEnabled": true, "cacheTTL": 3600}, "remark": "内置企业数据查询服务，支持多维度企业信息查询，包括基本信息、财务状况、法律风险等。支持智能参数提取，用户可通过自然语言描述查询需求。"}, "usage_examples": [{"scenario": "基本企业查询", "user_inputs": ["我想了解阿里巴巴公司的基本信息", "查询腾讯企业的详细资料", "帮我搜索华为公司的情况"], "extracted_params": [{"companyName": "阿里巴巴集团控股有限公司", "queryType": "basic", "searchScope": "全国", "resultLimit": 10}, {"companyName": "腾讯控股有限公司", "queryType": "basic", "searchScope": "全国", "resultLimit": 10}, {"companyName": "华为技术有限公司", "queryType": "basic", "searchScope": "全国", "resultLimit": 10}]}, {"scenario": "财务信息查询", "user_inputs": ["查询比亚迪公司的财务状况", "我要看小米集团的年报数据", "了解美团的财务信息"], "extracted_params": [{"companyName": "比亚迪股份有限公司", "queryType": "financial", "searchScope": "全国", "resultLimit": 10}, {"companyName": "小米集团", "queryType": "financial", "searchScope": "全国", "resultLimit": 10}, {"companyName": "美团", "queryType": "financial", "searchScope": "全国", "resultLimit": 10}]}, {"scenario": "风险评估查询", "user_inputs": ["评估字节跳动的风险情况", "查询滴滴公司的法律风险", "了解拼多多的信用评级"], "extracted_params": [{"companyName": "字节跳动", "queryType": "risk", "searchScope": "全国", "resultLimit": 10}, {"companyName": "滴滴", "queryType": "legal", "searchScope": "全国", "resultLimit": 10}, {"companyName": "拼多多", "queryType": "risk", "searchScope": "全国", "resultLimit": 10}]}, {"scenario": "地域限制查询", "user_inputs": ["查询深圳地区的腾讯公司信息", "北京有哪些知名互联网企业", "上海的金融公司前5家"], "extracted_params": [{"companyName": "腾讯", "queryType": "basic", "searchScope": "深圳", "resultLimit": 10}, {"companyName": "互联网企业", "queryType": "basic", "searchScope": "北京", "resultLimit": 10}, {"companyName": "金融公司", "queryType": "basic", "searchScope": "上海", "resultLimit": 5}]}], "api_responses": {"company_search_response": {"success": true, "data": {"total": 1, "companies": [{"id": "91330000MA28F3E96U", "name": "阿里巴巴（中国）有限公司", "legalPerson": "张勇", "registeredCapital": "8000000万人民币", "establishDate": "1999-09-09", "status": "存续", "address": "浙江省杭州市余杭区文一西路969号", "businessScope": "技术开发、技术咨询、技术服务...", "industry": "互联网和相关服务", "companyType": "有限责任公司"}]}, "message": "查询成功"}, "company_detail_response": {"success": true, "data": {"basicInfo": {"name": "阿里巴巴（中国）有限公司", "creditCode": "91330000MA28F3E96U", "legalPerson": "张勇", "registeredCapital": "8000000万人民币", "establishDate": "1999-09-09", "status": "存续"}, "financialInfo": {"revenue2023": "8688.88亿元", "profit2023": "1363.25亿元", "assets": "15000亿元", "employees": "245700人"}, "riskInfo": {"creditRating": "AAA", "legalCases": 0, "administrativePenalties": 0, "riskLevel": "低风险"}}, "message": "查询成功"}}}