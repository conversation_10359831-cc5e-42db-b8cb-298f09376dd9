{"title": "MCP参数提取配置示例", "description": "展示如何在MCP服务参数定义中配置参数提取功能", "examples": [{"name": "企业查询API服务参数配置", "description": "配置从用户输入中提取企业名称", "parameters": [{"name": "companyName", "displayName": "企业名称", "description": "要查询的企业名称", "type": "string", "required": true, "validation": {"extractFromInput": true, "extractionPrompt": "请提取用户想要查询的企业或公司名称，只返回企业名称，不要包含'公司'、'企业'等后缀", "keywords": ["企业", "公司", "公司名称", "企业名称", "查询", "搜索"], "pattern": "(?:企业|公司)[:：]?\\s*([\\u4e00-\\u9fa5a-zA-Z0-9\\s]+)", "defaultValue": null}}, {"name": "queryType", "displayName": "查询类型", "description": "查询信息的类型", "type": "string", "required": false, "validation": {"extractFromInput": true, "extractionPrompt": "请判断用户想要查询企业的什么信息：基本信息、财务信息、法律信息等，如果没有明确说明，返回'基本信息'", "keywords": ["基本信息", "财务", "法律", "经营状况", "股东信息", "结算数据", "应付数据", "订单数据", "开票数据"], "defaultValue": "基本信息"}}]}, {"name": "天气查询API服务参数配置", "description": "配置从用户输入中提取城市和天数", "parameters": [{"name": "city", "displayName": "城市名称", "description": "要查询天气的城市", "type": "string", "required": true, "validation": {"extractFromInput": true, "extractionPrompt": "请提取用户想要查询天气的城市名称，只返回城市名称", "keywords": ["天气", "城市", "地区", "温度", "气温"], "pattern": "([\\u4e00-\\u9fa5]+(?:市|县|区|省)?)(?:的)?(?:天气|气温|温度)", "defaultValue": "北京"}}, {"name": "days", "displayName": "查询天数", "description": "查询未来几天的天气", "type": "number", "required": false, "validation": {"extractFromInput": true, "extractionPrompt": "请提取用户想要查询几天的天气，如果是'今天'返回1，'明天'返回1，'后天'返回2，如果没有明确说明返回1", "keywords": ["今天", "明天", "后天", "几天", "天"], "pattern": "(?:未来|接下来)?(\\d+)天", "defaultValue": "1"}}]}, {"name": "商品搜索API服务参数配置", "description": "配置从用户输入中提取商品搜索条件", "parameters": [{"name": "keyword", "displayName": "搜索关键词", "description": "商品搜索关键词", "type": "string", "required": true, "validation": {"extractFromInput": true, "extractionPrompt": "请提取用户想要搜索的商品关键词，只返回商品名称", "keywords": ["搜索", "查找", "商品", "产品", "买", "购买"], "pattern": "(?:搜索|查找|买|购买)\\s*([\\u4e00-\\u9fa5a-zA-Z0-9\\s]+)"}}, {"name": "priceRange", "displayName": "价格范围", "description": "商品价格范围", "type": "string", "required": false, "validation": {"extractFromInput": true, "extractionPrompt": "请提取用户提到的价格范围，格式如：100-500，如果只有一个价格，返回该价格", "keywords": ["价格", "多少钱", "元", "块", "预算"], "pattern": "(\\d+)[-到至](\\d+)(?:元|块|钱)?", "defaultValue": null}}, {"name": "category", "displayName": "商品分类", "description": "商品分类", "type": "string", "required": false, "validation": {"extractFromInput": true, "extractionPrompt": "请判断用户搜索的商品属于什么分类：电子产品、服装、食品、书籍、家电等", "keywords": ["手机", "电脑", "衣服", "食品", "书籍", "家电"], "defaultValue": "全部"}}]}], "usage_scenarios": [{"scenario": "企业查询", "user_inputs": ["我想查询阿里巴巴公司的基本信息", "帮我搜索腾讯企业的财务状况", "查一下华为公司的法律信息"], "expected_extractions": [{"companyName": "阿里巴巴", "queryType": "基本信息"}, {"companyName": "腾讯", "queryType": "财务"}, {"companyName": "华为", "queryType": "法律信息"}]}, {"scenario": "天气查询", "user_inputs": ["北京明天的天气怎么样", "上海今天气温多少度", "深圳未来3天的天气预报"], "expected_extractions": [{"city": "北京", "days": 1}, {"city": "上海", "days": 1}, {"city": "深圳", "days": 3}]}, {"scenario": "商品搜索", "user_inputs": ["我想买一个500到1000元的手机", "搜索苹果电脑", "查找2000元以下的笔记本"], "expected_extractions": [{"keyword": "手机", "priceRange": "500-1000", "category": "电子产品"}, {"keyword": "苹果电脑", "category": "电子产品"}, {"keyword": "笔记本", "priceRange": "2000", "category": "电子产品"}]}], "configuration_guide": {"extractFromInput": {"description": "是否启用从用户输入中提取参数", "type": "boolean", "default": false, "example": true}, "extractionPrompt": {"description": "AI提取提示词，用于指导AI模型如何提取参数", "type": "string", "tips": ["明确指定要提取的内容", "说明返回格式要求", "提供默认值处理方式", "避免过于复杂的指令"], "example": "请提取用户想要查询的企业名称，只返回企业名称，不要包含'公司'、'企业'等后缀"}, "keywords": {"description": "关键词列表，用于关键词匹配提取", "type": "array", "tips": ["包含直接关键词（如'企业'、'公司'）", "包含动作关键词（如'查询'、'搜索'）", "包含同义词和变体", "考虑用户的表达习惯"], "example": ["企业", "公司", "公司名称", "查询", "搜索"]}, "pattern": {"description": "正则表达式模式，用于精确匹配提取", "type": "string", "tips": ["使用捕获组()来提取目标值", "考虑中英文混合情况", "处理常见的分隔符（冒号、空格等）", "测试正则表达式的准确性"], "example": "(?:企业|公司)[:：]?\\s*([\\u4e00-\\u9fa5a-zA-Z0-9\\s]+)"}, "defaultValue": {"description": "默认值，当无法提取到值时使用", "type": "string", "tips": ["设置合理的默认值", "考虑业务场景的常用值", "可以设置为null表示无默认值"], "example": "基本信息"}}, "best_practices": ["优先级：正则表达式 > 关键词匹配 > AI提取 > 默认值", "多种方法结合使用，提高提取准确率", "定期测试和优化提取规则", "考虑用户的多样化表达方式", "为敏感参数设置合理的默认值", "使用清晰明确的AI提取提示词"]}