{"title": "MCP参数提取配置示例", "description": "展示如何配置从用户输入中提取参数的各种方式", "examples": [{"name": "企业查询API服务", "description": "从用户输入中提取企业名称进行查询", "parameters": [{"name": "companyName", "displayName": "企业名称", "description": "要查询的企业名称", "type": "string", "required": true, "validation": {"extractFromInput": true, "extractionPrompt": "请提取用户想要查询的企业或公司名称，只返回企业名称，不要包含其他文字", "keywords": ["企业", "公司", "公司名称", "企业名称", "查询", "搜索"], "pattern": "(?:企业|公司)[:：]?\\s*([\\u4e00-\\u9fa5a-zA-Z0-9\\s]+)", "defaultValue": null}}, {"name": "queryType", "displayName": "查询类型", "description": "查询信息的类型", "type": "string", "required": false, "validation": {"extractFromInput": true, "extractionPrompt": "请判断用户想要查询企业的什么信息：基本信息、财务信息、法律信息等", "keywords": ["基本信息", "财务", "法律", "经营状况", "股东信息"], "defaultValue": "基本信息"}}]}, {"name": "天气查询API服务", "description": "从用户输入中提取城市名称查询天气", "parameters": [{"name": "city", "displayName": "城市名称", "description": "要查询天气的城市", "type": "string", "required": true, "validation": {"extractFromInput": true, "extractionPrompt": "请提取用户想要查询天气的城市名称", "keywords": ["天气", "城市", "地区", "温度", "气温"], "pattern": "([\\u4e00-\\u9fa5]+(?:市|县|区|省)?)(?:的)?(?:天气|气温|温度)", "defaultValue": "北京"}}, {"name": "days", "displayName": "查询天数", "description": "查询未来几天的天气", "type": "number", "required": false, "validation": {"extractFromInput": true, "extractionPrompt": "请提取用户想要查询几天的天气，如果没有明确说明，返回1", "keywords": ["今天", "明天", "后天", "几天", "天"], "pattern": "(?:未来|接下来)?(\\d+)天", "defaultValue": "1"}}]}, {"name": "用户管理API服务", "description": "从用户输入中提取用户信息进行操作", "parameters": [{"name": "userName", "displayName": "用户名", "description": "要操作的用户名", "type": "string", "required": true, "validation": {"extractFromInput": true, "extractionPrompt": "请提取用户提到的用户名或姓名", "keywords": ["用户", "用户名", "姓名", "账号"], "pattern": "(?:用户|用户名|姓名|账号)[:：]?\\s*([a-zA-Z0-9\\u4e00-\\u9fa5_]+)"}}, {"name": "action", "displayName": "操作类型", "description": "要执行的操作", "type": "string", "required": true, "validation": {"extractFromInput": true, "extractionPrompt": "请判断用户想要执行什么操作：创建、删除、更新、查询等", "keywords": ["创建", "新建", "添加", "删除", "移除", "更新", "修改", "查询", "搜索"], "defaultValue": "查询"}}]}, {"name": "商品搜索API服务", "description": "从用户输入中提取商品搜索条件", "parameters": [{"name": "keyword", "displayName": "搜索关键词", "description": "商品搜索关键词", "type": "string", "required": true, "validation": {"extractFromInput": true, "extractionPrompt": "请提取用户想要搜索的商品关键词", "keywords": ["搜索", "查找", "商品", "产品", "买"], "pattern": "(?:搜索|查找|买)\\s*([\\u4e00-\\u9fa5a-zA-Z0-9\\s]+)"}}, {"name": "priceRange", "displayName": "价格范围", "description": "商品价格范围", "type": "string", "required": false, "validation": {"extractFromInput": true, "extractionPrompt": "请提取用户提到的价格范围，格式如：100-500", "keywords": ["价格", "多少钱", "元", "块", "预算"], "pattern": "(\\d+)[-到至](\\d+)(?:元|块|钱)?"}}, {"name": "category", "displayName": "商品分类", "description": "商品分类", "type": "string", "required": false, "validation": {"extractFromInput": true, "extractionPrompt": "请判断用户搜索的商品属于什么分类：电子产品、服装、食品等", "keywords": ["手机", "电脑", "衣服", "食品", "书籍", "家电"], "defaultValue": "全部"}}]}], "usage_examples": [{"scenario": "企业查询", "user_input": "我想查询阿里巴巴公司的基本信息", "extracted_params": {"companyName": "阿里巴巴", "queryType": "基本信息"}}, {"scenario": "天气查询", "user_input": "北京明天的天气怎么样", "extracted_params": {"city": "北京", "days": 1}}, {"scenario": "用户管理", "user_input": "删除用户张三的账号", "extracted_params": {"userName": "张三", "action": "删除"}}, {"scenario": "商品搜索", "user_input": "我想买一个500到1000元的手机", "extracted_params": {"keyword": "手机", "priceRange": "500-1000", "category": "电子产品"}}], "extraction_methods": {"regex_pattern": {"description": "使用正则表达式匹配特定模式", "example": "(?:企业|公司)[:：]?\\s*([\\u4e00-\\u9fa5a-zA-Z0-9\\s]+)", "pros": ["精确匹配", "性能好"], "cons": ["需要预定义模式", "灵活性有限"]}, "keyword_matching": {"description": "基于关键词匹配提取相邻的值", "example": ["企业", "公司", "公司名称"], "pros": ["简单易用", "覆盖面广"], "cons": ["可能误匹配", "精度有限"]}, "ai_extraction": {"description": "使用AI模型理解语义并提取参数", "example": "请提取用户想要查询的企业名称", "pros": ["语义理解强", "灵活性高"], "cons": ["性能开销大", "需要AI模型"]}}, "parameter_priority": {"description": "参数值的优先级顺序", "order": ["1. 工具调用时传入的参数（最高优先级）", "2. 从用户输入提取的参数", "3. 应用配置的参数", "4. 参数定义中的默认值（最低优先级）"]}}