<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
  ~
  ~ Licensed under the GNU Affero General Public License, Version 3 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     https://www.gnu.org/licenses/agpl-3.0.html
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://maven.apache.org/POM/4.0.0"
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.2.3</version>
    </parent>

    <groupId>cn.tycoding</groupId>
    <artifactId>langchat</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <name>${project.artifactId}</name>

    <properties>
        <java.version>17</java.version>
        <revision>1.0.0</revision>

        <hutool.version>5.8.26</hutool.version>
        <mybatis-plus.version>3.5.5</mybatis-plus.version>
        <druid.version>1.2.23</druid.version>
        <mysql-connector.version>8.3.0</mysql-connector.version>
        <fastjson.version>2.0.47</fastjson.version>
        <snakeyaml.version>2.1</snakeyaml.version>
        <langchain4j.version>1.0.0-beta1</langchain4j.version>
        <sa-token.version>1.37.0</sa-token.version>
        <rapidocr.version>0.0.7</rapidocr.version>
    </properties>

    <modules>
        <module>langchat-common</module>
        <module>langchat-ai</module>
        <module>langchat-server</module>
        <module>langchat-upms</module>
        <module>langchat-auth</module>
        <module>langchat-mcp</module>
        <module>tinyflow-java</module>
        <module>image-server</module>
    </modules>

    <repositories>
<!--        <repository>-->
<!--            <id>snapshot</id>-->
<!--            <name>snapshot maven</name>-->
<!--            <url>https://test.jingruiit.com:18481/nexus/content/repositories/snapshots/</url>-->
<!--            <releases>-->
<!--                <enabled>false</enabled>-->
<!--            </releases>-->
<!--            <snapshots>-->
<!--                <enabled>true</enabled>-->
<!--            </snapshots>-->
<!--        </repository>-->
<!--        <repository>-->
<!--            <id>snapshot</id>-->
<!--            <name>snapshot maven</name>-->
<!--            <url>https://test.jingruiit.com:18481/nexus/content/repositories/releases/</url>-->
<!--            <releases>-->
<!--                <enabled>true</enabled>-->
<!--            </releases>-->
<!--            <snapshots>-->
<!--                <enabled>false</enabled>-->
<!--            </snapshots>-->
<!--        </repository>-->

        <repository>
            <id>release</id>
            <name>release maven</name>
            <url>https://ceshi.jingruiit.com:18481/nexus/content/groups/public/</url>
        </repository>
    </repositories>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>cn.tycoding</groupId>
                <artifactId>langchat-common-bom</artifactId>
                <version>${revision}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
