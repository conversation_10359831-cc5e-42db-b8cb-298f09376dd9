<template>
  <div class="layout-content3">
    <!-- 动态渲染匹配到的组件 -->
    <component v-if="currentComponent" :is="currentComponent" :key="route.fullPath" />
    <div v-else class="empty-container">
      <n-empty description="暂无数据" size="huge"> </n-empty>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { computed, defineAsyncComponent } from 'vue';
  import { useRoute, useRouter } from 'vue-router';

  const route = useRoute();
  const router = useRouter();

  // 计算属性，根据 query 中的 path 参数匹配组件
  const currentComponent = computed(() => {
    const path = ('/' + route.query.path) as string | undefined;
    if (!path) return null;
    let matchedRoute = router.getRoutes().find((r) => r.path === path);

    if (matchedRoute && matchedRoute.components) {
      const component: any = matchedRoute.components.default;
      // 如果是异步加载函数，则包装成 async component
      if (typeof component === 'function') {
        return defineAsyncComponent(component);
      }
      return component;
    }
    return null;
  });
</script>
<style lang="less" scoped>
.layout-content3 {
  position: relative;
  height: 100vh;

  .empty-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
</style>
