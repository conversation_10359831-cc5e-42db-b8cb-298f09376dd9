/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

export interface ChatMessage {
  chatId: string;
  role: 'user' | 'assistant' | 'system';
  message: string;
  fileUrls?: string[];
  createTime: string;
  isError?: boolean;
}

export interface FileInfo {
  name: string;
  url: string;
  size: number;
  type: string;
  originalName: string;
}

export interface ChatRequest {
  chatId: string;
  conversationId: string | null;
  appId: any;
  message: string;
  role: string;
  modelId: string | null;
  modelName: string | null;
  modelProvider: string | null;
  mcpServiceIds?: string[];
  mcpServices?: any[];
  fileUrls?: string[];
  userId?: string;
}
