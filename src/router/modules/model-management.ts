/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { RouteRecordRaw } from 'vue-router';
import { Layout } from '@/router/constant';

const routes: RouteRecordRaw = {
  path: '/aigc/model-management',
  name: 'ModelManagement',
  component: Layout,
  redirect: '/aigc/model-management/index',
  meta: {
    title: '模型管理',
    icon: 'SettingsOutline',
    sort: 30,
  },
  children: [
    {
      path: '/aigc/model-management/index',
      name: 'ModelManagementIndex',
      meta: {
        title: '模型管理',
        permissions: ['model:management'],
      },
      component: () => import('@/views/aigc/model-management/index.vue'),
    },
    {
      path: '/aigc/model-management/web-search/index',
      name: 'SearchEngineManagement',
      meta: {
        title: '搜索引擎管理',
        permissions: ['model:search-engine'],
      },
      component: () => import('@/views/aigc/model-management/web-search/index.vue'),
    },
    {
      path: '/aigc/model-management/provider-style',
      name: 'ModelProviderStyle',
      meta: {
        title: '模型供应商管理',
        permissions: ['model:provider-style'],
      },
      component: () => import('@/views/aigc/model/index.vue'),
    },
  ],
};

export default routes;
