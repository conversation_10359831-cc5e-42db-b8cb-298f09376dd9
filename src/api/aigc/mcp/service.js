import request from '@/utils/request'

const API_PREFIX = '/aigc/mcp/service'

/**
 * 获取MCP服务列表
 */
export function getServiceList(params) {
  return request({
    url: `${API_PREFIX}/list`,
    method: 'get',
    params
  })
}

/**
 * 根据ID获取MCP服务详情
 */
export function getServiceById(id) {
  return request({
    url: `${API_PREFIX}/${id}`,
    method: 'get'
  })
}

/**
 * 新增MCP服务
 */
export function addService(data) {
  return request({
    url: API_PREFIX,
    method: 'post',
    data
  })
}

/**
 * 更新MCP服务
 */
export function updateService(data) {
  return request({
    url: API_PREFIX,
    method: 'put',
    data
  })
}

/**
 * 删除MCP服务
 */
export function deleteService(ids) {
  return request({
    url: API_PREFIX,
    method: 'delete',
    data: ids
  })
}

/**
 * 启用/禁用MCP服务
 */
export function toggleServiceEnabled(id, data) {
  return request({
    url: `${API_PREFIX}/${id}/toggle`,
    method: 'put',
    data
  })
}

/**
 * 测试MCP服务连接
 */
export function testServiceConnection(id) {
  return request({
    url: `${API_PREFIX}/${id}/test`,
    method: 'post'
  })
}

/**
 * 同步MCP服务到客户端
 */
export function syncServiceToMcp(id) {
  return request({
    url: `${API_PREFIX}/${id}/sync`,
    method: 'post'
  })
}

/**
 * 批量同步所有启用的MCP服务
 */
export function syncAllServices() {
  return request({
    url: `${API_PREFIX}/sync/all`,
    method: 'post'
  })
}

/**
 * 获取MCP服务健康状态
 */
export function getServiceHealth(id) {
  return request({
    url: `${API_PREFIX}/${id}/health`,
    method: 'get'
  })
}

/**
 * 刷新MCP服务工具列表
 */
export function refreshServiceTools(id) {
  return request({
    url: `${API_PREFIX}/${id}/refresh-tools`,
    method: 'post'
  })
}

/**
 * 获取启用的MCP服务列表
 */
export function getEnabledServices() {
  return request({
    url: `${API_PREFIX}/enabled`,
    method: 'get'
  })
}

/**
 * 根据分类获取MCP服务列表
 */
export function getServicesByCategory(category) {
  return request({
    url: `${API_PREFIX}/category/${category}`,
    method: 'get'
  })
}

/**
 * 获取MCP服务统计信息
 */
export function getServiceStats() {
  return request({
    url: `${API_PREFIX}/stats`,
    method: 'get'
  })
}

/**
 * 获取服务类型选项
 */
export function getServiceTypes() {
  return request({
    url: `${API_PREFIX}/types`,
    method: 'get'
  })
}

/**
 * 获取服务分类选项
 */
export function getServiceCategories() {
  return request({
    url: `${API_PREFIX}/categories`,
    method: 'get'
  })
}

/**
 * 获取认证类型选项
 */
export function getAuthTypes() {
  return request({
    url: `${API_PREFIX}/auth-types`,
    method: 'get'
  })
}

/**
 * 获取服务状态选项
 */
export function getServiceStatuses() {
  return request({
    url: `${API_PREFIX}/statuses`,
    method: 'get'
  })
}
