/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { http } from '@/utils/http/axios';

/**
 * 获取MCP服务列表
 */
export function page(params: any) {
  return http.request({
    url: '/aigc/mcp/service/list',
    method: 'get',
    params,
  });
}

/**
 * 获取指定MCP服务信息
 */
export function getServiceByIds(params) {
  return http.request({
    url: '/aigc/mcp/service/findByIds',
    method: 'get',
    params,
  });
}

/**
 * 根据ID获取MCP服务详情
 */
export function getById(id: string) {
  return http.request({
    url: `/aigc/mcp/service/${id}`,
    method: 'get',
  });
}

/**
 * 新增MCP服务
 */
export function add(data: any) {
  return http.request({
    url: '/aigc/mcp/service',
    method: 'post',
    data,
  });
}

/**
 * 更新MCP服务
 */
export function update(data: any) {
  return http.request({
    url: '/aigc/mcp/service',
    method: 'put',
    data,
  });
}

/**
 * 删除MCP服务
 */
export function del(ids: string[]) {
  return http.request({
    url: '/aigc/mcp/service',
    method: 'delete',
    data: ids,
  });
}

/**
 * 启用/禁用MCP服务
 */
export function toggleEnabled(id: string, enabled: boolean) {
  return http.request({
    url: `/aigc/mcp/service/${id}/toggle`,
    method: 'put',
    data: { enabled },
  });
}

/**
 * 测试MCP服务连接
 */
export function testConnection(id: string) {
  return http.request({
    url: `/aigc/mcp/service/${id}/test`,
    method: 'post',
  });
}

/**
 * 同步MCP服务到客户端
 */
export function syncService(id: string) {
  return http.request({
    url: `/aigc/mcp/service/${id}/sync`,
    method: 'post',
  });
}

/**
 * 批量同步所有启用的MCP服务
 */
export function syncAllServices() {
  return http.request({
    url: '/aigc/mcp/service/sync/all',
    method: 'post',
  });
}

/**
 * 获取MCP服务健康状态
 */
export function getHealthStatus(id: string) {
  return http.request({
    url: `/aigc/mcp/service/${id}/health`,
    method: 'get',
  });
}

/**
 * 刷新MCP服务工具列表
 */
export function refreshTools(id: string) {
  return http.request({
    url: `/aigc/mcp/service/${id}/refresh-tools`,
    method: 'post',
  });
}

/**
 * 获取启用的MCP服务列表
 */
export function getEnabledServices() {
  return http.request({
    url: '/aigc/mcp/service/enabled',
    method: 'get',
  });
}

/**
 * 根据分类获取MCP服务列表
 */
export function getServicesByCategory(category: string) {
  return http.request({
    url: `/aigc/mcp/service/category/${category}`,
    method: 'get',
  });
}

/**
 * 获取MCP服务统计信息
 */
export function getStats() {
  return http.request({
    url: '/aigc/mcp/service/stats',
    method: 'get',
  });
}

/**
 * 获取服务类型选项
 */
export function getServiceTypes() {
  return http.request({
    url: '/aigc/mcp/service/types',
    method: 'get',
  });
}

/**
 * 获取服务分类选项
 */
export function getServiceCategories() {
  return http.request({
    url: '/aigc/mcp/service/categories',
    method: 'get',
  });
}

/**
 * 获取认证类型选项
 */
export function getAuthTypes() {
  return http.request({
    url: '/aigc/mcp/service/auth-types',
    method: 'get',
  });
}

/**
 * 获取服务状态选项
 */
export function getServiceStatuses() {
  return http.request({
    url: '/aigc/mcp/service/statuses',
    method: 'get',
  });
}

/**
 * 调用MCP工具
 */
export function callTool(data: any) {
  return http.request({
    url: '/mcp/tools/call',
    method: 'post',
    data,
  });
}

/**
 * 批量调用MCP工具
 */
export function callToolsBatch(data: any[]) {
  return http.request({
    url: '/mcp/tools/call/batch',
    method: 'post',
    data,
  });
}

/**
 * 智能编排执行（生成计划并执行）
 */
export function orchestrateExecute(data: any) {
  return http.request({
    url: '/aigc/mcp/orchestration/execute',
    method: 'post',
    data,
  });
}

/**
 * 生成执行计划（不执行）
 */
export function orchestratePlan(data: any) {
  return http.request({
    url: '/aigc/mcp/orchestration/plan',
    method: 'post',
    data,
  });
}

/**
 * 预览编排计划
 */
export function orchestratePreview(data: any) {
  return http.request({
    url: '/aigc/mcp/orchestration/preview',
    method: 'post',
    data,
  });
}

/**
 * 获取编排示例
 */
export function getOrchestrationExamples() {
  return http.request({
    url: '/aigc/mcp/orchestration/examples',
    method: 'get',
  });
}

/**
 * 获取编排模板
 */
export function getOrchestrationTemplates() {
  return http.request({
    url: '/aigc/mcp/orchestration/templates',
    method: 'get',
  });
}

/**
 * 获取MCP服务的工具列表
 */
export function getServiceTools(serviceName: string) {
  return http.request({
    url: `/mcp/services/${serviceName}/tools`,
    method: 'get',
  });
}

/**
 * 获取编排统计信息
 */
export function getOrchestrationStats() {
  return http.request({
    url: '/mcp/orchestration/stats',
    method: 'get',
  });
}
