/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { http } from '@/utils/http/axios';
import { AxiosProgressEvent } from 'axios';

export function chat(
  data: any,
  controller: AbortController,
  onDownloadProgress?: (progressEvent: AxiosProgressEvent) => void
) {
  let requestData = { ...data };

  // 确保普通聊天接口的数据完整性
  requestData = {
    ...data,
    mcpServiceIds: data.mcpServiceIds || [],
    mcpServices: data.mcpServices || [],
    url: data.url, // 添加文件URL支持
  };
  console.log('使用普通聊天接口');

  return http.request(
    {
      method: 'post',
      url: '/aigc/chat/completions',
      data: requestData,
      signal: controller.signal,
      onDownloadProgress: onDownloadProgress,
    },
    {
      isReturnNativeResponse: true,
    }
  );
}

/**
 * 普通聊天接口（不支持MCP）
 */
export function chatNormal(
  data: any,
  controller: AbortController,
  onDownloadProgress?: (progressEvent: AxiosProgressEvent) => void
) {
  return http.request(
    {
      method: 'post',
      url: '/aigc/chat/completions',
      data,
      signal: controller.signal,
      onDownloadProgress: onDownloadProgress,
    },
    {
      isReturnNativeResponse: true,
    }
  );
}

/**
 * MCP编排执行接口
 */
export function chatWithMcpOrchestration(
  data: any,
  controller: AbortController,
  onDownloadProgress?: (progressEvent: AxiosProgressEvent) => void
) {
  // 转换数据格式为编排接口需要的格式
  const orchestrationData = {
    userPrompt: data.message,
    serviceIds: data.mcpServiceIds || [],
    userId: data.userId || 'anonymous',
    conversationId: data.conversationId,
    // 保留其他聊天相关的参数
    chatId: data.chatId,
    appId: data.appId,
    modelId: data.modelId,
    modelName: data.modelName,
    modelProvider: data.modelProvider,
  };

  return http.request(
    {
      method: 'post',
      url: '/aigc/mcp/orchestration/execute',
      data: orchestrationData,
      signal: controller.signal,
      onDownloadProgress: onDownloadProgress,
    },
    {
      isReturnNativeResponse: true,
    }
  );
}

/**
 * MCP增强聊天接口（已废弃，建议使用编排接口）
 * @deprecated 请使用 chatWithMcpOrchestration 替代
 */
export function chatWithMcp(
  data: any,
  controller: AbortController,
  onDownloadProgress?: (progressEvent: AxiosProgressEvent) => void
) {
  return http.request(
    {
      method: 'post',
      url: '/aigc/chat/mcp/completions',
      data,
      signal: controller.signal,
      onDownloadProgress: onDownloadProgress,
    },
    {
      isReturnNativeResponse: true,
    }
  );
}

export function clean(conversationId: string | null) {
  return http.request({
    url: `/aigc/chat/messages/clean/${conversationId}`,
    method: 'delete',
  });
}

export function cleanAppMessages(appId: string) {
  return http.request({
    url: `/aigc/app/${appId}/messages/clean`,
    method: 'delete',
  });
}

export function getAppConversations(appId: string) {
  return http.request({
    url: `/aigc/app/${appId}/conversations`,
    method: 'get',
  });
}

export function getMessages(conversationId?: string) {
  return http.request({
    url: `/aigc/chat/messages/${conversationId}`,
    method: 'get',
  });
}

export function getAppInfo(params: any) {
  return http.request({
    url: `/aigc/app/info`,
    method: 'get',
    params,
  });
}

export function getImageModels() {
  return http.request({
    method: 'get',
    url: '/aigc/chat/getImageModels',
  });
}

/**
 * @description 生成图片
 */
export function genImage(data: any) {
  return http.request({
    url: '/aigc/chat/image',
    method: 'post',
    data: data,
  });
}

/**
 * @description: 生成思维导图
 */
export function genMindMap(data: any) {
  return http.request({
    url: '/aigc/chat/mindmap',
    method: 'post',
    data: data,
  });
}
