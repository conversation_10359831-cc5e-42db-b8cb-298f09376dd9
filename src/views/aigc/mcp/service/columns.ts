/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { h } from 'vue';
import { NTag, NTooltip, NIcon, NSwitch } from 'naive-ui';
import { CheckCircleOutlined, CloseCircleOutlined, ExclamationCircleOutlined, QuestionCircleOutlined } from '@vicons/antd';

export function getColumns() {
  return [
    {
      title: '服务信息',
      key: 'serviceInfo',
      width: 250,
      render(row: any) {
        return h('div', { class: 'flex items-center' }, [
          h('div', { class: 'mr-3 text-2xl' }, row.icon || '🔧'),
          h('div', [
            h('div', { class: 'font-medium text-gray-900' }, row.displayName || row.name),
            h('div', { class: 'text-sm text-gray-500 font-mono' }, row.name),
            h('div', { class: 'text-xs text-gray-400 mt-1' }, row.description || '暂无描述'),
          ]),
        ]);
      },
    },
    {
      title: '分类',
      key: 'category',
      width: 100,
      align: 'center',
      render(row: any) {
        const categoryMap = {
          builtin: { type: 'success', text: '内置' },
          external: { type: 'primary', text: '外部' },
        };
        const config = categoryMap[row.category] || { type: 'default', text: row.category };
        return h(NTag, { type: config.type }, { default: () => config.text });
      },
    },
    {
      title: '类型',
      key: 'type',
      width: 100,
      align: 'center',
      render(row: any) {
        return h(NTag, { type: 'info' }, { default: () => row.type });
      },
    },
    {
      title: '状态',
      key: 'status',
      width: 100,
      align: 'center',
      render(row: any) {
        const statusMap = {
          ACTIVE: { type: 'success', icon: CheckCircleOutlined, text: '活跃' },
          INACTIVE: { type: 'default', icon: CloseCircleOutlined, text: '非活跃' },
          ERROR: { type: 'error', icon: ExclamationCircleOutlined, text: '错误' },
          UNKNOWN: { type: 'warning', icon: QuestionCircleOutlined, text: '未知' },
        };
        const config = statusMap[row.status] || statusMap.UNKNOWN;
        return h(
          NTooltip,
          { trigger: 'hover' },
          {
            trigger: () =>
              h(NTag, { type: config.type }, {
                icon: () => h(NIcon, { component: config.icon }),
                default: () => config.text,
              }),
            default: () => `状态: ${config.text}`,
          }
        );
      },
    },
    {
      title: '启用状态',
      key: 'enabled',
      width: 100,
      align: 'center',
      render(row: any) {
        return h(NSwitch, {
          value: row.enabled,
          disabled: true,
        });
      },
    },
    {
      title: '优先级',
      key: 'priority',
      width: 80,
      align: 'center',
      render(row: any) {
        return h(NTag, { size: 'small' }, { default: () => row.priority || 5 });
      },
    },
    {
      title: '工具数量',
      key: 'toolsCount',
      width: 100,
      align: 'center',
      render(row: any) {
        let count = 0;
        try {
          count = row.tools ? JSON.parse(row.tools).length : 0;
        } catch {
          count = 0;
        }
        return h(
          NTooltip,
          { trigger: 'hover' },
          {
            trigger: () => h(NTag, { type: 'info', size: 'small' }, { default: () => count }),
            default: () => `该服务包含 ${count} 个工具`,
          }
        );
      },
    },
    {
      title: '端点',
      key: 'endpoint',
      width: 200,
      ellipsis: {
        tooltip: true,
      },
      render(row: any) {
        return h('span', { class: 'font-mono text-sm' }, row.endpoint || '-');
      },
    },
    {
      title: '更新时间',
      key: 'updateTime',
      width: 180,
      align: 'center',
      render(row: any) {
        return row.updateTime ? new Date(row.updateTime).toLocaleString() : '-';
      },
    },
  ];
}
