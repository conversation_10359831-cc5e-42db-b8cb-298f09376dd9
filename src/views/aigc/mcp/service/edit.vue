<!--
  - Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
  -
  - Licensed under the GNU Affero General Public License, Version 3 (the "License");
  - you may not use this file except in compliance with the License.
  - You may obtain a copy of the License at
  -
  -     https://www.gnu.org/licenses/agpl-3.0.html
  -
  - Unless required by applicable law or agreed to in writing, software
  - distributed under the License is distributed on an "AS IS" BASIS,
  - WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  - See the License for the specific language governing permissions and
  - limitations under the License.
  -->

<script lang="ts" setup>
  import { ref, reactive, computed } from 'vue';
  import { useMessage } from 'naive-ui';
  import { AddOutline, TrashOutline } from '@vicons/ionicons5';
  import { add, update, getById } from '@/api/aigc/mcp';
  import SimpleParameterForm from '@/components/mcp/SimpleParameterForm.vue';
  import EnhancedParameterForm from '@/components/mcp/EnhancedParameterForm.vue';

  const emit = defineEmits(['reload']);
  const message = useMessage();

  const showModal = ref(false);
  const isEdit = ref(false);
  const loading = ref(false);
  const activeTab = ref('basic');
  const useEnhancedForm = ref(true); // 默认使用增强模式

  const formRef = ref();
  const formData = reactive({
    id: '',
    name: '',
    displayName: '',
    description: '',
    endpoint: '',
    version: '1.0.0',
    type: 'HTTP',
    category: 'external',
    authType: 'none',
    authConfig: '',
    icon: '',
    tags: '',
    priority: 5,
    enabled: true,
    requiresConfirmation: false,
    timeout: 30,
    maxRetries: 3,
    healthCheckUrl: '',
    healthCheckInterval: 30,
    config: '',
    tools: '',
    parameters: '',
    parameterValues: '',
    remark: '',
  });

  const authConfigForm = reactive({
    type: 'none',
    token: '',
    username: '',
    password: '',
    apiKey: '',
  });

  const toolsList = ref([]);
  const parametersList = ref([]);
  const currentParameterValues = ref({});

  const rules = {
    name: { required: true, message: '请输入服务名称', trigger: 'blur' },
    displayName: { required: true, message: '请输入显示名称', trigger: 'blur' },
    category: { required: true, message: '请选择服务分类', trigger: 'change' },
    type: { required: true, message: '请选择服务类型', trigger: 'change' },
    endpoint: { required: true, message: '请输入服务端点', trigger: 'blur' },
  };

  const modalTitle = computed(() => {
    return isEdit.value ? '编辑MCP服务' : '新增MCP服务';
  });

  function show(record: any = {}) {
    showModal.value = true;
    isEdit.value = !!record.id;

    if (isEdit.value) {
      loadData(record.id);
    } else {
      resetForm();
    }
  }

  async function loadData(id: string) {
    try {
      const response = await getById(id);
      Object.assign(formData, response);

      // 解析认证配置
      if (response.authConfig) {
        try {
          const authData = JSON.parse(response.authConfig);
          Object.assign(authConfigForm, authData);
        } catch (error) {
          console.error('解析认证配置失败', error);
        }
      }

      // 解析工具列表
      if (response.tools) {
        try {
          toolsList.value = JSON.parse(response.tools);
        } catch (error) {
          console.error('解析工具列表失败', error);
          toolsList.value = [];
        }
      }

      // 解析参数定义
      if (response.parameters) {
        try {
          const params = JSON.parse(response.parameters);
          // 确保每个参数都有validation字段
          parametersList.value = params.map((param) => ({
            ...param,
            validation: param.validation || {
              extractFromInput: false,
              extractionPrompt: '',
              keywords: [],
              pattern: '',
              defaultValue: '',
            },
          }));
        } catch (error) {
          console.error('解析参数定义失败', error);
          parametersList.value = [];
        }
      }

      // 解析参数值
      if (response.parameterValues) {
        try {
          currentParameterValues.value = JSON.parse(response.parameterValues);
        } catch (error) {
          console.error('解析参数值失败', error);
          currentParameterValues.value = {};
        }
      }
    } catch (error) {
      message.error('获取服务详情失败');
    }
  }

  function resetForm() {
    Object.assign(formData, {
      id: '',
      name: '',
      displayName: '',
      description: '',
      endpoint: '',
      version: '1.0.0',
      type: 'HTTP',
      category: 'external',
      authType: 'none',
      authConfig: '',
      icon: '',
      tags: '',
      priority: 5,
      enabled: true,
      requiresConfirmation: false,
      timeout: 30,
      maxRetries: 3,
      healthCheckUrl: '',
      healthCheckInterval: 30,
      config: '',
      tools: '',
      parameters: '',
      parameterValues: '',
      remark: '',
    });

    Object.assign(authConfigForm, {
      type: 'none',
      token: '',
      username: '',
      password: '',
      apiKey: '',
    });

    toolsList.value = [];
    parametersList.value = [];
    currentParameterValues.value = {};
    activeTab.value = 'basic';
  }

  async function handleSubmit() {
    try {
      await formRef.value?.validate();
      loading.value = true;

      // 构建提交数据
      const submitData = { ...formData };

      // 处理认证配置
      if (formData.authType !== 'none') {
        submitData.authConfig = JSON.stringify(authConfigForm);
      } else {
        submitData.authConfig = '';
      }

      // 处理工具列表
      if (toolsList.value.length > 0) {
        submitData.tools = JSON.stringify(toolsList.value);
      } else {
        submitData.tools = '';
      }

      // 处理参数定义
      if (parametersList.value.length > 0) {
        submitData.parameters = JSON.stringify(parametersList.value);
      } else {
        submitData.parameters = '';
      }

      // 处理参数值
      if (Object.keys(currentParameterValues.value).length > 0) {
        submitData.parameterValues = JSON.stringify(currentParameterValues.value);
      } else {
        submitData.parameterValues = '';
      }

      // 验证JSON格式
      if (submitData.config) {
        try {
          JSON.parse(submitData.config);
        } catch (error) {
          message.error('服务配置JSON格式错误');
          return;
        }
      }

      if (isEdit.value) {
        await update(submitData);
        message.success('更新成功');
      } else {
        await add(submitData);
        message.success('新增成功');
      }

      showModal.value = false;
      emit('reload');
    } catch (error) {
      message.error(isEdit.value ? '更新失败' : '新增失败');
    } finally {
      loading.value = false;
    }
  }

  function handleCancel() {
    showModal.value = false;
  }

  function handleAuthTypeChange() {
    // 清空认证配置
    Object.assign(authConfigForm, {
      type: formData.authType,
      token: '',
      username: '',
      password: '',
      apiKey: '',
    });
  }

  function addTool() {
    toolsList.value.push({
      name: '',
      description: '',
      category: '',
      priority: 5,
    });
  }

  function removeTool(index: number) {
    toolsList.value.splice(index, 1);
  }

  // 参数管理方法
  function addParameter() {
    const newParam = {
      name: '',
      displayName: '',
      description: '',
      type: 'string',
      required: false,
      sensitive: false,
      defaultValue: '',
      group: 'basic',
      order: parametersList.value.length + 1,
    };

    // 如果使用增强模式，添加validation字段
    if (useEnhancedForm.value) {
      newParam.validation = {
        extractFromInput: false,
        extractionPrompt: '',
        keywords: [],
        pattern: '',
        defaultValue: '',
      };
    }

    parametersList.value.push(newParam);
  }

  function removeParameter(index: number) {
    parametersList.value.splice(index, 1);
  }

  defineExpose({
    show,
  });
</script>

<template>
  <n-modal
    v-model:show="showModal"
    :mask-closable="false"
    preset="dialog"
    :title="modalTitle"
    class="w-4/5 max-w-4xl"
  >
    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="left"
      label-width="120px"
    >
      <n-tabs v-model:value="activeTab" type="line">
        <!-- 基本信息 -->
        <n-tab-pane name="basic" tab="基本信息">
          <n-grid :cols="2" :x-gap="24">
            <n-grid-item>
              <n-form-item label="服务名称" path="name">
                <n-input
                  v-model:value="formData.name"
                  placeholder="请输入服务名称（英文，唯一）"
                  :disabled="isEdit"
                />
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="显示名称" path="displayName">
                <n-input v-model:value="formData.displayName" placeholder="请输入显示名称" />
              </n-form-item>
            </n-grid-item>
          </n-grid>

          <n-form-item label="服务描述" path="description">
            <n-input
              v-model:value="formData.description"
              type="textarea"
              :rows="3"
              placeholder="请输入服务描述"
            />
          </n-form-item>

          <n-grid :cols="2" :x-gap="24">
            <n-grid-item>
              <n-form-item label="服务分类" path="category">
                <n-select
                  v-model:value="formData.category"
                  placeholder="请选择分类"
                  :options="[
                    { label: '内置服务', value: 'builtin' },
                    { label: '外部服务', value: 'external' },
                  ]"
                />
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="服务类型" path="type">
                <n-select
                  v-model:value="formData.type"
                  placeholder="请选择类型"
                  :options="[
                    { label: 'HTTP', value: 'HTTP' },
                    { label: 'WebSocket', value: 'WEBSOCKET' },
                    { label: 'gRPC', value: 'GRPC' },
                  ]"
                />
              </n-form-item>
            </n-grid-item>
          </n-grid>

          <n-form-item label="服务端点" path="endpoint">
            <n-input v-model:value="formData.endpoint" placeholder="请输入服务端点URL" />
          </n-form-item>

          <n-grid :cols="3" :x-gap="24">
            <n-grid-item>
              <n-form-item label="版本" path="version">
                <n-input v-model:value="formData.version" placeholder="如：1.0.0" />
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="优先级" path="priority">
                <n-input-number v-model:value="formData.priority" :min="1" :max="10" />
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="服务图标" path="icon">
                <n-input v-model:value="formData.icon" placeholder="如：🔧" />
              </n-form-item>
            </n-grid-item>
          </n-grid>

          <n-form-item label="服务标签" path="tags">
            <n-input v-model:value="formData.tags" placeholder="多个标签用逗号分隔" />
          </n-form-item>
        </n-tab-pane>

        <!-- 认证配置 -->
        <n-tab-pane name="auth" tab="认证配置">
          <n-form-item label="认证类型" path="authType">
            <n-select
              v-model:value="formData.authType"
              placeholder="请选择认证类型"
              @update:value="handleAuthTypeChange"
              :options="[
                { label: '无认证', value: 'none' },
                { label: 'Bearer Token', value: 'bearer' },
                { label: 'Basic Auth', value: 'basic' },
                { label: 'API Key', value: 'api_key' },
              ]"
            />
          </n-form-item>

          <!-- Bearer Token -->
          <template v-if="formData.authType === 'bearer'">
            <n-form-item label="Token">
              <n-input
                v-model:value="authConfigForm.token"
                type="password"
                placeholder="请输入Bearer Token"
                show-password-on="click"
              />
            </n-form-item>
          </template>

          <!-- Basic Auth -->
          <template v-if="formData.authType === 'basic'">
            <n-grid :cols="2" :x-gap="24">
              <n-grid-item>
                <n-form-item label="用户名">
                  <n-input v-model:value="authConfigForm.username" placeholder="请输入用户名" />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="密码">
                  <n-input
                    v-model:value="authConfigForm.password"
                    type="password"
                    placeholder="请输入密码"
                    show-password-on="click"
                  />
                </n-form-item>
              </n-grid-item>
            </n-grid>
          </template>

          <!-- API Key -->
          <template v-if="formData.authType === 'api_key'">
            <n-form-item label="API Key">
              <n-input
                v-model:value="authConfigForm.apiKey"
                type="password"
                placeholder="请输入API Key"
                show-password-on="click"
              />
            </n-form-item>
          </template>
        </n-tab-pane>

        <!-- 参数配置 -->
        <n-tab-pane name="parameters" tab="参数配置">
          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <div>
                <h4 class="text-lg font-medium">服务参数定义</h4>
                <p class="text-sm text-gray-500">定义此服务需要的配置参数，支持智能提取配置</p>
              </div>
              <n-switch v-model:value="useEnhancedForm" class="ml-4">
                <template #checked>增强模式</template>
                <template #unchecked>简单模式</template>
              </n-switch>
            </div>

            <!-- 增强模式：使用增强参数配置组件 -->
            <div v-if="useEnhancedForm">
              <EnhancedParameterForm v-model="parametersList" />
            </div>

            <!-- 简单模式：原有的参数配置 -->
            <div v-else>
              <div class="flex justify-end mb-4">
                <n-button type="primary" @click="addParameter">
                  <template #icon>
                    <n-icon>
                      <AddOutline />
                    </n-icon>
                  </template>
                  添加参数
                </n-button>
              </div>

              <div v-if="parametersList.length === 0" class="text-center py-8 text-gray-500">
                暂无参数定义，点击"添加参数"开始配置
              </div>

              <div v-else class="space-y-4">
                <n-card
                  v-for="(param, index) in parametersList"
                  :key="index"
                  size="small"
                  class="parameter-card"
                >
                  <template #header>
                    <div class="flex justify-between items-center">
                      <span class="font-medium">参数 {{ index + 1 }}</span>
                      <n-button
                        quaternary
                        type="error"
                        size="small"
                        @click="removeParameter(index)"
                      >
                        <template #icon>
                          <n-icon>
                            <TrashOutline />
                          </n-icon>
                        </template>
                      </n-button>
                    </div>
                  </template>

                  <n-grid :cols="2" :x-gap="16">
                    <n-grid-item>
                      <n-form-item label="参数名称">
                        <n-input v-model:value="param.name" placeholder="如：api_key" />
                      </n-form-item>
                    </n-grid-item>
                    <n-grid-item>
                      <n-form-item label="显示名称">
                        <n-input v-model:value="param.displayName" placeholder="如：API密钥" />
                      </n-form-item>
                    </n-grid-item>
                  </n-grid>

                  <n-form-item label="参数描述">
                    <n-input v-model:value="param.description" placeholder="参数的详细说明" />
                  </n-form-item>

                  <n-grid :cols="3" :x-gap="16">
                    <n-grid-item>
                      <n-form-item label="参数类型">
                        <n-select
                          v-model:value="param.type"
                          :options="[
                            { label: '字符串', value: 'string' },
                            { label: '数字', value: 'number' },
                            { label: '布尔值', value: 'boolean' },
                            { label: '对象', value: 'object' },
                          ]"
                        />
                      </n-form-item>
                    </n-grid-item>
                    <n-grid-item>
                      <n-form-item label="是否必填">
                        <n-switch v-model:value="param.required" />
                      </n-form-item>
                    </n-grid-item>
                    <n-grid-item>
                      <n-form-item label="敏感信息">
                        <n-switch v-model:value="param.sensitive" />
                      </n-form-item>
                    </n-grid-item>
                  </n-grid>

                  <n-form-item label="默认值">
                    <n-input v-model:value="param.defaultValue" placeholder="参数的默认值" />
                  </n-form-item>
                </n-card>
              </div>
            </div>
          </div>
        </n-tab-pane>

        <!-- 高级配置 -->
        <n-tab-pane name="advanced" tab="高级配置">
          <n-grid :cols="3" :x-gap="24">
            <n-grid-item>
              <n-form-item label="超时时间(秒)">
                <n-input-number v-model:value="formData.timeout" :min="1" :max="300" />
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="最大重试">
                <n-input-number v-model:value="formData.maxRetries" :min="0" :max="10" />
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="健康检查间隔(秒)">
                <n-input-number
                  v-model:value="formData.healthCheckInterval"
                  :min="10"
                  :max="3600"
                />
              </n-form-item>
            </n-grid-item>
          </n-grid>

          <n-form-item label="健康检查URL">
            <n-input v-model:value="formData.healthCheckUrl" placeholder="如：/health 或完整URL" />
          </n-form-item>

          <n-grid :cols="2" :x-gap="24">
            <n-grid-item>
              <n-form-item label="启用服务">
                <n-switch v-model:value="formData.enabled" />
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="需要确认">
                <n-switch v-model:value="formData.requiresConfirmation" />
              </n-form-item>
            </n-grid-item>
          </n-grid>

          <n-form-item label="服务配置">
            <n-input
              v-model:value="formData.config"
              type="textarea"
              :rows="4"
              placeholder="JSON格式的服务配置"
            />
            <template #feedback>
              <span class="text-gray-500 text-sm">JSON格式，如：{"key": "value"}</span>
            </template>
          </n-form-item>

          <n-form-item label="备注">
            <n-input
              v-model:value="formData.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </n-form-item>
        </n-tab-pane>
      </n-tabs>
    </n-form>

    <template #action>
      <n-space>
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="primary" :loading="loading" @click="handleSubmit">确定</n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<style lang="less" scoped></style>
