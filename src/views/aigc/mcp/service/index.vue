<!--
  - Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
  -
  - Licensed under the GNU Affero General Public License, Version 3 (the "License");
  - you may not use this file except in compliance with the License.
  - You may obtain a copy of the License at
  -
  -     https://www.gnu.org/licenses/agpl-3.0.html
  -
  - Unless required by applicable law or agreed to in writing, software
  - distributed under the License is distributed on an "AS IS" BASIS,
  - WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  - See the License for the specific language governing permissions and
  - limitations under the License.
  -->

<script lang="ts" setup>
  import { BasicTable, TableAction } from '@/components/Table';
  import { DeleteOutlined, EditOutlined, PlusOutlined, ReloadOutlined, SyncOutlined, PlayCircleOutlined, StopOutlined, SettingOutlined } from '@vicons/antd';
  import Edit from './edit.vue';
  import { computed, h, reactive, ref, onMounted } from 'vue';
  import { getColumns } from './columns';
  import { page, del, toggleEnabled, testConnection, syncService, syncAllServices, refreshTools, getStats } from '@/api/aigc/mcp';
  import { useDialog, useMessage } from 'naive-ui';

  const message = useMessage();
  const dialog = useDialog();
  const actionRef = ref();
  const editRef = ref();
  const stats = ref({
    totalCount: 0,
    enabledCount: 0,
    builtinCount: 0,
    externalCount: 0,
    activeCount: 0,
  });

  const searchParams = reactive({
    displayName: '',
  });

  const actionColumn = reactive({
    width: 200,
    title: '操作',
    key: 'action',
    fixed: 'right',
    align: 'center',
    render(record: any) {
      return h(TableAction as any, {
        style: 'text',
        actions: [
          {
            type: 'info',
            icon: EditOutlined,
            onClick: handleEdit.bind(null, record),
            tooltip: '编辑',
          },
          {
            type: record.enabled ? 'warning' : 'success',
            icon: record.enabled ? StopOutlined : PlayCircleOutlined,
            onClick: handleToggleEnabled.bind(null, record),
            tooltip: record.enabled ? '禁用' : '启用',
          },
          {
            type: 'primary',
            icon: SyncOutlined,
            onClick: handleSync.bind(null, record),
            tooltip: '同步',
          },
          {
            type: 'info',
            icon: SettingOutlined,
            onClick: handleTest.bind(null, record),
            tooltip: '测试连接',
          },
          {
            type: 'error',
            icon: DeleteOutlined,
            onClick: handleDel.bind(null, record),
            tooltip: '删除',
          },
        ],
      });
    },
  });

  const columns = computed(() => {
    return getColumns();
  });

  const loadDataTable = async (params: any) => {
    const response = await page({
      ...params,
      ...searchParams,
    });
    return {
      rows: response.records || response.data || response || [],
      total: response.total || 0,
    };
  };

  async function handleAdd() {
    editRef.value.show({});
  }

  function handleEdit(record: any) {
    editRef.value.show(record);
  }

  function reloadTable() {
    actionRef.value.reload();
    loadStats();
  }

  function handleDel(record: any) {
    dialog.warning({
      title: '警告',
      content: `你确定删除 [${record.displayName || record.name}] 服务吗？删除后将无法使用该服务`,
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        await del([record.id]);
        reloadTable();
        message.success('服务删除成功');
      },
    });
  }

  async function handleToggleEnabled(record: any) {
    try {
      await toggleEnabled(record.id, !record.enabled);
      reloadTable();
      message.success(record.enabled ? '服务已禁用' : '服务已启用');
    } catch (error) {
      message.error('操作失败');
    }
  }

  async function handleTest(record: any) {
    try {
      const result = await testConnection(record.id);
      if (result) {
        message.success('连接测试成功');
      } else {
        message.error('连接测试失败');
      }
      reloadTable();
    } catch (error) {
      message.error('测试失败');
    }
  }

  async function handleSync(record: any) {
    try {
      await syncService(record.id);
      message.success('服务同步成功');
      reloadTable();
    } catch (error) {
      message.error('同步失败');
    }
  }

  async function handleSyncAll() {
    try {
      await syncAllServices();
      message.success('批量同步成功');
      reloadTable();
    } catch (error) {
      message.error('批量同步失败');
    }
  }

  async function handleRefreshTools(record: any) {
    try {
      await refreshTools(record.id);
      message.success('工具列表刷新成功');
      reloadTable();
    } catch (error) {
      message.error('刷新失败');
    }
  }

  async function loadStats() {
    try {
      const response = await getStats();
      stats.value = response;
    } catch (error) {
      console.error('加载统计信息失败', error);
    }
  }

  function handleSearch() {
    reloadTable();
  }

  function handleReset() {
    Object.assign(searchParams, {
      name: '',
      category: '',
      status: '',
      enabled: null,
    });
    reloadTable();
  }

  // 组件挂载时加载统计信息
  onMounted(() => {
    loadStats();
  });
</script>

<template>
  <div>
    <!-- 统计信息 -->
    <div class="mb-4">
      <n-grid :cols="5" :x-gap="12">
        <n-grid-item>
          <n-statistic label="总服务数" :value="stats.totalCount" />
        </n-grid-item>
        <n-grid-item>
          <n-statistic label="启用服务" :value="stats.enabledCount" />
        </n-grid-item>
        <n-grid-item>
          <n-statistic label="内置服务" :value="stats.builtinCount" />
        </n-grid-item>
        <n-grid-item>
          <n-statistic label="外部服务" :value="stats.externalCount" />
        </n-grid-item>
        <n-grid-item>
          <n-statistic label="活跃服务" :value="stats.activeCount" />
        </n-grid-item>
      </n-grid>
    </div>

    <!-- 搜索表单 -->
    <n-card class="mb-4">
      <n-form inline :model="searchParams" label-placement="left">
        <n-form-item label="服务名称">
          <n-input v-model:value="searchParams.displayName" placeholder="请输入服务名称" clearable />
        </n-form-item>
        <n-form-item>
          <n-button type="primary" @click="handleSearch">搜索</n-button>
          <n-button class="ml-2" @click="handleReset">重置</n-button>
        </n-form-item>
      </n-form>
    </n-card>

    <!-- 表格 -->
    <BasicTable
      ref="actionRef"
      :actionColumn="actionColumn"
      :columns="columns"
      :request="loadDataTable"
      :row-key="(row: any) => row.id"
      :single-line="false"
    >
      <template #tableTitle>
        <n-button type="primary" @click="handleAdd">
          <template #icon>
            <n-icon>
              <PlusOutlined />
            </n-icon>
          </template>
          新增服务
        </n-button>
        <n-button type="info" class="ml-2" @click="handleSyncAll">
          <template #icon>
            <n-icon>
              <SyncOutlined />
            </n-icon>
          </template>
          同步所有服务
        </n-button>
      </template>
    </BasicTable>

    <Edit ref="editRef" @reload="reloadTable" />
  </div>
</template>

<style lang="less" scoped></style>
