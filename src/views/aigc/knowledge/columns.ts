/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { FormSchema } from '@/components/Form';

export const searchSchemas: FormSchema[] = [
  {
    field: 'name',
    component: 'NInput',
    label: '知识库名称',
    componentProps: {
      placeholder: '请输入知识库名称',
    },
  },
];

export const formSchemas: FormSchema[] = [
  {
    field: 'id',
    label: 'ID',
    component: 'NInput',
    isHidden: true,
  },
  {
    field: 'name',
    component: 'NInput',
    label: '知识库名称',
    componentProps: {
      placeholder: '请输入知识库名称',
    },
    rules: [{ required: true, message: '请输入知识库名称', trigger: ['blur'] }],
  },
  {
    field: 'embedStoreId',
    label: '向量数据库',
    component: 'NInput',
    slot: 'embedStoreSlot',
    rules: [{ required: true, message: '请选择关联向量数据库', trigger: ['blur'] }],
  },
  {
    field: 'embedModelId',
    label: '向量模型',
    component: 'NInput',
    slot: 'embedModelSlot',
    rules: [{ required: true, message: '请选择关联向量模型', trigger: ['blur'] }],
  },
  {
    field: 'des',
    component: 'NInput',
    label: '知识库描述',
    componentProps: {
      placeholder: '请输入知识库描述',
      type: 'textarea',
      autosize: {
        minRows: 3,
        maxRows: 3,
      },
    },
    rules: [{ required: true, message: '请输入知识库描述', trigger: ['blur'] }],
  },
];
