<template>
  <div>
    <n-card :bordered="false" title="模型管理">
      <template #header-extra>
        <n-space>
          <n-button type="primary" @click="refreshData">
            <template #icon>
              <n-icon>
                <ReloadOutlined />
              </n-icon>
            </template>
            刷新数据
          </n-button>
        </n-space>
      </template>
      
      <n-tabs type="line" animated>
        <n-tab-pane name="category" tab="模型分类">
          <CategoryManagement ref="categoryRef" />
        </n-tab-pane>
        <n-tab-pane name="provider" tab="模型供应商">
          <ProviderManagement ref="providerRef" />
        </n-tab-pane>
        <n-tab-pane name="version" tab="模型版本">
          <VersionManagement ref="versionRef" />
        </n-tab-pane>
        <n-tab-pane name="search-engine" tab="搜索引擎">
          <SearchEngineManagement ref="searchEngineRef" />
        </n-tab-pane>
      </n-tabs>
    </n-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { useMessage } from 'naive-ui';
  import { ReloadOutlined } from '@vicons/antd';
  import CategoryManagement from './category/index.vue';
  import ProviderManagement from './provider/index.vue';
  import VersionManagement from './version/index.vue';
  import SearchEngineManagement from './web-search/index.vue';

  const message = useMessage();
  const categoryRef = ref();
  const providerRef = ref();
  const versionRef = ref();
  const searchEngineRef = ref();

  function refreshData() {
    // 刷新所有标签页的数据
    if (categoryRef.value?.reloadTable) {
      categoryRef.value.reloadTable();
    }
    if (providerRef.value?.reloadTable) {
      providerRef.value.reloadTable();
    }
    if (versionRef.value?.reloadTable) {
      versionRef.value.reloadTable();
    }
    if (searchEngineRef.value?.reloadTable) {
      searchEngineRef.value.reloadTable();
    }
    message.success('数据已刷新');
  }
</script>

<style lang="less" scoped>
  :deep(.n-tabs-nav) {
    background: #fff;
  }
</style>
