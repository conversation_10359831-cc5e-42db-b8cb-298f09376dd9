<template>
  <div>
    <div class="n-layout-page-header">
      <n-card :bordered="false" title="模型供应商管理">
        <template #header-extra>
          <n-button type="primary" @click="handleAdd">
            <template #icon>
              <n-icon>
                <PlusOutlined />
              </n-icon>
            </template>
            新增供应商
          </n-button>
        </template>
      </n-card>
    </div>
    <n-card :bordered="false" class="proCard">
      <BasicTable
        :columns="columns"
        :request="loadDataTable"
        :row-key="(row) => row.id"
        ref="actionRef"
        :actionColumn="actionColumn"
        @update:checked-row-keys="onCheckedRow"
        :scroll-x="1800"
      >
        <template #tableTitle>
          <n-button
            type="error"
            @click="handleBatchDelete"
            :disabled="batchDeleteDisabled"
            class="min-left-space"
            v-if="hasPermission(['aigc:model:provider:delete'])"
          >
            <template #icon>
              <n-icon>
                <DeleteOutlined />
              </n-icon>
            </template>
            批量删除
          </n-button>
        </template>
      </BasicTable>
    </n-card>

    <Edit
      @reloadTable="reloadTable"
      @updateShowModal="updateShowModal"
      :showModal="showModal"
      :editData="editData"
    />
  </div>
</template>

<script lang="ts" setup>
  import { reactive, ref, computed, h } from 'vue';
  import { useDialog, useMessage } from 'naive-ui';
  import { BasicTable, TableAction } from '@/components/Table';
  import { PlusOutlined, DeleteOutlined, EditOutlined, EyeOutlined } from '@vicons/antd';
  import { usePermission } from '@/hooks/web/usePermission';
  import { page, del } from '@/api/aigc/modelProvider';
  import Edit from './edit.vue';

  const { hasPermission } = usePermission();
  const dialog = useDialog();
  const message = useMessage();

  const actionRef = ref();
  const showModal = ref(false);
  const editData = ref(null);

  const columns = [
    {
      type: 'selection',
      key: 'selection',
    },
    {
      title: '供应商名称',
      key: 'name',
      width: 150,
    },
    {
      title: '供应商编码',
      key: 'code',
      minWidth: 120,
    },
    {
      title: 'Logo',
      key: 'logoUrl',
      minWidth: 80,
      render(row) {
        if (row.logoUrl) {
          return h('img', {
            src: row.logoUrl,
            style: { width: '32px', height: '32px', borderRadius: '4px' },
          });
        }
        return '-';
      },
    },
    {
      title: '描述',
      key: 'description',
      minWidth: 200,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '官方网站',
      key: 'officialWebsite',
      minWidth: 150,
      render(row) {
        if (row.officialWebsite) {
          return h(
            'a',
            {
              href: row.officialWebsite,
              target: '_blank',
              style: { color: '#1890ff' },
            },
            '访问官网'
          );
        }
        return '-';
      },
    },
    {
      title: '默认BaseUrl',
      key: 'defaultBaseUrl',
      minWidth: 200,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '认证类型',
      key: 'authType',
      minWidth: 100,
      render(row) {
        const typeMap = {
          API_KEY: 'API密钥',
          OAUTH: 'OAuth',
          LOCAL: '本地',
        };
        return typeMap[row.authType] || row.authType;
      },
    },
    {
      title: '支持模型类型',
      key: 'supportedModelTypes',
      minWidth: 200,
      render(row) {
        if (!row.supportedModelTypes || row.supportedModelTypes.length === 0) {
          return '-';
        }
        const typeMap = {
          CHAT: '对话',
          EMBEDDING: '向量',
          TEXT_IMAGE: '图像',
          WEB_SEARCH: '搜索',
        };
        return h('div', {},
          row.supportedModelTypes.map((type, index) =>
            h('n-tag', {
              key: type,
              size: 'small',
              type: 'info',
              style: { marginRight: index < row.supportedModelTypes.length - 1 ? '4px' : '0' }
            }, typeMap[type] || type)
          )
        );
      },
    },
    {
      title: '排序',
      key: 'sortOrder',
      minWidth: 80,
    },
    {
      title: '状态',
      key: 'status',
      minWidth: 80,
      render(row) {
        return h(
          'n-tag',
          {
            style: {
              marginRight: '6px',
            },
            type: row.status ? 'success' : 'error',
            bordered: false,
          },
          {
            default: () => (row.status ? '启用' : '禁用'),
          }
        );
      },
    },
    {
      title: '创建时间',
      key: 'createTime',
      minWidth: 180,
    },
  ];

  const actionColumn = reactive({
    minWidth: 220,
    title: '操作',
    key: 'action',
    fixed: 'right',
    render(record) {
      return h(TableAction as any, {
        style: 'text',
        actions: [
          {
            label: '编辑',
            type: 'primary',
            icon: EditOutlined,
            onClick: handleEdit.bind(null, record),
          },
          {
            label: '删除',
            type: 'error',
            icon: DeleteOutlined,
            onClick: handleDelete.bind(null, record),
          },
        ],
      });
    },
  });

  const checkedIds = ref([]);
  const batchDeleteDisabled = computed(() => {
    return checkedIds.value.length <= 0;
  });

  const loadDataTable = async (res) => {
    return await page({ ...res });
  };

  const reloadTable = () => {
    actionRef.value.reload();
  };

  function onCheckedRow(rowKeys) {
    checkedIds.value = rowKeys;
  }

  function updateShowModal(value) {
    showModal.value = value;
  }

  function handleAdd() {
    editData.value = null;
    showModal.value = true;
  }

  function handleEdit(record) {
    editData.value = record;
    showModal.value = true;
  }

  function handleDelete(record) {
    dialog.warning({
      title: '警告',
      content: `您确定删除供应商 "${record.name}" 吗？`,
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        await del(record.id);
        message.success('删除成功');
        reloadTable();
      },
    });
  }

  function handleBatchDelete() {
    dialog.warning({
      title: '警告',
      content: `您确定删除选中的 ${checkedIds.value.length} 个供应商吗？`,
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        for (const id of checkedIds.value) {
          await del(id);
        }
        message.success('批量删除成功');
        checkedIds.value = [];
        reloadTable();
      },
    });
  }
</script>

<style lang="less" scoped></style>
