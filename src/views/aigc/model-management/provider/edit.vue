<template>
  <n-modal
    v-model:show="showModal"
    :show-icon="false"
    preset="dialog"
    :title="editData ? '编辑供应商' : '新增供应商'"
    :style="{
      width: dialogWidth,
    }"
  >
    <n-form
      :model="formParams"
      :rules="rules"
      ref="formRef"
      label-placement="left"
      :label-width="100"
      class="py-4"
    >
      <n-form-item label="供应商名称" path="name">
        <n-input placeholder="请输入供应商名称" v-model:value="formParams.name" />
      </n-form-item>
      <n-form-item label="供应商编码" path="code">
        <n-input 
          placeholder="请输入供应商编码" 
          v-model:value="formParams.code"
          :disabled="!!editData"
        />
      </n-form-item>
      <n-form-item label="Logo地址" path="logoUrl">
        <n-input placeholder="请输入Logo地址" v-model:value="formParams.logoUrl" />
      </n-form-item>
      <n-form-item label="描述" path="description">
        <n-input
          type="textarea"
          placeholder="请输入描述"
          v-model:value="formParams.description"
          :autosize="{ minRows: 3, maxRows: 5 }"
        />
      </n-form-item>
      <n-form-item label="官方网站" path="officialWebsite">
        <n-input placeholder="请输入官方网站地址" v-model:value="formParams.officialWebsite" />
      </n-form-item>
      <n-form-item label="API文档地址" path="apiDocUrl">
        <n-input placeholder="请输入API文档地址" v-model:value="formParams.apiDocUrl" />
      </n-form-item>
      <n-form-item label="默认BaseUrl" path="defaultBaseUrl">
        <n-input placeholder="请输入默认BaseUrl" v-model:value="formParams.defaultBaseUrl" />
      </n-form-item>
      <n-form-item label="认证类型" path="authType">
        <n-select
          placeholder="请选择认证类型"
          v-model:value="formParams.authType"
          :options="authTypeOptions"
        />
      </n-form-item>
      <n-form-item label="支持模型类型" path="supportedModelTypes">
        <n-select
          placeholder="请选择支持的模型类型"
          v-model:value="formParams.supportedModelTypes"
          :options="modelTypeOptions"
          multiple
          clearable
        />
      </n-form-item>
      <n-form-item label="排序" path="sortOrder">
        <n-input-number
          placeholder="请输入排序"
          v-model:value="formParams.sortOrder"
          :min="0"
          class="w-full"
        />
      </n-form-item>
      <n-form-item label="状态" path="status">
        <n-switch v-model:value="formParams.status">
          <template #checked>启用</template>
          <template #unchecked>禁用</template>
        </n-switch>
      </n-form-item>
    </n-form>

    <template #action>
      <n-space>
        <n-button @click="closeModal">取消</n-button>
        <n-button type="primary" :loading="formBtnLoading" @click="confirmForm">确定</n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script lang="ts" setup>
  import { computed, ref, watch } from 'vue';
  import { useMessage } from 'naive-ui';
  import { add, update } from '@/api/aigc/modelProvider';

  const emit = defineEmits(['reloadTable', 'updateShowModal']);

  interface Props {
    showModal: boolean;
    editData?: any;
  }

  const props = withDefaults(defineProps<Props>(), {
    showModal: false,
    editData: null,
  });

  const message = useMessage();
  const formRef = ref();
  const formBtnLoading = ref(false);
  const dialogWidth = ref('600px');

  const authTypeOptions = [
    { label: 'API密钥', value: 'API_KEY' },
    { label: 'OAuth', value: 'OAUTH' },
    { label: '本地', value: 'LOCAL' },
  ];

  const modelTypeOptions = [
    { label: '对话模型', value: 'CHAT' },
    { label: '向量模型', value: 'EMBEDDING' },
    { label: '图像生成', value: 'TEXT_IMAGE' },
    { label: '搜索引擎', value: 'WEB_SEARCH' },
  ];

  const defaultFormParams = {
    id: '',
    name: '',
    code: '',
    logoUrl: '',
    description: '',
    officialWebsite: '',
    apiDocUrl: '',
    defaultBaseUrl: '',
    authType: 'API_KEY',
    supportedModelTypes: [],
    sortOrder: 0,
    status: true,
  };

  const formParams = ref({ ...defaultFormParams });

  const rules = {
    name: {
      required: true,
      message: '请输入供应商名称',
      trigger: 'blur',
    },
    code: {
      required: true,
      message: '请输入供应商编码',
      trigger: 'blur',
    },
    authType: {
      required: true,
      message: '请选择认证类型',
      trigger: 'change',
    },
    supportedModelTypes: {
      required: true,
      type: 'array',
      min: 1,
      message: '请选择支持的模型类型',
      trigger: ['change', 'blur'],
    },
  };

  const showModal = computed({
    get: () => {
      return props.showModal;
    },
    set: (value) => {
      emit('updateShowModal', value);
    },
  });

  function confirmForm(e) {
    e.preventDefault();
    formRef.value.validate(async (errors) => {
      if (!errors) {
        // 额外检查支持的模型类型
        if (!formParams.value.supportedModelTypes || formParams.value.supportedModelTypes.length === 0) {
          message.error('请选择支持的模型类型');
          return;
        }

        formBtnLoading.value = true;
        try {
          // 过滤掉不需要的字段
          const submitData = {
            ...formParams.value,
          };

          // 删除时间字段，让后端自动处理
          if (!props.editData) {
            delete submitData.id;
            delete submitData.createTime;
            delete submitData.updateTime;
          } else {
            delete submitData.createTime;
            delete submitData.updateTime;
          }



          if (props.editData) {
            await update(submitData);
            message.success('编辑成功');
          } else {
            await add(submitData);
            message.success('新增成功');
          }
          setTimeout(() => {
            showModal.value = false;
            emit('reloadTable');
          });
        } catch (error) {
          console.error(error);
          message.error('操作失败，请重试');
        } finally {
          formBtnLoading.value = false;
        }
      } else {
        // 查找具体的错误信息
        const errorMessages = [];
        for (const field in errors) {
          if (errors[field] && errors[field].length > 0) {
            errorMessages.push(errors[field][0].message);
          }
        }
        if (errorMessages.length > 0) {
          message.error(errorMessages[0]);
        } else {
          message.error('请填写完整信息');
        }
      }
    });
  }

  function closeModal() {
    showModal.value = false;
  }

  watch(
    () => props.showModal,
    (newValue) => {
      if (newValue) {
        if (props.editData) {
          formParams.value = {
            ...props.editData,
            // 确保 supportedModelTypes 是数组
            supportedModelTypes: props.editData.supportedModelTypes || []
          };
        } else {
          formParams.value = { ...defaultFormParams };
        }

        // 清除之前的验证错误
        setTimeout(() => {
          formRef.value?.restoreValidation();
        }, 100);
      }
    }
  );
</script>

<style lang="less" scoped></style>
