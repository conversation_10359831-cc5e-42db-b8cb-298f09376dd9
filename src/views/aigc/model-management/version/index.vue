<template>
  <div>
    <div class="n-layout-page-header">
      <n-card :bordered="false" title="模型版本管理">
        <template #header-extra>
          <n-button type="primary" @click="handleAdd">
            <template #icon>
              <n-icon>
                <PlusOutlined />
              </n-icon>
            </template>
            新增版本
          </n-button>
        </template>
      </n-card>
    </div>
    <n-card :bordered="false" class="proCard">
      <BasicTable
        :columns="columns"
        :request="loadDataTable"
        :row-key="(row) => row.id"
        ref="actionRef"
        :actionColumn="actionColumn"
        @update:checked-row-keys="onCheckedRow"
        :scroll-x="1200"
      >
        <template #tableTitle>
          <n-button
            type="error"
            @click="handleBatchDelete"
            :disabled="batchDeleteDisabled"
            class="min-left-space"
            v-if="hasPermission(['aigc:model:version:delete'])"
          >
            <template #icon>
              <n-icon>
                <DeleteOutlined />
              </n-icon>
            </template>
            批量删除
          </n-button>
        </template>
      </BasicTable>
    </n-card>

    <Edit
      @reloadTable="reloadTable"
      @updateShowModal="updateShowModal"
      :showModal="showModal"
      :editData="editData"
    />
  </div>
</template>

<script lang="ts" setup>
  import { reactive, ref, computed, h } from 'vue';
  import { useDialog, useMessage } from 'naive-ui';
  import { BasicTable, TableAction } from '@/components/Table';
  import { PlusOutlined, DeleteOutlined, EditOutlined, EyeOutlined } from '@vicons/antd';
  import { usePermission } from '@/hooks/web/usePermission';
  import { page, del } from '@/api/aigc/modelVersion';
  import Edit from './edit.vue';

  const { hasPermission } = usePermission();
  const dialog = useDialog();
  const message = useMessage();

  const actionRef = ref();
  const showModal = ref(false);
  const editData = ref(null);

  const columns = [
    {
      type: 'selection',
      key: 'selection',
    },
    {
      title: '模型名称',
      key: 'modelName',
      width: 200,
    },
    {
      title: '模型编码',
      key: 'modelCode',
      width: 200,
    },
    {
      title: '供应商',
      key: 'provider',
      width: 150,
      render(row) {
        return row.provider?.name || '-';
      },
    },
    {
      title: '分类',
      key: 'category',
      width: 120,
      render(row) {
        return row.category?.name || '-';
      },
    },
    {
      title: '描述',
      key: 'description',
      width: 250,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '排序',
      key: 'sortOrder',
      width: 80,
    },
    {
      title: '状态',
      key: 'status',
      width: 80,
      render(row) {
        return h(
          'n-tag',
          {
            style: {
              marginRight: '6px',
            },
            type: row.status ? 'success' : 'error',
            bordered: false,
          },
          {
            default: () => (row.status ? '启用' : '禁用'),
          }
        );
      },
    },
    {
      title: '创建时间',
      key: 'createTime',
      width: 180,
    },
  ];

  const actionColumn = reactive({
    width: 220,
    title: '操作',
    key: 'action',
    fixed: 'right',
    render(record) {
      return h(TableAction as any, {
        style: 'text',
        actions: [

          {
            label: '编辑',
            type: 'primary',
            icon: EditOutlined,
            onClick: handleEdit.bind(null, record),
          },
          {
            label: '删除',
            type: 'error',
            icon: DeleteOutlined,
            onClick: handleDelete.bind(null, record),
          },
        ],
      });
    },
  });

  const checkedIds = ref([]);
  const batchDeleteDisabled = computed(() => {
    return checkedIds.value.length <= 0;
  });

  const loadDataTable = async (res) => {
    return await page({ ...res });
  };

  const reloadTable = () => {
    actionRef.value.reload();
  };

  function onCheckedRow(rowKeys) {
    checkedIds.value = rowKeys;
  }

  function updateShowModal(value) {
    showModal.value = value;
  }

  function handleAdd() {
    editData.value = null;
    showModal.value = true;
  }

  function handleEdit(record) {
    editData.value = record;
    showModal.value = true;
  }

  function handleDelete(record) {
    dialog.warning({
      title: '警告',
      content: `您确定删除模型版本 "${record.modelName}" 吗？`,
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        await del(record.id);
        message.success('删除成功');
        reloadTable();
      },
    });
  }

  function handleBatchDelete() {
    dialog.warning({
      title: '警告',
      content: `您确定删除选中的 ${checkedIds.value.length} 个模型版本吗？`,
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        for (const id of checkedIds.value) {
          await del(id);
        }
        message.success('批量删除成功');
        checkedIds.value = [];
        reloadTable();
      },
    });
  }
</script>

<style lang="less" scoped></style>
