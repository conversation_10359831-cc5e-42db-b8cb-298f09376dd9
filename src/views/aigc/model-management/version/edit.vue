<template>
  <n-modal
    v-model:show="showModal"
    :show-icon="false"
    preset="dialog"
    :title="editData ? '编辑模型版本' : '新增模型版本'"
    :style="{
      width: dialogWidth,
    }"
  >
    <n-form
      :model="formParams"
      :rules="rules"
      ref="formRef"
      label-placement="left"
      :label-width="120"
      class="py-4"
    >
      <n-grid :cols="2" :x-gap="12">
        <n-form-item-gi label="供应商" path="providerId">
          <n-select
            placeholder="请选择供应商"
            v-model:value="formParams.providerId"
            :options="providerOptions"
            @update:value="onProviderChange"
          />
        </n-form-item-gi>
        <n-form-item-gi label="分类" path="categoryId">
          <n-select
            placeholder="请选择分类"
            v-model:value="formParams.categoryId"
            :options="categoryOptions"
          />
        </n-form-item-gi>
      </n-grid>

      <n-form-item label="模型名称" path="modelName">
        <n-input placeholder="请输入模型名称，如：GPT-3.5 Turbo" v-model:value="formParams.modelName" />
      </n-form-item>

      <n-form-item label="模型编码" path="modelCode">
        <n-input placeholder="请输入模型编码，如：gpt-3.5-turbo" v-model:value="formParams.modelCode" />
      </n-form-item>

      <n-form-item label="描述" path="description">
        <n-input
          type="textarea"
          placeholder="请输入模型描述"
          v-model:value="formParams.description"
          :autosize="{ minRows: 2, maxRows: 4 }"
        />
      </n-form-item>

      <n-grid :cols="2" :x-gap="12">
        <n-form-item-gi label="排序" path="sortOrder">
          <n-input-number
            placeholder="排序"
            v-model:value="formParams.sortOrder"
            :min="0"
            class="w-full"
          />
        </n-form-item-gi>
        <n-form-item-gi label="状态" path="status">
          <n-switch v-model:value="formParams.status">
            <template #checked>启用</template>
            <template #unchecked>禁用</template>
          </n-switch>
        </n-form-item-gi>
      </n-grid>
    </n-form>

    <template #action>
      <n-space>
        <n-button @click="closeModal">取消</n-button>
        <n-button type="primary" :loading="formBtnLoading" @click="confirmForm">确定</n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script lang="ts" setup>
  import { computed, ref, watch, onMounted } from 'vue';
  import { useMessage } from 'naive-ui';
  import { add, update } from '@/api/aigc/modelVersion';
  import { getEnabledProviders } from '@/api/aigc/modelProvider';
  import { getEnabledCategories } from '@/api/aigc/modelCategory';

  const emit = defineEmits(['reloadTable', 'updateShowModal']);

  interface Props {
    showModal: boolean;
    editData?: any;
  }

  const props = withDefaults(defineProps<Props>(), {
    showModal: false,
    editData: null,
  });

  const message = useMessage();
  const formRef = ref();
  const formBtnLoading = ref(false);
  const dialogWidth = ref('600px');

  const providerOptions = ref([]);
  const categoryOptions = ref([]);

  const defaultFormParams = {
    id: '',
    providerId: '',
    categoryId: '',
    modelName: '',
    modelCode: '',
    description: '',
    sortOrder: 0,
    status: true,
  };

  const formParams = ref({ ...defaultFormParams });

  const rules = {
    providerId: {
      required: true,
      message: '请选择供应商',
      trigger: 'change',
    },
    categoryId: {
      required: true,
      message: '请选择分类',
      trigger: 'change',
    },
    modelName: {
      required: true,
      message: '请输入模型名称',
      trigger: 'blur',
    },
    modelCode: {
      required: true,
      message: '请输入模型编码',
      trigger: 'blur',
    },
  };

  const showModal = computed({
    get: () => {
      return props.showModal;
    },
    set: (value) => {
      emit('updateShowModal', value);
    },
  });

  async function loadProviders() {
    try {
      const res = await getEnabledProviders();
      providerOptions.value = res.map(item => ({
        label: item.name,
        value: item.id,
        supportedModelTypes: item.supportedModelTypes || [],
      }));
    } catch (error) {
      // 加载供应商失败
    }
  }

  async function loadCategories() {
    try {
      const res = await getEnabledCategories();
      allCategories.value = res;
      updateCategoryOptions();
    } catch (error) {
      // 加载分类失败
    }
  }

  const allCategories = ref([]);

  function updateCategoryOptions() {
    if (!formParams.value.providerId || allCategories.value.length === 0) {
      categoryOptions.value = allCategories.value.map(item => ({
        label: item.name,
        value: item.id,
      }));
      return;
    }

    // 根据选择的供应商过滤分类
    const selectedProvider = providerOptions.value.find(p => p.value === formParams.value.providerId);
    if (selectedProvider && selectedProvider.supportedModelTypes) {
      const supportedCategories = allCategories.value.filter(category =>
        selectedProvider.supportedModelTypes.includes(category.code)
      );
      categoryOptions.value = supportedCategories.map(item => ({
        label: item.name,
        value: item.id,
      }));
    } else {
      categoryOptions.value = allCategories.value.map(item => ({
        label: item.name,
        value: item.id,
      }));
    }
  }

  function onProviderChange() {
    // 清空分类选择
    formParams.value.categoryId = '';
    // 更新分类选项
    updateCategoryOptions();
  }

  function confirmForm(e) {
    e.preventDefault();
    formRef.value.validate(async (errors) => {
      if (!errors) {
        formBtnLoading.value = true;
        try {
          if (props.editData) {
            await update(formParams.value);
            message.success('编辑成功');
          } else {
            await add(formParams.value);
            message.success('新增成功');
          }
          setTimeout(() => {
            showModal.value = false;
            emit('reloadTable');
          });
        } catch (error) {
          // 操作失败
        } finally {
          formBtnLoading.value = false;
        }
      } else {
        message.error('请填写完整信息');
      }
    });
  }

  function closeModal() {
    showModal.value = false;
  }

  watch(
    () => props.showModal,
    (newValue) => {
      if (newValue) {
        if (props.editData) {
          formParams.value = { ...props.editData };
        } else {
          formParams.value = { ...defaultFormParams };
        }
      }
    }
  );

  onMounted(() => {
    loadProviders();
    loadCategories();
  });
</script>

<style lang="less" scoped></style>
