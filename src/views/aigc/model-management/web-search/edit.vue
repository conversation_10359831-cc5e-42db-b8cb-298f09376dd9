<template>
  <n-modal
    v-model:show="showModal"
    :show-icon="false"
    preset="dialog"
    :title="isReadonly ? '查看搜索引擎' : (editData ? '编辑搜索引擎' : '新增搜索引擎')"
    :style="{
      width: dialogWidth,
    }"
  >
    <n-form
      :model="formParams"
      :rules="rules"
      ref="formRef"
      label-placement="left"
      :label-width="120"
      class="py-4"
    >
      <n-form-item label="搜索引擎名称" path="name">
        <n-input
          placeholder="请输入搜索引擎名称"
          v-model:value="formParams.name"
          :readonly="isReadonly"
        />
      </n-form-item>
      <n-form-item label="提供商" path="provider">
        <n-select
          placeholder="请选择提供商"
          v-model:value="formParams.provider"
          :options="providerOptions"
          :disabled="!!editData || isReadonly"
        />
      </n-form-item>
      <n-form-item label="API密钥" path="apiKey">
        <n-input
          type="password"
          placeholder="请输入API密钥"
          v-model:value="formParams.apiKey"
          show-password-on="click"
          :readonly="isReadonly"
        />
      </n-form-item>
      <n-form-item label="基础URL" path="baseUrl">
        <n-input
          placeholder="请输入基础URL"
          v-model:value="formParams.baseUrl"
          :readonly="isReadonly"
        />
      </n-form-item>
      <n-form-item label="搜索端点" path="searchEndpoint">
        <n-input
          placeholder="请输入搜索端点"
          v-model:value="formParams.searchEndpoint"
          :readonly="isReadonly"
        />
      </n-form-item>
      <n-form-item label="最大结果数" path="maxResults">
        <n-input-number
          placeholder="请输入最大结果数"
          v-model:value="formParams.maxResults"
          :min="1"
          :max="100"
          :readonly="isReadonly"
          class="w-full"
        />
      </n-form-item>
      <n-form-item label="超时时间(秒)" path="timeoutSeconds">
        <n-input-number
          placeholder="请输入超时时间"
          v-model:value="formParams.timeoutSeconds"
          :min="1"
          :max="300"
          :readonly="isReadonly"
          class="w-full"
        />
      </n-form-item>
      <n-form-item label="额外参数" path="additionalParams">
        <n-input
          type="textarea"
          placeholder="请输入额外参数(JSON格式)"
          v-model:value="formParams.additionalParams"
          :rows="3"
          :readonly="isReadonly"
        />
      </n-form-item>
      <n-form-item label="状态" path="status">
        <n-switch
          v-model:value="formParams.status"
          :checked-value="1"
          :unchecked-value="0"
          :disabled="isReadonly"
        >
          <template #checked>启用</template>
          <template #unchecked>禁用</template>
        </n-switch>
      </n-form-item>
    </n-form>

    <template #action>
      <n-space>
        <n-button @click="closeModal">{{ isReadonly ? '关闭' : '取消' }}</n-button>
        <n-button
          v-if="!isReadonly"
          type="primary"
          :loading="formBtnLoading"
          @click="confirmForm"
        >
          确定
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script lang="ts" setup>
  import { computed, ref, watch } from 'vue';
  import { useMessage } from 'naive-ui';
  import { add, update } from '@/api/aigc/searchEngine';

  interface Props {
    showModal: boolean;
    editData?: any;
    isReadonly?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    showModal: false,
    editData: null,
    isReadonly: false,
  });

  const emit = defineEmits(['updateShowModal', 'reloadTable']);

  const message = useMessage();
  const formRef = ref();
  const formBtnLoading = ref(false);
  const dialogWidth = ref('600px');

  const providerOptions = [
    { label: '博查AI', value: 'BOCHAAI' },
    { label: 'Google', value: 'google' },
    { label: 'Bing', value: 'bing' },
    { label: '百度', value: 'baidu' },
  ];

  const defaultFormParams = {
    id: '',
    name: '',
    provider: '',
    apiKey: '',
    baseUrl: '',
    searchEndpoint: '/search',
    maxResults: 10,
    timeoutSeconds: 30,
    additionalParams: '',
    status: 1,
  };

  const formParams = ref({ ...defaultFormParams });

  const rules = {
    name: {
      required: true,
      message: '请输入搜索引擎名称',
      trigger: 'blur',
    },
    provider: {
      required: true,
      message: '请选择提供商',
      trigger: 'change',
    },
    apiKey: {
      required: true,
      message: '请输入API密钥',
      trigger: 'blur',
    },
    baseUrl: {
      required: true,
      message: '请输入基础URL',
      trigger: 'blur',
    },
    maxResults: {
      required: true,
      type: 'number',
      message: '请输入最大结果数',
      trigger: 'blur',
    },
    timeoutSeconds: {
      required: true,
      type: 'number',
      message: '请输入超时时间',
      trigger: 'blur',
    },
  };

  const showModal = computed({
    get: () => {
      return props.showModal;
    },
    set: (value) => {
      emit('updateShowModal', value);
    },
  });

  watch(
    () => props.editData,
    (newVal) => {
      if (newVal) {
        formParams.value = { ...newVal };
      } else {
        formParams.value = { ...defaultFormParams };
      }
    },
    { immediate: true }
  );

  function closeModal() {
    showModal.value = false;
  }

  async function confirmForm() {
    try {
      await formRef.value?.validate();
      formBtnLoading.value = true;
      
      const submitData = {
        ...formParams.value,
      };

      if (!props.editData) {
        delete submitData.id;
        delete submitData.createTime;
        delete submitData.updateTime;
      } else {
        delete submitData.createTime;
        delete submitData.updateTime;
      }

      if (props.editData) {
        await update(submitData);
        message.success('编辑成功');
      } else {
        await add(submitData);
        message.success('新增成功');
      }
      
      setTimeout(() => {
        showModal.value = false;
        emit('reloadTable');
      });
    } catch (error) {
      console.error(error);
      message.error('操作失败，请重试');
    } finally {
      formBtnLoading.value = false;
    }
  }
</script>
