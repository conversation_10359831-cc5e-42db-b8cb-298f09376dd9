<template>
  <n-modal
    v-model:show="showModal"
    :show-icon="false"
    preset="dialog"
    :title="`测试搜索引擎 - ${testData?.name || ''}`"
    :style="{
      width: dialogWidth,
    }"
  >
    <n-form
      :model="formParams"
      :rules="rules"
      ref="formRef"
      label-placement="left"
      :label-width="100"
      class="py-4"
    >
      <n-form-item label="搜索关键词" path="keyword">
        <n-input 
          placeholder="请输入搜索关键词" 
          v-model:value="formParams.keyword"
          @keyup.enter="handleTest"
        />
      </n-form-item>
      <n-form-item label="最大结果数" path="maxResults">
        <n-input-number
          placeholder="请输入最大结果数"
          v-model:value="formParams.maxResults"
          :min="1"
          :max="20"
          class="w-full"
        />
      </n-form-item>
    </n-form>

    <!-- 测试结果展示区域 -->
    <div v-if="testResults.length > 0" class="mt-4">
      <n-divider title-placement="left">
        <span class="text-sm text-gray-500">测试结果 ({{ testResults.length }} 条)</span>
      </n-divider>
      <div class="max-h-96 overflow-y-auto">
        <n-list>
          <n-list-item v-for="(result, index) in testResults" :key="index">
            <n-thing>
              <template #header>
                <a 
                  :href="result.url" 
                  target="_blank" 
                  class="text-blue-600 hover:text-blue-800 text-sm font-medium"
                >
                  {{ result.title || '无标题' }}
                </a>
              </template>
              <template #description>
                <div class="text-xs text-gray-500 mb-1">{{ result.url }}</div>
                <div class="text-sm text-gray-700">{{ result.snippet || '无描述' }}</div>
              </template>
            </n-thing>
          </n-list-item>
        </n-list>
      </div>
    </div>

    <!-- 无结果提示 -->
    <div v-if="hasSearched && testResults.length === 0" class="mt-4">
      <n-empty description="未找到相关结果" />
    </div>

    <!-- 错误信息展示 -->
    <div v-if="errorMessage" class="mt-4">
      <n-alert type="error" :title="errorMessage" />
    </div>

    <template #action>
      <n-space>
        <n-button @click="closeModal">关闭</n-button>
        <n-button 
          type="primary" 
          :loading="testLoading" 
          @click="handleTest"
          :disabled="!formParams.keyword"
        >
          <template #icon>
            <n-icon>
              <ExperimentOutlined />
            </n-icon>
          </template>
          开始测试
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script lang="ts" setup>
  import { computed, ref, watch } from 'vue';
  import { useMessage } from 'naive-ui';
  import { ExperimentOutlined } from '@vicons/antd';
  import { testSearch } from '@/api/aigc/searchEngine';

  interface Props {
    showModal: boolean;
    testData?: any;
  }

  const props = withDefaults(defineProps<Props>(), {
    showModal: false,
    testData: null,
  });

  const emit = defineEmits(['updateShowModal']);

  const message = useMessage();
  const formRef = ref();
  const testLoading = ref(false);
  const dialogWidth = ref('800px');
  const testResults = ref([]);
  const hasSearched = ref(false);
  const errorMessage = ref('');

  const defaultFormParams = {
    keyword: '',
    maxResults: 10,
  };

  const formParams = ref({ ...defaultFormParams });

  const rules = {
    keyword: {
      required: true,
      message: '请输入搜索关键词',
      trigger: 'blur',
    },
    maxResults: {
      required: true,
      type: 'number',
      message: '请输入最大结果数',
      trigger: 'blur',
    },
  };

  const showModal = computed({
    get: () => {
      return props.showModal;
    },
    set: (value) => {
      emit('updateShowModal', value);
    },
  });

  watch(
    () => props.showModal,
    (newVal) => {
      if (newVal) {
        // 重置表单和结果
        formParams.value = { ...defaultFormParams };
        testResults.value = [];
        hasSearched.value = false;
        errorMessage.value = '';
      }
    }
  );

  function closeModal() {
    showModal.value = false;
  }

  async function handleTest() {
    try {
      await formRef.value?.validate();
      
      if (!props.testData?.id) {
        message.error('搜索引擎信息不完整');
        return;
      }

      testLoading.value = true;
      errorMessage.value = '';
      hasSearched.value = true;
      
      const response = await testSearch(
        props.testData.id, 
        formParams.value.keyword, 
        formParams.value.maxResults
      );
      console.log(response)
      testResults.value = response || [];
      
      if (testResults.value.length === 0) {
        message.warning('未找到相关搜索结果');
      } else {
        message.success(`测试成功，找到 ${testResults.value.length} 条结果`);
      }
    } catch (error) {
      console.error('测试搜索引擎失败:', error);
      errorMessage.value = error.response?.data?.message || error.message || '测试失败，请检查搜索引擎配置';
      testResults.value = [];
      message.error('测试失败');
    } finally {
      testLoading.value = false;
    }
  }
</script>

<style scoped>
  .max-h-96 {
    max-height: 24rem;
  }
</style>
