<template>
  <div>
    <div class="n-layout-page-header">
      <n-card :bordered="false" title="搜索引擎管理">
        <template #header-extra>
          <n-button type="primary" @click="handleAdd">
            <template #icon>
              <n-icon>
                <PlusOutlined />
              </n-icon>
            </template>
            新增搜索引擎
          </n-button>
        </template>
      </n-card>
    </div>
    <n-card :bordered="false" class="proCard">
      <BasicTable
        :columns="columns"
        :request="loadDataTable"
        :row-key="(row) => row.id"
        ref="actionRef"
        :actionColumn="actionColumn"
        @update:checked-row-keys="onCheckedRow"
        :scroll-x="1290"
      >
        <template #tableTitle>
          <n-button
            type="error"
            @click="handleBatchDelete"
            :disabled="batchDeleteDisabled"
            class="min-left-space"
            v-if="true"
          >
            <template #icon>
              <n-icon>
                <DeleteOutlined />
              </n-icon>
            </template>
            批量删除
          </n-button>
        </template>
      </BasicTable>
    </n-card>

    <Edit
      :showModal="showModal"
      :editData="editData"
      :isReadonly="isReadonly"
      @updateShowModal="updateShowModal"
      @reloadTable="reloadTable"
    />

    <TestModal
      :showModal="showTestModal"
      :testData="testData"
      @updateShowModal="updateShowTestModal"
    />
  </div>
</template>

<script lang="ts" setup>
  import { reactive, ref, computed, h } from 'vue';
  import { useDialog, useMessage } from 'naive-ui';
  import { BasicTable, TableAction } from '@/components/Table';
  import { PlusOutlined, DeleteOutlined, EditOutlined, EyeOutlined, ExperimentOutlined } from '@vicons/antd';
  import { page, del } from '@/api/aigc/searchEngine';
  import Edit from './edit.vue';
  import TestModal from './test.vue';

  const dialog = useDialog();
  const message = useMessage();

  const actionRef = ref();
  const showModal = ref(false);
  const editData = ref(null);
  const isReadonly = ref(false);
  const showTestModal = ref(false);
  const testData = ref(null);

  const columns = [
    {
      type: 'selection',
      key: 'selection',
    },
    {
      title: '搜索引擎名称',
      key: 'name',
      width: 150,
    },
    {
      title: '提供商',
      key: 'provider',
      width: 120,
      render(row) {
        const providerMap = {
          bochaai: '博查AI',
          google: 'Google',
          bing: 'Bing',
          baidu: '百度',
        };
        return providerMap[row.provider] || row.provider;
      },
    },
    {
      title: '基础URL',
      key: 'baseUrl',
      width: 200,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '最大结果数',
      key: 'maxResults',
      width: 100,
    },
    {
      title: '超时时间(秒)',
      key: 'timeoutSeconds',
      width: 120,
    },
    {
      title: '状态',
      key: 'status',
      width: 80,
      render(row) {
        return h(
          'n-tag',
          {
            type: row.status === 1 ? 'success' : 'error',
            size: 'small',
          },
          row.status === 1 ? '启用' : '禁用'
        );
      },
    },
    {
      title: '创建时间',
      key: 'createTime',
      width: 180,
    },
  ];

  const actionColumn = reactive({
    width: 280,
    title: '操作',
    key: 'action',
    fixed: 'right',
    render(record) {
      return h(TableAction as any, {
        style: 'button',
        actions: [
          {
            label: '查看',
            type: 'info',
            icon: EyeOutlined,
            onClick: handleView.bind(null, record),
          },
          {
            label: '编辑',
            type: 'primary',
            icon: EditOutlined,
            onClick: handleEdit.bind(null, record),
            // auth: ['aigc:search-engine:update'], // 临时注释掉权限验证
          },
          {
            label: '测试',
            type: 'warning',
            icon: ExperimentOutlined,
            onClick: handleTest.bind(null, record),
            // auth: ['aigc:search-engine:test'], // 临时注释掉权限验证
          },
          // {
          //   label: '删除',
          //   type: 'error',
          //   icon: DeleteOutlined,
          //   onClick: handleDelete.bind(null, record),
          //   // auth: ['aigc:search-engine:delete'], // 临时注释掉权限验证
          // },
        ],
      });
    },
  });

  const checkedIds = ref([]);
  const batchDeleteDisabled = computed(() => {
    return checkedIds.value.length <= 0;
  });

  const loadDataTable = async (res) => {
    return await page({ ...res });
  };

  const reloadTable = () => {
    actionRef.value.reload();
  };

  function onCheckedRow(rowKeys) {
    checkedIds.value = rowKeys;
  }

  function updateShowModal(value) {
    showModal.value = value;
  }

  function updateShowTestModal(value) {
    showTestModal.value = value;
  }

  function handleAdd() {
    editData.value = null;
    isReadonly.value = false;
    showModal.value = true;
  }

  function handleView(record) {
    editData.value = record;
    isReadonly.value = true;
    showModal.value = true;
  }

  function handleEdit(record) {
    editData.value = record;
    isReadonly.value = false;
    showModal.value = true;
  }

  function handleTest(record) {
    testData.value = record;
    showTestModal.value = true;
  }

  function handleDelete(record) {
    dialog.warning({
      title: '警告',
      content: `您确定删除搜索引擎 "${record.name}" 吗？`,
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        await del(record.id);
        message.success('删除成功');
        reloadTable();
      },
    });
  }

  function handleBatchDelete() {
    if (checkedIds.value.length === 0) {
      message.warning('请选择要删除的数据');
      return;
    }
    dialog.warning({
      title: '警告',
      content: `您确定删除选中的 ${checkedIds.value.length} 条数据吗？`,
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        try {
          for (const id of checkedIds.value) {
            await del(id);
          }
          message.success('批量删除成功');
          checkedIds.value = [];
          reloadTable();
        } catch (error) {
          message.error('批量删除失败');
        }
      },
    });
  }

  defineExpose({
    reloadTable,
  });
</script>
