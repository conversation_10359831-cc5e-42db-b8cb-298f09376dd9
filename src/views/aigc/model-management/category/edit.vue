<template>
  <n-modal
    v-model:show="showModal"
    :show-icon="false"
    preset="dialog"
    :title="editData ? '编辑分类' : '新增分类'"
    :style="{
      width: dialogWidth,
    }"
  >
    <n-form
      :model="formParams"
      :rules="rules"
      ref="formRef"
      label-placement="left"
      :label-width="80"
      class="py-4"
    >
      <n-form-item label="分类名称" path="name">
        <n-input placeholder="请输入分类名称" v-model:value="formParams.name" />
      </n-form-item>
      <n-form-item label="分类编码" path="code">
        <n-input 
          placeholder="请输入分类编码" 
          v-model:value="formParams.code"
          :disabled="!!editData"
        />
      </n-form-item>
      <n-form-item label="描述" path="description">
        <n-input
          type="textarea"
          placeholder="请输入描述"
          v-model:value="formParams.description"
          :autosize="{ minRows: 3, maxRows: 5 }"
        />
      </n-form-item>
      <n-form-item label="排序" path="sortOrder">
        <n-input-number
          placeholder="请输入排序"
          v-model:value="formParams.sortOrder"
          :min="0"
          class="w-full"
        />
      </n-form-item>
      <n-form-item label="状态" path="status">
        <n-switch v-model:value="formParams.status">
          <template #checked>启用</template>
          <template #unchecked>禁用</template>
        </n-switch>
      </n-form-item>
    </n-form>

    <template #action>
      <n-space>
        <n-button @click="closeModal">取消</n-button>
        <n-button type="primary" :loading="formBtnLoading" @click="confirmForm">确定</n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script lang="ts" setup>
  import { computed, ref, watch } from 'vue';
  import { useMessage } from 'naive-ui';
  import { add, update } from '@/api/aigc/modelCategory';

  const emit = defineEmits(['reloadTable', 'updateShowModal']);

  interface Props {
    showModal: boolean;
    editData?: any;
  }

  const props = withDefaults(defineProps<Props>(), {
    showModal: false,
    editData: null,
  });

  const message = useMessage();
  const formRef = ref();
  const formBtnLoading = ref(false);
  const dialogWidth = ref('500px');

  const defaultFormParams = {
    id: '',
    name: '',
    code: '',
    description: '',
    sortOrder: 0,
    status: true,
  };

  const formParams = ref({ ...defaultFormParams });

  const rules = {
    name: {
      required: true,
      message: '请输入分类名称',
      trigger: 'blur',
    },
    code: {
      required: true,
      message: '请输入分类编码',
      trigger: 'blur',
    },
  };

  const showModal = computed({
    get: () => {
      return props.showModal;
    },
    set: (value) => {
      emit('updateShowModal', value);
    },
  });

  function confirmForm(e) {
    e.preventDefault();
    formRef.value.validate(async (errors) => {
      if (!errors) {
        formBtnLoading.value = true;
        try {
          if (props.editData) {
            await update(formParams.value);
            message.success('编辑成功');
          } else {
            await add(formParams.value);
            message.success('新增成功');
          }
          setTimeout(() => {
            showModal.value = false;
            emit('reloadTable');
          });
        } catch (error) {
          console.error(error);
        } finally {
          formBtnLoading.value = false;
        }
      } else {
        message.error('请填写完整信息');
      }
    });
  }

  function closeModal() {
    showModal.value = false;
  }

  watch(
    () => props.showModal,
    (newValue) => {
      if (newValue) {
        if (props.editData) {
          formParams.value = { ...props.editData };
        } else {
          formParams.value = { ...defaultFormParams };
        }
      }
    }
  );
</script>

<style lang="less" scoped></style>
