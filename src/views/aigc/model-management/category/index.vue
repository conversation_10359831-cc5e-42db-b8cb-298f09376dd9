<template>
  <div>
    <div class="n-layout-page-header">
      <n-card :bordered="false" title="模型分类管理">
        <template #header-extra>
          <n-button type="primary" @click="handleAdd">
            <template #icon>
              <n-icon>
                <PlusOutlined />
              </n-icon>
            </template>
            新增分类
          </n-button>
        </template>
      </n-card>
    </div>
    <n-card :bordered="false" class="proCard">
      <BasicTable
        :columns="columns"
        :request="loadDataTable"
        :row-key="(row) => row.id"
        ref="actionRef"
        :actionColumn="actionColumn"
        @update:checked-row-keys="onCheckedRow"
        :scroll-x="1090"
      >
        <template #tableTitle>
          <n-button
            type="error"
            @click="handleBatchDelete"
            :disabled="batchDeleteDisabled"
            class="min-left-space"
            v-if="hasPermission(['aigc:model:category:delete'])"
          >
            <template #icon>
              <n-icon>
                <DeleteOutlined />
              </n-icon>
            </template>
            批量删除
          </n-button>
        </template>
      </BasicTable>
    </n-card>

    <Edit
      @reloadTable="reloadTable"
      @updateShowModal="updateShowModal"
      :showModal="showModal"
      :editData="editData"
    />
  </div>
</template>

<script lang="ts" setup>
  import { reactive, ref, computed, h } from 'vue';
  import { useDialog, useMessage } from 'naive-ui';
  import { BasicTable, TableAction } from '@/components/Table';
  import { BasicForm, FormSchema } from '@/components/Form/index';
  import { PlusOutlined, DeleteOutlined, EditOutlined, EyeOutlined } from '@vicons/antd';
  import { usePermission } from '@/hooks/web/usePermission';
  import { page, del } from '@/api/aigc/modelCategory';
  import Edit from './edit.vue';

  const { hasPermission } = usePermission();
  const dialog = useDialog();
  const message = useMessage();

  const actionRef = ref();
  const showModal = ref(false);
  const editData = ref(null);

  const searchFormSchema: FormSchema[] = [
    {
      field: 'name',
      label: '分类名称',
      component: 'NInput',
      componentProps: {
        placeholder: '请输入分类名称',
        clearable: true,
      },
    },
    {
      field: 'code',
      label: '分类编码',
      component: 'NInput',
      componentProps: {
        placeholder: '请输入分类编码',
        clearable: true,
      },
    },
    {
      field: 'status',
      label: '状态',
      component: 'NSelect',
      componentProps: {
        placeholder: '请选择状态',
        clearable: true,
        options: [
          { label: '启用', value: true },
          { label: '禁用', value: false },
        ],
      },
    },
  ];

  const columns = [
    {
      type: 'selection',
      key: 'selection',
    },
    {
      title: '分类名称',
      key: 'name',
      width: 150,
    },
    {
      title: '分类编码',
      key: 'code',
      width: 120,
    },
    {
      title: '描述',
      key: 'description',
      width: 200,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '排序',
      key: 'sortOrder',
      width: 80,
    },
    {
      title: '状态',
      key: 'status',
      width: 80,
      render(row) {
        return h(
          'n-tag',
          {
            style: {
              marginRight: '6px',
            },
            type: row.status ? 'success' : 'error',
            bordered: false,
          },
          {
            default: () => (row.status ? '启用' : '禁用'),
          }
        );
      },
    },
    {
      title: '创建时间',
      key: 'createTime',
      width: 180,
    },
  ];

  const actionColumn = reactive({
    width: 220,
    title: '操作',
    key: 'action',
    fixed: 'right',
    render(record) {
      return h(TableAction as any, {
        style: 'text',
        actions: [
          {
            type: 'info',
            icon: EditOutlined,
            onClick: handleEdit.bind(null, record),
            tooltip: '编辑',
          },
          {
            label: '删除',
            type: 'error',
            icon: DeleteOutlined,
            onClick: handleDelete.bind(null, record),
          },
        ],
      });
    },
  });

  const checkedIds = ref([]);
  const batchDeleteDisabled = computed(() => {
    return checkedIds.value.length <= 0;
  });

  const loadDataTable = async (res) => {
    return await page({ ...res });
  };

  const reloadTable = () => {
    actionRef.value.reload();
  };

  function onCheckedRow(rowKeys) {
    checkedIds.value = rowKeys;
  }

  function updateShowModal(value) {
    showModal.value = value;
  }

  function handleAdd() {
    editData.value = null;
    showModal.value = true;
  }

  function handleEdit(record) {
    editData.value = record;
    showModal.value = true;
  }

  function handleDelete(record) {
    dialog.warning({
      title: '警告',
      content: `您确定删除分类 "${record.name}" 吗？`,
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        await del(record.id);
        message.success('删除成功');
        reloadTable();
      },
    });
  }

  function handleBatchDelete() {
    dialog.warning({
      title: '警告',
      content: `您确定删除选中的 ${checkedIds.value.length} 个分类吗？`,
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        for (const id of checkedIds.value) {
          await del(id);
        }
        message.success('批量删除成功');
        checkedIds.value = [];
        reloadTable();
      },
    });
  }
</script>

<style lang="less" scoped></style>
