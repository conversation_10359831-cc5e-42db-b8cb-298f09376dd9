export enum ProviderEnum {
  OPENAI = 'OPENAI',
  AZURE_OPENAI = 'AZURE_OPENAI',
  GEMINI = 'GEMINI',
  OLLAMA = 'OLLAMA',
  CLAUDE = 'CLAUDE',
  Q_FAN = 'Q_FAN',
  Q_WEN = 'Q_WEN',
  ZHIPU = 'ZHIPU',
  GITEEAI = 'GITEEAI',
  DEEPSEEK = 'DEEPSEEK',
  DOUYIN = 'DOUYIN',
  SILICON = 'SILICON',
  YI = 'YI',
  SPARK = 'SPARK',
}

export function getModels(provider: string, providers: Array<any>) {
  // 确保providers是数组
  if (!Array.isArray(providers)) {
    console.warn('getModels: providers is not an array:', providers);
    return [];
  }

  // 确保provider不为空
  if (!provider) {
    console.warn('getModels: provider is empty');
    return [];
  }

  const arr = providers.filter((i) => i && i.model === provider);
  if (arr.length === 0) {
    return [];
  }

  const providerData = arr[0];
  if (!providerData.models || !Array.isArray(providerData.models)) {
    console.warn('getModels: models is not an array for provider:', provider, providerData);
    return [];
  }

  if (providerData.models.length === 0) {
    return [];
  }

  if (typeof providerData.models[0] === 'string') {
    return providerData.models.map((i) => {
      return {
        label: i,
        value: i,
      };
    });
  } else {
    return providerData.models;
  }
}

export function getTitle(provider: string, providers: Array<any>) {
  // 确保providers是数组
  if (!Array.isArray(providers)) {
    console.warn('getTitle: providers is not an array:', providers);
    return '';
  }

  // 确保provider不为空
  if (!provider) {
    console.warn('getTitle: provider is empty');
    return '';
  }

  const arr = providers.filter((i) => i && i.model === provider);
  if (arr.length === 0) {
    return '';
  }

  return arr[0].name || '';
}
