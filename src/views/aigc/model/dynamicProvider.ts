/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { getAvailableProvidersAndVersions } from '@/api/aigc/model';
import { ModelTypeEnum } from '@/api/models';

// 缓存动态加载的模型数据
const modelCache = new Map<string, any[]>();

/**
 * 动态获取模型供应商和版本数据
 * @param modelType 模型类型
 * @returns Promise<any[]>
 */
export async function getDynamicProviders(modelType: string): Promise<any[]> {
  // 检查缓存
  if (modelCache.has(modelType)) {
    return modelCache.get(modelType) || [];
  }

  try {
    const response = await getAvailableProvidersAndVersions(modelType);
    const providers = response || [];
    // 缓存数据
    modelCache.set(modelType, providers);

    return providers;
  } catch (error) {
    return [];
  }
}

/**
 * 清除缓存
 * @param modelType 可选，指定清除某个类型的缓存，不传则清除所有
 */
export function clearModelCache(modelType?: string) {
  if (modelType) {
    modelCache.delete(modelType);
  } else {
    modelCache.clear();
  }
}

/**
 * 获取Chat模型供应商
 */
export async function getChatProviders() {
  return getDynamicProviders(ModelTypeEnum.CHAT);
}

/**
 * 获取Embedding模型供应商
 */
export async function getEmbeddingProviders() {
  return getDynamicProviders(ModelTypeEnum.EMBEDDING);
}

/**
 * 获取Image模型供应商
 */
export async function getImageProviders() {
  return getDynamicProviders(ModelTypeEnum.TEXT_IMAGE);
}

/**
 * 获取搜索引擎模型供应商
 */
export async function getSearchProviders() {
  return getDynamicProviders(ModelTypeEnum.WEB_SEARCH);
}

/**
 * 根据供应商代码和模型列表获取模型选项
 * @param provider 供应商代码
 * @param providers 供应商列表
 * @returns 模型选项数组
 */
export function getModels(provider: string, providers: Array<any>) {
  const arr = providers.filter((i) => i.model === provider);
  if (arr.length === 0) {
    return [];
  }
  if (typeof arr[0].models[0] === 'string') {
    return arr[0].models.map((i: string) => {
      return {
        label: i,
        value: i,
      };
    });
  } else {
    return arr[0].models;
  }
}

/**
 * 根据供应商代码获取供应商名称
 * @param provider 供应商代码
 * @param providers 供应商列表
 * @returns 供应商名称
 */
export function getTitle(provider: string, providers: Array<any>) {
  const found = providers.filter((i) => i.model === provider);
  return found.length > 0 ? found[0].name : '';
}
