/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { getChatProviders } from '@/views/aigc/model/dynamicProvider';
import { ref } from 'vue';

// 响应式的LLM供应商数据
export const LLMProviders = ref<any[]>([]);

// 加载状态
export const loading = ref(false);

// 数据源状态：'dynamic' | 'static' | 'unknown'
export const dataSource = ref<string>('unknown');

/**
 * 初始化LLM供应商数据
 */
export async function initLLMProviders() {
  if (loading.value) return;

  loading.value = true;
  try {
    const providers = await getChatProviders();
    LLMProviders.value = providers;
    dataSource.value = providers.length > 0 ? 'dynamic' : 'empty';
  } catch (error) {
    LLMProviders.value = [];
    dataSource.value = 'error';
  } finally {
    loading.value = false;
  }
}

/**
 * 刷新LLM供应商数据
 */
export async function refreshLLMProviders() {
  const { clearModelCache } = await import('@/views/aigc/model/dynamicProvider');
  clearModelCache('CHAT');
  await initLLMProviders();
}
