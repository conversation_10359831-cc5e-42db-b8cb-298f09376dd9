<script lang="ts" setup>
import {BasicTable, TableAction} from '@/components/Table';
import {DeleteOutlined, EditOutlined, ExperimentOutlined, PlusOutlined, ReloadOutlined} from '@vicons/antd';
import Edit from './edit.vue';
import TestModal from './TestModal.vue';
import {computed, h, nextTick, onMounted, reactive, ref} from 'vue';
import {getColumns} from './columns';
import {SearchProviders, setSearchProviders} from './consts';
import {del, page as getSearchEngines} from '@/api/aigc/searchEngine';
import {getProvidersByModelType} from '@/api/aigc/modelProvider';
import {useDialog, useMessage} from 'naive-ui';

const provider = ref('');
const message = useMessage();
const dialog = useDialog();
const actionRef = ref();
const editRef = ref();
const loading = ref(false);
const showTestModal = ref(false);
const testData = ref(null);
const providersLoading = ref(false);

const actionColumn = reactive({
  width: 120,
  title: '操作',
  key: 'action',
  fixed: 'right',
  align: 'center',
  render(record: any) {
    return h(TableAction as any, {
      style: 'text',
      actions: [
        {
          type: 'info',
          icon: EditOutlined,
          onClick: handleEdit.bind(null, record),
        },
        {
          type: 'warning',
          icon: ExperimentOutlined,
          onClick: handleTest.bind(null, record),
        },
        {
          type: 'error',
          icon: DeleteOutlined,
          onClick: handleDel.bind(null, record),
        },
      ],
    });
  },
});

const columns = computed(() => {
  nextTick();
  return getColumns(provider.value);
});

// 加载搜索引擎供应商
async function loadSearchProviders() {
  try {
    providersLoading.value = true;
    const response = await getProvidersByModelType('WEB_SEARCH');
    
    if (response && response.length > 0) {
      setSearchProviders(response);
      // 设置默认选中第一个供应商
      if (!provider.value && SearchProviders.length > 0) {
        provider.value = SearchProviders[0].model;
        reloadTable();
      }
    } else {
      message.warning('未找到支持搜索引擎的供应商');
    }
  } catch (error) {
    message.error('加载搜索引擎供应商失败: ' + (error.message || '未知错误'));
  } finally {
    providersLoading.value = false;
  }
}

const loadDataTable = async (params: any) => {
  if (provider.value === '') {
    if (SearchProviders.length > 0) {
      provider.value = SearchProviders[0].model;
    } else {
      return {records: [], total: 0};
    }
  }
  try {
    return await getSearchEngines({...params, provider: provider.value});
  } catch (error) {
    message.error('加载搜索引擎数据失败: ' + (error.message || '未知错误'));
    return {records: [], total: 0};
  }
};

async function handleAdd() {
  editRef.value.show({provider: provider.value});
}

function handleEdit(record: any) {
  editRef.value.show(record);
}

function reloadTable() {
  actionRef.value.reload();
}

function handleDel(record: any) {
  dialog.warning({
    title: '警告',
    content: `你确定删除 [${record.name}] 搜索引擎吗？删除之后不可再使用该搜索引擎`,
    positiveText: '确定',
    negativeText: '不确定',
    onPositiveClick: async () => {
      await del(record.id);
      reloadTable();
      message.success('搜索引擎删除成功');
    },
  });
}

function handleTest(record: any) {
  testData.value = record;
  showTestModal.value = true;
}

// 组件挂载时加载供应商
onMounted(async () => {
  await loadSearchProviders();
});

async function handleRefresh() {
  await loadSearchProviders();
  reloadTable();
  message.success('模型供应商数据已刷新');
}
</script>

<template>
  <div class="flex gap-2">
    <div class="w-52 flex flex-col gap-2 py-1">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-2">
          <div class="font-bold text-base">搜索引擎列表</div>
        </div>
        <n-button
            size="small"
            type="primary"
            ghost
            :loading="loading"
            @click="handleRefresh"
        >
          <template #icon>
            <n-icon>
              <ReloadOutlined/>
            </n-icon>
          </template>
        </n-button>
      </div>
      <n-spin :show="loading">
        <n-menu
            v-model:value="provider"
            :key-field="'model'"
            :label-field="'name'"
            :options="SearchProviders"
            class="model-menu"
            @update:value="reloadTable"
        />
      </n-spin>
    </div>
    
    <div class="w-full">
      <n-alert
          class="w-full mb-4 mt-2 min-alert"
          title="配置不同的搜索引擎供应商，为AI应用提供网络搜索能力"
          type="info"
      />
      <BasicTable
          ref="actionRef"
          :actionColumn="actionColumn"
          :columns="columns"
          :pagination="true"
          :request="loadDataTable"
          :row-key="(row:any) => row.id"
          :single-line="false"
          default-expand-all
      >
        <template #tableTitle>
          <n-button type="primary" @click="handleAdd">
            <template #icon>
              <n-icon>
                <PlusOutlined/>
              </n-icon>
            </template>
            新增搜索引擎
          </n-button>
        </template>
      </BasicTable>
      <Edit ref="editRef" :provider="provider" @reload="reloadTable"/>
      <TestModal v-model:showModal="showTestModal" :testData="testData"/>
    </div>
  </div>
</template>

<style lang="less" scoped></style>
