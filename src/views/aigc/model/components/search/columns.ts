// 列定义不再依赖硬编码的枚举，支持动态供应商

export const baseColumns = [
  {
    title: '搜索引擎名称',
    key: 'name',
  },
  {
    title: 'Base URL',
    key: 'baseUrl',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '最大结果数',
    key: 'maxResults',
  },
  {
    title: '超时时间(秒)',
    key: 'timeoutSeconds',
  },
  {
    title: '状态',
    key: 'status',
    render(row: any) {
      return row.status ? '启用' : '禁用';
    },
  },
];

export const bochaiColumns = [
  ...baseColumns,
  {
    title: 'API Key',
    key: 'apiKey',
  },
];

export function getColumns(provider: string) {
  switch (provider?.toLowerCase()) {
    case 'bochaai': {
      return bochaiColumns;
    }
    default: {
      return baseColumns;
    }
  }
}
