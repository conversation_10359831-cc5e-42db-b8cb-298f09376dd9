<!--
  - Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
  -
  - Licensed under the GNU Affero General Public License, Version 3 (the "License");
  - you may not use this file except in compliance with the License.
  - You may obtain a copy of the License at
  -
  -     https://www.gnu.org/licenses/agpl-3.0.html
  -
  - Unless required by applicable law or agreed to in writing, software
  - distributed under the License is distributed on an "AS IS" BASIS,
  - WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  - See the License for the specific language governing permissions and
  - limitations under the License.
  -->

<script lang="ts" setup>
  import { computed, nextTick, ref } from 'vue';
  import { BasicForm, useForm } from '@/components/Form';
  import { getSearchSchemas } from './searchSchemas';
  import { isNullOrWhitespace } from '@/utils/is';
  import { add, update } from '@/api/aigc/searchEngine';
  import { useMessage } from 'naive-ui';
  import { getSearchProviderTitle } from './consts';

  const props = defineProps<{
    provider: string;
  }>();
  const emit = defineEmits(['reload']);
  const isShow = ref(false);
  const info = ref();
  const message = useMessage();
  const title = computed(() => {
    return info.value == undefined || info.value.provider == undefined
      ? '新增搜索引擎'
      : getSearchProviderTitle(info.value.provider);
  });
  const form: any = {
    maxResults: 10,
    timeoutSeconds: 30,
    status: 1,
  };

  const schemas = computed(() => {
    nextTick();
    return getSearchSchemas(props.provider);
  });

  async function show(record?: any) {
    isShow.value = true;
    await nextTick();
    info.value = record;
    const formData = { ...form, ...record };
    if (record && !record.provider) {
      formData.provider = props.provider;
    }
    // 确保数字字段有正确的默认值
    if (!formData.maxResults) {
      formData.maxResults = 10;
    }
    if (!formData.timeoutSeconds) {
      formData.timeoutSeconds = 30;
    }
    if (formData.status === undefined || formData.status === null) {
      formData.status = 1;
    }
    setFieldsValue(formData);
  }

  const [register, { setFieldsValue }] = useForm({
    labelWidth: 120,
    gridProps: { cols: 1 },
    layout: 'horizontal',
    submitButtonText: '提交',
  });

  async function onSubmit(values: any) {
    if (values !== false) {
      const data = { ...values };

      // 确保数字字段是数字类型
      if (data.maxResults) {
        data.maxResults = Number(data.maxResults);
      }
      if (data.timeoutSeconds) {
        data.timeoutSeconds = Number(data.timeoutSeconds);
      }

      // 处理额外参数
      if (data.additionalParams) {
        try {
          JSON.parse(data.additionalParams);
        } catch (e) {
          message.error('额外参数格式错误，请输入有效的JSON格式');
          return;
        }
      }

      try {
        if (isNullOrWhitespace(data.id)) {
          await add(data);
          message.success('新增成功');
        } else {
          await update(data);
          message.success('修改成功');
        }
        isShow.value = false;
        emit('reload');
      } catch (error: any) {
        message.error('操作失败：' + (error.message || '未知错误'));
      }
    } else {
      message.error('请完善表单');
    }
  }

  defineExpose({ show });
</script>

<template>
  <n-drawer v-model:show="isShow" placement="right" width="40%">
    <n-drawer-content :title="title" closable>
      <BasicForm :schemas="schemas" class="mt-5" @register="register" @submit="onSubmit" />
    </n-drawer-content>
  </n-drawer>
</template>

<style lang="less" scoped></style>
