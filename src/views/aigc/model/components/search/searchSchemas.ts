import { FormSchema } from '@/components/Form';
// 表单配置不再依赖硬编码的枚举，支持动态供应商
const baseHeadSchemas: FormSchema[] = [
  {
    field: 'id',
    label: 'ID',
    component: 'NInput',
    isHidden: true,
  },
  {
    field: 'provider',
    label: '搜索引擎供应商',
    component: 'NInput',
    componentProps: {
      disabled: true,
    },
    rules: [{ required: true, message: '请选择搜索引擎供应商', trigger: ['blur'] }],
  },
  {
    field: 'name',
    label: '搜索引擎名称',
    component: 'NInput',
    componentProps: {
      placeholder: '请输入搜索引擎名称',
    },
    rules: [{ required: true, message: '请输入搜索引擎名称', trigger: ['blur'] }],
  },
  {
    field: 'baseUrl',
    label: 'Base URL',
    component: 'NInput',
    componentProps: {
      placeholder: '请输入API基础URL',
    },
    rules: [{ required: true, message: '请输入API基础URL', trigger: ['blur'] }],
  },
  {
    field: 'maxResults',
    label: '最大结果数',
    component: 'NInputNumber',
    componentProps: {
      placeholder: '请输入最大结果数',
      min: 1,
      max: 100,
    },
    defaultValue: 10,
    rules: [{
      required: true,
      type: 'number',
      min: 1,
      max: 100,
      message: '请输入最大结果数(1-100)',
      trigger: ['blur', 'change']
    }],
  },
  {
    field: 'timeoutSeconds',
    label: '超时时间(秒)',
    component: 'NInputNumber',
    componentProps: {
      placeholder: '请输入超时时间',
      min: 1,
      max: 300,
    },
    defaultValue: 30,
    rules: [{
      required: true,
      type: 'number',
      min: 1,
      max: 300,
      message: '请输入超时时间(1-300秒)',
      trigger: ['blur', 'change']
    }],
  },
  {
    field: 'status',
    label: '状态',
    component: 'NSwitch',
    componentProps: {
      checkedValue: 1,
      uncheckedValue: 0,
    },
    defaultValue: 1,
  },
];

const keySchemas: FormSchema[] = [
  {
    field: 'apiKey',
    label: 'API Key',
    component: 'NInput',
    componentProps: {
      placeholder: '请输入API Key',
      type: 'password',
      showPasswordOn: 'click',
    },
    rules: [{ required: true, message: '请输入API Key', trigger: ['blur'] }],
  },
];

export const bochaiSchemas: FormSchema[] = [
  ...baseHeadSchemas,
  ...keySchemas,
  {
    field: 'additionalParams',
    label: '额外参数',
    component: 'NInput',
    componentProps: {
      type: 'textarea',
      placeholder: '请输入额外参数(JSON格式)，如：{"freshness": "noLimit"}',
      rows: 3,
    },
  },
];


export function getSearchSchemas(provider: string) {
  switch (provider?.toLowerCase()) {
    case 'BOCHAAI': {
      return bochaiSchemas;
    }
    default: {
      return bochaiSchemas;
    }
  }
}
