/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// 搜索引擎供应商将从后台动态加载
export let SearchProviders: any[] = [];

// 设置搜索引擎供应商列表
export function setSearchProviders(providers: any[]) {
  SearchProviders = [];
  SearchProviders.push(...providers.map(provider => ({
    model: provider.code,
    name: provider.name,
  })));
}

export function getSearchModels(provider: string) {
  const providerData = SearchProviders.find((p) => p.model === provider);
  return providerData ? providerData.models : [];
}

export function getSearchProviderTitle(provider: string) {
  const providerData = SearchProviders.find((p) => p.model === provider);
  return providerData ? providerData.name : provider;
}
