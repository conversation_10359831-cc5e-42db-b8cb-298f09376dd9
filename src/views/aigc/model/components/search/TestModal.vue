<!--
  Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.

  Licensed under the GNU Affero General Public License, Version 3 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      https://www.gnu.org/licenses/agpl-3.0.html

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->

<template>
  <n-modal
    v-model:show="showModal"
    :show-icon="false"
    preset="dialog"
    :title="`测试搜索引擎 - ${testData?.name || ''}`"
    style="width: 600px;"
  >
    <n-form
      :model="formParams"
      :rules="rules"
      ref="formRef"
      label-placement="left"
      :label-width="100"
      class="py-4"
    >
      <n-form-item label="搜索关键词" path="keyword">
        <n-input 
          placeholder="请输入搜索关键词，如：人工智能" 
          v-model:value="formParams.keyword"
          @keyup.enter="handleTest"
        />
      </n-form-item>
      <n-form-item label="最大结果数" path="maxResults">
        <n-input-number
          placeholder="请输入最大结果数"
          v-model:value="formParams.maxResults"
          :min="1"
          :max="20"
          class="w-full"
        />
      </n-form-item>
    </n-form>

    <!-- 测试结果展示区域 -->
    <div v-if="testResults.length > 0" class="mt-4">
      <n-divider title-placement="left">
        <span class="text-sm text-gray-500">测试结果 ({{ testResults.length }} 条)</span>
      </n-divider>
      <div class="max-h-96 overflow-y-auto">
        <div v-for="(result, index) in testResults" :key="index" class="mb-3 p-3 border border-gray-200 rounded-lg bg-gray-50">
          <div class="mb-2">
            <a 
              :href="result.url" 
              target="_blank" 
              class="text-blue-600 hover:text-blue-800 text-sm font-medium cursor-pointer"
            >
              {{ result.title || result.name || '无标题' }}
            </a>
          </div>
          <div class="text-green-600 text-xs mb-2">{{ result.url || '' }}</div>
          <div class="text-gray-600 text-sm line-clamp-3">
            {{ result.content || result.snippet || '无描述' }}
          </div>
        </div>
      </div>
    </div>

    <!-- 错误信息展示 -->
    <div v-if="errorMessage && hasSearched" class="mt-4">
      <n-alert type="error" :title="errorMessage" />
    </div>

    <!-- 无结果提示 -->
    <div v-if="testResults.length === 0 && hasSearched && !testLoading && !errorMessage" class="mt-4">
      <n-alert type="warning" title="未找到相关搜索结果" />
    </div>

    <template #action>
      <n-space>
        <n-button @click="closeModal">关闭</n-button>
        <n-button 
          type="primary" 
          :loading="testLoading" 
          @click="handleTest"
          :disabled="!formParams.keyword"
        >
          <template #icon>
            <n-icon>
              <ExperimentOutlined />
            </n-icon>
          </template>
          开始测试
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script lang="ts" setup>
  import { computed, ref, watch } from 'vue';
  import { useMessage } from 'naive-ui';
  import { ExperimentOutlined } from '@vicons/antd';
  import { testSearch } from '@/api/aigc/searchEngine';

  interface Props {
    showModal: boolean;
    testData?: any;
  }

  const props = withDefaults(defineProps<Props>(), {
    showModal: false,
    testData: null,
  });

  const emit = defineEmits(['update:showModal']);

  const message = useMessage();
  const formRef = ref();
  const testLoading = ref(false);
  const testResults = ref<any[]>([]);
  const errorMessage = ref('');
  const hasSearched = ref(false);

  const showModal = computed({
    get: () => props.showModal,
    set: (value) => emit('update:showModal', value),
  });

  const formParams = ref({
    keyword: '',
    maxResults: 5,
  });

  const rules = {
    keyword: [
      {
        required: true,
        message: '请输入搜索关键词',
        trigger: ['input', 'blur'],
      },
    ],
    maxResults: [
      {
        required: true,
        type: 'number',
        min: 1,
        max: 20,
        message: '最大结果数必须在1-20之间',
        trigger: ['input', 'blur'],
      },
    ],
  };

  // 监听模态框显示状态，重置表单
  watch(showModal, (newVal) => {
    if (newVal) {
      resetForm();
    }
  });

  function resetForm() {
    formParams.value = {
      keyword: '',
      maxResults: 5,
    };
    testResults.value = [];
    errorMessage.value = '';
    hasSearched.value = false;
    testLoading.value = false;
  }

  function closeModal() {
    showModal.value = false;
  }

  async function handleTest() {
    try {
      await formRef.value?.validate();
      
      if (!props.testData?.id) {
        message.error('搜索引擎信息不完整');
        return;
      }

      testLoading.value = true;
      errorMessage.value = '';
      hasSearched.value = true;
      
      const response = await testSearch(
        props.testData.id, 
        formParams.value.keyword, 
        formParams.value.maxResults
      );
      
      testResults.value = response || [];
      
      if (testResults.value.length === 0) {
        message.warning('未找到相关搜索结果');
      } else {
        message.success(`测试成功！返回 ${testResults.value.length} 条搜索结果`);
      }
      
    } catch (error: any) {
      console.error('搜索测试失败:', error);
      errorMessage.value = error.message || '搜索测试失败，请检查搜索引擎配置';
      testResults.value = [];
      message.error(`测试失败：${error.message || '未知错误'}`);
    } finally {
      testLoading.value = false;
    }
  }
</script>

<style scoped>
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
