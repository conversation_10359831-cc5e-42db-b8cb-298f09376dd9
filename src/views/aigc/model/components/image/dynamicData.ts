/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { getImageProviders } from '@/views/aigc/model/dynamicProvider';
import { ref } from 'vue';

// 响应式的Image供应商数据
export const LLMProviders = ref<any[]>([]);

// 加载状态
export const loading = ref(false);

/**
 * 初始化Image供应商数据
 */
export async function initImageProviders() {
  if (loading.value) return;
  
  loading.value = true;
  try {
    const providers = await getImageProviders();
    LLMProviders.value = providers;
  } catch (error) {
    console.error('Failed to load Image providers:', error);
    // 如果动态加载失败，使用静态配置作为后备
    const { LLMProviders: staticProviders } = await import('./data');
    LLMProviders.value = staticProviders;
  } finally {
    loading.value = false;
  }
}

/**
 * 刷新Image供应商数据
 */
export async function refreshImageProviders() {
  // 清除缓存并重新加载
  const { clearModelCache } = await import('@/views/aigc/model/dynamicProvider');
  clearModelCache('TEXT_IMAGE');
  await initImageProviders();
}

// 保持原有的枚举，用于兼容性
export enum ProviderEnum {
  OPENAI = 'OPENAI',
  AZURE_OPENAI = 'AZURE_OPENAI',
  ZHIPU = 'ZHIPU',
}
