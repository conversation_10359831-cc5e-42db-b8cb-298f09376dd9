<!--
  - Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
  -
  - Licensed under the GNU Affero General Public License, Version 3 (the "License");
  - you may not use this file except in compliance with the License.
  - You may obtain a copy of the License at
  -
  -     https://www.gnu.org/licenses/agpl-3.0.html
  -
  - Unless required by applicable law or agreed to in writing, software
  - distributed under the License is distributed on an "AS IS" BASIS,
  - WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  - See the License for the specific language governing permissions and
  - limitations under the License.
  -->

<script lang="ts" setup>
  import AiReqChart from './AiReqChart.vue';
  import AiTokenChart from './AiTokenChart.vue';
</script>

<template>
  <div class="mt-4 w-full">
    <n-card :bordered="false" content-style="padding: 0;">
      <n-tabs :tabs-padding="20" pane-style="padding: 10px" size="large" type="line">
        <n-tab-pane name="Token消耗量统计">
          <AiTokenChart />
        </n-tab-pane>
        <n-tab-pane name="AI请求量统计">
          <AiReqChart />
        </n-tab-pane>
      </n-tabs>
    </n-card>
  </div>
</template>
