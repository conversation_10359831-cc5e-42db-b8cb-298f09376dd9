/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { defineStore } from 'pinia';
import { update } from '@/api/aigc/app';

export interface AppState {
  activeMenu: string;
  info: any;
  modelId: string | null;
  knowledgeIds: any[];
  knowledges: any[];
  mcpServiceIds: string[];
  mcpServices: any[];
  mcpServiceParams: Record<string, any>;
}

export const useAppStore = defineStore('app-store', {
  state: (): AppState =>
    <AppState>{
      activeMenu: 'setting',
      info: {},
      modelId: '',
      knowledgeIds: [],
      knowledges: [],
      mcpServiceIds: [],
      mcpServices: [],
      mcpServiceParams: {},
    },

  getters: {},

  actions: {
    setActiveMenu(active: string) {
      this.activeMenu = active;
    },
    addKnowledge(item: any) {
      this.knowledgeIds.push(item.id);
      this.knowledges.push(item);
      this.updateInfo();
    },

    removeKnowledge(item: any) {
      this.knowledgeIds = this.knowledgeIds.filter((i) => i !== item.id);
      this.knowledges = this.knowledges.filter((i) => i.id !== item.id);
      this.updateInfo();
    },

    addMcpService(item: any) {
      if (!this.mcpServiceIds.includes(item.id)) {
        this.mcpServiceIds.push(item.id);
        this.mcpServices.push(item);
        this.updateInfo();
      }
    },

    removeMcpService(serviceId: string) {
      this.mcpServiceIds = this.mcpServiceIds.filter((id) => id !== serviceId);
      this.mcpServices = this.mcpServices.filter((service) => service.id !== serviceId);
      // 清理对应的服务参数配置
      if (this.mcpServiceParams[serviceId]) {
        delete this.mcpServiceParams[serviceId];
      }
      this.updateInfo();
    },

    updateMcpServices(serviceIds: string[]) {
      this.mcpServiceIds = [...serviceIds];
      this.updateInfo();
    },

    updateMcpServiceParams(serviceId: string, params: any) {
      this.mcpServiceParams[serviceId] = params;
      this.updateInfo();
    },

    async updateInfo() {
      this.info.modelId = this.modelId;
      this.info.knowledgeIds = this.knowledgeIds;
      this.info.knowledges = this.knowledges;
      this.info.mcpServiceIds = this.mcpServiceIds;
      this.info.mcpServices = this.mcpServices;
      this.info.mcpServiceParams = this.mcpServiceParams;
      await update({ ...this.info });
    },

    async setInfo(data: any) {
      this.info = data;
      this.modelId = data.modelId || '';
      this.knowledgeIds = data.knowledgeIds || [];
      this.knowledges = data.knowledges || [];
      this.mcpServiceIds = data.mcpServiceIds || [];
      this.mcpServices = data.mcpServices || [];
      this.mcpServiceParams = data.mcpServiceParams || {};
    },
  },
});
