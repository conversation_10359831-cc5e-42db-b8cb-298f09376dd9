<!--
  - Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
  -
  - Licensed under the GNU Affero General Public License, Version 3 (the "License");
  - you may not use this file except in compliance with the License.
  - You may obtain a copy of the License at
  -
  -     https://www.gnu.org/licenses/agpl-3.0.html
  -
  - Unless required by applicable law or agreed to in writing, software
  - distributed under the License is distributed on an "AS IS" BASIS,
  - WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  - See the License for the specific language governing permissions and
  - limitations under the License.
  -->

<script lang="ts" setup>
  import { ref, onMounted, computed } from 'vue';
  import { useMessage } from 'naive-ui';
  import SvgIcon from '@/components/SvgIcon/index.vue';
  import { getEnabledServices } from '@/api/aigc/mcp';
  import { useAppStore } from '@/views/app/store';
  import SimpleParameterForm from '@/components/mcp/SimpleParameterForm.vue';

  const emit = defineEmits(['update']);
  const appStore = useAppStore();
  const ms = useMessage();

  const showModal = ref(false);
  const showParamsModal = ref(false);
  const loading = ref(false);
  const mcpServices = ref<any[]>([]);
  const selectedServices = ref<string[]>([]);
  const currentConfigService = ref<any>(null);
  const currentServiceParams = ref<any>({});

  // 计算已选择的服务详情
  const selectedServiceDetails = computed(() => {
    return mcpServices.value.filter((service) => appStore.mcpServiceIds?.includes(service.id));
  });

  onMounted(() => {
    fetchMcpServices();
  });

  async function fetchMcpServices() {
    try {
      loading.value = true;
      const data = await getEnabledServices();
      mcpServices.value = data || [];
      // 初始化已选择的服务
      selectedServices.value = [...(appStore.mcpServiceIds || [])];
    } catch (error) {
      ms.error('获取MCP服务列表失败');
      console.error('获取MCP服务列表失败:', error);
    } finally {
      loading.value = false;
    }
  }

  function show() {
    selectedServices.value = [...(appStore.mcpServiceIds || [])];
    showModal.value = true;
  }

  function handleConfirm() {
    // 更新app store中的MCP服务配置
    appStore.mcpServiceIds = [...selectedServices.value];
    appStore.mcpServices = mcpServices.value.filter((service) =>
      selectedServices.value.includes(service.id)
    );

    // 同步更新到应用信息并保存
    appStore.updateInfo();

    emit('update');
    showModal.value = false;

    const selectedCount = selectedServices.value.length;
    if (selectedCount > 0) {
      ms.success(
        `MCP服务配置已更新，已选择 ${selectedCount} 个服务。聊天时将使用MCP智能编排执行接口。`
      );
    } else {
      ms.success('MCP服务配置已更新，将使用普通聊天接口。');
    }
  }

  function handleCancel() {
    selectedServices.value = [...(appStore.mcpServiceIds || [])];
    showModal.value = false;
  }

  // 配置服务参数
  function configureServiceParams(service: any) {
    currentConfigService.value = service;

    // 获取当前服务的参数配置
    currentServiceParams.value = appStore.mcpServiceParams[service.id] || {};

    showParamsModal.value = true;
  }

  // 保存服务参数
  function saveServiceParams() {
    if (currentConfigService.value) {
      appStore.updateMcpServiceParams(currentConfigService.value.id, currentServiceParams.value);
      ms.success(
        `${
          currentConfigService.value.displayName || currentConfigService.value.name
        } 参数配置已保存`
      );
    }
    showParamsModal.value = false;
  }

  // 取消参数配置
  function cancelServiceParams() {
    currentServiceParams.value = {};
    showParamsModal.value = false;
  }

  // 获取服务的参数定义
  function getServiceParameters(service: any) {

    if (!service.parameters) return [];
    try {

      return JSON.parse(service.parameters);
    } catch (error) {
      console.error('解析服务参数定义失败:', error);
      return [];
    }
  }

  // 检查服务是否有参数定义
  function hasParameters(service: any) {
    const params = getServiceParameters(service);
    return params.length > 0;
  }

  // 检查服务参数是否已配置
  function isParametersConfigured(service: any) {
    if (!hasParameters(service)) return true;

    const params = getServiceParameters(service);
    const configuredParams = appStore.mcpServiceParams[service.id] || {};

    // 检查必填参数是否都已配置
    return params.every((param) => {
      if (param.required) {
        const value = configuredParams[param.name];
        return value !== undefined && value !== null && value !== '';
      }
      return true;
    });
  }

  function getServiceDisplayName(serviceName: string) {
    const service = mcpServices.value.find((s) => s.name === serviceName);
    return service?.displayName || serviceName;
  }

  function removeService(serviceId: string) {
    appStore.removeMcpService(serviceId);
    emit('update');
    ms.success('MCP服务已移除');
  }

  function getServiceIcon(service: any) {
    // 根据服务类型返回不同的图标
    switch (service.category?.toLowerCase()) {
      case 'image':
        return 'mdi:image-outline';
      case 'search':
        return 'mdi:magnify';
      case 'text':
        return 'mdi:text-box-outline';
      case 'file':
        return 'mdi:file-outline';
      case 'data':
        return 'mdi:database-outline';
      default:
        return 'mdi:api';
    }
  }

  function getServiceStatusColor(status: string) {
    switch (status?.toLowerCase()) {
      case 'healthy':
        return 'success';
      case 'unhealthy':
        return 'error';
      default:
        return 'warning';
    }
  }

  defineExpose({
    show,
  });
</script>

<template>
  <div>
    <!-- 已选择的MCP服务列表 -->
    <div
      v-if="selectedServiceDetails.length > 0"
      class="selected-services-container"
      style="max-height: 300px; overflow-y: auto"
    >
      <n-list clickable hoverable>
        <n-list-item
          v-for="service in selectedServiceDetails"
          :key="service.id"
          class="w-full bg-white overflow-hidden !rounded-lg hover:bg-gray-100"
        >
          <div class="flex items-center justify-between">
            <div class="flex gap-3 items-center">
              <div class="relative">
                <SvgIcon class="text-2xl text-blue-500" :icon="getServiceIcon(service)" />
                <n-badge
                  :type="getServiceStatusColor(service.healthStatus)"
                  :value="service.healthStatus === 'healthy' ? '●' : '●'"
                  class="absolute -top-1 -right-1"
                  size="small"
                />
              </div>
              <div class="flex flex-col flex-1">
                <div class="font-medium">{{ service.displayName || service.name }}</div>
                <div class="text-xs text-gray-500">
                  {{ service.description || '暂无描述' }}
                </div>
                <div class="text-xs text-gray-400 mt-1 flex items-center gap-2">
                  <n-tag size="tiny" :type="service.category === 'builtin' ? 'info' : 'default'">
                    {{ service.category === 'builtin' ? '内置' : '外部' }}
                  </n-tag>
                  <span>{{ service.type || 'HTTP' }}</span>
                  <!-- 参数配置状态 -->
                  <n-tag
                    v-if="hasParameters(service)"
                    size="tiny"
                    :type="isParametersConfigured(service) ? 'success' : 'warning'"
                  >
                    {{ isParametersConfigured(service) ? '已配置' : '需配置' }}
                  </n-tag>
                </div>
              </div>
              <!-- 参数配置按钮 -->
              <div v-if="hasParameters(service)" class="ml-2">
                <n-button
                  size="small"
                  secondary
                  @click.stop="configureServiceParams(service)"
                  :type="isParametersConfigured(service) ? 'default' : 'warning'"
                >
                  <template #icon>
                    <SvgIcon icon="mdi:cog" />
                  </template>
                  配置参数
                </n-button>
              </div>
            </div>
            <n-button text @click="removeService(service.id)">
              <SvgIcon class="text-red-500" icon="mdi:close" />
            </n-button>
          </div>
        </n-list-item>
      </n-list>
    </div>

    <!-- 空状态 -->
    <div v-else class="text-gray-400 text-sm py-4">
      <div class="text-center">
        <SvgIcon class="text-4xl mb-2" icon="mdi:api-off" />
        <div>暂未配置MCP服务</div>
        <div class="text-xs mt-1">
          MCP服务可以为AI应用提供额外的功能，如图片生成、网络搜索、文件处理等
        </div>
      </div>
    </div>

    <!-- MCP服务选择弹窗 -->
    <n-modal v-model:show="showModal" preset="dialog" title="选择MCP服务" style="width: 800px">
      <template #header>
        <div class="flex items-center gap-2">
          <SvgIcon icon="mdi:api" />
          <span>选择MCP服务</span>
        </div>
      </template>

      <div class="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
        <div class="flex items-center gap-2 mb-2">
          <SvgIcon class="text-blue-500" icon="mdi:information-outline" />
          <span class="text-sm font-medium text-blue-700">MCP服务说明</span>
        </div>
        <div class="text-xs text-blue-600 leading-relaxed">
          •
          选择MCP服务后，聊天将使用<strong>MCP智能编排执行接口</strong>，AI会自动生成执行计划并调用相应服务<br />
          • 未选择任何服务时，将使用<strong>普通聊天接口</strong>，仅提供基础对话功能<br />
          • 编排接口支持多服务协同工作，如：生成图片→上传到CDN→发布到网站<br />
          • 建议根据应用需求选择合适的服务，如图片生成、网络搜索、文件处理等
        </div>
      </div>

      <!-- 服务列表容器 - 改进滚动条样式和高度管理 -->
      <div
        class="mcp-service-list-container"
        style="max-height: 60vh; overflow-y: auto; padding-right: 4px"
      >
        <n-spin :show="loading">
          <div v-if="mcpServices.length === 0 && !loading" class="text-center py-8 text-gray-400">
            <SvgIcon class="text-4xl mb-2" icon="mdi:api-off" />
            <div>暂无可用的MCP服务</div>
            <div class="text-xs mt-1">请先在系统管理中配置MCP服务</div>
          </div>

          <n-checkbox-group v-model:value="selectedServices" @update:value="handleServiceChange">
            <div class="space-y-2">
              <n-card
                v-for="service in mcpServices"
                :key="service.id"
                size="small"
                class="cursor-pointer hover:shadow-md transition-shadow"
                :class="{ 'ring-2 ring-blue-500': selectedServices.includes(service.id) }"
              >
                <div class="flex items-center gap-3">
                  <n-checkbox :value="service.id" />
                  <div class="relative">
                    <SvgIcon class="text-2xl text-blue-500" :icon="getServiceIcon(service)" />
                    <n-badge
                      :type="getServiceStatusColor(service.healthStatus)"
                      :value="service.healthStatus === 'healthy' ? '●' : '●'"
                      class="absolute -top-1 -right-1"
                      size="small"
                    />
                  </div>
                  <div class="flex-1">
                    <div class="flex items-center gap-2">
                      <span class="font-medium">{{ service.displayName || service.name }}</span>
                      <n-tag
                        size="tiny"
                        :type="service.category === 'builtin' ? 'info' : 'default'"
                      >
                        {{ service.category === 'builtin' ? '内置' : '外部' }}
                      </n-tag>
                    </div>
                    <div class="text-sm text-gray-500 mt-1">
                      {{ service.description || '暂无描述' }}
                    </div>
                    <div class="text-xs text-gray-400 mt-1 flex items-center gap-2">
                      <span>{{ service.type || 'HTTP' }}</span>
                      <span>优先级: {{ service.priority || 50 }}</span>
                      <span v-if="service.tools">
                        工具数: {{ JSON.parse(service.tools || '[]').length }}
                      </span>
                      <!-- 参数提示 -->
                      <n-tag v-if="hasParameters(service)" size="tiny" type="info">
                        需要参数配置
                      </n-tag>
                    </div>
                  </div>
                  <!-- 参数配置按钮 -->
                  <div
                    v-if="hasParameters(service) && selectedServices.includes(service.id)"
                    class="mt-2"
                  >
                    <n-button
                      size="small"
                      secondary
                      block
                      @click.stop="configureServiceParams(service)"
                      :type="isParametersConfigured(service) ? 'default' : 'warning'"
                    >
                      <template #icon>
                        <SvgIcon icon="mdi:cog" />
                      </template>
                      {{ isParametersConfigured(service) ? '修改参数' : '配置参数' }}
                    </n-button>
                  </div>
                </div>
              </n-card>
            </div>
          </n-checkbox-group>
        </n-spin>
      </div>

      <template #action>
        <div class="flex justify-end gap-2">
          <n-button @click="handleCancel">取消</n-button>
          <n-button type="primary" @click="handleConfirm">确定</n-button>
        </div>
      </template>
    </n-modal>

    <!-- 参数配置弹窗 -->
    <n-modal v-model:show="showParamsModal" preset="dialog" style="width: 600px">
      <template #header>
        <div class="flex items-center gap-2">
          <SvgIcon icon="mdi:cog" />
          <span
            >配置 {{ currentConfigService?.displayName || currentConfigService?.name }} 参数</span
          >
        </div>
      </template>

      <div v-if="currentConfigService">
        <div class="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
          <div class="flex items-center gap-2 mb-2">
            <SvgIcon class="text-blue-500" icon="mdi:information-outline" />
            <span class="text-sm font-medium text-blue-700">参数配置说明</span>
          </div>
          <div class="text-xs text-blue-600 leading-relaxed">
            • 这些参数将在应用中使用该MCP服务时自动传递<br />
            • 敏感信息（如API密钥）会被安全存储<br />
            • 必填参数必须配置后才能正常使用服务
          </div>
        </div>

        <SimpleParameterForm
          :parameters="getServiceParameters(currentConfigService)"
          v-model="currentServiceParams"
        />
      </div>

      <template #action>
        <div class="flex justify-end gap-2">
          <n-button @click="cancelServiceParams">取消</n-button>
          <n-button type="primary" @click="saveServiceParams">保存</n-button>
        </div>
      </template>
    </n-modal>
  </div>
</template>

<style lang="less" scoped>
  ::v-deep(.n-list) {
    background: transparent !important;
  }

  ::v-deep(.n-card) {
    transition: all 0.3s ease;
  }

  ::v-deep(.n-checkbox-group .n-card:hover) {
    transform: translateY(-1px);
  }

  /* 自定义滚动条样式 - 应用到服务选择和已选择服务列表 */
  .mcp-service-list-container,
  .selected-services-container {
    /* Webkit浏览器滚动条样式 */

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }

    /* Firefox滚动条样式 */
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f1f1f1;
  }

  /* 确保服务卡片在滚动容器中正确显示 */
  ::v-deep(.mcp-service-list-container .n-card) {
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  /* 已选择服务列表样式优化 */
  .selected-services-container {
    padding-right: 4px;

    ::v-deep(.n-list-item) {
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  /* 响应式优化 - 在小屏幕上调整高度 */
  @media (max-height: 800px) {
    .mcp-service-list-container {
      max-height: 50vh;
    }

    .selected-services-container {
      max-height: 200px;
    }
  }
</style>
