<script lang="ts" setup>

import {useDialog, useMessage} from 'naive-ui';
import SvgIcon from '@/components/SvgIcon/index.vue';
import {onMounted, ref, toRaw, watch} from 'vue';
import {del, getList} from '@/api/aigc/workflow';
import router from '@/router';
import Edit from './edit.vue';
import { useRoute } from 'vue-router'
const route = useRoute();

const ms = useMessage();
const dialog = useDialog();
const list = ref();
const editRef = ref();

onMounted(async () => {
  await fetchData();
});

async function fetchData() {
  list.value = await getList({});
}

function handleEdit(record: Recordable) {
  console.log('edit', record.id);
  editRef.value.show(toRaw(record.id));
}

function onSelectAction(key, item) {
  if (key === 'delete') {
    dialog.info({
      title: '提示',
      content: `您确定删除 ${item.name} 流程？`,
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        await del({id: item.id});
        ms.success('删除成功');
        await fetchData();
      },
      onNegativeClick: () => {
      },
    });
  }else if (key === 'edit') {
    handleEdit(item);
  }else if (key === 'apikey') {
    if (route.fullPath.includes('/layout3')) {
      router.push('/workflow3/apikey3/' + item.id);
    } else {
      router.push('/workflow/apikey/' + item.id);
    }
  }
}

const pathRouter = ref('')
watch(
  () => route,
  (val) => {
    if (val.fullPath.includes('/layout3')) {
      pathRouter.value = '/workflow3/info3'
    } else {
      pathRouter.value = '/workflow/info'
    }
  },
  { immediate: true, deep: true }
)

async function onInfo(item) {
  await router.push({path: pathRouter.value, query: {id: item.id}});
}

function handleAdd() {
  router.push(pathRouter.value);
}


function getOptions(item: any) {
  return [
    {label: '编辑流程', key: 'edit'},
    {type: 'divider'},
    {key: 'delete', label: '删除流程'},
    {type: 'divider'},
    { key: 'apikey', label: '流程API配置' },
    { type: 'divider' },
  ];
}

const activeDropdownId = ref(null);
const handleDropdownShow = (show: boolean, itemId) => {
  activeDropdownId.value = show ? itemId : null;
};
</script>

<template>
  <section class="overflow-y-auto h-full px-3 py-4">
    <div class="grid grid-cols-1 gap-6 md:grid-cols-2 xl:grid-cols-4 mb-8">
      <div
        class="bg-[#eceef0] py-3 pt-4 transition-all duration-300 px-2 hover:border hover:border-blue-400 border border-transparent cursor-pointer rounded-xl group"
      >
        <div class="font-bold text-xs mb-1.5 px-6 text-gray-500">创建应用</div>
        <div
          class="w-full transition-all hover:bg-white rounded-lg py-1.5 px-6 flex items-center gap-1 font-medium hover:text-blue-500"
          @click="handleAdd"
        >
          <SvgIcon icon="line-md:file-plus"/>
          <span class="text-sm">创建流程</span>
        </div>
      </div>

      <div
        v-for="item in list"
        :key="item.id"
        :class="[activeDropdownId === item.id ? '!border-blue-400' : '']"
        class="bg-white px-4 py-3 pt-4 transition-all hover:border hover:border-blue-400 border border-transparent duration-300 transform cursor-pointer rounded-xl group"
        @click="onInfo(item)"
      >
        <div class="flex flex-col sm:-mx-4 sm:flex-row">
          <div class="sm:mx-4">
            <div v-if="!item.cover" class="relative bg-orange-100 p-4 rounded-lg">
              <SvgIcon class="text-3xl" icon="prime:microchip-ai"/>

              <div
                class="absolute bottom-[-6px] p-1 right-[-5px] shadow bg-white mx-auto rounded-lg"
              >
              </div>
            </div>

            <div v-if="item.cover" class="relative bg-orange-100 p-4 rounded-lg">
              <n-image  class="workflow-image" :src="item.cover"/>

              <div
                class="absolute bottom-[-6px] p-1 right-[-5px] shadow bg-white mx-auto rounded-lg"
              >
              </div>
            </div>
          </div>

          <div class="pr-4">
            <h1 class="text-lg font-semibold text-gray-700 capitalize"> {{ item.name }} </h1>

            <p class="mt-2 text-gray-500 capitalize text-xs">
              {{ item.description }}
            </p>
          </div>
        </div>

        <div class="flex mt-4 -mx-2 px-2 text-gray-400 justify-between items-center">
          <div class="flex items-center gap-1">
          </div>
          <div class="flex items-center" @click.stop>
            <n-dropdown
              :options="getOptions(item)"
              class="justify-start min-w-[160px] transition-all"
              placement="bottom-end"
              size="small"
              trigger="click"
              @select="(key) => onSelectAction(key, item)"
              @update:show="(show) => handleDropdownShow(show, item.id)"
            >
              <div
                :class="[activeDropdownId === item.id ? 'bg-gray-200' : 'hover:bg-gray-200']"
                class="rounded p-1 transition-all"
              >
                <SvgIcon class="w-5 h-5" icon="ri:more-fill"/>
              </div>
            </n-dropdown>
          </div>
        </div>
      </div>
    </div>

    <Edit ref="editRef" @reload="fetchData" />
  </section>
</template>

<style lang="less" scoped>
  .workflow-image {
    width: 30px;
  }

</style>
