<script lang="ts" setup>
import {nextTick, ref} from 'vue';
  import { detail, commit } from '@/api/aigc/workflow';
  import { UploadCustomRequestOptions, useMessage } from 'naive-ui';
  import { formSchemas } from './columns';
  import { BasicForm, useForm } from '@/components/Form';
  import { basicModal, useModal } from '@/components/Modal';
  import { isNullOrWhitespace } from '@/utils/is';
  import { uploadApi } from '@/api/aigc/oss';

  const emit = defineEmits(['reload']);
  const message = useMessage();
  const appId = ref(null)

  const [modalRegister, { openModal: openModal, closeModal: closeModal }] = useModal({
    title: '编辑',
    closable: true,
    maskClosable: false,
    showCloseBtn: false,
    showSubBtn: false,
  });
  const [register, { setFieldsValue, getFieldsValue }] = useForm({
    gridProps: { cols: 1 },
    labelWidth: 120,
    layout: 'horizontal',
    submitButtonText: '提交',
    schemas: formSchemas,
  });

  async function show(id: string) {
    openModal();
    await nextTick();
    if (id) {
      const data = await detail({id:id});
      appId.value = data.appId;
      setFieldsValue({ ...data });
    }
  }

  async function handleSubmit(values: any) {
    if (values !== false) {
      closeModal();
      if (!isNullOrWhitespace(values.id)) {
        await commit(values);
        emit('reload');
        message.success('修改成功');
      }
    } else {
      message.error('请完善表单');
    }
  }

  const handleImport = ({ file, onFinish, onError, onProgress }: UploadCustomRequestOptions) => {
    uploadApi(
      {
        file: file.file,
      },
      (progressEvent) => {
        onProgress({
          percent: Math.round((progressEvent.loaded * 100) / Number(progressEvent.total)),
        });
      }
    )
      .then((res) => {
        setFieldsValue({ ...getFieldsValue, cover: res.url });
        message.success('上传成功');
        onFinish();
      })
      .catch((err) => {
        console.error(err);
        message.error('上传失败');
        onError();
      });
  };
  defineExpose({ show });
</script>

<template>
  <basicModal style="width: 45%" @register="modalRegister">
    <BasicForm class="mt-5" @register="register" @submit="handleSubmit">
      <template #coverSlot>
        <n-upload
          :custom-request="handleImport"
          accept=".jpg,.jpeg,.png,.gif,.bmp,.webp"
          directory-dnd
          list-type="image-card"
        />
      </template>
      <template #appIdSlot>
        <n-text>{{appId}}</n-text>
      </template>
    </BasicForm>
  </basicModal>
</template>

<style lang="less" scoped></style>
