<!--
  - Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
  -
  - Licensed under the GNU Affero General Public License, Version 3 (the "License");
  - you may not use this file except in compliance with the License.
  - You may obtain a copy of the License at
  -
  -     https://www.gnu.org/licenses/agpl-3.0.html
  -
  - Unless required by applicable law or agreed to in writing, software
  - distributed under the License is distributed on an "AS IS" BASIS,
  - WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  - See the License for the specific language governing permissions and
  - limitations under the License.
  -->

<script lang="ts" setup>
  import { computed, ref } from 'vue';
  import { useMessage } from 'naive-ui';
  import TextComponent from './TextComponent.vue';
  import SvgIcon from '@/components/SvgIcon/index.vue';
  import { useIconRender } from '../store/useIconRender';
  import { copyToClip } from '@/utils/copy';

  interface Props {
    dateTime?: string;
    text?: string;
    inversion?: boolean;
    error?: boolean;
    loading?: boolean;
    fileUrls?: string[];
  }

  interface Emit {
    (ev: 'delete'): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emit>();
  const isHover = ref(false);
  const { iconRender } = useIconRender();
  const message = useMessage();
  const textRef = ref<HTMLElement>();
  const asRawText = ref(props.inversion);
  const messageRef = ref<HTMLElement>();
  const options = computed(() => {
    const common = [
      {
        label: '复制',
        key: 'copyText',
        icon: iconRender({ icon: 'ri:file-copy-2-line' }),
      },
    ];

    if (!props.inversion) {
      common.push({
        label: asRawText.value ? '预览' : '显示原文',
        key: 'toggleRenderType',
        icon: iconRender({ icon: asRawText.value ? 'ic:outline-code-off' : 'ic:outline-code' }),
      });
    }

    return common;
  });

  function handleSelect(key: 'copyText' | 'delete' | 'toggleRenderType') {
    switch (key) {
      case 'copyText':
        handleCopy();
        return;
      case 'toggleRenderType':
        asRawText.value = !asRawText.value;
        return;
      case 'delete':
        emit('delete');
    }
  }

  async function handleCopy() {
    try {
      await copyToClip(props.text || '');
      message.success('复制成功');
    } catch {
      message.error('复制失败');
    }
  }

  // 文件处理辅助函数
  function getFileName(url: string): string {
    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname;
      const fileName = pathname.split('/').pop() || 'unknown';
      return decodeURIComponent(fileName);
    } catch {
      return url.split('/').pop() || 'unknown';
    }
  }

  function getFileIcon(url: string): string {
    const fileName = getFileName(url).toLowerCase();
    const extension = fileName.split('.').pop() || '';

    // 图片文件
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(extension)) {
      return 'mdi:image';
    }
    // 视频文件
    if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'].includes(extension)) {
      return 'mdi:video';
    }
    // 音频文件
    if (['mp3', 'wav', 'flac', 'aac', 'ogg'].includes(extension)) {
      return 'mdi:music';
    }
    // PDF文件
    if (extension === 'pdf') {
      return 'mdi:file-pdf-box';
    }
    // Word文档
    if (['doc', 'docx'].includes(extension)) {
      return 'mdi:file-word-box';
    }
    // Excel文档
    if (['xls', 'xlsx'].includes(extension)) {
      return 'mdi:file-excel-box';
    }
    // PowerPoint文档
    if (['ppt', 'pptx'].includes(extension)) {
      return 'mdi:file-powerpoint-box';
    }
    // 文本文件
    if (['txt', 'md', 'json', 'xml', 'csv'].includes(extension)) {
      return 'mdi:file-document';
    }
    // 默认文件图标
    return 'mdi:file';
  }
</script>

<template>
  <div
    ref="messageRef"
    :class="[{ 'flex-row-reverse': inversion }]"
    class="flex w-full overflow-hidden"
  >
    <div
      :class="[inversion ? 'ml-2' : 'mr-2']"
      class="flex items-center justify-center bg-gray-200 flex-shrink-0 h-8 overflow-hidden rounded-full basis-8"
    >
      <SvgIcon v-if="inversion" icon="solar:user-broken" />
      <SvgIcon v-else icon="mingcute:ai-line" />
    </div>
    <div :class="[inversion ? 'items-end' : 'items-start']" class="overflow-hidden text-sm">
      <p :class="[inversion ? 'text-right' : 'text-left']" class="text-xs text-[#b4bbc4]">
        {{ dateTime }}
      </p>
      <div
        @mouseover="isHover = true"
        @mouseleave="isHover = false"
        :class="[inversion ? 'flex-row-reverse' : 'flex-row']"
        class="flex items-end gap-1 mt-2 transition-all"
      >
        <div class="flex flex-col">
          <!-- 文件显示区域 -->
          <div v-if="fileUrls && fileUrls.length > 0" class="mb-2">
            <div class="flex flex-wrap gap-2">
              <div
                v-for="(fileUrl, index) in fileUrls"
                :key="index"
                class="file-attachment flex items-center p-2 bg-gray-100 rounded-lg max-w-xs"
              >
                <SvgIcon
                  :icon="getFileIcon(fileUrl)"
                  class="mr-2 text-blue-500 flex-shrink-0"
                />
                <div class="flex-1 min-w-0">
                  <div class="text-sm font-medium truncate" :title="getFileName(fileUrl)">
                    {{ getFileName(fileUrl) }}
                  </div>
                  <a
                    :href="fileUrl"
                    target="_blank"
                    class="text-xs text-blue-600 hover:text-blue-800"
                  >
                    查看文件
                  </a>
                </div>
              </div>
            </div>
          </div>

          <!-- 文本内容 -->
          <TextComponent
            ref="textRef"
            :as-raw-text="asRawText"
            :error="error"
            :inversion="inversion"
            :loading="loading"
            :text="text"
          />
        </div>
        <div class="flex flex-col transition-all w-[45px]">
          <n-space v-if="isHover" class="transition-all gap-1.5 flex-nowrap justify-end">
            <n-popover v-for="item in options" :key="item" class="custom-popover">
              <template #trigger>
                <button
                  @click="handleSelect(item.key as any)"
                  class="transition text-neutral-400 hover:text-neutral-800"
                >
                  <component :is="item.icon" />
                </button>
              </template>
              {{ item.label }}
            </n-popover>
          </n-space>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped></style>
