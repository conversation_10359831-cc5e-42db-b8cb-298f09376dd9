<!--
  - Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
  -
  - Licensed under the GNU Affero General Public License, Version 3 (the "License");
  - you may not use this file except in compliance with the License.
  - You may obtain a copy of the License at
  -
  -     https://www.gnu.org/licenses/agpl-3.0.html
  -
  - Unless required by applicable law or agreed to in writing, software
  - distributed under the License is distributed on an "AS IS" BASIS,
  - WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  - See the License for the specific language governing permissions and
  - limitations under the License.
  -->

<script lang="ts" setup>
  import Message from './message/Message.vue';
  import FileUpload from '@/components/FileUpload/NaiveFileUpload.vue';
  import { useProjectSetting } from '@/hooks/setting/useProjectSetting';
  import { computed, ref, watch, onMounted } from 'vue';
  import { v4 as uuidv4 } from 'uuid';
  import { useChatStore } from './store/useChatStore';
  import { useScroll } from './store/useScroll';
  import { useDialog, useMessage } from 'naive-ui';
  import SvgIcon from '@/components/SvgIcon/index.vue';
  import { chat, cleanAppMessages } from '@/api/aigc/chat';
  import { useRoute } from 'vue-router';

  const dialog = useDialog();
  const ms = useMessage();
  const chatStore = useChatStore();
  const { scrollRef, contentRef, scrollToBottom, scrollToBottomIfAtBottom } = useScroll();
  const { isMobile } = useProjectSetting();
  const loading = ref<boolean>(false);
  const route = useRoute();
  const message = ref('');
  const chatId = ref<string>('');
  const aiChatId = ref<string>('');
  const fileUploadRef = ref();
  const uploadedFiles = ref([]);
  let controller = new AbortController();

  const footerClass = computed(() => {
    let classes = ['p-4'];
    if (isMobile.value) {
      classes = ['sticky', 'left-0', 'bottom-0', 'right-0', 'p-2', 'pr-3', 'overflow-hidden'];
    }
    return classes;
  });

  const dataSources = computed(() => {
    scrollToBottom();
    return chatStore.messages;
  });

  watch(
    () => route,
    (newValue) => {
      if (newValue.path === '/app/chat') {
        chatStore.appId = null;
      }
    },
    {
      deep: true,
      immediate: true,
    }
  );

  function handleEnter(event: KeyboardEvent) {
    if (!isMobile.value) {
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        handleSubmit();
      }
    } else {
      if (event.key === 'Enter' && event.ctrlKey) {
        event.preventDefault();
        handleSubmit();
      }
    }
  }

  async function handleSubmit() {
    const msg = message.value;
    if (loading.value) {
      return;
    }
    if (!msg || msg.trim() === '') {
      ms.error('请先输入消息内容');
      return;
    }
    controller = new AbortController();

    // user
    chatId.value = uuidv4();
    await chatStore.addMessage(msg, 'user', chatId.value, uploadedFiles.value);

    loading.value = true;
    message.value = '';

    // ai
    await scrollToBottom();
    aiChatId.value = uuidv4();
    await scrollToBottom();
    await chatStore.addMessage('', 'assistant', aiChatId.value);
    await scrollToBottomIfAtBottom();

    await onChat(msg, uploadedFiles.value.toString());

    // 清空已上传的文件
    if (fileUploadRef.value) {
      uploadedFiles.value = [];
      fileUploadRef.value.clearFiles();
    }
  }

  async function onChat(message: string, url: string) {
    try {
      // 检查是否有MCP服务配置
      const hasMcpServices = chatStore.mcpServiceIds && chatStore.mcpServiceIds.length > 0;

      // 调试信息
      if (hasMcpServices) {
        console.log(
          '使用MCP增强聊天接口，已配置的MCP服务:',
          chatStore.mcpServices.map((s) => s.displayName || s.name)
        );
      } else {
        console.log('使用普通聊天接口');
      }

      await chat(
        {
          chatId: chatId.value,
          conversationId: chatStore.conversationId,
          appId: chatStore.appId,
          message,
          role: 'user',
          modelId: chatStore.modelId,
          modelName: chatStore.modelName,
          modelProvider: chatStore.modelProvider,
          mcpServiceIds: chatStore.mcpServiceIds || [],
          mcpServices: chatStore.mcpServices || [],
          url, // 添加文件URL列表
          // 为编排接口添加必要的参数
          userId: 'current_user', // 这里应该从用户状态中获取
        },
        controller,
        async ({ event }) => {
          const list = event.target.responseText.split('\n\n');

          let text = '';
          let isRun = true;
          list.forEach((i: any) => {
            if (i.startsWith('data:Error')) {
              isRun = false;
              text += i.substring(5, i.length);
              chatStore.updateMessage(aiChatId.value, text, true);
              return;
            }
            if (!i.startsWith('data:{')) {
              return;
            }

            const { done, message } = JSON.parse(i.substring(5, i.length));
            if (done || message === null) {
              return;
            }
            text += message;
          });
          if (!isRun) {
            await scrollToBottomIfAtBottom();
            return;
          }
          await chatStore.updateMessage(aiChatId.value, text, false);
          await scrollToBottomIfAtBottom();
        }
      )
        .catch((e: any) => {
          loading.value = false;
          console.error('chat error', e);
          if (e.message !== undefined) {
            chatStore.updateMessage(aiChatId.value, e.message || 'chat error', true);
            return;
          }
          if (e.startsWith('data:Error')) {
            chatStore.updateMessage(aiChatId.value, e.substring(5, e.length), true);
            return;
          }
        })
        .finally(() => {
          scrollToBottomIfAtBottom();
        });
    } finally {
      loading.value = false;
    }
  }

  function handleStop() {
    if (loading.value) {
      controller.abort();
      controller = new AbortController();
      loading.value = false;
    }
  }

  // 文件上传相关处理函数
  function handleFileChange(files: any[]) {
    uploadedFiles.value.push(...files.map((file) => file.url));
  }

  function handleUploadSuccess(file: any) {
    console.log('文件上传成功:', file);
  }

  function handleUploadError(error: string) {
    console.error('文件上传失败:', error);
    ms.error(`文件上传失败: ${error}`);
  }

  function handleDelete(item: any) {
    if (loading.value) {
      return;
    }

    dialog.warning({
      title: '删除消息',
      content: '确认删除消息',
      positiveText: '是',
      negativeText: '否',
      onPositiveClick: () => {
        chatStore.delMessage(item);
      },
    });
  }

  function handleClearMessages() {
    if (loading.value) {
      return;
    }

    dialog.warning({
      title: '清空消息',
      content: '确认清空当前应用的所有消息？此操作不可恢复。',
      positiveText: '确认',
      negativeText: '取消',
      onPositiveClick: async () => {
        try {
          if (chatStore.appId) {
            await cleanAppMessages(chatStore.appId);
            chatStore.messages = [];
            ms.success('消息已清空');
          } else {
            ms.error('无法获取应用信息');
          }
        } catch (error) {
          console.error('清空消息失败:', error);
          ms.error('清空消息失败');
        }
      },
    });
  }
</script>

<template>
  <div class="flex flex-col w-full h-full">
    <!-- 顶部工具栏 -->
    <div v-if="chatStore.appId" class="flex justify-end p-2 border-b border-gray-200">
      <n-button
        size="small"
        type="error"
        secondary
        @click="handleClearMessages"
        :disabled="loading || chatStore.messages.length === 0"
      >
        <template #icon>
          <SvgIcon icon="mdi:delete-sweep" />
        </template>
        清空消息
      </n-button>
    </div>

    <main class="flex-1 overflow-hidden">
      <div ref="contentRef" class="h-full overflow-hidden overflow-y-auto">
        <div
          ref="scrollRef"
          :class="[isMobile ? 'p-2' : 'p-5']"
          class="w-full max-w-screen-3xl m-auto"
        >
          <Message
            v-for="(item, index) of dataSources"
            :key="index"
            :class="dataSources.length - 1 == index ? '!mb-2' : 'mb-6'"
            :date-time="item.createTime"
            :error="item.isError"
            :inversion="item.role !== 'assistant'"
            :loading="loading"
            :text="item.message"
            :file-urls="item.fileUrls"
            @delete="handleDelete(item)"
          />
        </div>
      </div>
    </main>

    <footer :class="footerClass">
      <div class="w-full max-w-screen-3xl m-auto pb-6 relative">
        <!-- 文件上传区域 -->
        <div class="mb-3">
          <FileUpload
            ref="fileUploadRef"
            :max-files="5"
            :max-size="10"
            accept="image/*,application/pdf,.doc,.docx,.txt"
            @change="handleFileChange"
            @upload-success="handleUploadSuccess"
            @upload-error="handleUploadError"
          />
        </div>
        <!-- MCP服务状态显示：有已选中的MCP服务时展示 -->
        <McpServiceStatus v-if="chatStore.mcpServiceIds && chatStore.mcpServiceIds.length > 0" />
        <!-- 输入框区域 -->
        <div class="flex items-end space-x-2">
          <!-- 输入框 -->
          <div class="flex-1">
            <n-input
              ref="inputRef"
              v-model:value="message"
              :autosize="{ minRows: 1, maxRows: isMobile ? 1 : 4 }"
              class="rounded-xl px-3 py-1 custom-input"
              placeholder="今天想聊些什么~"
              size="large"
              type="textarea"
              @keypress="handleEnter"
            >
              <template #suffix>
                <n-button
                  v-if="!loading"
                  class="!cursor-pointer"
                  size="large"
                  text
                  type="primary"
                  @click="handleSubmit"
                >
                  <template #icon>
                    <SvgIcon icon="mdi:sparkles-outline" />
                  </template>
                </n-button>
                <div v-if="loading" class="!cursor-pointer" @click="handleStop">
                  <SvgIcon
                    class="!text-3xl hover:text-gray-500 !cursor-pointer"
                    icon="ri:stop-circle-line"
                  />
                </div>
              </template>
            </n-input>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<style lang="less" scoped>
  ::v-deep(.custom-input) {
    .n-input-wrapper {
      padding-right: 6px !important;
    }
  }

  ::v-deep(.min-collapse) {
    .n-collapse-item__content-inner {
      padding-top: 0 !important;
    }
  }
</style>
