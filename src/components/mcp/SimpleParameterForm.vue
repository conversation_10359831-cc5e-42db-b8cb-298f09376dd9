<template>
  <div class="simple-parameter-form">
    <div v-if="parameters.length === 0" class="no-parameters">
      <n-empty description="此服务无需配置参数" />
    </div>

    <div v-else class="parameter-list">
      <n-form :model="parameterValues" label-width="120px" label-placement="left">
        <n-form-item
          v-for="parameter in parameters"
          :key="parameter.name"
          :label="parameter.displayName"
          :path="parameter.name"
        >
          <template #label>
            <div class="parameter-label">
              <span>{{ parameter.displayName }}</span>
              <span v-if="parameter.required" class="required-mark">*</span>
              <n-tooltip v-if="parameter.description" trigger="hover">
                <template #trigger>
                  <n-icon class="info-icon" size="14">
                    <InformationCircleOutline />
                  </n-icon>
                </template>
                {{ parameter.description }}
              </n-tooltip>
            </div>
          </template>

          <!-- 字符串输入 -->
          <n-input
            v-if="parameter.type === 'string' && !parameter.sensitive"
            v-model:value="parameterValues[parameter.name]"
            :placeholder="getPlaceholder(parameter)"
            clearable
          />

          <!-- 密码输入 -->
          <n-input
            v-else-if="parameter.type === 'string' && parameter.sensitive"
            v-model:value="parameterValues[parameter.name]"
            type="password"
            :placeholder="getPlaceholder(parameter)"
            show-password-on="click"
            clearable
          />

          <!-- 数字输入 -->
          <n-input-number
            v-else-if="parameter.type === 'number'"
            v-model:value="parameterValues[parameter.name]"
            :placeholder="getPlaceholder(parameter)"
            style="width: 100%"
          />

          <!-- 布尔值 -->
          <n-switch
            v-else-if="parameter.type === 'boolean'"
            v-model:value="parameterValues[parameter.name]"
          />

          <!-- 对象/JSON -->
          <n-input
            v-else-if="parameter.type === 'object'"
            v-model:value="jsonStrings[parameter.name]"
            type="textarea"
            :rows="4"
            :placeholder="getPlaceholder(parameter)"
            @blur="validateJson(parameter.name)"
          />

          <!-- 默认字符串输入 -->
          <n-input
            v-else
            v-model:value="parameterValues[parameter.name]"
            :placeholder="getPlaceholder(parameter)"
            clearable
          />

          <!-- JSON错误提示 -->
          <div v-if="jsonErrors[parameter.name]" class="json-error">
            <n-text type="error" style="font-size: 12px">{{ jsonErrors[parameter.name] }}</n-text>
          </div>
        </n-form-item>
      </n-form>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch } from 'vue';
  import { InformationCircleOutline } from '@vicons/ionicons5';
  import { isEqual } from 'lodash-es';

  interface Parameter {
    name: string;
    displayName: string;
    description?: string;
    type: string;
    required?: boolean;
    defaultValue?: any;
    sensitive?: boolean;
  }

  const props = defineProps<{
    parameters: Parameter[];
    modelValue: Record<string, any>;
  }>();

  const emit = defineEmits<{
    'update:modelValue': [value: Record<string, any>];
  }>();

  const parameterValues = ref({ ...props.modelValue });
  const jsonStrings = ref<Record<string, string>>({});
  const jsonErrors = ref<Record<string, string>>({});
  // 添加一个标志位，用于控制是否触发更新
  const isUpdating = ref(false);

  // 验证JSON格式
  const validateJson = (paramName: string) => {
    const jsonStr = jsonStrings.value[paramName];
    if (!jsonStr || jsonStr.trim() === '') {
      parameterValues.value[paramName] = null;
      jsonErrors.value[paramName] = '';
      return;
    }

    try {
      const parsed = JSON.parse(jsonStr);
      parameterValues.value[paramName] = parsed;
      jsonErrors.value[paramName] = '';
    } catch (error: any) {
      jsonErrors.value[paramName] = 'JSON格式错误: ' + error.message;
    }
  };

  // 获取占位符文本
  const getPlaceholder = (parameter: Parameter) => {
    if (parameter.defaultValue !== undefined) {
      return `默认值: ${parameter.defaultValue}`;
    }

    switch (parameter.type) {
      case 'string':
        return parameter.sensitive ? '请输入密钥或密码' : '请输入文本';
      case 'number':
        return '请输入数字';
      case 'object':
        return '请输入JSON格式的配置';
      default:
        return '请输入值';
    }
  };

  // 监听参数值变化
  watch(
    parameterValues,
    (newValues) => {
      // 比较新旧值是否相同，避免不必要的更新
      emit('update:modelValue', { ...newValues });
    },
    { deep: true }
  );

  // 监听props变化
  watch(
    () => props.modelValue,
    (newValue, oldValue) => {
      // 比较新旧值是否相同，避免不必要的更新
      if (!isEqual(newValue, oldValue)) {
        parameterValues.value = { ...newValue };
      }
    },
    { deep: true }
  );
</script>

<style scoped>
  .simple-parameter-form {
    width: 100%;
  }

  .parameter-label {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .required-mark {
    color: #d03050;
  }

  .info-icon {
    color: #999;
    cursor: help;
  }

  .json-error {
    margin-top: 4px;
  }

  .no-parameters {
    padding: 40px 0;
    text-align: center;
  }

  .parameter-list {
    padding: 16px 0;
  }
</style>
