<template>
  <div class="enhanced-parameter-form">
    <div v-if="!parameters || parameters.length === 0" class="no-parameters">
      <n-empty description="暂无参数配置">
        <template #extra>
          <n-button @click="addParameter">添加参数</n-button>
        </template>
      </n-empty>
    </div>

    <div v-else class="parameter-list">
      <n-card
        v-for="(param, index) in parameters"
        :key="param.name || index"
        class="parameter-card"
        size="small"
      >
        <template #header>
          <div class="parameter-header">
            <span class="parameter-title">
              {{ param.displayName || param.name }}
              <n-tag v-if="param.required" type="error" size="tiny" class="ml-2">必填</n-tag>
              <n-tag v-if="param.sensitive" type="warning" size="tiny" class="ml-2">敏感</n-tag>
            </span>
            <n-button-group size="tiny">
              <n-button @click="moveParameter(index, -1)" :disabled="index === 0">
                <template #icon>
                  <n-icon>
                    <ChevronUp />
                  </n-icon>
                </template>
              </n-button>
              <n-button
                @click="moveParameter(index, 1)"
                :disabled="index === parameters.length - 1"
              >
                <template #icon>
                  <n-icon>
                    <ChevronDown />
                  </n-icon>
                </template>
              </n-button>
              <n-button @click="removeParameter(index)" type="error">
                <template #icon>
                  <n-icon>
                    <TrashOutline />
                  </n-icon>
                </template>
              </n-button>
            </n-button-group>
          </div>
        </template>

        <n-form label-placement="top" size="small">
          <!-- 基本信息 -->
          <n-grid :cols="2" :x-gap="12">
            <n-grid-item>
              <n-form-item label="参数名称" required>
                <n-input v-model:value="param.name" placeholder="参数名称（英文）" />
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="显示名称" required>
                <n-input v-model:value="param.displayName" placeholder="显示名称（中文）" />
              </n-form-item>
            </n-grid-item>
          </n-grid>

          <n-form-item label="参数描述">
            <n-input
              v-model:value="param.description"
              placeholder="参数描述"
              type="textarea"
              :rows="2"
            />
          </n-form-item>

          <n-grid :cols="3" :x-gap="12">
            <n-grid-item>
              <n-form-item label="参数类型">
                <n-select v-model:value="param.type" :options="typeOptions" />
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="是否必填">
                <n-switch v-model:value="param.required">
                  <template #checked>必填</template>
                  <template #unchecked>可选</template>
                </n-switch>
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="敏感信息">
                <n-switch v-model:value="param.sensitive">
                  <template #checked>敏感</template>
                  <template #unchecked>普通</template>
                </n-switch>
              </n-form-item>
            </n-grid-item>
          </n-grid>

          <n-form-item label="默认值">
            <n-input v-model:value="param.defaultValue" placeholder="默认值" />
          </n-form-item>

          <!-- 智能提取配置 -->
          <n-divider title-placement="left">
            <span class="text-sm text-gray-600">智能提取配置</span>
          </n-divider>

          <n-form-item label="启用输入提取">
            <n-switch v-model:value="param.validation.extractFromInput">
              <template #checked>启用</template>
              <template #unchecked>禁用</template>
            </n-switch>
            <template #feedback>
              <span class="text-xs text-gray-500">
                启用后，系统会从用户输入中自动提取此参数的值
              </span>
            </template>
          </n-form-item>

          <!-- 提取配置详情 -->
          <div v-if="param.validation.extractFromInput" class="extraction-config">
            <n-form-item label="关键词列表">
              <n-dynamic-tags
                v-model:value="param.validation.keywords"
                placeholder="输入关键词后按回车添加"
              />
              <template #feedback>
                <span class="text-xs text-gray-500">
                  系统会在用户输入中查找这些关键词，并提取相邻的值
                </span>
              </template>
            </n-form-item>

            <n-form-item label="正则表达式">
              <n-input
                v-model:value="param.validation.pattern"
                placeholder="例如: (?:企业|公司)[:：]?\s*([\\u4e00-\\u9fa5a-zA-Z0-9\\s]+)"
                type="textarea"
                :rows="2"
              />
              <template #feedback>
                <span class="text-xs text-gray-500">
                  使用正则表达式精确匹配参数值，第一个捕获组将作为参数值
                </span>
              </template>
            </n-form-item>

            <n-form-item label="AI提取提示词">
              <n-input
                v-model:value="param.validation.extractionPrompt"
                placeholder="例如: 请提取用户想要查询的企业名称，只返回企业名称"
                type="textarea"
                :rows="3"
              />
              <template #feedback>
                <span class="text-xs text-gray-500">
                  AI模型将根据这个提示词从用户输入中提取参数值
                </span>
              </template>
            </n-form-item>

            <n-form-item label="提取默认值">
              <n-input
                v-model:value="param.validation.defaultValue"
                placeholder="当无法提取到值时使用的默认值"
              />
              <template #feedback>
                <span class="text-xs text-gray-500"> 当所有提取方法都失败时，使用此默认值 </span>
              </template>
            </n-form-item>

            <!-- 测试区域 -->
            <n-form-item label="提取测试">
              <n-space vertical>
                <n-input
                  v-model:value="testInputs[param.name]"
                  placeholder="输入一段文本来测试参数提取效果"
                  type="textarea"
                  :rows="2"
                />
                <n-space>
                  <n-button
                    size="small"
                    @click="testExtraction(param)"
                    :loading="testingParams[param.name]"
                  >
                    测试提取
                  </n-button>
                  <n-button size="small" @click="clearTest(param.name)">清空</n-button>
                </n-space>

                <!-- 测试结果 -->
                <div v-if="testResults[param.name]" class="test-result">
                  <n-alert
                    :type="testResults[param.name].success ? 'success' : 'warning'"
                    size="small"
                  >
                    <template #header>
                      {{ testResults[param.name].success ? '提取成功' : '提取失败' }}
                    </template>
                    <div v-if="testResults[param.name].success">
                      <strong>提取值:</strong> {{ testResults[param.name].value || '(空值)' }}
                    </div>
                    <div v-else><strong>原因:</strong> {{ testResults[param.name].message }}</div>
                  </n-alert>
                </div>
              </n-space>
            </n-form-item>
          </div>
        </n-form>
      </n-card>

      <!-- 添加参数按钮 -->
      <div class="add-parameter">
        <n-button @click="addParameter" dashed block>
          <template #icon>
            <n-icon>
              <AddOutline />
            </n-icon>
          </template>
          添加参数
        </n-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch } from 'vue';
  import { ChevronUp, ChevronDown, TrashOutline, AddOutline } from '@vicons/ionicons5';
  import { isEqual } from 'lodash-es';

  interface Parameter {
    name: string;
    displayName: string;
    description?: string;
    type: string;
    required?: boolean;
    defaultValue?: any;
    sensitive?: boolean;
    validation: {
      extractFromInput?: boolean;
      extractionPrompt?: string;
      keywords?: string[];
      pattern?: string;
      defaultValue?: string;
    };
  }

  const props = defineProps<{
    modelValue: Parameter[];
  }>();

  const emit = defineEmits<{
    'update:modelValue': [value: Parameter[]];
  }>();

  // 状态
  const parameters = ref<Parameter[]>([...props.modelValue]);
  const testInputs = ref<Record<string, string>>({});
  const testResults = ref<Record<string, any>>({});
  const testingParams = ref<Record<string, boolean>>({});

  // 参数类型选项
  const typeOptions = [
    { label: '字符串', value: 'string' },
    { label: '数字', value: 'number' },
    { label: '布尔值', value: 'boolean' },
    { label: '对象', value: 'object' },
    { label: '数组', value: 'array' },
  ];

  // 创建新参数
  const createNewParameter = (): Parameter => ({
    name: '',
    displayName: '',
    description: '',
    type: 'string',
    required: false,
    sensitive: false,
    defaultValue: '',
    validation: {
      extractFromInput: false,
      extractionPrompt: '',
      keywords: [],
      pattern: '',
      defaultValue: '',
    },
  });

  // 添加参数
  const addParameter = () => {
    parameters.value.push(createNewParameter());
  };

  // 删除参数
  const removeParameter = (index: number) => {
    parameters.value.splice(index, 1);
  };

  // 移动参数
  const moveParameter = (index: number, direction: number) => {
    const newIndex = index + direction;
    if (newIndex >= 0 && newIndex < parameters.value.length) {
      const temp = parameters.value[index];
      parameters.value[index] = parameters.value[newIndex];
      parameters.value[newIndex] = temp;
    }
  };

  // 测试提取
  const testExtraction = async (param: Parameter) => {
    const testInput = testInputs.value[param.name];
    if (!testInput?.trim()) {
      testResults.value[param.name] = {
        success: false,
        message: '请输入测试文本',
      };
      return;
    }

    testingParams.value[param.name] = true;
    testResults.value[param.name] = null;

    try {
      // 模拟提取逻辑
      let extractedValue = null;

      // 1. 关键词匹配
      if (param.validation.keywords?.length > 0) {
        extractedValue = testKeywordExtraction(testInput, param.validation.keywords);
      }

      // 2. 正则表达式
      if (!extractedValue && param.validation.pattern) {
        extractedValue = testPatternExtraction(testInput, param.validation.pattern);
      }

      // 3. 使用默认值
      if (!extractedValue && param.validation.defaultValue) {
        extractedValue = param.validation.defaultValue;
      }

      testResults.value[param.name] = {
        success: !!extractedValue,
        value: extractedValue,
        message: extractedValue ? undefined : '未能提取到有效值',
      };
    } catch (error) {
      testResults.value[param.name] = {
        success: false,
        message: `提取失败: ${error}`,
      };
    } finally {
      testingParams.value[param.name] = false;
    }
  };

  // 关键词提取测试
  const testKeywordExtraction = (input: string, keywords: string[]) => {
    const lowerInput = input.toLowerCase();

    for (const keyword of keywords) {
      const lowerKeyword = keyword.toLowerCase();
      const index = lowerInput.indexOf(lowerKeyword);

      if (index !== -1) {
        const afterKeyword = input.substring(index + keyword.length).trim();

        if (afterKeyword.startsWith('：') || afterKeyword.startsWith(':')) {
          const value = afterKeyword.substring(1).trim();

          // 对于企业名称，尝试提取完整名称
          const companyName = extractCompanyNameFromText(value);
          if (companyName) {
            return companyName;
          }

          const words = value.split(/[\s,，。；;]/);
          if (words.length > 0 && words[0].trim()) {
            return words[0].trim();
          }
        }
      }
    }

    return null;
  };

  // 从文本中提取企业名称（包括完整后缀）
  const extractCompanyNameFromText = (text: string) => {
    const companySuffixes = [
      '有限责任公司',
      '股份有限公司',
      '有限公司',
      '股份公司',
      '集团有限公司',
      '集团股份有限公司',
      '集团',
      '公司',
      '企业',
    ];

    // 按后缀长度排序，优先匹配长后缀
    companySuffixes.sort((a, b) => b.length - a.length);

    for (const suffix of companySuffixes) {
      const suffixIndex = text.indexOf(suffix);
      if (suffixIndex !== -1) {
        // 提取包含后缀的完整企业名称
        const companyName = text.substring(0, suffixIndex + suffix.length).trim();
        if (companyName.length > suffix.length) {
          return companyName;
        }
      }
    }

    // 如果没有找到标准后缀，返回第一个词
    const words = text.split(/[\s,，。；;的]/);
    if (words.length > 0 && words[0].trim()) {
      return words[0].trim();
    }

    return null;
  };

  // 正则表达式提取测试
  const testPatternExtraction = (input: string, pattern: string) => {
    try {
      const regex = new RegExp(pattern, 'i');
      const match = input.match(regex);

      if (match) {
        return match.length > 1 ? match[1] : match[0];
      }
    } catch (error) {
      console.error('正则表达式错误:', error);
    }

    return null;
  };

  // 清空测试
  const clearTest = (paramName: string) => {
    testInputs.value[paramName] = '';
    testResults.value[paramName] = null;
  };

  // 监听变化
  watch(
    parameters,
    (newValue) => {
      emit('update:modelValue', [...newValue]);
    },
    { deep: true }
  );

  watch(
    () => props.modelValue,
    (newValue, oldValue) => {
      if (!isEqual(newValue, oldValue)) {
        parameters.value = [...newValue];
      }
    },
    { deep: true }
  );

</script>

<style scoped>
  .enhanced-parameter-form {
    width: 100%;
  }

  .parameter-card {
    margin-bottom: 16px;
  }

  .parameter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .parameter-title {
    font-weight: 500;
  }

  .extraction-config {
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 6px;
    margin-top: 12px;
  }

  .add-parameter {
    margin-top: 16px;
  }

  .no-parameters {
    padding: 40px 0;
    text-align: center;
  }

  .test-result {
    margin-top: 8px;
  }
</style>
