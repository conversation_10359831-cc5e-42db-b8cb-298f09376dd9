<template>
  <div class="mcp-parameter-form">
    <div v-if="parameterGroups.length === 0" class="no-parameters">
      <n-empty description="此服务无需配置参数" />
    </div>

    <div v-else>
      <n-collapse v-model:expanded-names="activeGroups">
        <n-collapse-item
          v-for="group in parameterGroups"
          :key="group.name"
          :name="group.name"
          :title="group.displayName"
        >
          <template #header>
            <div class="group-header">
              <span class="group-title">{{ group.displayName }}</span>
              <span class="group-description">{{ group.description }}</span>
            </div>
          </template>

          <div class="parameter-group-content">
            <n-form
              :model="parameterValues"
              :rules="validationRules"
              ref="parameterForm"
              label-width="120px"
              label-placement="top"
            >
              <n-form-item
                v-for="parameter in group.parameters"
                :key="parameter.name"
                :label="parameter.displayName"
                :path="parameter.name"
                :class="getParameterClass(parameter)"
              >
                <template #label>
                  <div class="parameter-label">
                    <span>{{ parameter.displayName }}</span>
                    <span v-if="parameter.required" class="required-mark">*</span>
                    <n-tooltip v-if="parameter.description" trigger="hover">
                      <template #trigger>
                        <n-icon class="info-icon" size="14">
                          <InformationCircleOutline />
                        </n-icon>
                      </template>
                      {{ parameter.description }}
                    </n-tooltip>
                  </div>
                </template>

                <!-- 字符串输入 -->
                <n-input
                  v-if="parameter.uiHint?.inputType === 'text' || parameter.type === 'string'"
                  v-model:value="parameterValues[parameter.name]"
                  :placeholder="parameter.uiHint?.placeholder"
                  :disabled="parameter.disabled"
                  clearable
                />

                <!-- URL输入 -->
                <n-input
                  v-else-if="parameter.uiHint?.inputType === 'url'"
                  v-model:value="parameterValues[parameter.name]"
                  :placeholder="parameter.uiHint?.placeholder"
                  :disabled="parameter.disabled"
                  clearable
                >
                  <template #prefix>
                    <n-icon>
                      <LinkOutline />
                    </n-icon>
                  </template>
                </n-input>

                <!-- 密码输入 -->
                <n-input
                  v-else-if="parameter.uiHint?.inputType === 'password'"
                  v-model:value="parameterValues[parameter.name]"
                  type="password"
                  :placeholder="parameter.uiHint?.placeholder"
                  :disabled="parameter.disabled"
                  show-password-on="click"
                  clearable
                />

                <!-- 数字输入 -->
                <n-input-number
                  v-else-if="parameter.type === 'number'"
                  v-model:value="parameterValues[parameter.name]"
                  :min="parameter.validation?.min"
                  :max="parameter.validation?.max"
                  :placeholder="parameter.uiHint?.placeholder"
                  :disabled="parameter.disabled"
                  style="width: 100%"
                />

                <!-- 选择框 -->
                <n-select
                  v-else-if="parameter.uiHint?.inputType === 'select'"
                  v-model:value="parameterValues[parameter.name]"
                  :placeholder="parameter.uiHint?.placeholder || '请选择'"
                  :disabled="parameter.disabled"
                  clearable
                  :options="getSelectOptions(parameter)"
                />

                <!-- 复选框 -->
                <n-checkbox
                  v-else-if="parameter.type === 'boolean'"
                  v-model:checked="parameterValues[parameter.name]"
                  :disabled="parameter.disabled"
                >
                  {{ parameter.uiHint?.helpText || '启用' }}
                </n-checkbox>

                <!-- 文本域 -->
                <n-input
                  v-else-if="parameter.uiHint?.inputType === 'textarea'"
                  v-model:value="parameterValues[parameter.name]"
                  type="textarea"
                  :rows="parameter.uiHint?.multiline ? 5 : 3"
                  :placeholder="parameter.uiHint?.placeholder"
                  :disabled="parameter.disabled"
                />

                <!-- JSON编辑器 -->
                <div v-else-if="parameter.type === 'object'" class="json-editor">
                  <n-input
                    v-model:value="jsonStrings[parameter.name]"
                    type="textarea"
                    :rows="6"
                    :placeholder="parameter.uiHint?.placeholder"
                    :disabled="parameter.disabled"
                    @blur="validateJson(parameter.name)"
                  />
                  <div v-if="jsonErrors[parameter.name]" class="json-error">
                    <n-text type="error">{{ jsonErrors[parameter.name] }}</n-text>
                  </div>
                </div>

                <!-- 帮助文本 -->
                <div v-if="parameter.uiHint?.helpText" class="help-text">
                  <n-text depth="3" style="font-size: 12px">{{ parameter.uiHint.helpText }}</n-text>
                </div>
              </n-form-item>
            </n-form>
          </div>
        </n-collapse-item>
      </n-collapse>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, watch, nextTick } from 'vue';
  import { InformationCircleOutline, LinkOutline } from '@vicons/ionicons5';

  interface Parameter {
    name: string;
    displayName: string;
    description?: string;
    type: string;
    required?: boolean;
    defaultValue?: any;
    group?: string;
    order?: number;
    sensitive?: boolean;
    disabled?: boolean;
    validation?: {
      min?: number;
      max?: number;
      minLength?: number;
      maxLength?: number;
      pattern?: string;
      errorMessage?: string;
      enumValues?: any[];
    };
    uiHint?: {
      inputType?: string;
      placeholder?: string;
      helpText?: string;
      multiline?: boolean;
      width?: string;
      options?: Array<{
        label: string;
        value: any;
        disabled?: boolean;
      }>;
    };
  }

  const props = defineProps<{
    parameters: Parameter[];
    modelValue: Record<string, any>;
    disabled?: boolean;
  }>();

  const emit = defineEmits<{
    'update:modelValue': [value: Record<string, any>];
    validate: [valid: boolean, errors?: any];
  }>();

  const parameterValues = ref({ ...props.modelValue });
  const jsonStrings = ref<Record<string, string>>({});
  const jsonErrors = ref<Record<string, string>>({});
  const activeGroups = ref<string[]>([]);
  const parameterForm = ref();

  // 计算参数分组
  const parameterGroups = computed(() => {
    const groups: Record<string, any> = {};

    props.parameters.forEach((param) => {
      const groupName = param.group || 'default';
      if (!groups[groupName]) {
        groups[groupName] = {
          name: groupName,
          displayName: getGroupDisplayName(groupName),
          description: getGroupDescription(groupName),
          parameters: [],
        };
      }
      groups[groupName].parameters.push(param);
    });

    // 按order排序
    Object.values(groups).forEach((group: any) => {
      group.parameters.sort((a: Parameter, b: Parameter) => (a.order || 0) - (b.order || 0));
    });

    return Object.values(groups).sort((a: any, b: any) => {
      const aOrder = getGroupOrder(a.name);
      const bOrder = getGroupOrder(b.name);
      return aOrder - bOrder;
    });
  });

  // 验证规则
  const validationRules = computed(() => {
    const rules: Record<string, any[]> = {};

    props.parameters.forEach((param) => {
      const paramRules: any[] = [];

      if (param.required) {
        paramRules.push({
          required: true,
          message: `${param.displayName}是必填项`,
          trigger: 'blur',
        });
      }

      if (param.validation) {
        const validation = param.validation;

        // 字符串长度验证
        if (param.type === 'string') {
          if (validation.minLength) {
            paramRules.push({
              min: validation.minLength,
              message: `${param.displayName}长度不能少于${validation.minLength}个字符`,
              trigger: 'blur',
            });
          }
          if (validation.maxLength) {
            paramRules.push({
              max: validation.maxLength,
              message: `${param.displayName}长度不能超过${validation.maxLength}个字符`,
              trigger: 'blur',
            });
          }
          if (validation.pattern) {
            paramRules.push({
              pattern: new RegExp(validation.pattern),
              message: validation.errorMessage || `${param.displayName}格式不正确`,
              trigger: 'blur',
            });
          }
        }

        // 数字范围验证
        if (param.type === 'number') {
          if (validation.min !== undefined) {
            paramRules.push({
              type: 'number',
              min: validation.min,
              message: `${param.displayName}不能小于${validation.min}`,
              trigger: 'blur',
            });
          }
          if (validation.max !== undefined) {
            paramRules.push({
              type: 'number',
              max: validation.max,
              message: `${param.displayName}不能大于${validation.max}`,
              trigger: 'blur',
            });
          }
        }
      }

      if (paramRules.length > 0) {
        rules[param.name] = paramRules;
      }
    });

    return rules;
  });

  // 初始化参数值
  const initializeParameters = () => {
    const values = { ...props.modelValue };
    const jsonStrs: Record<string, string> = {};

    props.parameters.forEach((param) => {
      // 设置默认值
      if (values[param.name] === undefined && param.defaultValue !== undefined) {
        values[param.name] = param.defaultValue;
      }

      // 初始化JSON字符串
      if (param.type === 'object') {
        if (values[param.name]) {
          jsonStrs[param.name] = JSON.stringify(values[param.name], null, 2);
        } else {
          jsonStrs[param.name] = param.uiHint?.placeholder || '{}';
        }
      }
    });

    parameterValues.value = values;
    jsonStrings.value = jsonStrs;

    // 默认展开第一个分组
    if (parameterGroups.value.length > 0) {
      activeGroups.value = [parameterGroups.value[0].name];
    }
  };

  // 验证JSON格式
  const validateJson = (paramName: string) => {
    const jsonStr = jsonStrings.value[paramName];
    if (!jsonStr || jsonStr.trim() === '') {
      parameterValues.value[paramName] = null;
      jsonErrors.value[paramName] = '';
      return;
    }

    try {
      const parsed = JSON.parse(jsonStr);
      parameterValues.value[paramName] = parsed;
      jsonErrors.value[paramName] = '';
    } catch (error: any) {
      jsonErrors.value[paramName] = 'JSON格式错误: ' + error.message;
    }
  };

  // 获取参数样式类
  const getParameterClass = (parameter: Parameter) => {
    const classes = ['parameter-item'];
    if (parameter.uiHint?.width) {
      classes.push(`width-${parameter.uiHint.width}`);
    }
    if (parameter.sensitive) {
      classes.push('sensitive');
    }
    return classes.join(' ');
  };

  // 获取选择框选项
  const getSelectOptions = (parameter: Parameter) => {
    if (!parameter.uiHint?.options) return [];

    return parameter.uiHint.options.map((option) => ({
      label: option.label,
      value: option.value,
      disabled: option.disabled,
    }));
  };

  // 获取分组显示名称
  const getGroupDisplayName = (groupName: string) => {
    const groupNames: Record<string, string> = {
      connection: '连接配置',
      auth: '认证配置',
      advanced: '高级配置',
      basic: '基础配置',
      default: '基本配置',
    };
    return groupNames[groupName] || groupName;
  };

  // 获取分组描述
  const getGroupDescription = (groupName: string) => {
    const descriptions: Record<string, string> = {
      connection: '服务连接相关的配置',
      auth: '认证和安全相关的配置',
      advanced: '高级选项和性能调优',
      basic: '基本功能配置',
      default: '基本配置选项',
    };
    return descriptions[groupName] || '';
  };

  // 获取分组排序
  const getGroupOrder = (groupName: string) => {
    const orders: Record<string, number> = {
      basic: 1,
      connection: 2,
      auth: 3,
      advanced: 4,
      default: 5,
    };
    return orders[groupName] || 10;
  };

  // 验证表单
  const validate = async () => {
    if (!parameterForm.value) return true;

    try {
      await parameterForm.value.validate();
      emit('validate', true, null);
      return true;
    } catch (error) {
      emit('validate', false, error);
      return false;
    }
  };

  // 重置表单
  const resetFields = () => {
    if (parameterForm.value) {
      parameterForm.value.resetFields();
    }
    initializeParameters();
  };

  // 监听参数值变化
  watch(
    parameterValues,
    (newValues) => {
      emit('update:modelValue', { ...newValues });
    },
    { deep: true }
  );

  // 监听props变化
  watch(
    () => props.modelValue,
    (newValue) => {
      parameterValues.value = { ...newValue };
    },
    { deep: true }
  );

  watch(
    () => props.parameters,
    () => {
      nextTick(() => {
        initializeParameters();
      });
    },
    { immediate: true }
  );

  // 暴露方法
  defineExpose({
    validate,
    resetFields,
  });
</script>

<style scoped>
  .mcp-parameter-form {
    width: 100%;
  }

  .group-header {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

  .group-title {
    font-weight: 600;
    font-size: 14px;
  }

  .group-description {
    font-size: 12px;
    color: #999;
    margin-top: 2px;
  }

  .parameter-group-content {
    padding: 16px 0;
  }

  .parameter-label {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .required-mark {
    color: #d03050;
  }

  .info-icon {
    color: #999;
    cursor: help;
  }

  .help-text {
    margin-top: 4px;
  }

  .json-editor {
    width: 100%;
  }

  .json-error {
    margin-top: 4px;
  }

  .option-description {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    margin-left: 8px;
  }

  .parameter-item.width-small :deep(.el-form-item__content) {
    max-width: 200px;
  }

  .parameter-item.width-medium :deep(.el-form-item__content) {
    max-width: 400px;
  }

  .parameter-item.width-full :deep(.el-form-item__content) {
    width: 100%;
  }

  .parameter-item.sensitive {
    position: relative;
  }

  .parameter-item.sensitive::after {
    content: '🔒';
    position: absolute;
    top: 0;
    right: 0;
    font-size: 12px;
    opacity: 0.6;
  }

  .no-parameters {
    padding: 40px 0;
    text-align: center;
  }
</style>
