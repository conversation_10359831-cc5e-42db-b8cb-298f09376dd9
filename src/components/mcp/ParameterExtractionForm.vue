<template>
  <div class="parameter-extraction-form">
    <n-card title="参数提取配置" size="small">
      <template #header-extra>
        <n-tooltip>
          <template #trigger>
            <n-icon size="16" class="text-gray-400">
              <InformationCircleOutline />
            </n-icon>
          </template>
          配置如何从用户输入中自动提取参数值
        </n-tooltip>
      </template>

      <n-form :model="extractionConfig" label-placement="top">
        <!-- 是否启用提取 -->
        <n-form-item label="启用输入提取">
          <n-switch
            v-model:value="extractionConfig.extractFromInput"
            @update:value="handleExtractionToggle"
          >
            <template #checked>启用</template>
            <template #unchecked>禁用</template>
          </n-switch>
        </n-form-item>

        <!-- 提取配置 -->
        <div v-if="extractionConfig.extractFromInput" class="extraction-config">
          <!-- 提取方法选择 -->
          <n-form-item label="提取方法">
            <n-checkbox-group v-model:value="selectedMethods">
              <n-space>
                <n-checkbox value="keywords" label="关键词匹配" />
                <n-checkbox value="pattern" label="正则表达式" />
                <n-checkbox value="ai" label="AI智能提取" />
              </n-space>
            </n-checkbox-group>
          </n-form-item>

          <!-- 关键词配置 -->
          <n-form-item v-if="selectedMethods.includes('keywords')" label="关键词列表">
            <n-dynamic-tags
              v-model:value="extractionConfig.keywords"
              placeholder="输入关键词后按回车添加"
            />
            <template #suffix>
              <n-tooltip>
                <template #trigger>
                  <n-icon size="14" class="text-gray-400">
                    <InformationCircleOutline />
                  </n-icon>
                </template>
                系统会在用户输入中查找这些关键词，并提取相邻的值
              </n-tooltip>
            </template>
          </n-form-item>

          <!-- 正则表达式配置 -->
          <n-form-item v-if="selectedMethods.includes('pattern')" label="正则表达式">
            <n-input
              v-model:value="extractionConfig.pattern"
              placeholder="例如: (?:企业|公司)[:：]?\s*([\\u4e00-\\u9fa5a-zA-Z0-9\\s]+)"
              type="textarea"
              :rows="2"
            />
            <template #suffix>
              <n-tooltip>
                <template #trigger>
                  <n-icon size="14" class="text-gray-400">
                    <InformationCircleOutline />
                  </n-icon>
                </template>
                使用正则表达式精确匹配参数值，第一个捕获组将作为参数值
              </n-tooltip>
            </template>
          </n-form-item>

          <!-- AI提取配置 -->
          <n-form-item v-if="selectedMethods.includes('ai')" label="AI提取提示词">
            <n-input
              v-model:value="extractionConfig.extractionPrompt"
              placeholder="例如: 请提取用户想要查询的企业名称，只返回企业名称"
              type="textarea"
              :rows="3"
            />
            <template #suffix>
              <n-tooltip>
                <template #trigger>
                  <n-icon size="14" class="text-gray-400">
                    <InformationCircleOutline />
                  </n-icon>
                </template>
                AI模型将根据这个提示词从用户输入中提取参数值
              </n-tooltip>
            </template>
          </n-form-item>

          <!-- 默认值 -->
          <n-form-item label="默认值">
            <n-input
              v-model:value="extractionConfig.defaultValue"
              placeholder="当无法提取到值时使用的默认值"
            />
          </n-form-item>
        </div>

        <!-- 测试区域 -->
        <n-divider title-placement="left">提取测试</n-divider>

        <n-form-item label="测试输入">
          <n-input
            v-model:value="testInput"
            placeholder="输入一段文本来测试参数提取效果"
            type="textarea"
            :rows="3"
          />
        </n-form-item>

        <n-form-item>
          <n-space>
            <n-button @click="testExtraction" :loading="testing">测试提取</n-button>
            <n-button @click="clearTest">清空</n-button>
          </n-space>
        </n-form-item>

        <!-- 测试结果 -->
        <n-form-item v-if="testResult" label="提取结果">
          <n-alert :type="testResult.success ? 'success' : 'warning'">
            <template #header>
              {{ testResult.success ? '提取成功' : '提取失败' }}
            </template>
            <div v-if="testResult.success">
              <strong>提取值:</strong> {{ testResult.value || '(空值)' }}
            </div>
            <div v-else> <strong>原因:</strong> {{ testResult.message }} </div>
          </n-alert>
        </n-form-item>
      </n-form>
    </n-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, watch } from 'vue';
  import { InformationCircleOutline } from '@vicons/ionicons5';

  interface ExtractionConfig {
    extractFromInput: boolean;
    keywords: string[];
    pattern: string;
    extractionPrompt: string;
    defaultValue: string;
  }

  interface TestResult {
    success: boolean;
    value?: string;
    message?: string;
  }

  // Props
  interface Props {
    parameter: any;
  }

  const props = defineProps<Props>();

  // Emits
  const emit = defineEmits<{
    'update:extraction-config': [config: any];
  }>();

  // 状态
  const extractionConfig = ref<ExtractionConfig>({
    extractFromInput: false,
    keywords: [],
    pattern: '',
    extractionPrompt: '',
    defaultValue: '',
  });

  const selectedMethods = ref<string[]>([]);
  const testInput = ref('');
  const testResult = ref<TestResult | null>(null);
  const testing = ref(false);

  // 初始化配置
  const initConfig = () => {
    if (props.parameter?.validation) {
      const validation = props.parameter.validation;
      extractionConfig.value = {
        extractFromInput: validation.extractFromInput || false,
        keywords: validation.keywords || [],
        pattern: validation.pattern || '',
        extractionPrompt: validation.extractionPrompt || '',
        defaultValue: validation.defaultValue || '',
      };

      // 初始化选中的方法
      selectedMethods.value = [];
      if (extractionConfig.value.keywords.length > 0) {
        selectedMethods.value.push('keywords');
      }
      if (extractionConfig.value.pattern) {
        selectedMethods.value.push('pattern');
      }
      if (extractionConfig.value.extractionPrompt) {
        selectedMethods.value.push('ai');
      }
    }
  };

  // 监听参数变化
  watch(() => props.parameter, initConfig, { immediate: true });

  // 监听配置变化
  watch(
    extractionConfig,
    (newConfig) => {
      emit('update:extraction-config', {
        extractFromInput: newConfig.extractFromInput,
        keywords: newConfig.keywords,
        pattern: newConfig.pattern,
        extractionPrompt: newConfig.extractionPrompt,
        defaultValue: newConfig.defaultValue,
      });
    },
    { deep: true }
  );

  // 处理提取开关
  const handleExtractionToggle = (enabled: boolean) => {
    if (!enabled) {
      selectedMethods.value = [];
      extractionConfig.value.keywords = [];
      extractionConfig.value.pattern = '';
      extractionConfig.value.extractionPrompt = '';
    }
  };

  // 测试提取
  const testExtraction = async () => {
    if (!testInput.value.trim()) {
      testResult.value = {
        success: false,
        message: '请输入测试文本',
      };
      return;
    }

    testing.value = true;
    testResult.value = null;

    try {
      // 模拟提取逻辑
      let extractedValue = null;

      // 1. 关键词匹配
      if (
        selectedMethods.value.includes('keywords') &&
        extractionConfig.value.keywords.length > 0
      ) {
        extractedValue = testKeywordExtraction(testInput.value, extractionConfig.value.keywords);
      }

      // 2. 正则表达式
      if (
        !extractedValue &&
        selectedMethods.value.includes('pattern') &&
        extractionConfig.value.pattern
      ) {
        extractedValue = testPatternExtraction(testInput.value, extractionConfig.value.pattern);
      }

      // 3. 使用默认值
      if (!extractedValue && extractionConfig.value.defaultValue) {
        extractedValue = extractionConfig.value.defaultValue;
      }

      testResult.value = {
        success: !!extractedValue,
        value: extractedValue,
        message: extractedValue ? undefined : '未能提取到有效值',
      };
    } catch (error) {
      testResult.value = {
        success: false,
        message: `提取失败: ${error}`,
      };
    } finally {
      testing.value = false;
    }
  };

  // 关键词提取测试
  const testKeywordExtraction = (input: string, keywords: string[]) => {
    const lowerInput = input.toLowerCase();

    for (const keyword of keywords) {
      const lowerKeyword = keyword.toLowerCase();
      const index = lowerInput.indexOf(lowerKeyword);

      if (index !== -1) {
        const afterKeyword = input.substring(index + keyword.length).trim();

        if (afterKeyword.startsWith('：') || afterKeyword.startsWith(':')) {
          const value = afterKeyword.substring(1).trim();
          const words = value.split(/[\s,，。；;]/);
          if (words.length > 0 && words[0].trim()) {
            return words[0].trim();
          }
        }
      }
    }

    return null;
  };

  // 正则表达式提取测试
  const testPatternExtraction = (input: string, pattern: string) => {
    try {
      const regex = new RegExp(pattern, 'i');
      const match = input.match(regex);

      if (match) {
        return match.length > 1 ? match[1] : match[0];
      }
    } catch (error) {
      console.error('正则表达式错误:', error);
    }

    return null;
  };

  // 清空测试
  const clearTest = () => {
    testInput.value = '';
    testResult.value = null;
  };

  // 初始化
  initConfig();
</script>

<style scoped>
  .parameter-extraction-form {
    margin-top: 16px;
  }

  .extraction-config {
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 6px;
    margin-top: 12px;
  }
</style>
