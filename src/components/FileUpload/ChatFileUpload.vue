<template>
  <div class="chat-file-upload">
    <!-- 文件上传区域 -->
    <div v-if="uploadedFiles.length === 0" class="upload-area">
      <n-upload
        :custom-request="handleUpload"
        :accept="accept"
        :multiple="maxFiles > 1"
        :max="maxFiles"
        :show-file-list="false"
        @remove="handleRemove"
      >
        <n-upload-dragger class="upload-dragger">
          <div class="upload-content">
            <n-icon size="28" :depth="3" class="upload-icon">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
              </svg>
            </n-icon>
            <n-text class="upload-text">
              点击或拖拽文件到此区域上传
            </n-text>
            <n-text depth="3" class="upload-hint">
              支持图片、PDF、Word文档等格式，最大{{ maxSize }}MB
            </n-text>
          </div>
        </n-upload-dragger>
      </n-upload>
    </div>

    <!-- 已上传文件列表 -->
    <div v-else class="uploaded-files">
      <div class="files-header">
        <n-text class="files-title">已上传文件 ({{ uploadedFiles.length }}/{{ maxFiles }})</n-text>
        <n-button
          v-if="uploadedFiles.length < maxFiles"
          size="small"
          type="primary"
          ghost
          @click="triggerUpload"
          :loading="uploading"
          class="add-more-btn"
        >
          <template #icon>
            <n-icon>
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z" />
              </svg>
            </n-icon>
          </template>
          添加文件
        </n-button>
      </div>
      
      <div class="files-list">
        <div
          v-for="(file, index) in uploadedFiles"
          :key="index"
          class="file-item"
        >
          <div class="file-info">
            <n-icon class="file-icon" :color="getFileIconColor(file.type)">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" :d="getFileIconPath(file.type)" />
              </svg>
            </n-icon>
            <div class="file-details">
              <n-text class="file-name" :title="file.name">{{ file.name }}</n-text>
              <n-text depth="3" class="file-size">{{ formatFileSize(file.size) }}</n-text>
            </div>
          </div>
          <n-button
            size="small"
            quaternary
            circle
            @click="removeFile(index)"
            class="remove-btn"
          >
            <template #icon>
              <n-icon>
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
                </svg>
              </n-icon>
            </template>
          </n-button>
        </div>
      </div>

      <!-- 隐藏的上传组件用于添加更多文件 -->
      <n-upload
        ref="hiddenUploadRef"
        :custom-request="handleUpload"
        :accept="accept"
        :multiple="maxFiles > 1"
        :max="maxFiles"
        :show-file-list="false"
        style="display: none"
        @remove="handleRemove"
      >
        <div></div>
      </n-upload>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { useMessage, UploadCustomRequestOptions } from 'naive-ui';
import { uploadApi } from '@/api/aigc/oss';

interface FileInfo {
  name: string;
  url: string;
  size: number;
  type: string;
}

interface Props {
  maxFiles?: number;
  maxSize?: number; // MB
  accept?: string;
  disabled?: boolean;
}

interface Emits {
  (e: 'change', files: FileInfo[]): void;
  (e: 'upload-success', file: FileInfo): void;
  (e: 'upload-error', error: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  maxFiles: 5,
  maxSize: 10,
  accept: '*',
  disabled: false,
});

const emit = defineEmits<Emits>();
const message = useMessage();

const hiddenUploadRef = ref();
const uploadedFiles = ref<FileInfo[]>([]);
const uploading = ref(false);

// 触发隐藏的上传组件
const triggerUpload = () => {
  if (hiddenUploadRef.value) {
    const input = hiddenUploadRef.value.$el.querySelector('input[type="file"]');
    if (input) {
      input.click();
    }
  }
};

// 文件上传处理
const handleUpload = ({ file, onFinish, onError, onProgress }: UploadCustomRequestOptions) => {
  console.log('开始上传文件:', file.name);
  
  // 检查文件数量
  if (uploadedFiles.value.length >= props.maxFiles) {
    message.error(`最多只能上传${props.maxFiles}个文件`);
    onError();
    return;
  }

  // 检查文件大小
  if (props.maxSize && file.file!.size / 1024 / 1024 > props.maxSize) {
    message.error(`文件大小不能超过${props.maxSize}MB`);
    onError();
    return;
  }

  uploading.value = true;

  // 使用uploadApi上传
  uploadApi(
    {
      file: file.file,
    },
    (progressEvent) => {
      if (progressEvent.total) {
        const percent = Math.round((progressEvent.loaded * 100) / Number(progressEvent.total));
        console.log('上传进度:', percent + '%');
        onProgress({
          percent: percent,
        });
      }
    }
  )
    .then((res) => {
      console.log('上传成功响应:', res);
      
      const fileInfo: FileInfo = {
        name: res.data?.originalFilename || file.name,
        url: res.data?.url || res.url,
        size: file.file!.size,
        type: file.file!.type,
      };

      uploadedFiles.value.push(fileInfo);
      emit('change', uploadedFiles.value);
      emit('upload-success', fileInfo);
      
      message.success('文件上传成功');
      onFinish();
    })
    .catch((err) => {
      console.error('上传失败:', err);
      const errorMsg = err.message || '文件上传失败';
      message.error(errorMsg);
      emit('upload-error', errorMsg);
      onError();
    })
    .finally(() => {
      uploading.value = false;
    });
};

// 处理文件移除
const handleRemove = ({ file }) => {
  console.log('移除文件:', file);
  const index = uploadedFiles.value.findIndex(f => f.name === file.name);
  if (index > -1) {
    removeFile(index);
  }
  return true;
};

// 移除文件
const removeFile = (index: number) => {
  uploadedFiles.value.splice(index, 1);
  emit('change', uploadedFiles.value);
};

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

// 获取文件图标路径
const getFileIconPath = (type: string) => {
  if (type.startsWith('image/')) {
    return 'M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z';
  }
  if (type.includes('pdf')) {
    return 'M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z';
  }
  if (type.includes('word') || type.includes('document')) {
    return 'M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z';
  }
  if (type.includes('excel') || type.includes('spreadsheet')) {
    return 'M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z';
  }
  // 默认文件图标
  return 'M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z';
};

// 获取文件图标颜色
const getFileIconColor = (type: string) => {
  if (type.startsWith('image/')) return '#52c41a';
  if (type.includes('pdf')) return '#f5222d';
  if (type.includes('word') || type.includes('document')) return '#1890ff';
  if (type.includes('excel') || type.includes('spreadsheet')) return '#52c41a';
  return '#8c8c8c';
};

// 清空所有文件
const clearFiles = () => {
  uploadedFiles.value = [];
  emit('change', uploadedFiles.value);
};

// 获取文件URL列表
const getFileUrls = () => {
  return uploadedFiles.value.map(file => file.url);
};

// 暴露方法给父组件
defineExpose({
  clearFiles,
  getFileUrls,
  uploadedFiles: computed(() => uploadedFiles.value),
});
</script>

<style lang="less" scoped>
.chat-file-upload {
  .upload-area {
    .upload-dragger {
      border: 2px dashed #d9d9d9;
      border-radius: 8px;
      background: #fafafa;
      transition: all 0.3s;
      
      &:hover {
        border-color: #40a9ff;
        background: #f0f8ff;
      }
    }
    
    .upload-content {
      padding: 16px;
      text-align: center;
      
      .upload-icon {
        margin-bottom: 8px;
      }
      
      .upload-text {
        display: block;
        margin-bottom: 4px;
        font-size: 14px;
        font-weight: 500;
      }
      
      .upload-hint {
        display: block;
        font-size: 12px;
      }
    }
  }
  
  .uploaded-files {
    .files-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      
      .files-title {
        font-weight: 500;
        font-size: 14px;
      }
      
      .add-more-btn {
        font-size: 12px;
      }
    }
    
    .files-list {
      .file-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        margin-bottom: 8px;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        transition: all 0.2s;
        
        &:hover {
          border-color: #40a9ff;
          background: #f0f8ff;
        }
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .file-info {
          display: flex;
          align-items: center;
          flex: 1;
          min-width: 0;
          
          .file-icon {
            margin-right: 8px;
            flex-shrink: 0;
          }
          
          .file-details {
            flex: 1;
            min-width: 0;
            
            .file-name {
              display: block;
              font-size: 13px;
              font-weight: 500;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              max-width: 200px;
            }
            
            .file-size {
              display: block;
              font-size: 11px;
              margin-top: 2px;
            }
          }
        }
        
        .remove-btn {
          flex-shrink: 0;
          margin-left: 8px;
          
          &:hover {
            color: #ff4d4f;
          }
        }
      }
    }
  }
}
</style>
