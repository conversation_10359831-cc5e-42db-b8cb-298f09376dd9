<!--
  - Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
  -
  - Licensed under the GNU Affero General Public License, Version 3 (the "License");
  - you may not use this file except in compliance with the License.
  - You may obtain a copy of the License at
  -
  -     https://www.gnu.org/licenses/agpl-3.0.html
  -
  - Unless required by applicable law or agreed to in writing, software
  - distributed under the License is distributed on an "AS IS" BASIS,
  - WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  - See the License for the specific language governing permissions and
  - limitations under the License.
  -->

<template>
  <div class="file-upload-container">
    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInputRef"
      type="file"
      :accept="accept"
      :multiple="maxFiles > 1"
      style="display: none"
      @change="handleFileSelect"
    />

    <!-- 文件上传按钮 -->
    <n-button
      :loading="uploading"
      size="small"
      quaternary
      circle
      :disabled="disabled || uploadedFiles.length >= maxFiles"
      @click="triggerFileSelect"
      title="上传文件"
    >
      <template #icon>
        <SvgIcon
          :icon="uploading ? 'mdi:loading' : 'mdi:attachment'"
          :class="{ 'animate-spin': uploading }"
        />
      </template>
    </n-button>

    <!-- 已上传文件列表 -->
    <div v-if="uploadedFiles.length > 0" class="uploaded-files mt-2">
      <div
        v-for="(file, index) in uploadedFiles"
        :key="index"
        class="file-item flex items-center justify-between p-2 bg-gray-50 rounded mb-1"
      >
        <div class="file-info flex items-center flex-1 min-w-0">
          <SvgIcon 
            :icon="getFileIcon(file.type)" 
            class="mr-2 text-blue-500 flex-shrink-0"
          />
          <span class="file-name truncate text-sm" :title="file.name">
            {{ file.name }}
          </span>
          <span class="file-size text-xs text-gray-500 ml-2 flex-shrink-0">
            {{ formatFileSize(file.size) }}
          </span>
        </div>
        <n-button
          size="tiny"
          quaternary
          circle
          @click="removeFile(index)"
        >
          <template #icon>
            <SvgIcon icon="mdi:close" />
          </template>
        </n-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { useMessage } from 'naive-ui';
import SvgIcon from '@/components/SvgIcon/index.vue';
import { uploadApi } from '@/api/aigc/oss';

interface FileInfo {
  name: string;
  url: string;
  size: number;
  type: string;
  originalName: string;
}

interface Props {
  maxFiles?: number;
  maxSize?: number; // MB
  accept?: string;
  disabled?: boolean;
}

interface Emits {
  (e: 'change', files: FileInfo[]): void;
  (e: 'upload-success', file: FileInfo): void;
  (e: 'upload-error', error: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  maxFiles: 5,
  maxSize: 10,
  accept: '*',
  disabled: false,
});

const emit = defineEmits<Emits>();
const message = useMessage();

const fileInputRef = ref();
const uploadedFiles = ref<FileInfo[]>([]);
const uploading = ref(false);

const canUpload = computed(() => {
  return uploadedFiles.value.length < props.maxFiles && !props.disabled;
});

// 文件类型图标映射
const getFileIcon = (type: string) => {
  if (type.startsWith('image/')) return 'mdi:image';
  if (type.startsWith('video/')) return 'mdi:video';
  if (type.startsWith('audio/')) return 'mdi:music';
  if (type.includes('pdf')) return 'mdi:file-pdf-box';
  if (type.includes('word') || type.includes('document')) return 'mdi:file-word-box';
  if (type.includes('excel') || type.includes('spreadsheet')) return 'mdi:file-excel-box';
  if (type.includes('powerpoint') || type.includes('presentation')) return 'mdi:file-powerpoint-box';
  if (type.includes('text')) return 'mdi:file-document';
  return 'mdi:file';
};

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

// 触发文件选择
const triggerFileSelect = () => {
  console.log('触发文件选择');
  if (fileInputRef.value) {
    console.log('点击文件输入框');
    fileInputRef.value.click();
  } else {
    console.error('文件输入框引用不存在');
  }
};

// 处理文件选择
const handleFileSelect = (event: Event) => {
  console.log('文件选择事件触发');
  const target = event.target as HTMLInputElement;
  const files = target.files;

  console.log('选择的文件数量:', files?.length);

  if (!files || files.length === 0) {
    console.log('没有选择文件');
    return;
  }

  // 处理选中的文件
  Array.from(files).forEach(file => {
    console.log('处理文件:', file.name, file.size, file.type);
    if (validateFile(file)) {
      uploadFile(file);
    }
  });

  // 清空input的值，允许重复选择相同文件
  target.value = '';
};

// 文件验证
const validateFile = (file: File): boolean => {
  // 检查文件数量
  if (uploadedFiles.value.length >= props.maxFiles) {
    message.error(`最多只能上传${props.maxFiles}个文件`);
    return false;
  }

  // 检查文件大小
  if (props.maxSize && file.size / 1024 / 1024 > props.maxSize) {
    message.error(`文件大小不能超过${props.maxSize}MB`);
    return false;
  }

  return true;
};

// 上传文件
const uploadFile = async (file: File) => {
  uploading.value = true;

  try {
    const formData = new FormData();
    formData.append('file', file);

    const response = await uploadApi(formData);

    if (response.code === 200) {
      const fileInfo: FileInfo = {
        name: response.data.originalFilename || file.name,
        url: response.data.url,
        size: file.size,
        type: file.type,
        originalName: file.name,
      };

      uploadedFiles.value.push(fileInfo);
      emit('change', uploadedFiles.value);
      emit('upload-success', fileInfo);

      message.success('文件上传成功');
    } else {
      throw new Error(response.message || '上传失败');
    }
  } catch (error) {
    console.error('文件上传失败:', error);
    const errorMsg = error.message || '文件上传失败';
    message.error(errorMsg);
    emit('upload-error', errorMsg);
  } finally {
    uploading.value = false;
  }
};

// 移除文件
const removeFile = (index: number) => {
  uploadedFiles.value.splice(index, 1);
  emit('change', uploadedFiles.value);
};



// 清空所有文件
const clearFiles = () => {
  uploadedFiles.value = [];
  emit('change', uploadedFiles.value);
};

// 获取文件URL列表
const getFileUrls = () => {
  return uploadedFiles.value.map(file => file.url);
};

// 暴露方法给父组件
defineExpose({
  clearFiles,
  getFileUrls,
  triggerFileSelect,
  uploadedFiles: computed(() => uploadedFiles.value),
});
</script>

<style lang="less" scoped>
.file-upload-container {
  .uploaded-files {
    max-height: 120px;
    overflow-y: auto;
    
    .file-item {
      border: 1px solid #e5e7eb;
      transition: all 0.2s;
      
      &:hover {
        border-color: #3b82f6;
        background-color: #f8fafc;
      }
    }
    
    .file-name {
      max-width: 150px;
    }
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
