<template>
  <div class="naive-file-upload">
    <!-- 使用n-upload组件，完全参照app/edit.vue的实现 -->
    <n-upload
      v-model:file-list="fileList"
      :custom-request="handleUpload"
      :accept="props.accept"
      directory-dnd
      :list-type="isImageOnly ? 'image-card' : 'text'"
      @remove="handleRemove"
    >
      <n-button
        :loading="uploading"
        size="small"
        quaternary
        circle
        :disabled="disabled"
        title="上传文件"
      >
        <template #icon>
          <n-icon>
            <svg viewBox="0 0 24 24">
              <path
                fill="currentColor"
                d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"
              />
            </svg>
          </n-icon>
        </template>
      </n-button>
    </n-upload>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { useMessage, UploadCustomRequestOptions } from 'naive-ui';
  import { uploadApi } from '@/api/aigc/oss';

  interface FileInfo {
    name: string;
    url: string;
    size: number;
    type: string;
  }

  interface Props {
    maxFiles?: number;
    maxSize?: number; // MB
    accept?: string;
    disabled?: boolean;
  }

  interface Emits {
    (e: 'change', files: FileInfo[]): void;

    (e: 'upload-success', file: FileInfo): void;

    (e: 'upload-error', error: string): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    maxFiles: 5,
    maxSize: 10,
    accept: '*',
    disabled: false,
  });

  const emit = defineEmits<Emits>();
  const message = useMessage();

  const fileList = ref<any[]>([]);
  const uploadedFiles = ref<FileInfo[]>([]);
  const uploading = ref(false);

  // 判断是否只接受图片文件
  const isImageOnly = computed(() => {
    const accept = props.accept.toLowerCase();
    return accept.includes('image/*') && !accept.includes('application/') && !accept.includes('.doc') && !accept.includes('.pdf') && !accept.includes('.txt');
  });

  // 文件上传处理 - 完全参照app/edit.vue的handleImport实现
  const handleUpload = ({ file, onFinish, onError, onProgress }: UploadCustomRequestOptions) => {
    console.log('开始上传文件:', file.name);

    // 检查文件数量
    if (uploadedFiles.value.length >= props.maxFiles) {
      message.error(`最多只能上传${props.maxFiles}个文件`);
      onError();
      return;
    }

    // 检查文件大小
    if (props.maxSize && file.file!.size / 1024 / 1024 > props.maxSize) {
      message.error(`文件大小不能超过${props.maxSize}MB`);
      onError();
      return;
    }

    uploading.value = true;

    // 使用与app/edit.vue完全相同的uploadApi调用方式
    uploadApi(
      {
        file: file.file, // 直接传递file对象
      },
      (progressEvent) => {
        if (progressEvent.total) {
          const percent = Math.round((progressEvent.loaded * 100) / Number(progressEvent.total));
          console.log('上传进度:', percent + '%');
          onProgress({
            percent: percent,
          });
        }
      }
    )
      .then((res) => {
        console.log('上传成功响应:', res);

        const fileInfo: FileInfo = {
          name: res.data?.originalFilename || file.name,
          url: res.data?.url || res.url,
          size: file.file!.size,
          type: file.file!.type,
        };
        uploadedFiles.value.push(fileInfo);

        // 同步更新fileList，确保n-upload组件显示正确
        //fileList.value.push(fileInfo);

        emit('change', uploadedFiles.value);
        emit('upload-success', fileInfo);

        message.success('文件上传成功');
        onFinish();
      })
      .catch((err) => {
        console.error('上传失败:', err);
        const errorMsg = err.message || '文件上传失败';
        message.error(errorMsg);
        emit('upload-error', errorMsg);
        onError();
      })
      .finally(() => {
        uploading.value = false;
      });
  };

  // 处理文件移除
  const handleRemove = ({ file }) => {
    console.log('移除文件:', file);

    const index = uploadedFiles.value.findIndex((f) => f.name === file.name);
    const fileIndex = fileList.value.findIndex((f) => f.name === file.name);
    if (index > -1) {
      uploadedFiles.value.splice(index, 1);
      emit('change', uploadedFiles.value);
    }
    if (fileIndex > -1) {
      fileList.value.splice(fileIndex, 1);
    }
    return true;
  };

  // 清空所有文件
  const clearFiles = () => {
    uploadedFiles.value = [];
    fileList.value = [];
    emit('change', uploadedFiles.value);
  };

  // 获取文件URL列表
  const getFileUrls = () => {
    return uploadedFiles.value.map((file) => file.url);
  };

  // 暴露方法给父组件
  defineExpose({
    clearFiles,
    getFileUrls,
    uploadedFiles: computed(() => uploadedFiles.value),
  });
</script>

<style lang="less" scoped>
  .naive-file-upload {
    .n-upload {
      width: 100%;
    }
  }
</style>
