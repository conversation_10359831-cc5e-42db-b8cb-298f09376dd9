<!--
  - Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
  -
  - Licensed under the GNU Affero General Public License, Version 3 (the "License");
  - you may not use this file except in compliance with the License.
  - You may obtain a copy of the License at
  -
  -     https://www.gnu.org/licenses/agpl-3.0.html
  -
  - Unless required by applicable law or agreed to in writing, software
  - distributed under the License is distributed on an "AS IS" BASIS,
  - WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  - See the License for the specific language governing permissions and
  - limitations under the License.
  -->

<script lang="ts" setup>
  import { onMounted } from 'vue';
  import SvgIcon from '@/components/SvgIcon/index.vue';
  import { useChatStore } from '@/views/chat/store/useChatStore';

  import { getServiceByIds } from '@/api/aigc/mcp';

  const chatStore = useChatStore();

  function getServiceById(ids: string) {
    if (ids) {
      getServiceByIds({ ids: ids }).then((res) => {
        chatStore.mcpServices = res;
      });
    }
  }

  onMounted(() => {
    if (chatStore.mcpServiceIds.length > 0) {
      getServiceById(chatStore.mcpServiceIds.toString());
    }
  });

  // 获取服务图标
  function getServiceIcon(service: any) {
    switch (service.category?.toLowerCase()) {
      case 'image':
        return 'mdi:image-outline';
      case 'search':
        return 'mdi:magnify';
      case 'text':
        return 'mdi:text-box-outline';
      case 'file':
        return 'mdi:file-outline';
      case 'data':
        return 'mdi:database-outline';
      default:
        return 'mdi:api';
    }
  }

  // 获取服务状态颜色
  function getServiceStatusColor(status: string) {
    switch (status?.toLowerCase()) {
      case 'healthy':
        return 'success';
      case 'unhealthy':
        return 'error';
      default:
        return 'warning';
    }
  }
</script>

<template>
  <div v-if="chatStore.mcpServiceIds.length > 0" class="mcp-service-status">
    <div class="flex items-center gap-2 mb-2">
      <SvgIcon class="text-blue-500 text-sm" icon="mdi:api" />
      <span class="text-xs text-gray-600 font-medium">MCP服务已启用</span>
      <n-tag size="tiny" type="success"> {{ chatStore.mcpServiceIds.length }}个服务</n-tag>
    </div>

    <div class="flex flex-wrap gap-1">
      <n-tooltip v-for="service in chatStore.mcpServices" :key="service.id" placement="top">
        <template #trigger>
          <n-tag
            size="small"
            :type="getServiceStatusColor(service.healthStatus)"
            class="cursor-pointer"
          >
            <div class="flex items-center gap-1">
              <SvgIcon class="text-xs" :icon="getServiceIcon(service)" />
              <span class="text-xs">{{ service.displayName || service.name }}</span>
            </div>
          </n-tag>
        </template>

        <div class="max-w-xs">
          <div class="font-medium mb-1">{{ service.displayName || service.name }}</div>
          <div class="text-xs text-gray-600 mb-2">
            {{ service.description || '暂无描述' }}
          </div>
          <div class="flex items-center gap-2 text-xs">
            <n-tag size="tiny" :type="service.category === 'builtin' ? 'info' : 'default'">
              {{ service.category === 'builtin' ? '内置' : '外部' }}
            </n-tag>
            <span>{{ service.type || 'HTTP' }}</span>
            <n-badge
              :type="getServiceStatusColor(service.healthStatus)"
              :value="service.healthStatus === 'healthy' ? '正常' : '异常'"
              size="small"
            />
          </div>
          <div v-if="service.tools" class="text-xs text-gray-500 mt-1">
            工具数量: {{ JSON.parse(service.tools || '[]').length }}
          </div>
        </div>
      </n-tooltip>
    </div>
  </div>
</template>

<style lang="less" scoped>
  .mcp-service-status {
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    margin-bottom: 8px;
  }

  ::v-deep(.n-tag) {
    transition: all 0.2s ease;
  }

  ::v-deep(.n-tag:hover) {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
</style>
