/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

.scroll-y-transition {
  .transition-default();

  &-enter-from,
  &-leave-to {
    opacity: 0;
  }

  &-enter-from {
    transform: translateY(-15px);
  }

  &-leave-to {
    transform: translateY(15px);
  }
}

.scroll-y-reverse-transition {
  .transition-default();

  &-enter-from,
  &-leave-to {
    opacity: 0;
  }

  &-enter-from {
    transform: translateY(15px);
  }

  &-leave-to {
    transform: translateY(-15px);
  }
}

.scroll-x-transition {
  .transition-default();

  &-enter-from,
  &-leave-to {
    opacity: 0;
  }

  &-enter-from {
    transform: translateX(-15px);
  }

  &-leave-to {
    transform: translateX(15px);
  }
}

.scroll-x-reverse-transition {
  .transition-default();

  &-enter-from,
  &-leave-to {
    opacity: 0;
  }

  &-enter-from {
    transform: translateX(15px);
  }

  &-leave-to {
    transform: translateX(-15px);
  }
}
