#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 65536 bytes. Error detail: Failed to commit metaspace.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (virtualSpaceNode.cpp:110), pid=24432, tid=22324
#
# JRE version: OpenJDK Runtime Environment JBR-17.0.14*******.22-nomod (17.0.14+1) (build 17.0.14+1-b1367.22)
# Java VM: OpenJDK 64-Bit Server VM JBR-17.0.14*******.22-nomod (17.0.14+1-b1367.22, mixed mode, emulated-client, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:24911,suspend=y,server=n -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2025.1\captureAgent\debugger-agent.jar=file:///C:/Users/<USER>/AppData/Local/Temp/capture13192552871307146871.props -agentpath:C:\Users\<USER>\AppData\Local\Temp\idea_libasyncProfiler_dll_temp_folder9909\libasyncProfiler.dll=version,jfr,event=wall,interval=10ms,cstack=no,file=C:\Users\<USER>\IdeaSnapshots\LangChatApp_2025_08_12_192341.jfr,dbghelppath=C:\Users\<USER>\AppData\Local\Temp\idea_dbghelp_dll_temp_folder5\dbghelp.dll,log=C:\Users\<USER>\AppData\Local\Temp\LangChatApp_2025_08_12_192341.jfr.log.txt,logLevel=DEBUG -XX:TieredStopAtLevel=1 -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dmanagement.endpoints.jmx.exposure.include=* -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 cn.tycoding.langchat.LangChatApp

Host: Intel(R) Xeon(R) CPU E5-2676 v3 @ 2.40GHz, 24 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.3636)
Time: Tue Aug 12 19:23:42 2025  Windows 10 , 64 bit Build 19041 (10.0.19041.3636) elapsed time: 0.950549 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x00000202597aea50):  JavaThread "main" [_thread_in_vm, id=22324, stack(0x0000002e54b00000,0x0000002e54c00000)]

Stack: [0x0000002e54b00000,0x0000002e54c00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x68b969]

Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  java.lang.ClassLoader.defineClass1(Ljava/lang/ClassLoader;Ljava/lang/String;[BIILjava/security/ProtectionDomain;Ljava/lang/String;)Ljava/lang/Class;+0 java.base@17.0.14
j  java.lang.ClassLoader.defineClass(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class;+27 java.base@17.0.14
j  java.security.SecureClassLoader.defineClass(Ljava/lang/String;[BIILjava/security/CodeSource;)Ljava/lang/Class;+12 java.base@17.0.14
j  jdk.internal.loader.BuiltinClassLoader.defineClass(Ljava/lang/String;Ljdk/internal/loader/Resource;)Ljava/lang/Class;+117 java.base@17.0.14
j  jdk.internal.loader.BuiltinClassLoader.findClassOnClassPathOrNull(Ljava/lang/String;)Ljava/lang/Class;+37 java.base@17.0.14
j  jdk.internal.loader.BuiltinClassLoader.loadClassOrNull(Ljava/lang/String;Z)Ljava/lang/Class;+111 java.base@17.0.14
j  jdk.internal.loader.BuiltinClassLoader.loadClass(Ljava/lang/String;Z)Ljava/lang/Class;+3 java.base@17.0.14
j  jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(Ljava/lang/String;Z)Ljava/lang/Class;+36 java.base@17.0.14
j  java.lang.ClassLoader.loadClass(Ljava/lang/String;)Ljava/lang/Class;+3 java.base@17.0.14
v  ~StubRoutines::call_stub
j  reactor.util.Loggers$ConsoleLoggerFactory.apply(Ljava/lang/String;)Lreactor/util/Logger;+58
j  reactor.util.Loggers$ConsoleLoggerFactory.apply(Ljava/lang/Object;)Ljava/lang/Object;+5
j  reactor.util.Loggers.useConsoleLoggers()V+21
j  reactor.util.Loggers.resetLoggerFactory()V+19
j  reactor.util.Loggers.<clinit>()V+0
v  ~StubRoutines::call_stub
j  reactor.core.publisher.Hooks.<clinit>()V+19
v  ~StubRoutines::call_stub
j  java.lang.Class.forName0(Ljava/lang/String;ZLjava/lang/ClassLoader;Ljava/lang/Class;)Ljava/lang/Class;+0 java.base@17.0.14
j  java.lang.Class.forName(Ljava/lang/String;ZLjava/lang/ClassLoader;)Ljava/lang/Class;+43 java.base@17.0.14
v  ~StubRoutines::call_stub
j  com.intellij.rt.debugger.agent.DebuggerAgent.premain(Ljava/lang/String;Ljava/lang/instrument/Instrumentation;)V+0
v  ~StubRoutines::call_stub
j  jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Ljava/lang/reflect/Method;Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+0 java.base@17.0.14
j  jdk.internal.reflect.NativeMethodAccessorImpl.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+133 java.base@17.0.14
j  jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+6 java.base@17.0.14
j  java.lang.reflect.Method.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+59 java.base@17.0.14
j  sun.instrument.InstrumentationImpl.loadClassAndStartAgent(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V+167 java.instrument@17.0.14
j  sun.instrument.InstrumentationImpl.loadClassAndCallPremain(Ljava/lang/String;Ljava/lang/String;)V+6 java.instrument@17.0.14
v  ~StubRoutines::call_stub

---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002027e0996c0, length=16, elements={
0x00000202597aea50, 0x0000020279b46450, 0x0000020279b49410, 0x0000020279b681b0,
0x0000020279b6acb0, 0x0000020279b6efa0, 0x0000020279b6e070, 0x0000020279b738d0,
0x0000020279b88fe0, 0x0000020279d3fc80, 0x0000020279d401a0, 0x0000020279d40ad0,
0x0000020279b6d650, 0x000002027e088c00, 0x000002027e0a7990, 0x0000020279b6bd00
}
_to_delete_list=0x000002027e099660, length=15, elements={
0x00000202597aea50, 0x0000020279b46450, 0x0000020279b49410, 0x0000020279b681b0,
0x0000020279b6acb0, 0x0000020279b6efa0, 0x0000020279b6e070, 0x0000020279b738d0,
0x0000020279b88fe0, 0x0000020279d3fc80, 0x0000020279d401a0, 0x0000020279d40ad0,
0x0000020279b6d650, 0x000002027e088c00, 0x000002027e0a7990
}

Java Threads: ( => current thread )
=>0x00000202597aea50 JavaThread "main" [_thread_in_vm, id=22324, stack(0x0000002e54b00000,0x0000002e54c00000)]
  0x0000020279b46450 JavaThread "Reference Handler" daemon [_thread_blocked, id=18540, stack(0x0000002e55200000,0x0000002e55300000)]
  0x0000020279b49410 JavaThread "Finalizer" daemon [_thread_blocked, id=15308, stack(0x0000002e55300000,0x0000002e55400000)]
  0x0000020279b681b0 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=7088, stack(0x0000002e55400000,0x0000002e55500000)]
  0x0000020279b6acb0 JavaThread "Attach Listener" daemon [_thread_blocked, id=27068, stack(0x0000002e55500000,0x0000002e55600000)]
  0x0000020279b6efa0 JavaThread "Service Thread" daemon [_thread_blocked, id=25640, stack(0x0000002e55600000,0x0000002e55700000)]
  0x0000020279b6e070 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=20932, stack(0x0000002e55700000,0x0000002e55800000)]
  0x0000020279b738d0 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=18068, stack(0x0000002e55800000,0x0000002e55900000)]
  0x0000020279b88fe0 JavaThread "Sweeper thread" daemon [_thread_blocked, id=21280, stack(0x0000002e55900000,0x0000002e55a00000)]
  0x0000020279d3fc80 JavaThread "C1 CompilerThread1" daemon [_thread_blocked, id=14588, stack(0x0000002e55a00000,0x0000002e55b00000)]
  0x0000020279d401a0 JavaThread "C1 CompilerThread2" daemon [_thread_blocked, id=24096, stack(0x0000002e55b00000,0x0000002e55c00000)]
  0x0000020279d40ad0 JavaThread "C1 CompilerThread3" daemon [_thread_blocked, id=17512, stack(0x0000002e55c00000,0x0000002e55d00000)]
  0x0000020279b6d650 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=14948, stack(0x0000002e55d00000,0x0000002e55e00000)]
  0x000002027e088c00 JavaThread "JDWP Transport Listener: dt_socket" daemon [_thread_blocked, id=13328, stack(0x0000002e55e00000,0x0000002e55f00000)]
  0x000002027e0a7990 JavaThread "JDWP Event Helper Thread" daemon [_thread_blocked, id=1592, stack(0x0000002e55f00000,0x0000002e56000000)]
  0x0000020279b6bd00 JavaThread "JDWP Command Reader" daemon [_thread_in_native, id=17092, stack(0x0000002e56000000,0x0000002e56100000)]

Other Threads:
  0x0000020279b45010 VMThread "VM Thread" [stack: 0x0000002e55100000,0x0000002e55200000] [id=848]
  0x000002025f1da2e0 GCTaskThread "GC Thread#0" [stack: 0x0000002e54c00000,0x0000002e54d00000] [id=24132]
  0x000002025f1eb230 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000002e54d00000,0x0000002e54e00000] [id=28512]
  0x000002025f1ec6d0 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000002e54e00000,0x0000002e54f00000] [id=19016]
  0x000002025f2ad990 ConcurrentGCThread "G1 Refine#0" [stack: 0x0000002e54f00000,0x0000002e55000000] [id=26200]
  0x000002025f2ae2d0 ConcurrentGCThread "G1 Service" [stack: 0x0000002e55000000,0x0000002e55100000] [id=18588]

Threads with active compile tasks:

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x0000020259772700] Metaspace_lock - owner thread: 0x00000202597aea50

OutOfMemory and StackOverflow Exception counts:
OutOfMemoryError java_heap_errors=1

Heap address: 0x0000000602400000, size: 8156 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000020200000000-0x0000020200c00000-0x0000020200c00000), size 12582912, SharedBaseAddress: 0x0000020200000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000020201000000-0x0000020241000000, reserved size: 1073741824
Narrow klass base: 0x0000020200000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 24 total, 24 available
 Memory: 32621M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 512M
 Heap Max Capacity: 8156M
 Pre-touch: Disabled
 Parallel Workers: 18
 Concurrent Workers: 5
 Concurrent Refinement Workers: 18
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 524288K, used 16384K [0x0000000602400000, 0x0000000800000000)
  region size 4096K, 6 young (24576K), 0 survivors (0K)
 Metaspace       used 573K, committed 704K, reserved 1114112K
  class space    used 56K, committed 128K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000602400000, 0x0000000602400000, 0x0000000602800000|  0%| F|  |TAMS 0x0000000602400000, 0x0000000602400000| Untracked 
|   1|0x0000000602800000, 0x0000000602800000, 0x0000000602c00000|  0%| F|  |TAMS 0x0000000602800000, 0x0000000602800000| Untracked 
|   2|0x0000000602c00000, 0x0000000602c00000, 0x0000000603000000|  0%| F|  |TAMS 0x0000000602c00000, 0x0000000602c00000| Untracked 
|   3|0x0000000603000000, 0x0000000603000000, 0x0000000603400000|  0%| F|  |TAMS 0x0000000603000000, 0x0000000603000000| Untracked 
|   4|0x0000000603400000, 0x0000000603400000, 0x0000000603800000|  0%| F|  |TAMS 0x0000000603400000, 0x0000000603400000| Untracked 
|   5|0x0000000603800000, 0x0000000603800000, 0x0000000603c00000|  0%| F|  |TAMS 0x0000000603800000, 0x0000000603800000| Untracked 
|   6|0x0000000603c00000, 0x0000000603c00000, 0x0000000604000000|  0%| F|  |TAMS 0x0000000603c00000, 0x0000000603c00000| Untracked 
|   7|0x0000000604000000, 0x0000000604000000, 0x0000000604400000|  0%| F|  |TAMS 0x0000000604000000, 0x0000000604000000| Untracked 
|   8|0x0000000604400000, 0x0000000604400000, 0x0000000604800000|  0%| F|  |TAMS 0x0000000604400000, 0x0000000604400000| Untracked 
|   9|0x0000000604800000, 0x0000000604800000, 0x0000000604c00000|  0%| F|  |TAMS 0x0000000604800000, 0x0000000604800000| Untracked 
|  10|0x0000000604c00000, 0x0000000604c00000, 0x0000000605000000|  0%| F|  |TAMS 0x0000000604c00000, 0x0000000604c00000| Untracked 
|  11|0x0000000605000000, 0x0000000605000000, 0x0000000605400000|  0%| F|  |TAMS 0x0000000605000000, 0x0000000605000000| Untracked 
|  12|0x0000000605400000, 0x0000000605400000, 0x0000000605800000|  0%| F|  |TAMS 0x0000000605400000, 0x0000000605400000| Untracked 
|  13|0x0000000605800000, 0x0000000605800000, 0x0000000605c00000|  0%| F|  |TAMS 0x0000000605800000, 0x0000000605800000| Untracked 
|  14|0x0000000605c00000, 0x0000000605c00000, 0x0000000606000000|  0%| F|  |TAMS 0x0000000605c00000, 0x0000000605c00000| Untracked 
|  15|0x0000000606000000, 0x0000000606000000, 0x0000000606400000|  0%| F|  |TAMS 0x0000000606000000, 0x0000000606000000| Untracked 
|  16|0x0000000606400000, 0x0000000606400000, 0x0000000606800000|  0%| F|  |TAMS 0x0000000606400000, 0x0000000606400000| Untracked 
|  17|0x0000000606800000, 0x0000000606800000, 0x0000000606c00000|  0%| F|  |TAMS 0x0000000606800000, 0x0000000606800000| Untracked 
|  18|0x0000000606c00000, 0x0000000606c00000, 0x0000000607000000|  0%| F|  |TAMS 0x0000000606c00000, 0x0000000606c00000| Untracked 
|  19|0x0000000607000000, 0x0000000607000000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607000000, 0x0000000607000000| Untracked 
|  20|0x0000000607400000, 0x0000000607400000, 0x0000000607800000|  0%| F|  |TAMS 0x0000000607400000, 0x0000000607400000| Untracked 
|  21|0x0000000607800000, 0x0000000607800000, 0x0000000607c00000|  0%| F|  |TAMS 0x0000000607800000, 0x0000000607800000| Untracked 
|  22|0x0000000607c00000, 0x0000000607c00000, 0x0000000608000000|  0%| F|  |TAMS 0x0000000607c00000, 0x0000000607c00000| Untracked 
|  23|0x0000000608000000, 0x0000000608000000, 0x0000000608400000|  0%| F|  |TAMS 0x0000000608000000, 0x0000000608000000| Untracked 
|  24|0x0000000608400000, 0x0000000608400000, 0x0000000608800000|  0%| F|  |TAMS 0x0000000608400000, 0x0000000608400000| Untracked 
|  25|0x0000000608800000, 0x0000000608800000, 0x0000000608c00000|  0%| F|  |TAMS 0x0000000608800000, 0x0000000608800000| Untracked 
|  26|0x0000000608c00000, 0x0000000608c00000, 0x0000000609000000|  0%| F|  |TAMS 0x0000000608c00000, 0x0000000608c00000| Untracked 
|  27|0x0000000609000000, 0x0000000609000000, 0x0000000609400000|  0%| F|  |TAMS 0x0000000609000000, 0x0000000609000000| Untracked 
|  28|0x0000000609400000, 0x0000000609400000, 0x0000000609800000|  0%| F|  |TAMS 0x0000000609400000, 0x0000000609400000| Untracked 
|  29|0x0000000609800000, 0x0000000609800000, 0x0000000609c00000|  0%| F|  |TAMS 0x0000000609800000, 0x0000000609800000| Untracked 
|  30|0x0000000609c00000, 0x0000000609c00000, 0x000000060a000000|  0%| F|  |TAMS 0x0000000609c00000, 0x0000000609c00000| Untracked 
|  31|0x000000060a000000, 0x000000060a000000, 0x000000060a400000|  0%| F|  |TAMS 0x000000060a000000, 0x000000060a000000| Untracked 
|  32|0x000000060a400000, 0x000000060a400000, 0x000000060a800000|  0%| F|  |TAMS 0x000000060a400000, 0x000000060a400000| Untracked 
|  33|0x000000060a800000, 0x000000060a800000, 0x000000060ac00000|  0%| F|  |TAMS 0x000000060a800000, 0x000000060a800000| Untracked 
|  34|0x000000060ac00000, 0x000000060ac00000, 0x000000060b000000|  0%| F|  |TAMS 0x000000060ac00000, 0x000000060ac00000| Untracked 
|  35|0x000000060b000000, 0x000000060b000000, 0x000000060b400000|  0%| F|  |TAMS 0x000000060b000000, 0x000000060b000000| Untracked 
|  36|0x000000060b400000, 0x000000060b400000, 0x000000060b800000|  0%| F|  |TAMS 0x000000060b400000, 0x000000060b400000| Untracked 
|  37|0x000000060b800000, 0x000000060b800000, 0x000000060bc00000|  0%| F|  |TAMS 0x000000060b800000, 0x000000060b800000| Untracked 
|  38|0x000000060bc00000, 0x000000060bc00000, 0x000000060c000000|  0%| F|  |TAMS 0x000000060bc00000, 0x000000060bc00000| Untracked 
|  39|0x000000060c000000, 0x000000060c000000, 0x000000060c400000|  0%| F|  |TAMS 0x000000060c000000, 0x000000060c000000| Untracked 
|  40|0x000000060c400000, 0x000000060c400000, 0x000000060c800000|  0%| F|  |TAMS 0x000000060c400000, 0x000000060c400000| Untracked 
|  41|0x000000060c800000, 0x000000060c800000, 0x000000060cc00000|  0%| F|  |TAMS 0x000000060c800000, 0x000000060c800000| Untracked 
|  42|0x000000060cc00000, 0x000000060cc00000, 0x000000060d000000|  0%| F|  |TAMS 0x000000060cc00000, 0x000000060cc00000| Untracked 
|  43|0x000000060d000000, 0x000000060d000000, 0x000000060d400000|  0%| F|  |TAMS 0x000000060d000000, 0x000000060d000000| Untracked 
|  44|0x000000060d400000, 0x000000060d400000, 0x000000060d800000|  0%| F|  |TAMS 0x000000060d400000, 0x000000060d400000| Untracked 
|  45|0x000000060d800000, 0x000000060d800000, 0x000000060dc00000|  0%| F|  |TAMS 0x000000060d800000, 0x000000060d800000| Untracked 
|  46|0x000000060dc00000, 0x000000060dc00000, 0x000000060e000000|  0%| F|  |TAMS 0x000000060dc00000, 0x000000060dc00000| Untracked 
|  47|0x000000060e000000, 0x000000060e000000, 0x000000060e400000|  0%| F|  |TAMS 0x000000060e000000, 0x000000060e000000| Untracked 
|  48|0x000000060e400000, 0x000000060e400000, 0x000000060e800000|  0%| F|  |TAMS 0x000000060e400000, 0x000000060e400000| Untracked 
|  49|0x000000060e800000, 0x000000060e800000, 0x000000060ec00000|  0%| F|  |TAMS 0x000000060e800000, 0x000000060e800000| Untracked 
|  50|0x000000060ec00000, 0x000000060ec00000, 0x000000060f000000|  0%| F|  |TAMS 0x000000060ec00000, 0x000000060ec00000| Untracked 
|  51|0x000000060f000000, 0x000000060f000000, 0x000000060f400000|  0%| F|  |TAMS 0x000000060f000000, 0x000000060f000000| Untracked 
|  52|0x000000060f400000, 0x000000060f400000, 0x000000060f800000|  0%| F|  |TAMS 0x000000060f400000, 0x000000060f400000| Untracked 
|  53|0x000000060f800000, 0x000000060f800000, 0x000000060fc00000|  0%| F|  |TAMS 0x000000060f800000, 0x000000060f800000| Untracked 
|  54|0x000000060fc00000, 0x000000060fc00000, 0x0000000610000000|  0%| F|  |TAMS 0x000000060fc00000, 0x000000060fc00000| Untracked 
|  55|0x0000000610000000, 0x0000000610000000, 0x0000000610400000|  0%| F|  |TAMS 0x0000000610000000, 0x0000000610000000| Untracked 
|  56|0x0000000610400000, 0x0000000610400000, 0x0000000610800000|  0%| F|  |TAMS 0x0000000610400000, 0x0000000610400000| Untracked 
|  57|0x0000000610800000, 0x0000000610800000, 0x0000000610c00000|  0%| F|  |TAMS 0x0000000610800000, 0x0000000610800000| Untracked 
|  58|0x0000000610c00000, 0x0000000610c00000, 0x0000000611000000|  0%| F|  |TAMS 0x0000000610c00000, 0x0000000610c00000| Untracked 
|  59|0x0000000611000000, 0x0000000611000000, 0x0000000611400000|  0%| F|  |TAMS 0x0000000611000000, 0x0000000611000000| Untracked 
|  60|0x0000000611400000, 0x0000000611400000, 0x0000000611800000|  0%| F|  |TAMS 0x0000000611400000, 0x0000000611400000| Untracked 
|  61|0x0000000611800000, 0x0000000611800000, 0x0000000611c00000|  0%| F|  |TAMS 0x0000000611800000, 0x0000000611800000| Untracked 
|  62|0x0000000611c00000, 0x0000000611c00000, 0x0000000612000000|  0%| F|  |TAMS 0x0000000611c00000, 0x0000000611c00000| Untracked 
|  63|0x0000000612000000, 0x0000000612000000, 0x0000000612400000|  0%| F|  |TAMS 0x0000000612000000, 0x0000000612000000| Untracked 
|  64|0x0000000612400000, 0x0000000612400000, 0x0000000612800000|  0%| F|  |TAMS 0x0000000612400000, 0x0000000612400000| Untracked 
|  65|0x0000000612800000, 0x0000000612800000, 0x0000000612c00000|  0%| F|  |TAMS 0x0000000612800000, 0x0000000612800000| Untracked 
|  66|0x0000000612c00000, 0x0000000612c00000, 0x0000000613000000|  0%| F|  |TAMS 0x0000000612c00000, 0x0000000612c00000| Untracked 
|  67|0x0000000613000000, 0x0000000613000000, 0x0000000613400000|  0%| F|  |TAMS 0x0000000613000000, 0x0000000613000000| Untracked 
|  68|0x0000000613400000, 0x0000000613400000, 0x0000000613800000|  0%| F|  |TAMS 0x0000000613400000, 0x0000000613400000| Untracked 
|  69|0x0000000613800000, 0x0000000613800000, 0x0000000613c00000|  0%| F|  |TAMS 0x0000000613800000, 0x0000000613800000| Untracked 
|  70|0x0000000613c00000, 0x0000000613c00000, 0x0000000614000000|  0%| F|  |TAMS 0x0000000613c00000, 0x0000000613c00000| Untracked 
|  71|0x0000000614000000, 0x0000000614000000, 0x0000000614400000|  0%| F|  |TAMS 0x0000000614000000, 0x0000000614000000| Untracked 
|  72|0x0000000614400000, 0x0000000614400000, 0x0000000614800000|  0%| F|  |TAMS 0x0000000614400000, 0x0000000614400000| Untracked 
|  73|0x0000000614800000, 0x0000000614800000, 0x0000000614c00000|  0%| F|  |TAMS 0x0000000614800000, 0x0000000614800000| Untracked 
|  74|0x0000000614c00000, 0x0000000614c00000, 0x0000000615000000|  0%| F|  |TAMS 0x0000000614c00000, 0x0000000614c00000| Untracked 
|  75|0x0000000615000000, 0x0000000615000000, 0x0000000615400000|  0%| F|  |TAMS 0x0000000615000000, 0x0000000615000000| Untracked 
|  76|0x0000000615400000, 0x0000000615400000, 0x0000000615800000|  0%| F|  |TAMS 0x0000000615400000, 0x0000000615400000| Untracked 
|  77|0x0000000615800000, 0x0000000615800000, 0x0000000615c00000|  0%| F|  |TAMS 0x0000000615800000, 0x0000000615800000| Untracked 
|  78|0x0000000615c00000, 0x0000000615c00000, 0x0000000616000000|  0%| F|  |TAMS 0x0000000615c00000, 0x0000000615c00000| Untracked 
|  79|0x0000000616000000, 0x0000000616000000, 0x0000000616400000|  0%| F|  |TAMS 0x0000000616000000, 0x0000000616000000| Untracked 
|  80|0x0000000616400000, 0x0000000616400000, 0x0000000616800000|  0%| F|  |TAMS 0x0000000616400000, 0x0000000616400000| Untracked 
|  81|0x0000000616800000, 0x0000000616800000, 0x0000000616c00000|  0%| F|  |TAMS 0x0000000616800000, 0x0000000616800000| Untracked 
|  82|0x0000000616c00000, 0x0000000616c00000, 0x0000000617000000|  0%| F|  |TAMS 0x0000000616c00000, 0x0000000616c00000| Untracked 
|  83|0x0000000617000000, 0x0000000617000000, 0x0000000617400000|  0%| F|  |TAMS 0x0000000617000000, 0x0000000617000000| Untracked 
|  84|0x0000000617400000, 0x0000000617400000, 0x0000000617800000|  0%| F|  |TAMS 0x0000000617400000, 0x0000000617400000| Untracked 
|  85|0x0000000617800000, 0x0000000617800000, 0x0000000617c00000|  0%| F|  |TAMS 0x0000000617800000, 0x0000000617800000| Untracked 
|  86|0x0000000617c00000, 0x0000000617c00000, 0x0000000618000000|  0%| F|  |TAMS 0x0000000617c00000, 0x0000000617c00000| Untracked 
|  87|0x0000000618000000, 0x0000000618000000, 0x0000000618400000|  0%| F|  |TAMS 0x0000000618000000, 0x0000000618000000| Untracked 
|  88|0x0000000618400000, 0x0000000618400000, 0x0000000618800000|  0%| F|  |TAMS 0x0000000618400000, 0x0000000618400000| Untracked 
|  89|0x0000000618800000, 0x0000000618800000, 0x0000000618c00000|  0%| F|  |TAMS 0x0000000618800000, 0x0000000618800000| Untracked 
|  90|0x0000000618c00000, 0x0000000618c00000, 0x0000000619000000|  0%| F|  |TAMS 0x0000000618c00000, 0x0000000618c00000| Untracked 
|  91|0x0000000619000000, 0x0000000619000000, 0x0000000619400000|  0%| F|  |TAMS 0x0000000619000000, 0x0000000619000000| Untracked 
|  92|0x0000000619400000, 0x0000000619400000, 0x0000000619800000|  0%| F|  |TAMS 0x0000000619400000, 0x0000000619400000| Untracked 
|  93|0x0000000619800000, 0x0000000619800000, 0x0000000619c00000|  0%| F|  |TAMS 0x0000000619800000, 0x0000000619800000| Untracked 
|  94|0x0000000619c00000, 0x0000000619c00000, 0x000000061a000000|  0%| F|  |TAMS 0x0000000619c00000, 0x0000000619c00000| Untracked 
|  95|0x000000061a000000, 0x000000061a000000, 0x000000061a400000|  0%| F|  |TAMS 0x000000061a000000, 0x000000061a000000| Untracked 
|  96|0x000000061a400000, 0x000000061a400000, 0x000000061a800000|  0%| F|  |TAMS 0x000000061a400000, 0x000000061a400000| Untracked 
|  97|0x000000061a800000, 0x000000061a800000, 0x000000061ac00000|  0%| F|  |TAMS 0x000000061a800000, 0x000000061a800000| Untracked 
|  98|0x000000061ac00000, 0x000000061ac00000, 0x000000061b000000|  0%| F|  |TAMS 0x000000061ac00000, 0x000000061ac00000| Untracked 
|  99|0x000000061b000000, 0x000000061b000000, 0x000000061b400000|  0%| F|  |TAMS 0x000000061b000000, 0x000000061b000000| Untracked 
| 100|0x000000061b400000, 0x000000061b400000, 0x000000061b800000|  0%| F|  |TAMS 0x000000061b400000, 0x000000061b400000| Untracked 
| 101|0x000000061b800000, 0x000000061b800000, 0x000000061bc00000|  0%| F|  |TAMS 0x000000061b800000, 0x000000061b800000| Untracked 
| 102|0x000000061bc00000, 0x000000061bc00000, 0x000000061c000000|  0%| F|  |TAMS 0x000000061bc00000, 0x000000061bc00000| Untracked 
| 103|0x000000061c000000, 0x000000061c000000, 0x000000061c400000|  0%| F|  |TAMS 0x000000061c000000, 0x000000061c000000| Untracked 
| 104|0x000000061c400000, 0x000000061c400000, 0x000000061c800000|  0%| F|  |TAMS 0x000000061c400000, 0x000000061c400000| Untracked 
| 105|0x000000061c800000, 0x000000061c800000, 0x000000061cc00000|  0%| F|  |TAMS 0x000000061c800000, 0x000000061c800000| Untracked 
| 106|0x000000061cc00000, 0x000000061cc00000, 0x000000061d000000|  0%| F|  |TAMS 0x000000061cc00000, 0x000000061cc00000| Untracked 
| 107|0x000000061d000000, 0x000000061d000000, 0x000000061d400000|  0%| F|  |TAMS 0x000000061d000000, 0x000000061d000000| Untracked 
| 108|0x000000061d400000, 0x000000061d400000, 0x000000061d800000|  0%| F|  |TAMS 0x000000061d400000, 0x000000061d400000| Untracked 
| 109|0x000000061d800000, 0x000000061d800000, 0x000000061dc00000|  0%| F|  |TAMS 0x000000061d800000, 0x000000061d800000| Untracked 
| 110|0x000000061dc00000, 0x000000061dc00000, 0x000000061e000000|  0%| F|  |TAMS 0x000000061dc00000, 0x000000061dc00000| Untracked 
| 111|0x000000061e000000, 0x000000061e000000, 0x000000061e400000|  0%| F|  |TAMS 0x000000061e000000, 0x000000061e000000| Untracked 
| 112|0x000000061e400000, 0x000000061e400000, 0x000000061e800000|  0%| F|  |TAMS 0x000000061e400000, 0x000000061e400000| Untracked 
| 113|0x000000061e800000, 0x000000061e800000, 0x000000061ec00000|  0%| F|  |TAMS 0x000000061e800000, 0x000000061e800000| Untracked 
| 114|0x000000061ec00000, 0x000000061ec00000, 0x000000061f000000|  0%| F|  |TAMS 0x000000061ec00000, 0x000000061ec00000| Untracked 
| 115|0x000000061f000000, 0x000000061f000000, 0x000000061f400000|  0%| F|  |TAMS 0x000000061f000000, 0x000000061f000000| Untracked 
| 116|0x000000061f400000, 0x000000061f400000, 0x000000061f800000|  0%| F|  |TAMS 0x000000061f400000, 0x000000061f400000| Untracked 
| 117|0x000000061f800000, 0x000000061f800000, 0x000000061fc00000|  0%| F|  |TAMS 0x000000061f800000, 0x000000061f800000| Untracked 
| 118|0x000000061fc00000, 0x000000061fc00000, 0x0000000620000000|  0%| F|  |TAMS 0x000000061fc00000, 0x000000061fc00000| Untracked 
| 119|0x0000000620000000, 0x0000000620000000, 0x0000000620400000|  0%| F|  |TAMS 0x0000000620000000, 0x0000000620000000| Untracked 
| 120|0x0000000620400000, 0x0000000620400000, 0x0000000620800000|  0%| F|  |TAMS 0x0000000620400000, 0x0000000620400000| Untracked 
| 121|0x0000000620800000, 0x0000000620800000, 0x0000000620c00000|  0%| F|  |TAMS 0x0000000620800000, 0x0000000620800000| Untracked 
| 122|0x0000000620c00000, 0x0000000620c8b5a0, 0x0000000621000000| 13%| E|  |TAMS 0x0000000620c00000, 0x0000000620c00000| Complete 
| 123|0x0000000621000000, 0x0000000621400000, 0x0000000621400000|100%| E|CS|TAMS 0x0000000621000000, 0x0000000621000000| Complete 
| 124|0x0000000621400000, 0x0000000621800000, 0x0000000621800000|100%| E|  |TAMS 0x0000000621400000, 0x0000000621400000| Complete 
| 125|0x0000000621800000, 0x0000000621c00000, 0x0000000621c00000|100%| E|CS|TAMS 0x0000000621800000, 0x0000000621800000| Complete 
| 126|0x0000000621c00000, 0x0000000622000000, 0x0000000622000000|100%| E|CS|TAMS 0x0000000621c00000, 0x0000000621c00000| Complete 
| 127|0x0000000622000000, 0x0000000622400000, 0x0000000622400000|100%| E|CS|TAMS 0x0000000622000000, 0x0000000622000000| Complete 

Card table byte_map: [0x0000020264650000,0x0000020265640000] _byte_map_base: 0x000002026163e000

Marking Bits (Prev, Next): (CMBitMap*) 0x000002025f1da810, (CMBitMap*) 0x000002025f1da850
 Prev Bits: [0x0000020266630000, 0x000002026e5a0000)
 Next Bits: [0x000002026e5a0000, 0x0000020276510000)

Polling page: 0x0000020259320000

Metaspace:

Usage:
  Non-class:    517.34 KB used.
      Class:     56.64 KB used.
       Both:    573.98 KB used.

Virtual space:
  Non-class space:       64.00 MB reserved,     576.00 KB ( <1%) committed,  1 nodes.
      Class space:        1.00 GB reserved,     128.00 KB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,     704.00 KB ( <1%) committed. 

Chunk freelists:
   Non-Class:  11.86 MB
       Class:  15.73 MB
        Both:  27.59 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 6.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 11.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 16.
num_chunk_merges: 0.
num_chunk_splits: 12.
num_chunks_enlarged: 8.
num_inconsistent_stats: 0.

CodeCache: size=49152Kb used=4011Kb max_used=4011Kb free=45140Kb
 bounds [0x000002025f2c0000, 0x000002025f6b0000, 0x00000202622c0000]
 total_blobs=971 nmethods=603 adapters=293
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 0.917 Thread 0x0000020279d40ad0 nmethod 594 0x000002025f6a3590 code [0x000002025f6a3720, 0x000002025f6a3868]
Event: 0.917 Thread 0x0000020279d40ad0  595       1       java.nio.file.Files::provider (10 bytes)
Event: 0.917 Thread 0x0000020279d40ad0 nmethod 595 0x000002025f6a3a90 code [0x000002025f6a3c20, 0x000002025f6a3d18]
Event: 0.917 Thread 0x0000020279d40ad0  596       1       sun.nio.fs.WindowsPath::getFileSystem (5 bytes)
Event: 0.917 Thread 0x0000020279b738d0 nmethod 592 0x000002025f6a3e90 code [0x000002025f6a40c0, 0x000002025f6a47e8]
Event: 0.917 Thread 0x0000020279d40ad0 nmethod 596 0x000002025f6a4c90 code [0x000002025f6a4e20, 0x000002025f6a4ef8]
Event: 0.917 Thread 0x0000020279b738d0  598       1       jdk.internal.perf.PerfCounter::getZipFileOpenTime (4 bytes)
Event: 0.917 Thread 0x0000020279d40ad0  599       1       jdk.internal.perf.PerfCounter::addElapsedTimeFrom (10 bytes)
Event: 0.917 Thread 0x0000020279b738d0 nmethod 598 0x000002025f6a4f90 code [0x000002025f6a5120, 0x000002025f6a51d8]
Event: 0.917 Thread 0x0000020279b738d0  600       1       jdk.internal.perf.PerfCounter::getZipFileCount (4 bytes)
Event: 0.917 Thread 0x0000020279b738d0 nmethod 600 0x000002025f6a5290 code [0x000002025f6a5420, 0x000002025f6a54d8]
Event: 0.917 Thread 0x0000020279b738d0  601       1       jdk.internal.perf.PerfCounter::increment (6 bytes)
Event: 0.917 Thread 0x0000020279d40ad0 nmethod 599 0x000002025f6a5590 code [0x000002025f6a5760, 0x000002025f6a5ab8]
Event: 0.917 Thread 0x0000020279b738d0 nmethod 601 0x000002025f6a5e90 code [0x000002025f6a6060, 0x000002025f6a6378]
Event: 0.918 Thread 0x0000020279d3fc80 nmethod 593 0x000002025f6a6710 code [0x000002025f6a69a0, 0x000002025f6a7508]
Event: 0.918 Thread 0x0000020279d401a0 nmethod 590 0x000002025f6a8290 code [0x000002025f6a8580, 0x000002025f6a93f8]
Event: 0.935 Thread 0x0000020279d401a0  602       1       java.lang.ThreadLocal::get (38 bytes)
Event: 0.935 Thread 0x0000020279d401a0 nmethod 602 0x000002025f6aa390 code [0x000002025f6aa540, 0x000002025f6aa708]
Event: 0.947 Thread 0x0000020279d401a0  603       1       java.util.Arrays::copyOf (19 bytes)
Event: 0.947 Thread 0x0000020279d401a0 nmethod 603 0x000002025f6aa910 code [0x000002025f6aaaa0, 0x000002025f6aac88]

GC Heap History (0 events):
No events

Dll operation events (3 events):
Event: 0.027 Loaded shared library C:\Users\<USER>\.jdks\jbr-17.0.14\bin\java.dll
Event: 0.029 Loaded shared library C:\Users\<USER>\.jdks\jbr-17.0.14\bin\zip.dll
Event: 0.053 Loaded shared library C:\Users\<USER>\.jdks\jbr-17.0.14\bin\jsvml.dll

Deoptimization events (20 events):
Event: 0.945 Thread 0x00000202597aea50 DEOPT PACKING pc=0x000002025f645dac sp=0x0000002e54bfb330
Event: 0.945 Thread 0x00000202597aea50 DEOPT UNPACKING pc=0x000002025f317143 sp=0x0000002e54bfa828 mode 1
Event: 0.945 Thread 0x00000202597aea50 DEOPT PACKING pc=0x000002025f696474 sp=0x0000002e54bfb3f0
Event: 0.945 Thread 0x00000202597aea50 DEOPT UNPACKING pc=0x000002025f317143 sp=0x0000002e54bfa8e8 mode 1
Event: 0.945 Thread 0x00000202597aea50 DEOPT PACKING pc=0x000002025f69de64 sp=0x0000002e54bfb4b0
Event: 0.945 Thread 0x00000202597aea50 DEOPT UNPACKING pc=0x000002025f317143 sp=0x0000002e54bfaa30 mode 1
Event: 0.945 Thread 0x00000202597aea50 DEOPT PACKING pc=0x000002025f61c04c sp=0x0000002e54bfb610
Event: 0.945 Thread 0x00000202597aea50 DEOPT UNPACKING pc=0x000002025f317143 sp=0x0000002e54bfab48 mode 1
Event: 0.945 Thread 0x00000202597aea50 DEOPT PACKING pc=0x000002025f62e3bc sp=0x0000002e54bfb730
Event: 0.945 Thread 0x00000202597aea50 DEOPT UNPACKING pc=0x000002025f317143 sp=0x0000002e54bfab88 mode 1
Event: 0.945 Thread 0x00000202597aea50 DEOPT PACKING pc=0x000002025f69bd94 sp=0x0000002e54bfb7a0
Event: 0.945 Thread 0x00000202597aea50 DEOPT UNPACKING pc=0x000002025f317143 sp=0x0000002e54bfac50 mode 1
Event: 0.945 Thread 0x00000202597aea50 DEOPT PACKING pc=0x000002025f61b14c sp=0x0000002e54bfb8f0
Event: 0.945 Thread 0x00000202597aea50 DEOPT UNPACKING pc=0x000002025f317143 sp=0x0000002e54bfacb8 mode 1
Event: 0.945 Thread 0x00000202597aea50 DEOPT PACKING pc=0x000002025f69b13c sp=0x0000002e54bfb9b0
Event: 0.945 Thread 0x00000202597aea50 DEOPT UNPACKING pc=0x000002025f317143 sp=0x0000002e54bfaf20 mode 1
Event: 0.945 Thread 0x00000202597aea50 DEOPT PACKING pc=0x000002025f689ef4 sp=0x0000002e54bfbae0
Event: 0.945 Thread 0x00000202597aea50 DEOPT UNPACKING pc=0x000002025f317143 sp=0x0000002e54bfb000 mode 1
Event: 0.945 Thread 0x00000202597aea50 DEOPT PACKING pc=0x000002025f648064 sp=0x0000002e54bfbbc0
Event: 0.945 Thread 0x00000202597aea50 DEOPT UNPACKING pc=0x000002025f317143 sp=0x0000002e54bfafa8 mode 1

Classes loaded (20 events):
Event: 0.875 Loading class org/slf4j/helpers/NOP_FallbackServiceProvider
Event: 0.875 Loading class org/slf4j/helpers/NOP_FallbackServiceProvider done
Event: 0.877 Loading class org/slf4j/helpers/NOPLoggerFactory
Event: 0.877 Loading class org/slf4j/helpers/NOPLoggerFactory done
Event: 0.878 Loading class org/slf4j/helpers/NOPMDCAdapter
Event: 0.878 Loading class org/slf4j/helpers/NOPMDCAdapter done
Event: 0.879 Loading class org/slf4j/helpers/Util
Event: 0.879 Loading class org/slf4j/helpers/Util done
Event: 0.880 Loading class java/lang/SecurityException
Event: 0.880 Loading class java/lang/SecurityException done
Event: 0.944 Loading class java/util/zip/DataFormatException
Event: 0.945 Loading class java/util/zip/DataFormatException done
Event: 0.945 Loading class java/security/PrivilegedActionException
Event: 0.945 Loading class java/security/PrivilegedActionException done
Event: 0.945 Loading class reactor/util/Loggers$ConsoleLoggerFactory
Event: 0.945 Loading class reactor/util/Loggers$ConsoleLoggerFactory done
Event: 0.946 Loading class reactor/util/Loggers$ConsoleLoggerKey
Event: 0.946 Loading class reactor/util/Loggers$ConsoleLoggerKey done
Event: 0.947 Loading class reactor/util/Loggers$ConsoleLogger
Event: 0.947 Loading class reactor/util/Loggers$ConsoleLogger done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (1 events):
Event: 0.944 Thread 0x00000202597aea50 Exception <a 'java/lang/OutOfMemoryError'{0x00000006213fd808}> (0x00000006213fd808) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 550]

VM Operations (10 events):
Event: 0.371 Executing VM operation: HandshakeAllThreads
Event: 0.371 Executing VM operation: HandshakeAllThreads done
Event: 0.390 Executing VM operation: ChangeBreakpoints
Event: 0.390 Executing VM operation: ChangeBreakpoints done
Event: 0.420 Executing VM operation: ChangeBreakpoints
Event: 0.420 Executing VM operation: ChangeBreakpoints done
Event: 0.421 Executing VM operation: ChangeBreakpoints
Event: 0.421 Executing VM operation: ChangeBreakpoints done
Event: 0.875 Executing VM operation: HandshakeAllThreads
Event: 0.875 Executing VM operation: HandshakeAllThreads done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 0.056 Thread 0x00000202597aea50 Thread added: 0x0000020279b46450
Event: 0.056 Thread 0x00000202597aea50 Thread added: 0x0000020279b49410
Event: 0.073 Thread 0x00000202597aea50 Thread added: 0x0000020279b681b0
Event: 0.074 Thread 0x00000202597aea50 Thread added: 0x0000020279b6acb0
Event: 0.074 Thread 0x00000202597aea50 Thread added: 0x0000020279b6efa0
Event: 0.074 Thread 0x00000202597aea50 Thread added: 0x0000020279b6e070
Event: 0.074 Thread 0x00000202597aea50 Thread added: 0x0000020279b738d0
Event: 0.074 Thread 0x00000202597aea50 Thread added: 0x0000020279b88fe0
Event: 0.229 Thread 0x0000020279b738d0 Thread added: 0x0000020279d3fc80
Event: 0.229 Thread 0x0000020279b738d0 Thread added: 0x0000020279d401a0
Event: 0.229 Thread 0x0000020279b738d0 Thread added: 0x0000020279d40ad0
Event: 0.353 Thread 0x00000202597aea50 Thread added: 0x0000020279b6d650
Event: 0.389 Thread 0x00000202597aea50 Thread added: 0x000002027e088c00
Event: 0.389 Thread 0x00000202597aea50 Thread added: 0x000002027e0a7990
Event: 0.389 Thread 0x000002027e088c00 Thread added: 0x0000020279b6bd00
Event: 0.487 Loaded shared library C:\Users\<USER>\.jdks\jbr-17.0.14\bin\instrument.dll
Event: 0.503 Loaded shared library C:\Users\<USER>\.jdks\jbr-17.0.14\bin\net.dll
Event: 0.506 Loaded shared library C:\Users\<USER>\.jdks\jbr-17.0.14\bin\nio.dll
Event: 0.512 Loaded shared library C:\Users\<USER>\.jdks\jbr-17.0.14\bin\zip.dll
Event: 0.883 Loaded shared library C:\Users\<USER>\.jdks\jbr-17.0.14\bin\jimage.dll


Dynamic libraries:
0x00007ff657970000 - 0x00007ff65797a000 	C:\Users\<USER>\.jdks\jbr-17.0.14\bin\java.exe
0x00007ffe2a090000 - 0x00007ffe2a288000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffe290c0000 - 0x00007ffe2917d000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffe27b20000 - 0x00007ffe27e16000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffe27930000 - 0x00007ffe27a30000 	C:\Windows\System32\ucrtbase.dll
0x00007ffe0e540000 - 0x00007ffe0e557000 	C:\Users\<USER>\.jdks\jbr-17.0.14\bin\jli.dll
0x00007ffe28610000 - 0x00007ffe287ae000 	C:\Windows\System32\USER32.dll
0x00007ffe27900000 - 0x00007ffe27922000 	C:\Windows\System32\win32u.dll
0x00007ffe102a0000 - 0x00007ffe102bb000 	C:\Users\<USER>\.jdks\jbr-17.0.14\bin\VCRUNTIME140.dll
0x00007ffe28a20000 - 0x00007ffe28a4c000 	C:\Windows\System32\GDI32.dll
0x00007ffe190e0000 - 0x00007ffe1937a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.3636_none_60b6a03d71f818d5\COMCTL32.dll
0x00007ffe277e0000 - 0x00007ffe278fa000 	C:\Windows\System32\gdi32full.dll
0x00007ffe28fe0000 - 0x00007ffe2907e000 	C:\Windows\System32\msvcrt.dll
0x00007ffe27a30000 - 0x00007ffe27acd000 	C:\Windows\System32\msvcp_win.dll
0x00007ffe29f90000 - 0x00007ffe29fc0000 	C:\Windows\System32\IMM32.DLL
0x00007ffe0e530000 - 0x00007ffe0e53c000 	C:\Users\<USER>\.jdks\jbr-17.0.14\bin\vcruntime140_1.dll
0x00007ffda1d80000 - 0x00007ffda1e0d000 	C:\Users\<USER>\.jdks\jbr-17.0.14\bin\msvcp140.dll
0x00007ffd8c570000 - 0x00007ffd8d1fe000 	C:\Users\<USER>\.jdks\jbr-17.0.14\bin\server\jvm.dll
0x00007ffe29730000 - 0x00007ffe297df000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffe281a0000 - 0x00007ffe2823c000 	C:\Windows\System32\sechost.dll
0x00007ffe288f0000 - 0x00007ffe28a16000 	C:\Windows\System32\RPCRT4.dll
0x00007ffe29330000 - 0x00007ffe2939b000 	C:\Windows\System32\WS2_32.dll
0x00007ffe26d20000 - 0x00007ffe26d6b000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffe1ffd0000 - 0x00007ffe1fff7000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffe21210000 - 0x00007ffe2121a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffe26b90000 - 0x00007ffe26ba2000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffe26010000 - 0x00007ffe26022000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffe0e520000 - 0x00007ffe0e52a000 	C:\Users\<USER>\.jdks\jbr-17.0.14\bin\jimage.dll
0x00007ffe25b40000 - 0x00007ffe25d24000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffe102c0000 - 0x00007ffe102f4000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffe27fb0000 - 0x00007ffe28032000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffe17f50000 - 0x00007ffe17f8b000 	C:\Users\<USER>\.jdks\jbr-17.0.14\bin\jdwp.dll
0x00007ffe23f90000 - 0x00007ffe23f9e000 	C:\Users\<USER>\.jdks\jbr-17.0.14\bin\instrument.dll
0x00007ffe023e0000 - 0x00007ffe02405000 	C:\Users\<USER>\.jdks\jbr-17.0.14\bin\java.dll
0x00007ffd8c350000 - 0x00007ffd8c561000 	C:\Users\<USER>\AppData\Local\Temp\idea_libasyncProfiler_dll_temp_folder9909\libasyncProfiler.dll
0x00007ffe06e30000 - 0x00007ffe06e48000 	C:\Users\<USER>\.jdks\jbr-17.0.14\bin\zip.dll
0x00007ffd8c270000 - 0x00007ffd8c347000 	C:\Users\<USER>\.jdks\jbr-17.0.14\bin\jsvml.dll
0x00007ffe297e0000 - 0x00007ffe29f25000 	C:\Windows\System32\SHELL32.dll
0x00007ffe24a30000 - 0x00007ffe251cb000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffe282b0000 - 0x00007ffe28604000 	C:\Windows\System32\combase.dll
0x00007ffe271b0000 - 0x00007ffe271dd000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ffe29680000 - 0x00007ffe2972d000 	C:\Windows\System32\SHCORE.dll
0x00007ffe28250000 - 0x00007ffe282a5000 	C:\Windows\System32\shlwapi.dll
0x00007ffe276a0000 - 0x00007ffe276c5000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffe23f70000 - 0x00007ffe23f7c000 	C:\Users\<USER>\.jdks\jbr-17.0.14\bin\dt_socket.dll
0x00007ffe26bb0000 - 0x00007ffe26beb000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffe26f10000 - 0x00007ffe26f7a000 	C:\Windows\system32\mswsock.dll
0x00007ffe06db0000 - 0x00007ffe06dc9000 	C:\Users\<USER>\.jdks\jbr-17.0.14\bin\net.dll
0x00007ffe1e300000 - 0x00007ffe1e40a000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffe023c0000 - 0x00007ffe023d6000 	C:\Users\<USER>\.jdks\jbr-17.0.14\bin\nio.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Users\<USER>\.jdks\jbr-17.0.14\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.3636_none_60b6a03d71f818d5;C:\Users\<USER>\.jdks\jbr-17.0.14\bin\server;C:\Users\<USER>\AppData\Local\Temp\idea_libasyncProfiler_dll_temp_folder9909

VM Arguments:
jvm_args: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:24911,suspend=y,server=n -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2025.1\captureAgent\debugger-agent.jar=file:///C:/Users/<USER>/AppData/Local/Temp/capture13192552871307146871.props -agentpath:C:\Users\<USER>\AppData\Local\Temp\idea_libasyncProfiler_dll_temp_folder9909\libasyncProfiler.dll=version,jfr,event=wall,interval=10ms,cstack=no,file=C:\Users\<USER>\IdeaSnapshots\LangChatApp_2025_08_12_192341.jfr,dbghelppath=C:\Users\<USER>\AppData\Local\Temp\idea_dbghelp_dll_temp_folder5\dbghelp.dll,log=C:\Users\<USER>\AppData\Local\Temp\LangChatApp_2025_08_12_192341.jfr.log.txt,logLevel=DEBUG -XX:TieredStopAtLevel=1 -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dmanagement.endpoints.jmx.exposure.include=* -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 
java_command: cn.tycoding.langchat.LangChatApp
java_class_path (initial): D:\awork\work-aigc\aigc-manage\langchat-server\target\classes;D:\awork\work-aigc\aigc-manage\langchat-auth\target\classes;D:\awork\work-aigc\aigc-manage\langchat-common\langchat-common-core\target\classes;D:\java\apache-maven-3.9.10\repository\org\springframework\boot\spring-boot-starter-web\3.2.3\spring-boot-starter-web-3.2.3.jar;D:\java\apache-maven-3.9.10\repository\org\springframework\boot\spring-boot-starter-json\3.2.3\spring-boot-starter-json-3.2.3.jar;D:\java\apache-maven-3.9.10\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.15.4\jackson-datatype-jdk8-2.15.4.jar;D:\java\apache-maven-3.9.10\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.15.4\jackson-datatype-jsr310-2.15.4.jar;D:\java\apache-maven-3.9.10\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.15.4\jackson-module-parameter-names-2.15.4.jar;D:\java\apache-maven-3.9.10\repository\org\springframework\boot\spring-boot-starter-tomcat\3.2.3\spring-boot-starter-tomcat-3.2.3.jar;D:\java\apache-maven-3.9.10\repository\org\apache\tomcat\embed\tomcat-embed-core\10.1.19\tomcat-embed-core-10.1.19.jar;D:\java\apache-maven-3.9.10\repository\org\apache\tomcat\embed\tomcat-embed-el\10.1.19\tomcat-embed-el-10.1.19.jar;D:\java\apache-maven-3.9.10\repository\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.19\tomcat-embed-websocket-10.1.19.jar;D:\java\apache-maven-3.9.10\repository\org\springframework\boot\spring-boot-starter-data-redis\3.2.3\spring-boot-starter-data-redis-3.2.3.jar;D:\java\apache-maven-3.9.10\repository\io\lettuce\lettuce-core\6.3.1.RELEASE\lettuce-core-6.3.1.RELEASE.jar;D:\java\apache-maven-3.9.10\repository\io\netty\netty-common\4.1.107.Final\netty-common-4.1.107.Final.jar;D:\java\apache-maven-3.9.10\repository\io\netty\netty-handler\4.1.107.Final\netty-handler-4.1.107.Final.jar;D:\java\apache-maven-3.9.10\repository\io\netty\netty-resolver\4.1.107.Final\netty-resolver-4.1.107.Final.jar;D:\java\apache-maven-
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 5                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 18                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 536870912                                 {product} {ergonomic}
     bool ManagementServer                         = true                                      {product} {command line}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8552185856                                {product} {ergonomic}
   size_t MaxNewSize                               = 5129633792                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 0                                      {pd product} {ergonomic}
     bool ProfileInterpreter                       = false                                  {pd product} {command line}
    uintx ProfiledCodeHeapSize                     = 0                                      {pd product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8552185856                             {manageable} {ergonomic}
     intx TieredStopAtLevel                        = 1                                         {product} {command line}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
PATH=D:\soft\vmware\bin\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\java\apache-maven-3.9.10\bin;D:\java\jdk\jdk1.8.0_141\bin;D:\soft\git\Git\cmd;C:\Program Files (x86)\NetSarang\Xftp 8\;C:\Program Files (x86)\NetSarang\Xshell 7\;C:\Program Files (x86)\NetSarang\Xshell 8\;C:\Program Files\MySQL\MySQL Server 8.0\bin\;D:\java\nvm\nodejs\node_global;%NVM_HOME%;%NVM_SYMLINK%;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\app\idea\IntelliJ IDEA 2025.1.3\bin;;D:\soft\idea\IntelliJ IDEA Community Edition 2024.1.7\bin;;D:\soft\PyCharm Community Edition 2021.3.3\bin;;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;D:\java\apache-maven-3.9.10\bin;D:\java\nvm\nodejs\node_global;D:\app\Microsoft VS Code\bin;D:\app\codeBuddy\CodeBuddy\bin;D:\java\nvm\nvm;D:\java\nodejs
USERNAME=Administrator
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 63 Stepping 2, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled

JNI global refs:
JNI global refs: 932, weak refs: 0

JNI global refs memory usage: 10387, weak refs: 273

Process memory usage:
Resident Set Size: 66648K (0% of 33403940K total physical memory with 4661828K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader bootstrap                                                                       : 55016B
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 18453B


---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.3636)
OS uptime: 0 days 2:37 hours

CPU: total 24 (initial active 24) (12 cores per cpu, 2 threads per core) family 6 model 63 stepping 2 microcode 0x43, cx8, cmov, fxsr, ht, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, fma, vzeroupper, clflush
Processor Information for all 24 processors :
  Max Mhz: 2401, Current Mhz: 2401, Mhz Limit: 2401

Memory: 4k page, system-wide physical 32621M (4552M free)
TotalPageFile size 51821M (AvailPageFile size 2M)
current process WorkingSet (physical memory assigned to process): 65M, peak: 65M
current process commit charge ("private bytes"): 654M, peak: 655M

vm_info: OpenJDK 64-Bit Server VM (17.0.14+1-b1367.22) for windows-amd64 JRE (17.0.14+1-b1367.22), built on 2025-01-30 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
