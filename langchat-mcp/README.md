# LangChat MCP 模块

## 概述

LangChat MCP (Model Context Protocol) 模块是一个独立的、高度可扩展的服务编排模块，专门用于管理和编排各种外部服务。该模块采用模块化设计，可以独立部署和使用，也可以集成到LangChat主系统中。

## 🏗️ 模块架构

```
langchat-mcp/
├── langchat-mcp-core/          # 核心模块
│   ├── entity/                 # 实体类
│   ├── protocol/               # MCP协议实现
│   ├── service/                # 服务接口
│   ├── orchestration/          # 智能编排
│   └── config/                 # 配置类
└── langchat-mcp-server/        # 服务器模块
    ├── controller/             # REST API
    ├── config/                 # 服务器配置
    └── resources/              # 配置文件
```

## 🚀 核心功能

### 1. **MCP协议支持**
- 完整的MCP协议实现
- 支持HTTP、WebSocket、gRPC多种传输协议
- 标准化的请求/响应格式
- 完善的错误处理机制

### 2. **服务管理**
- 动态服务注册和发现
- 服务健康检查
- 服务版本管理
- 服务分类和标签

### 3. **智能编排**
- 策略模式的编排架构
- AI驱动的计划生成
- 依赖关系管理
- 条件执行和结果转换

### 4. **工具调用**
- 统一的工具调用接口
- 参数验证和转换
- 结果格式化
- 调用日志记录

## 📦 快速开始

### 1. 添加依赖

在你的项目中添加MCP模块依赖：

```xml
<dependency>
    <groupId>cn.tycoding</groupId>
    <artifactId>langchat-mcp-core</artifactId>
    <version>1.0.0</version>
</dependency>

<dependency>
    <groupId>cn.tycoding</groupId>
    <artifactId>langchat-mcp-server</artifactId>
    <version>1.0.0</version>
</dependency>
```

### 2. 配置文件

在 `application.yml` 中添加MCP配置：

```yaml
langchat:
  mcp:
    enabled: true
    default-timeout: 30000
    default-retries: 3
    health-check-enabled: true
    health-check-interval: 60
    
    # 连接池配置
    connection-pool:
      max-total: 200
      max-per-route: 50
      connection-timeout: 5000
      
    # 编排配置
    orchestration:
      enabled: true
      max-steps: 10
      ai-driven-enabled: true
      
    # 自定义服务配置
    services:
      wanx-image-generation:
        endpoint: "https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis"
        type: "HTTP"
        auth-type: "api_key"
        auth-config:
          api-key: "your-api-key"
        enabled: true
        priority: 80
```

### 3. 数据库初始化

执行MCP模块的数据库迁移脚本：

```sql
-- 执行 sql/mcp_module_migration.sql
```

### 4. 启动应用

```java
@SpringBootApplication
@EnableMcp  // 启用MCP模块
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

## 🔧 使用示例

### 1. 服务管理

```java
@Autowired
private McpServiceManager mcpServiceManager;

// 获取所有启用的服务
List<McpService> services = mcpServiceManager.getEnabledServices();

// 调用工具
McpResponse response = mcpServiceManager.callTool(
    "wanx-image-generation", 
    "text_to_image", 
    Map.of("prompt", "可爱的小猫")
);
```

### 2. 智能编排

```java
@Autowired
private McpOrchestrator mcpOrchestrator;

// 智能编排
String userInput = "帮我生成一张可爱小猫的图片，然后发布到我的网站上";
List<String> serviceIds = Arrays.asList("wanx-image-generation", "edgeone-pages");

OrchestrationPlan plan = mcpOrchestrator.orchestrate(userInput, serviceIds);
Map<String, Object> result = mcpOrchestrator.execute(plan, userId, conversationId);
```

### 3. 自定义策略

```java
@Component
public class CustomStrategy implements McpOrchestrator.OrchestrationStrategy {
    
    @Override
    public boolean canHandle(String userInput, List<McpService> availableServices) {
        return userInput.contains("自定义需求");
    }
    
    @Override
    public OrchestrationPlan createPlan(String userInput, List<McpService> availableServices) {
        // 创建自定义编排计划
        return plan;
    }
    
    @Override
    public int getPriority() {
        return 100; // 最高优先级
    }
}
```

## 🌐 REST API

### 服务管理

```bash
# 获取所有服务
GET /mcp/services

# 获取服务详情
GET /mcp/services/{serviceName}

# 获取服务工具
GET /mcp/services/{serviceName}/tools

# 健康检查
GET /mcp/services/{serviceName}/health
```

### 工具调用

```bash
# 调用工具
POST /mcp/tools/call
{
  "serviceName": "wanx-image-generation",
  "toolName": "text_to_image",
  "parameters": {
    "prompt": "可爱的小猫"
  },
  "userId": "user123",
  "conversationId": "conv456"
}

# 批量调用
POST /mcp/tools/call/batch
[
  {
    "serviceName": "service1",
    "toolName": "tool1",
    "parameters": {...}
  }
]
```

### 智能编排

```bash
# 执行编排
POST /mcp/orchestration/execute
{
  "userInput": "生成图片然后发布",
  "serviceIds": ["wanx-image-generation", "edgeone-pages"],
  "userId": "user123",
  "conversationId": "conv456"
}

# 预览编排计划
POST /mcp/orchestration/preview
{
  "userInput": "生成图片然后发布",
  "serviceIds": ["wanx-image-generation", "edgeone-pages"]
}
```

## 🔌 扩展开发

### 1. 添加新的服务类型

```java
@Component
public class CustomServiceHandler implements McpServiceHandler {
    
    @Override
    public boolean supports(String serviceType) {
        return "CUSTOM".equals(serviceType);
    }
    
    @Override
    public McpResponse handleRequest(McpService service, McpRequest request) {
        // 处理自定义服务请求
        return response;
    }
}
```

### 2. 添加新的编排策略

```java
@Component
public class DataAnalysisStrategy implements OrchestrationStrategy {
    
    @Override
    public boolean canHandle(String userInput, List<McpService> availableServices) {
        return userInput.contains("数据分析");
    }
    
    @Override
    public OrchestrationPlan createPlan(String userInput, List<McpService> availableServices) {
        // 创建数据分析编排计划
        return plan;
    }
}
```

### 3. 自定义工具定义

```java
public class CustomTools {
    
    public static McpTool dataAnalyzer() {
        Map<String, Object> inputSchema = Map.of(
            "type", "object",
            "properties", Map.of(
                "data", Map.of("type", "string", "description", "要分析的数据"),
                "type", Map.of("type", "string", "description", "分析类型")
            )
        );
        
        return McpTool.create("analyze_data", "数据分析工具", inputSchema);
    }
}
```

## 📊 监控和统计

### 1. 服务统计

```bash
# 获取服务统计
GET /mcp/services/{serviceName}/stats

# 获取所有服务统计
GET /mcp/stats
```

### 2. 编排统计

```bash
# 获取编排统计
GET /mcp/orchestration/stats
```

### 3. 调用日志

```bash
# 查询调用日志
GET /mcp/logs?serviceName=xxx&userId=xxx&startTime=xxx&endTime=xxx
```

## 🔒 安全配置

### 1. 认证配置

```yaml
langchat:
  mcp:
    security:
      enabled: true
      auth-required: true
      rate-limit-enabled: true
      rate-limit-per-minute: 100
```

### 2. 服务认证

```yaml
langchat:
  mcp:
    services:
      secure-service:
        auth-type: "oauth2"
        auth-config:
          client-id: "your-client-id"
          client-secret: "your-client-secret"
          token-url: "https://auth.example.com/token"
```

## 🚀 部署指南

### 1. 独立部署

```bash
# 构建
mvn clean package

# 运行
java -jar langchat-mcp-server-1.0.0.jar
```

### 2. Docker部署

```dockerfile
FROM openjdk:17-jre-slim
COPY langchat-mcp-server-1.0.0.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

### 3. 集成部署

将MCP模块集成到现有的LangChat系统中，通过依赖注入使用。

## 📝 最佳实践

1. **服务设计**: 保持服务的单一职责，避免过于复杂的工具
2. **错误处理**: 实现完善的错误处理和重试机制
3. **性能优化**: 使用连接池和缓存提高性能
4. **监控告警**: 配置完善的监控和告警机制
5. **安全防护**: 实施适当的认证和授权机制

## 🤝 贡献指南

欢迎贡献代码和提出建议！请遵循以下步骤：

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 GNU Affero General Public License v3.0 许可证。
