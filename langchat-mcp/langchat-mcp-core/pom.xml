<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    
    <parent>
        <groupId>cn.tycoding</groupId>
        <artifactId>langchat-mcp</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>langchat-mcp-core</artifactId>
    <packaging>jar</packaging>
    <name>LangChat MCP Core</name>
    <description>LangChat MCP 核心模块，包含MCP协议实现、服务管理、智能编排等核心功能</description>

    <dependencies>
        <!-- LangChat Common Dependencies -->
        <dependency>
            <groupId>cn.tycoding</groupId>
            <artifactId>langchat-common-core</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>cn.tycoding</groupId>
            <artifactId>langchat-common-ai</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- WebSocket Support -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>

        <!-- JSON Processing -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>

        <!-- Redis Support (Optional) -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- Async Support -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.tycoding</groupId>
            <artifactId>langchat-ai-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>image-server</artifactId>
            <version>1.1.0</version>
        </dependency>

    </dependencies>
</project>
