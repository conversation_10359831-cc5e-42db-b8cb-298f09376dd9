/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.orchestration.context;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.tycoding.langchat.mcp.core.orchestration.impl.FlexibleMcpOrchestrator;
import cn.tycoding.langchat.mcp.core.protocol.McpResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 高级参数解析器
 * 支持复杂的参数引用、表达式计算和数据转换
 * 
 * <AUTHOR>
 * @since 2025/06/24
 */
@Slf4j
@Component
public class AdvancedParameterResolver {

    // 参数引用模式
    private static final Pattern REFERENCE_PATTERN = Pattern.compile("\\$\\{([^}]+)\\}");
    
    // 函数调用模式
    private static final Pattern FUNCTION_PATTERN = Pattern.compile("(\\w+)\\(([^)]*)\\)");

    /**
     * 解析参数值，支持复杂引用和函数
     */
    public Object resolveParameterValue(Object value, Map<String, FlexibleMcpOrchestrator.ExecutionStep> stepMap,
                                       FlexibleMcpOrchestrator.GlobalExecutionContext globalContext) {
        if (value == null) {
            return null;
        }

        if (value instanceof String) {
            return resolveStringValue((String) value, stepMap, globalContext);
        } else if (value instanceof Map) {
            return resolveMapValue((Map<String, Object>) value, stepMap, globalContext);
        } else if (value instanceof List) {
            return resolveListValue((List<Object>) value, stepMap, globalContext);
        }

        return value;
    }

    /**
     * 解析字符串值
     */
    private String resolveStringValue(String value, Map<String, FlexibleMcpOrchestrator.ExecutionStep> stepMap,
                                     FlexibleMcpOrchestrator.GlobalExecutionContext globalContext) {
        if (StrUtil.isBlank(value) || !value.contains("${")) {
            return value;
        }

        Matcher matcher = REFERENCE_PATTERN.matcher(value);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String reference = matcher.group(1);
            String replacement = resolveReference(reference, stepMap, globalContext);
            
            if (replacement != null) {
                matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
            } else {
                log.warn("无法解析参数引用: {}", reference);
                matcher.appendReplacement(sb, Matcher.quoteReplacement(matcher.group()));
            }
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 解析Map值
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> resolveMapValue(Map<String, Object> map, 
                                               Map<String, FlexibleMcpOrchestrator.ExecutionStep> stepMap,
                                               FlexibleMcpOrchestrator.GlobalExecutionContext globalContext) {
        Map<String, Object> resolvedMap = new HashMap<>();
        
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            Object resolvedValue = resolveParameterValue(entry.getValue(), stepMap, globalContext);
            resolvedMap.put(entry.getKey(), resolvedValue);
        }
        
        return resolvedMap;
    }

    /**
     * 解析List值
     */
    private List<Object> resolveListValue(List<Object> list, 
                                         Map<String, FlexibleMcpOrchestrator.ExecutionStep> stepMap,
                                         FlexibleMcpOrchestrator.GlobalExecutionContext globalContext) {
        List<Object> resolvedList = new ArrayList<>();
        
        for (Object item : list) {
            Object resolvedItem = resolveParameterValue(item, stepMap, globalContext);
            resolvedList.add(resolvedItem);
        }
        
        return resolvedList;
    }

    /**
     * 解析引用
     */
    private String resolveReference(String reference, Map<String, FlexibleMcpOrchestrator.ExecutionStep> stepMap,
                                   FlexibleMcpOrchestrator.GlobalExecutionContext globalContext) {
        
        // 检查是否是函数调用
        Matcher functionMatcher = FUNCTION_PATTERN.matcher(reference);
        if (functionMatcher.matches()) {
            String functionName = functionMatcher.group(1);
            String args = functionMatcher.group(2);
            return executeFunction(functionName, args, stepMap, globalContext);
        }

        // 普通引用解析
        String[] parts = reference.split("\\.");
        if (parts.length < 2) {
            log.warn("参数引用格式错误: {}", reference);
            return null;
        }

        String prefix = parts[0];
        
        switch (prefix) {
            case "global":
                return resolveGlobalReference(parts, globalContext);
            case "shared":
                return resolveSharedReference(parts, globalContext);
            case "context":
                return resolveContextReference(parts, globalContext);
            default:
                return resolveStepReference(parts, stepMap);
        }
    }

    /**
     * 解析全局变量引用
     */
    private String resolveGlobalReference(String[] parts, FlexibleMcpOrchestrator.GlobalExecutionContext globalContext) {
        if (globalContext == null || parts.length < 2) {
            return null;
        }

        String key = parts[1];
        Object value = globalContext.getGlobalVariable(key);
        
        if (value != null && parts.length > 2) {
            // 支持嵌套访问：global.stepId_result.field
            return extractNestedValue(value, Arrays.copyOfRange(parts, 2, parts.length));
        }
        
        return value != null ? String.valueOf(value) : null;
    }

    /**
     * 解析共享数据引用
     */
    private String resolveSharedReference(String[] parts, FlexibleMcpOrchestrator.GlobalExecutionContext globalContext) {
        if (globalContext == null || parts.length < 2) {
            return null;
        }

        String key = parts[1];
        Object value = globalContext.getSharedData(key);
        
        if (value != null && parts.length > 2) {
            return extractNestedValue(value, Arrays.copyOfRange(parts, 2, parts.length));
        }
        
        return value != null ? String.valueOf(value) : null;
    }

    /**
     * 解析上下文引用
     */
    private String resolveContextReference(String[] parts, FlexibleMcpOrchestrator.GlobalExecutionContext globalContext) {
        if (globalContext == null || parts.length < 2) {
            return null;
        }

        String field = parts[1];
        switch (field) {
            case "userId":
                return globalContext.getUserId();
            case "conversationId":
                return globalContext.getConversationId();
            case "userInput":
                return globalContext.getUserInput();
            case "modelId":
                return globalContext.getModelId();
            case "planId":
                return globalContext.getPlanId();
            case "startTime":
                return String.valueOf(globalContext.getStartTime());
            case "duration":
                Long duration = globalContext.getDuration();
                return duration != null ? String.valueOf(duration) : null;
            default:
                return null;
        }
    }

    /**
     * 解析步骤引用
     */
    private String resolveStepReference(String[] parts, Map<String, FlexibleMcpOrchestrator.ExecutionStep> stepMap) {
        String stepId = parts[0];
        FlexibleMcpOrchestrator.ExecutionStep step = stepMap.get(stepId);
        
        if (step == null) {
            log.warn("未找到步骤: {}", stepId);
            return null;
        }

        if (!step.isCompleted()) {
            log.warn("步骤 {} 尚未完成", stepId);
            return null;
        }

        if (parts.length < 2) {
            return null;
        }

        String field = parts[1];
        switch (field) {
            case "result":
                if (parts.length == 2) {
                    return extractResultValue(step.getResult());
                } else {
                    return extractNestedResultField(step.getResult(), Arrays.copyOfRange(parts, 2, parts.length));
                }
            case "parameters":
                if (parts.length > 2) {
                    Object paramValue = step.getParameters().get(parts[2]);
                    return paramValue != null ? String.valueOf(paramValue) : null;
                }
                break;
            case "context":
                if (parts.length > 2) {
                    Object contextValue = step.getExecutionContext().get(parts[2]);
                    return contextValue != null ? String.valueOf(contextValue) : null;
                }
                break;
            case "duration":
                Long duration = step.getDuration();
                return duration != null ? String.valueOf(duration) : null;
            case "success":
                return String.valueOf(step.isCompleted() && step.getResult() != null && step.getResult().isSuccess());
        }

        return null;
    }

    /**
     * 执行函数
     */
    private String executeFunction(String functionName, String args, 
                                  Map<String, FlexibleMcpOrchestrator.ExecutionStep> stepMap,
                                  FlexibleMcpOrchestrator.GlobalExecutionContext globalContext) {
        
        String[] argArray = StrUtil.isBlank(args) ? new String[0] : args.split(",");
        for (int i = 0; i < argArray.length; i++) {
            argArray[i] = argArray[i].trim();
        }

        switch (functionName) {
            case "json":
                // json(stepId.result) - 将结果转换为JSON字符串
                if (argArray.length > 0) {
                    String value = resolveReference(argArray[0], stepMap, globalContext);
                    return value != null ? JSONUtil.toJsonStr(value) : null;
                }
                break;
                
            case "length":
                // length(stepId.result) - 获取数组或字符串长度
                if (argArray.length > 0) {
                    String value = resolveReference(argArray[0], stepMap, globalContext);
                    if (value != null) {
                        try {
                            Object obj = JSONUtil.parse(value);
                            if (obj instanceof List) {
                                return String.valueOf(((List<?>) obj).size());
                            } else if (obj instanceof Map) {
                                return String.valueOf(((Map<?, ?>) obj).size());
                            }
                        } catch (Exception e) {
                            // 如果不是JSON，返回字符串长度
                            return String.valueOf(value.length());
                        }
                    }
                }
                break;
                
            case "first":
                // first(stepId.result) - 获取数组第一个元素
                if (argArray.length > 0) {
                    String value = resolveReference(argArray[0], stepMap, globalContext);
                    if (value != null) {
                        try {
                            Object obj = JSONUtil.parse(value);
                            if (obj instanceof List && !((List<?>) obj).isEmpty()) {
                                return String.valueOf(((List<?>) obj).get(0));
                            }
                        } catch (Exception e) {
                            log.warn("解析JSON失败: {}", value);
                        }
                    }
                }
                break;
                
            case "concat":
                // concat(arg1, arg2, ...) - 连接多个值
                StringBuilder sb = new StringBuilder();
                for (String arg : argArray) {
                    String value = resolveReference(arg, stepMap, globalContext);
                    if (value != null) {
                        sb.append(value);
                    }
                }
                return sb.toString();
                
            default:
                log.warn("未知函数: {}", functionName);
        }

        return null;
    }

    /**
     * 提取嵌套值
     */
    @SuppressWarnings("unchecked")
    private String extractNestedValue(Object value, String[] path) {
        Object current = value;
        
        for (String field : path) {
            if (current instanceof Map) {
                current = ((Map<String, Object>) current).get(field);
            } else if (current instanceof List && field.matches("\\d+")) {
                List<Object> list = (List<Object>) current;
                int index = Integer.parseInt(field);
                if (index >= 0 && index < list.size()) {
                    current = list.get(index);
                } else {
                    return null;
                }
            } else {
                return null;
            }
            
            if (current == null) {
                return null;
            }
        }
        
        return String.valueOf(current);
    }

    /**
     * 提取结果值
     */
    private String extractResultValue(McpResponse response) {
        if (response == null || response.getResult() == null) {
            return null;
        }
        
        Object result = response.getResult();
        if (result instanceof String) {
            return (String) result;
        } else {
            return JSONUtil.toJsonStr(result);
        }
    }

    /**
     * 提取嵌套结果字段
     */
    private String extractNestedResultField(McpResponse response, String[] fieldPath) {
        if (response == null || response.getResult() == null) {
            return null;
        }
        
        return extractNestedValue(response.getResult(), fieldPath);
    }
}
