/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * MCP服务参数定义
 * 
 * <AUTHOR>
 * @since 2024/12/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class McpServiceParameter {

    /**
     * 参数名称
     */
    private String name;

    /**
     * 参数显示名称
     */
    private String displayName;

    /**
     * 参数描述
     */
    private String description;

    /**
     * 参数类型：string, number, boolean, array, object
     */
    private String type;

    /**
     * 是否必填
     */
    private Boolean required;

    /**
     * 默认值
     */
    private Object defaultValue;

    /**
     * 参数分组（用于UI分组显示）
     */
    private String group;

    /**
     * 参数顺序（用于UI排序）
     */
    private Integer order;

    /**
     * 是否敏感信息（如密码、API Key等）
     */
    private Boolean sensitive;

    /**
     * 参数验证规则
     */
    private ParameterValidation validation;

    /**
     * UI渲染提示
     */
    private ParameterUIHint uiHint;

    /**
     * 参数依赖关系（当某个参数值满足条件时才显示此参数）
     */
    private List<ParameterDependency> dependencies;

    /**
     * 参数验证规则
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ParameterValidation {
        /** 最小值（数字类型） */
        private Number min;

        /** 最大值（数字类型） */
        private Number max;

        /** 最小长度（字符串类型） */
        private Integer minLength;

        /** 最大长度（字符串类型） */
        private Integer maxLength;

        /** 正则表达式验证 */
        private String pattern;

        /** 枚举值列表 */
        private List<Object> enumValues;

        /** 自定义验证错误消息 */
        private String errorMessage;

        // ========== 参数提取相关字段 ==========

        /** 是否从用户输入中提取 */
        private Boolean extractFromInput;

        /** AI提取提示词 */
        private String extractionPrompt;

        /** 关键词列表（用于关键词匹配提取） */
        private List<String> keywords;

        /** 默认值（当提取失败时使用） */
        private String defaultValue;
    }

    /**
     * UI渲染提示
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ParameterUIHint {
        /** 输入框类型：text, password, textarea, select, radio, checkbox, file, url, email */
        private String inputType;
        
        /** 占位符文本 */
        private String placeholder;
        
        /** 帮助文本 */
        private String helpText;
        
        /** 选项列表（用于select、radio等） */
        private List<ParameterOption> options;
        
        /** 是否多行输入 */
        private Boolean multiline;
        
        /** 输入框宽度（small, medium, large, full） */
        private String width;
    }

    /**
     * 参数选项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ParameterOption {
        /** 选项值 */
        private Object value;
        
        /** 选项显示文本 */
        private String label;
        
        /** 选项描述 */
        private String description;
        
        /** 是否禁用 */
        private Boolean disabled;
    }

    /**
     * 参数依赖关系
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ParameterDependency {
        /** 依赖的参数名称 */
        private String parameterName;
        
        /** 依赖条件：equals, not_equals, in, not_in, greater_than, less_than */
        private String condition;
        
        /** 依赖值 */
        private Object value;
    }
}
