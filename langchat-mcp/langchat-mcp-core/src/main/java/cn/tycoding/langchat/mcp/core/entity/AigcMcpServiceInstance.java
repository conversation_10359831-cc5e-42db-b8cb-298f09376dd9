/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * MCP服务实例配置实体
 * 用于存储用户对特定MCP服务的个性化配置
 * 
 * <AUTHOR>
 * @since 2024/12/19
 */
@Data
@Accessors(chain = true)
@TableName("aigc_mcp_service_instance")
public class AigcMcpServiceInstance implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 实例名称（用户自定义）
     */
    private String instanceName;

    /**
     * 实例显示名称
     */
    private String displayName;

    /**
     * 实例描述
     */
    private String description;

    /**
     * 关联的MCP服务ID
     */
    private String serviceId;

    /**
     * 服务名称（冗余字段，便于查询）
     */
    private String serviceName;

    /**
     * 用户ID（实例所属用户）
     */
    private String userId;

    /**
     * 组织ID（可选，用于团队共享）
     */
    private String organizationId;

    /**
     * 实例配置JSON（包含参数值、认证信息等）
     */
    private String config;

    /**
     * 实例标签
     */
    private String tags;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 是否为默认实例
     */
    private Boolean isDefault;

    /**
     * 是否公开（其他用户可见）
     */
    private Boolean isPublic;

    /**
     * 访问权限：private(私有), shared(共享), public(公开)
     */
    private String accessLevel;

    /**
     * 实例状态：active(活跃), inactive(非活跃), error(错误), testing(测试中)
     */
    private String status;

    /**
     * 最后使用时间
     */
    private Date lastUsedTime;

    /**
     * 使用次数
     */
    private Integer usageCount;

    /**
     * 实例版本
     */
    private String version;

    /**
     * 实例图标
     */
    private String icon;

    /**
     * 实例颜色（用于UI区分）
     */
    private String color;

    /**
     * 排序权重
     */
    private Integer sortOrder;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 备注
     */
    private String remark;
}
