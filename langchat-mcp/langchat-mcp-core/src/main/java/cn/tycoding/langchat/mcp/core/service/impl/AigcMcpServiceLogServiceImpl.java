/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.tycoding.langchat.mcp.core.entity.AigcMcpServiceLog;
import cn.tycoding.langchat.mcp.core.mapper.AigcMcpServiceLogMapper;
import cn.tycoding.langchat.mcp.core.protocol.McpResponse;
import cn.tycoding.langchat.mcp.core.service.AigcMcpServiceLogService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * MCP服务调用日志Service实现
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AigcMcpServiceLogServiceImpl extends ServiceImpl<AigcMcpServiceLogMapper, AigcMcpServiceLog> implements AigcMcpServiceLogService {

    @Override
    public IPage<AigcMcpServiceLog> list(AigcMcpServiceLog log, Map<String, Object> queryPage) {
        int page = (Integer) queryPage.getOrDefault("page", 1);
        int limit = (Integer) queryPage.getOrDefault("limit", 10);
        
        IPage<AigcMcpServiceLog> pageObj = new Page<>(page, limit);
        LambdaQueryWrapper<AigcMcpServiceLog> queryWrapper = Wrappers.<AigcMcpServiceLog>lambdaQuery()
                .like(StrUtil.isNotBlank(log.getServiceName()), AigcMcpServiceLog::getServiceName, log.getServiceName())
                .like(StrUtil.isNotBlank(log.getToolName()), AigcMcpServiceLog::getToolName, log.getToolName())
                .eq(StrUtil.isNotBlank(log.getUserId()), AigcMcpServiceLog::getUserId, log.getUserId())
                .eq(StrUtil.isNotBlank(log.getConversationId()), AigcMcpServiceLog::getConversationId, log.getConversationId())
                .eq(log.getSuccess() != null, AigcMcpServiceLog::getSuccess, log.getSuccess())
                .orderByDesc(AigcMcpServiceLog::getCreateTime);
        return baseMapper.selectPage(pageObj, queryWrapper);
    }

    @Override
    public void recordToolCall(String serviceId, String serviceName, String toolName, 
                              Map<String, Object> parameters, McpResponse response, 
                              String userId, String conversationId, long executionTime) {
        try {
            AigcMcpServiceLog logRecord = new AigcMcpServiceLog()
                    .setServiceId(serviceId)
                    .setServiceName(serviceName)
                    .setToolName(toolName)
                    .setRequestParams(JSONUtil.toJsonStr(parameters))
                    .setSuccess(response.isSuccess())
                    .setExecutionTime(executionTime)
                    .setUserId(userId)
                    .setConversationId(conversationId)
                    .setCreateTime(new Date());

            if (response.isSuccess()) {
                logRecord.setResponseData(JSONUtil.toJsonStr(response.getResult()));
            } else {
                logRecord.setErrorMessage(response.getError() != null ? response.getError().getMessage() : "未知错误");
            }

            baseMapper.insert(logRecord);
        } catch (Exception e) {
            log.error("记录MCP工具调用日志失败", e);
        }
    }

    @Override
    @Async
    public void recordToolCallAsync(String serviceId, String serviceName, String toolName, 
                                   Map<String, Object> parameters, McpResponse response, 
                                   String userId, String conversationId, long executionTime) {
        recordToolCall(serviceId, serviceName, toolName, parameters, response, userId, conversationId, executionTime);
    }

    @Override
    public List<AigcMcpServiceLog> getLogsByServiceName(String serviceName) {
        return baseMapper.selectList(Wrappers.<AigcMcpServiceLog>lambdaQuery()
                .eq(AigcMcpServiceLog::getServiceName, serviceName)
                .orderByDesc(AigcMcpServiceLog::getCreateTime)
                .last("LIMIT 100"));
    }

    @Override
    public List<AigcMcpServiceLog> getLogsByUserId(String userId) {
        return baseMapper.selectList(Wrappers.<AigcMcpServiceLog>lambdaQuery()
                .eq(AigcMcpServiceLog::getUserId, userId)
                .orderByDesc(AigcMcpServiceLog::getCreateTime)
                .last("LIMIT 100"));
    }

    @Override
    public List<AigcMcpServiceLog> getLogsByConversationId(String conversationId) {
        return baseMapper.selectList(Wrappers.<AigcMcpServiceLog>lambdaQuery()
                .eq(AigcMcpServiceLog::getConversationId, conversationId)
                .orderByDesc(AigcMcpServiceLog::getCreateTime));
    }

    @Override
    public Map<String, Object> getCallStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        // 总调用次数
        Long totalCalls = baseMapper.selectCount(null);
        stats.put("totalCalls", totalCalls);
        
        // 成功调用次数
        Long successCalls = baseMapper.selectCount(Wrappers.<AigcMcpServiceLog>lambdaQuery()
                .eq(AigcMcpServiceLog::getSuccess, true));
        stats.put("successCalls", successCalls);
        
        // 失败调用次数
        Long failedCalls = totalCalls - successCalls;
        stats.put("failedCalls", failedCalls);
        
        // 成功率
        double successRate = totalCalls > 0 ? (double) successCalls / totalCalls * 100 : 0;
        stats.put("successRate", Math.round(successRate * 100.0) / 100.0);
        
        return stats;
    }

    @Override
    public Map<String, Object> getServiceCallStatistics(String serviceName) {
        Map<String, Object> stats = new HashMap<>();
        
        LambdaQueryWrapper<AigcMcpServiceLog> baseQuery = Wrappers.<AigcMcpServiceLog>lambdaQuery()
                .eq(AigcMcpServiceLog::getServiceName, serviceName);
        
        // 总调用次数
        Long totalCalls = baseMapper.selectCount(baseQuery);
        stats.put("totalCalls", totalCalls);
        
        // 成功调用次数
        Long successCalls = baseMapper.selectCount(baseQuery.clone()
                .eq(AigcMcpServiceLog::getSuccess, true));
        stats.put("successCalls", successCalls);
        
        // 失败调用次数
        Long failedCalls = totalCalls - successCalls;
        stats.put("failedCalls", failedCalls);
        
        // 成功率
        double successRate = totalCalls > 0 ? (double) successCalls / totalCalls * 100 : 0;
        stats.put("successRate", Math.round(successRate * 100.0) / 100.0);
        
        return stats;
    }

    @Override
    public void cleanExpiredLogs(int days) {
        try {
            // 计算过期时间
            long expiredTime = System.currentTimeMillis() - (days * 24L * 60 * 60 * 1000);
            Date expiredDate = new Date(expiredTime);
            
            int deletedCount = baseMapper.delete(Wrappers.<AigcMcpServiceLog>lambdaQuery()
                    .lt(AigcMcpServiceLog::getCreateTime, expiredDate));
            
            log.info("清理过期MCP调用日志完成，删除 {} 条记录", deletedCount);
        } catch (Exception e) {
            log.error("清理过期MCP调用日志失败", e);
        }
    }
}
