/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.orchestration.context;

import cn.hutool.json.JSONUtil;
import cn.tycoding.langchat.mcp.core.orchestration.impl.FlexibleMcpOrchestrator;
import cn.tycoding.langchat.mcp.core.protocol.McpResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * MCP执行上下文管理器
 * 负责管理MCP服务间的上下文传递和数据共享
 * 
 * <AUTHOR>
 * @since 2025/06/24
 */
@Slf4j
@Component
public class McpExecutionContextManager {

    // 存储活跃的执行上下文
    private final Map<String, FlexibleMcpOrchestrator.GlobalExecutionContext> activeContexts = new ConcurrentHashMap<>();
    
    // 上下文过期时间（毫秒）
    private static final long CONTEXT_EXPIRE_TIME = 30 * 60 * 1000; // 30分钟

    /**
     * 创建新的执行上下文
     */
    public FlexibleMcpOrchestrator.GlobalExecutionContext createContext(String planId, String userId, 
                                                                       String conversationId, String userInput, String modelId) {
        FlexibleMcpOrchestrator.GlobalExecutionContext context = new FlexibleMcpOrchestrator.GlobalExecutionContext();
        context.setPlanId(planId);
        context.setUserId(userId);
        context.setConversationId(conversationId);
        context.setUserInput(userInput);
        context.setModelId(modelId);
        
        // 存储上下文
        activeContexts.put(planId, context);
        
        log.info("创建执行上下文: planId={}, userId={}", planId, userId);
        return context;
    }

    /**
     * 获取执行上下文
     */
    public FlexibleMcpOrchestrator.GlobalExecutionContext getContext(String planId) {
        return activeContexts.get(planId);
    }

    /**
     * 更新步骤结果到上下文
     */
    public void updateStepResult(String planId, String stepId, McpResponse response) {
        FlexibleMcpOrchestrator.GlobalExecutionContext context = activeContexts.get(planId);
        if (context == null) {
            log.warn("未找到执行上下文: planId={}", planId);
            return;
        }

        if (response.isSuccess() && response.getResult() != null) {
            // 存储步骤结果
            context.putGlobalVariable(stepId + "_result", response.getResult());
            
            // 智能提取结果字段
            extractResultFields(context, stepId, response.getResult());
            
            log.debug("更新步骤结果到上下文: planId={}, stepId={}", planId, stepId);
        }
    }

    /**
     * 智能提取结果字段
     */
    @SuppressWarnings("unchecked")
    private void extractResultFields(FlexibleMcpOrchestrator.GlobalExecutionContext context, String stepId, Object result) {
        if (result instanceof Map) {
            Map<String, Object> resultMap = (Map<String, Object>) result;
            
            // 提取常见字段
            String[] commonFields = {"url", "content", "text", "data", "value", "id", "name", "title", "description"};
            for (String field : commonFields) {
                if (resultMap.containsKey(field)) {
                    context.putGlobalVariable(stepId + "_" + field, resultMap.get(field));
                }
            }
            
            // 特殊处理图片URL
            if (resultMap.containsKey("url") && stepId.contains("image")) {
                context.putSharedData("last_image_url", resultMap.get("url"));
            }
            
            // 特殊处理搜索结果
            if (resultMap.containsKey("content") && stepId.contains("search")) {
                context.putSharedData("last_search_content", resultMap.get("content"));
            }
            
        } else if (result instanceof List) {
            List<Object> resultList = (List<Object>) result;
            context.putGlobalVariable(stepId + "_count", resultList.size());
            
            // 如果列表不为空，提取第一个元素
            if (!resultList.isEmpty()) {
                context.putGlobalVariable(stepId + "_first", resultList.get(0));
            }
        } else if (result instanceof String) {
            // 字符串结果直接存储
            context.putGlobalVariable(stepId + "_text", result);
        }
    }

    /**
     * 解析参数引用
     */
    public String resolveParameterReference(String planId, String reference) {
        FlexibleMcpOrchestrator.GlobalExecutionContext context = activeContexts.get(planId);
        if (context == null) {
            log.warn("未找到执行上下文: planId={}", planId);
            return null;
        }

        String[] parts = reference.split("\\.");
        if (parts.length < 2) {
            return null;
        }

        String prefix = parts[0];
        String key = parts[1];

        switch (prefix) {
            case "global":
                Object globalValue = context.getGlobalVariable(key);
                return globalValue != null ? String.valueOf(globalValue) : null;
                
            case "shared":
                Object sharedValue = context.getSharedData(key);
                return sharedValue != null ? String.valueOf(sharedValue) : null;
                
            case "context":
                // 访问上下文基本信息
                switch (key) {
                    case "userId":
                        return context.getUserId();
                    case "conversationId":
                        return context.getConversationId();
                    case "userInput":
                        return context.getUserInput();
                    case "modelId":
                        return context.getModelId();
                    default:
                        return null;
                }
                
            default:
                // 尝试作为步骤结果引用
                if (parts.length >= 3 && "result".equals(parts[1])) {
                    String stepId = parts[0];
                    String field = parts[2];
                    Object stepResult = context.getGlobalVariable(stepId + "_" + field);
                    return stepResult != null ? String.valueOf(stepResult) : null;
                }
                return null;
        }
    }

    /**
     * 批量解析参数
     */
    public Map<String, Object> resolveParameters(String planId, Map<String, Object> parameters) {
        Map<String, Object> resolvedParams = new HashMap<>();
        
        for (Map.Entry<String, Object> entry : parameters.entrySet()) {
            Object value = entry.getValue();
            if (value instanceof String) {
                String strValue = (String) value;
                String resolved = resolveParameterReferences(planId, strValue);
                resolvedParams.put(entry.getKey(), resolved);
            } else {
                resolvedParams.put(entry.getKey(), value);
            }
        }
        
        return resolvedParams;
    }

    /**
     * 解析字符串中的所有参数引用
     */
    private String resolveParameterReferences(String planId, String value) {
        if (value == null || !value.contains("${")) {
            return value;
        }

        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("\\$\\{([^}]+)\\}");
        java.util.regex.Matcher matcher = pattern.matcher(value);
        
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            String reference = matcher.group(1);
            String replacement = resolveParameterReference(planId, reference);
            if (replacement != null) {
                matcher.appendReplacement(sb, java.util.regex.Matcher.quoteReplacement(replacement));
            } else {
                log.warn("无法解析参数引用: {}", reference);
                matcher.appendReplacement(sb, java.util.regex.Matcher.quoteReplacement(matcher.group()));
            }
        }
        matcher.appendTail(sb);
        
        return sb.toString();
    }

    /**
     * 清理过期的上下文
     */
    public void cleanupExpiredContexts() {
        long currentTime = System.currentTimeMillis();
        Iterator<Map.Entry<String, FlexibleMcpOrchestrator.GlobalExecutionContext>> iterator = 
            activeContexts.entrySet().iterator();
        
        while (iterator.hasNext()) {
            Map.Entry<String, FlexibleMcpOrchestrator.GlobalExecutionContext> entry = iterator.next();
            FlexibleMcpOrchestrator.GlobalExecutionContext context = entry.getValue();
            
            if (currentTime - context.getStartTime() > CONTEXT_EXPIRE_TIME) {
                iterator.remove();
                log.info("清理过期上下文: planId={}", entry.getKey());
            }
        }
    }

    /**
     * 手动清理上下文
     */
    public void removeContext(String planId) {
        FlexibleMcpOrchestrator.GlobalExecutionContext removed = activeContexts.remove(planId);
        if (removed != null) {
            removed.markCompleted();
            log.info("手动清理上下文: planId={}, 执行时长={}ms", planId, removed.getDuration());
        }
    }

    /**
     * 获取上下文统计信息
     */
    public Map<String, Object> getContextStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("activeContextCount", activeContexts.size());
        stats.put("contexts", new ArrayList<>(activeContexts.keySet()));
        return stats;
    }
}
