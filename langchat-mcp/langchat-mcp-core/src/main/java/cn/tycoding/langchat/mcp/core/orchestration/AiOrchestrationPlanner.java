/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.orchestration;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.tycoding.langchat.ai.core.service.LangChatService;
import cn.tycoding.langchat.common.ai.dto.ChatReq;
import cn.tycoding.langchat.mcp.core.constants.McpServiceConstants;
import cn.tycoding.langchat.mcp.core.entity.AigcMcpService;
import cn.tycoding.langchat.mcp.core.orchestration.context.ParameterReferenceStandardizer;
import cn.tycoding.langchat.mcp.core.orchestration.impl.FlexibleMcpOrchestrator;
import cn.tycoding.langchat.mcp.core.service.AigcMcpServiceService;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * AI驱动的编排计划生成器
 * 通过AI分析用户意图，智能生成MCP服务执行计划
 * 
 * <AUTHOR>
 * @since 2025/06/15
 */
@Slf4j
@Service
@AllArgsConstructor
public class AiOrchestrationPlanner {

    private final LangChatService langChatService;

    private final AigcMcpServiceService aigcMcpServiceService;

    private final ParameterReferenceStandardizer referenceStandardizer;

    /**
     * 基于AI分析生成编排计划
     */
    public FlexibleMcpOrchestrator.OrchestrationPlan generatePlan(String userInput,
                                                                 List<AigcMcpService> availableServices,
                                                                 String systemPrompt,
                                                                 String modelId) {
        try {
            log.info("使用AI生成编排计划，用户输入: {}", userInput);
            
            // 1. 构建AI提示词
            String prompt = buildOrchestrationPrompt(userInput, availableServices, systemPrompt);

            // 2. 调用AI生成计划
            String aiResponse = callAiForPlanning(prompt, modelId);
            
            // 3. 解析AI响应为执行计划
            FlexibleMcpOrchestrator.OrchestrationPlan plan = parseAiResponse(aiResponse, availableServices);
            
            if (plan != null) {
                log.info("AI生成编排计划成功，策略: {}, 步骤数: {}", 
                    plan.getStrategyName(), plan.getSteps().size());
            }
            
            return plan;
            
        } catch (Exception e) {
            log.error("AI生成编排计划失败", e);
            return null;
        }
    }

    /**
     * 构建编排提示词（增强版 - 包含详细参数定义）
     */
    private String buildOrchestrationPrompt(String userInput, List<AigcMcpService> availableServices, String systemPrompt) {
        StringBuilder prompt = new StringBuilder();

        // 系统提示词
        if (systemPrompt != null && !systemPrompt.trim().isEmpty()) {
            prompt.append("系统指导原则:\n").append(systemPrompt).append("\n\n");
        }

        prompt.append("你是一个专业的MCP服务编排专家。请根据用户需求生成精确的执行计划。\n\n");
        prompt.append("重要要求:\n");
        prompt.append("1. 必须使用服务定义中的确切参数名称\n");
        prompt.append("2. 正确设置步骤依赖关系\n");
        prompt.append("3. 使用 ${stepId.result.field} 格式引用前面步骤的结果\n");
        prompt.append("4. 参数值要么是具体值，要么是正确的引用格式\n\n");

        // 用户输入
        prompt.append("用户需求: ").append(userInput).append("\n\n");

        // 可用服务及详细参数定义
        prompt.append("可用的MCP服务及其参数定义:\n");
        for (AigcMcpService service : availableServices) {
            prompt.append("服务名称: ").append(service.getName()).append("\n");
            prompt.append("服务描述: ").append(service.getDescription()).append("\n");

            // 添加详细的工具和参数定义
            if (StrUtil.isNotBlank(service.getTools())) {
                try {
                    JSONArray tools = JSONUtil.parseArray(service.getTools());
                    for (Object toolObj : tools) {
                        JSONObject tool = (JSONObject) toolObj;
                        prompt.append("  工具: ").append(tool.getStr("name")).append("\n");
                        prompt.append("  描述: ").append(tool.getStr("description")).append("\n");

                        // 添加参数定义
                        JSONObject inputSchema = tool.getJSONObject("inputSchema");
                        if (inputSchema != null && inputSchema.containsKey("properties")) {
                            JSONObject properties = inputSchema.getJSONObject("properties");
                            prompt.append("  参数:\n");
                            for (String paramName : properties.keySet()) {
                                JSONObject paramDef = properties.getJSONObject(paramName);
                                prompt.append("    - ").append(paramName)
                                        .append(" (").append(paramDef.getStr("type")).append("): ")
                                        .append(paramDef.getStr("description")).append("\n");
                            }

                            // 添加必需参数
                            JSONArray required = inputSchema.getJSONArray("required");
                            if (required != null && !required.isEmpty()) {
                                prompt.append("  必需参数: ").append(String.join(", ", required.toList(String.class))).append("\n");
                            }
                        }
                        prompt.append("\n");
                    }
                } catch (Exception e) {
                    log.warn("解析服务工具配置失败: {}", service.getName(), e);
                    // 回退到简单的工具信息
                    if (service.getTools() != null && !service.getTools().trim().isEmpty()) {
                        try {
                            List<String> tools = JSONUtil.toList(service.getTools(), String.class);
                            List<AigcMcpService> aigcMcpServices = aigcMcpServiceService.list(Wrappers.<AigcMcpService>lambdaQuery()
                                    .in(AigcMcpService::getId, tools));
                            for (AigcMcpService mcpService : aigcMcpServices) {
                                prompt.append("  工具: ").append(mcpService.getName())
                                        .append(" - ").append(mcpService.getDescription()).append("\n");
                            }
                        } catch (Exception ex) {
                            log.warn("解析服务工具信息失败: {}", service.getName());
                        }
                    }
                }
            }
            prompt.append("\n");
        }
        prompt.append("\n请按以下JSON格式返回执行计划:\n");
        prompt.append("{\n");
        prompt.append("  \"strategyName\": \"策略名称\",\n");
        prompt.append("  \"description\": \"计划描述\",\n");
        prompt.append("  \"steps\": [\n");
        prompt.append("    {\n");
        prompt.append("      \"stepId\": \"具体的步骤ID（如search_info, optimize_prompt, generate_image等，不要用step1, step2）\",\n");
        prompt.append("      \"serviceName\": \"服务名称（必须是可用服务列表中的名称）\",\n");
        prompt.append("      \"toolName\": \"工具名称（必须使用上面定义的确切工具名称）\",\n");
        prompt.append("      \"description\": \"步骤描述\",\n");
        prompt.append("      \"parameters\": {\n");
        prompt.append("        \"确切的参数名\": \"参数值或引用前一步骤结果\"\n");
        prompt.append("      },\n");
        prompt.append("      \"dependencies\": [\"依赖的步骤ID（必须与其他步骤的stepId完全匹配，如果没有依赖则为空数组）\"]\n");
        prompt.append("    }\n");
        prompt.append("  ]\n");
        prompt.append("}\n\n");

        prompt.append("关键参数使用指南:\n");
        prompt.append("EdgeOne Pages服务:\n");
        prompt.append("  - 工具名: deploy_html\n");
        prompt.append("  - 参数名: html_content (不是content或html)\n");
        prompt.append("  - 示例: \"html_content\": \"<!DOCTYPE html>...\"\n");
        prompt.append("Wanx图片生成服务:\n");
        prompt.append("  - 工具名: text_to_image\n");
        prompt.append("  - 参数名: prompt (不是description或text)\n");
        prompt.append("  - 示例: \"prompt\": \"可爱的小猫\"\n");
        prompt.append("Brave搜索服务:\n");
        prompt.append("  - 工具名: search\n");
        prompt.append("  - 参数名: q (不是query或search_query)\n");
        prompt.append("  - 示例: \"q\": \"人工智能发展趋势\"\n\n");

        prompt.append("参数引用格式说明:\n");
        prompt.append("当需要引用前一个步骤的结果时，请使用以下标准格式:\n");
        prompt.append("- ${stepId.result} - 获取步骤的完整结果\n");
        prompt.append("- ${stepId.result.field} - 获取结果中的特定字段\n");
        prompt.append("- ${context.userInput} - 获取用户输入\n");
        prompt.append("- ${context.userId} - 获取用户ID\n");
        prompt.append("- ${global.variableName} - 获取全局变量\n\n");

        prompt.append("常用结果字段引用示例:\n");
        prompt.append("- ${optimize_prompt.result.optimized_prompt} - 获取优化后的提示词\n");
        prompt.append("- ${generate_image.result.url} - 获取生成图片的URL\n");
        prompt.append("- ${search_info.result.content} - 获取搜索结果内容\n");
        prompt.append("- ${api_call.result.data} - 获取API调用返回的数据\n\n");
        
        prompt.append("注意事项:\n");
        prompt.append("1. 分析用户真正的需求意图\n");
        prompt.append("2. 选择最合适的服务和工具\n");
        prompt.append("3. 合理安排步骤顺序和依赖关系\n");
        prompt.append("4. stepId必须是具体的描述性名称，不要用step1, step2这样的通用名称\n");
        prompt.append("5. dependencies中的步骤ID必须与其他步骤的stepId完全匹配\n");
        prompt.append("6. 参数要具体明确，使用标准的引用格式\n");
        prompt.append("7. 如果需要多个步骤，必须使用${stepId.result.field}格式引用前置步骤结果\n");
        prompt.append("8. 对于图片生成任务，建议先添加AI提示词优化步骤\n");
        prompt.append("9. 严格按照参数引用格式说明使用引用语法\n");
        prompt.append("10. 不要生成重复的步骤\n");
        prompt.append("11. 只返回JSON，不要其他解释\n\n");

        return prompt.toString();
    }

    /**
     * 调用AI生成计划
     */
    private String callAiForPlanning(String prompt, String modelId) {
        try {
            ChatReq req = new ChatReq();
            req.setMessage(prompt);

            // 设置模型ID
            if (modelId != null && !modelId.trim().isEmpty()) {
                req.setModelId(modelId);
                log.debug("使用指定模型生成编排计划，模型ID: {}", modelId);
            } else {
                log.warn("未指定模型ID，使用默认模型配置");
            }

            String response = langChatService.text(req);
            log.debug("AI编排响应: {}", response);

            return response;

        } catch (Exception e) {
            log.error("调用AI生成编排计划失败，模型ID: {}", modelId, e);
            throw new RuntimeException("AI编排服务不可用: " + e.getMessage(), e);
        }
    }

    /**
     * 解析AI响应为执行计划
     */
    private FlexibleMcpOrchestrator.OrchestrationPlan parseAiResponse(String aiResponse, 
                                                                     List<AigcMcpService> availableServices) {
        try {
            // 提取JSON部分
            String jsonPart = extractJsonFromResponse(aiResponse);
            if (jsonPart == null) {
                log.warn("AI响应中未找到有效JSON");
                return null;
            }
            
            // 解析JSON
            Map<String, Object> planData = JSONUtil.toBean(jsonPart, Map.class);
            
            // 创建编排计划
            FlexibleMcpOrchestrator.OrchestrationPlan plan = new FlexibleMcpOrchestrator.OrchestrationPlan();
            plan.setPlanId(generatePlanId());
            plan.setStrategyName((String) planData.getOrDefault("strategyName", "AiGeneratedStrategy"));
            plan.setDescription((String) planData.getOrDefault("description", "AI生成的执行计划"));
            
            // 解析步骤
            List<Map<String, Object>> stepsData = (List<Map<String, Object>>) planData.get("steps");
            if (stepsData != null) {
                // 先收集所有步骤ID，用于验证依赖关系
                Set<String> allStepIds = new HashSet<>();
                List<FlexibleMcpOrchestrator.ExecutionStep> steps = new ArrayList<>();

                // 第一遍：解析所有步骤并收集ID
                for (Map<String, Object> stepData : stepsData) {
                    FlexibleMcpOrchestrator.ExecutionStep step = parseExecutionStep(stepData, availableServices);
                    if (step != null) {
                        allStepIds.add(step.getStepId());
                        steps.add(step);
                    }
                }

                // 第二遍：验证依赖关系
                for (FlexibleMcpOrchestrator.ExecutionStep step : steps) {
                    boolean validDependencies = true;
                    for (String dependency : step.getDependencies()) {
                        if (!allStepIds.contains(dependency)) {
                            log.error("步骤 {} 依赖不存在的步骤: {}，可用步骤: {}",
                                step.getStepId(), dependency, allStepIds);
                            validDependencies = false;
                        }
                    }

                    if (validDependencies) {
                        plan.addStep(step);
                    } else {
                        log.warn("跳过有无效依赖的步骤: {}", step.getStepId());
                    }
                }
            }
            
            // 添加元数据
            plan.putMetadata("generatedBy", "AI");
            plan.putMetadata("timestamp", System.currentTimeMillis());
            //plan.putMetadata("userInput", aiResponse);
            
            return plan;
            
        } catch (Exception e) {
            log.error("解析AI编排响应失败", e);
            return null;
        }
    }

    /**
     * 从响应中提取JSON
     */
    private String extractJsonFromResponse(String response) {
        // 查找JSON开始和结束位置
        int startIndex = response.indexOf("{");
        int endIndex = response.lastIndexOf("}");
        
        if (startIndex != -1 && endIndex != -1 && endIndex > startIndex) {
            return response.substring(startIndex, endIndex + 1);
        }
        
        return null;
    }

    /**
     * 解析执行步骤
     */
    private FlexibleMcpOrchestrator.ExecutionStep parseExecutionStep(Map<String, Object> stepData, 
                                                                    List<AigcMcpService> availableServices) {
        try {
            FlexibleMcpOrchestrator.ExecutionStep step = new FlexibleMcpOrchestrator.ExecutionStep();
            
            step.setStepId((String) stepData.get("stepId"));
            step.setServiceName((String) stepData.get("serviceName"));
            step.setToolName((String) stepData.get("toolName"));
            step.setDescription((String) stepData.get("description"));
            
            // 设置参数并进行验证和映射
            Map<String, Object> parameters = (Map<String, Object>) stepData.get("parameters");
            if (parameters != null) {
                // 验证并修正参数名称
                Map<String, Object> validatedParams = validateAndFixParameters(
                    parameters, step.getServiceName(), step.getToolName(), availableServices);

                // 标准化参数引用格式
                Object standardizedParams = referenceStandardizer.standardizeParameterReferences(validatedParams);
                if (standardizedParams instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> paramMap = (Map<String, Object>) standardizedParams;
                    step.setParameters(paramMap);
                    log.debug("步骤 {} 参数已验证并标准化: {}", step.getStepId(), paramMap.keySet());
                } else {
                    step.setParameters(validatedParams);
                }
            }
            
            // 设置依赖
            List<String> dependencies = (List<String>) stepData.get("dependencies");
            if (dependencies != null) {
                for (String dep : dependencies) {
                    step.addDependency(dep);
                }
            }
            
            // 验证服务是否存在
            boolean serviceExists = availableServices.stream()
                .anyMatch(service -> service.getName().equals(step.getServiceName()));
            
            if (!serviceExists) {
                log.warn("AI指定的服务不存在: {}", step.getServiceName());
                return null;
            }
            
            return step;
            
        } catch (Exception e) {
            log.error("解析执行步骤失败", e);
            return null;
        }
    }

    /**
     * 优化提示词（通用方法）
     */
    public String optimizePrompt(String originalPrompt, String optimizationType, String modelId) {
        try {
            log.info("使用AI优化提示词: {}, 类型: {}", originalPrompt, optimizationType);

            String optimizationPrompt = buildPromptOptimizationPrompt(originalPrompt, optimizationType);
            String optimizedPrompt = callAiForPlanning(optimizationPrompt, modelId);

            // 提取优化后的提示词
            String result = extractOptimizedPrompt(optimizedPrompt);

            log.info("提示词优化完成: {} -> {}", originalPrompt, result);
            return result;

        } catch (Exception e) {
            log.error("AI提示词优化失败，使用原始提示词", e);
            return originalPrompt;
        }
    }

    /**
     * 优化图片生成提示词（兼容方法）
     */
    public String optimizeImagePrompt(String originalPrompt, String modelId) {
        return optimizePrompt(originalPrompt, McpServiceConstants.ParameterValues.OPTIMIZATION_TYPE_IMAGE, modelId);
    }

    /**
     * 构建提示词优化的AI提示
     */
    private String buildPromptOptimizationPrompt(String originalPrompt, String optimizationType) {
        return switch (optimizationType) {
            case McpServiceConstants.ParameterValues.OPTIMIZATION_TYPE_IMAGE ->
                "你是一个专业的AI绘画提示词优化专家。请将用户的简单描述优化为详细、专业的图片生成提示词。\n\n" +
                "原始描述: " + originalPrompt + "\n\n" +
                "优化要求:\n" +
                "1. 保持原始意图不变\n" +
                "2. 添加具体的视觉细节描述\n" +
                "3. 包含艺术风格、光影效果、构图等专业元素\n" +
                "4. 使用英文关键词，用逗号分隔\n" +
                "5. 避免负面或不当内容\n" +
                "6. 长度控制在200字符以内\n\n" +
                "示例:\n" +
                "输入: 可爱的小猫\n" +
                "输出: cute kitten, fluffy fur, big bright eyes, sitting pose, soft lighting, warm colors, high quality, detailed, adorable expression\n\n" +
                "请直接输出优化后的提示词，不要其他解释:\n";

            case McpServiceConstants.ParameterValues.OPTIMIZATION_TYPE_TEXT ->
                "你是一个专业的文本生成提示词优化专家。请将用户的简单描述优化为详细、专业的文本生成提示词。\n\n" +
                "原始描述: " + originalPrompt + "\n\n" +
                "优化要求:\n" +
                "1. 保持原始意图和主题不变\n" +
                "2. 明确文本的风格、语调和目标受众\n" +
                "3. 添加具体的内容要求和结构指导\n" +
                "4. 包含长度、格式等技术要求\n" +
                "5. 确保内容积极正面\n\n" +
                "示例:\n" +
                "输入: 写一篇关于AI的文章\n" +
                "输出: 请写一篇关于人工智能发展现状的科普文章，要求：1）面向普通读者，语言通俗易懂；2）包含AI的定义、应用领域、发展趋势；3）字数800-1200字；4）结构清晰，有引言、正文、结论；5）语调客观中性，内容准确可信\n\n" +
                "请直接输出优化后的提示词，不要其他解释:\n";

            case McpServiceConstants.ParameterValues.OPTIMIZATION_TYPE_CREATIVE ->
                "你是一个专业的创意写作提示词优化专家。请将用户的简单描述优化为富有创意和想象力的写作提示词。\n\n" +
                "原始描述: " + originalPrompt + "\n\n" +
                "优化要求:\n" +
                "1. 保持核心创意不变\n" +
                "2. 增加创意元素和想象空间\n" +
                "3. 明确文体类型（小说、诗歌、剧本等）\n" +
                "4. 添加情感色彩和氛围描述\n" +
                "5. 激发创作灵感\n\n" +
                "示例:\n" +
                "输入: 写一个科幻故事\n" +
                "输出: 创作一个发生在2150年的科幻短篇小说，背景是人类已经移居火星，主角是一名火星考古学家，在挖掘古代地球文物时发现了一个神秘的时间胶囊，内容要包含悬疑、探索和人性思考，风格偏向硬科幻，字数3000-5000字\n\n" +
                "请直接输出优化后的提示词，不要其他解释:\n";

            case McpServiceConstants.ParameterValues.OPTIMIZATION_TYPE_TECHNICAL ->
                "你是一个专业的技术文档提示词优化专家。请将用户的简单描述优化为准确、专业的技术文档生成提示词。\n\n" +
                "原始描述: " + originalPrompt + "\n\n" +
                "优化要求:\n" +
                "1. 保持技术准确性\n" +
                "2. 明确文档类型和目标读者\n" +
                "3. 添加结构和格式要求\n" +
                "4. 包含必要的技术细节层次\n" +
                "5. 确保逻辑清晰、条理分明\n\n" +
                "示例:\n" +
                "输入: 写API文档\n" +
                "输出: 编写RESTful API技术文档，包含：1）API概述和认证方式；2）每个端点的详细说明（URL、HTTP方法、参数、响应格式）；3）错误码说明；4）使用示例和代码片段；5）版本变更记录；格式使用Markdown，面向开发者，确保信息完整准确\n\n" +
                "请直接输出优化后的提示词，不要其他解释:\n";

            default ->
                "你是一个专业的提示词优化专家。请将用户的简单描述优化为更详细、更有效的提示词。\n\n" +
                "原始描述: " + originalPrompt + "\n\n" +
                "优化要求:\n" +
                "1. 保持原始意图不变\n" +
                "2. 添加具体的细节和要求\n" +
                "3. 明确目标和期望结果\n" +
                "4. 使用清晰、准确的表达\n" +
                "5. 确保内容积极正面\n\n" +
                "请直接输出优化后的提示词，不要其他解释:\n";
        };
    }

    /**
     * 构建提示词优化的AI提示（兼容方法）
     */
    private String buildPromptOptimizationPrompt(String originalPrompt) {
        return buildPromptOptimizationPrompt(originalPrompt, McpServiceConstants.ParameterValues.OPTIMIZATION_TYPE_GENERAL);
    }

    /**
     * 从AI响应中提取优化后的提示词
     */
    private String extractOptimizedPrompt(String aiResponse) {
        if (aiResponse == null || aiResponse.trim().isEmpty()) {
            return "";
        }

        // 移除可能的前缀和后缀说明
        String cleaned = aiResponse.trim();

        // 如果包含"输出:"或"结果:"等标识，提取后面的内容
        String[] prefixes = {"输出:", "结果:", "优化后:", "Optimized:", "Result:"};
        for (String prefix : prefixes) {
            int index = cleaned.indexOf(prefix);
            if (index != -1) {
                cleaned = cleaned.substring(index + prefix.length()).trim();
                break;
            }
        }

        // 移除引号
        if (cleaned.startsWith("\"") && cleaned.endsWith("\"")) {
            cleaned = cleaned.substring(1, cleaned.length() - 1);
        }

        // 限制长度
        if (cleaned.length() > 200) {
            cleaned = cleaned.substring(0, 200);
            // 在最后一个逗号处截断，保持完整性
            int lastComma = cleaned.lastIndexOf(",");
            if (lastComma > 100) {
                cleaned = cleaned.substring(0, lastComma);
            }
        }

        return cleaned.trim();
    }

    /**
     * 验证并修正参数名称
     */
    private Map<String, Object> validateAndFixParameters(Map<String, Object> aiParameters,
                                                        String serviceName, String toolName,
                                                        List<AigcMcpService> availableServices) {
        Map<String, Object> validatedParams = new HashMap<>();

        // 查找对应的服务
        AigcMcpService service = availableServices.stream()
            .filter(s -> s.getName().equals(serviceName))
            .findFirst()
            .orElse(null);

        if (service == null || StrUtil.isBlank(service.getTools())) {
            log.warn("未找到服务定义或工具配置: {}", serviceName);
            return aiParameters;
        }

        try {
            // 获取工具的参数定义
            JSONArray tools = JSONUtil.parseArray(service.getTools());
            JSONObject targetTool = null;

            for (Object toolObj : tools) {
                JSONObject tool = (JSONObject) toolObj;
                if (toolName.equals(tool.getStr("name"))) {
                    targetTool = tool;
                    break;
                }
            }

            if (targetTool == null) {
                log.warn("未找到工具定义: service={}, tool={}", serviceName, toolName);
                return aiParameters;
            }

            JSONObject inputSchema = targetTool.getJSONObject("inputSchema");
            if (inputSchema == null || !inputSchema.containsKey("properties")) {
                return aiParameters;
            }

            JSONObject properties = inputSchema.getJSONObject("properties");
            Set<String> validParamNames = properties.keySet();

            // 参数名称映射规则
            Map<String, String> paramMapping = createParameterMapping(serviceName, toolName);

            for (Map.Entry<String, Object> entry : aiParameters.entrySet()) {
                String aiParamName = entry.getKey();
                Object paramValue = entry.getValue();

                // 检查是否是有效的参数名
                if (validParamNames.contains(aiParamName)) {
                    validatedParams.put(aiParamName, paramValue);
                } else {
                    // 尝试映射到正确的参数名
                    String mappedName = paramMapping.get(aiParamName);
                    if (mappedName != null && validParamNames.contains(mappedName)) {
                        validatedParams.put(mappedName, paramValue);
                        log.info("参数名称映射: {} -> {} (服务: {})", aiParamName, mappedName, serviceName);
                    } else {
                        log.warn("无效的参数名称: {} (服务: {}, 工具: {}, 有效参数: {})",
                            aiParamName, serviceName, toolName, validParamNames);
                        // 仍然保留原参数，让后端处理
                        validatedParams.put(aiParamName, paramValue);
                    }
                }
            }

            return validatedParams;

        } catch (Exception e) {
            log.error("验证参数失败", e);
            return aiParameters;
        }
    }

    /**
     * 创建参数名称映射规则
     */
    private Map<String, String> createParameterMapping(String serviceName, String toolName) {
        Map<String, String> mapping = new HashMap<>();

        // EdgeOne Pages 参数映射
        if ("edgeone-pages".equals(serviceName) && "deploy_html".equals(toolName)) {
            mapping.put("content", "html_content");
            mapping.put("html", "html_content");
            mapping.put("html_data", "html_content");
            mapping.put("page_content", "html_content");
            mapping.put("website_content", "html_content");
            // project_name 参数在EdgeOne Pages中不支持，会被忽略
        }

        // Wanx 图片生成参数映射
        if ("wanx-image-generation".equals(serviceName)) {
            mapping.put("description", "prompt");
            mapping.put("text", "prompt");
            mapping.put("content", "prompt");
            mapping.put("image_prompt", "prompt");
            mapping.put("image_description", "prompt");
        }

        // Brave 搜索参数映射
        if ("brave-search".equals(serviceName)) {
            mapping.put("query", "q");
            mapping.put("search_query", "q");
            mapping.put("keyword", "q");
            mapping.put("search_term", "q");
            mapping.put("search_text", "q");
        }

        return mapping;
    }

    /**
     * 生成计划ID
     */
    private String generatePlanId() {
        return "ai_plan_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }
}
