/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.client.handler;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.tycoding.langchat.mcp.core.constants.McpServiceConstants;
import cn.tycoding.langchat.mcp.core.entity.AigcMcpService;
import cn.tycoding.langchat.mcp.core.protocol.McpResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * API服务MCP工具处理器
 * 
 * <AUTHOR>
 * @since 2024/12/20
 */
@Slf4j
@Component
public class ApiMcpToolHandler extends AbstractMcpToolHandler {

    @Override
    protected McpResponse handleSpecificTool(AigcMcpService service, String toolName,
                                             Map<String, Object> parameters,
                                             Map<String, Object> serviceParams) {
        
        return switch (toolName) {
            case McpServiceConstants.ToolNames.HTTP_REQUEST -> handleHttpRequest(parameters, serviceParams);
            default -> McpResponse.error("未知的API工具: " + toolName);
        };
    }

    /**
     * 处理HTTP请求
     */
    private McpResponse handleHttpRequest(Map<String, Object> parameters, Map<String, Object> serviceParams) {
        logToolCall("http_request", parameters);

        // 从服务配置中获取基础配置
        String baseUrl = getParameterValue(serviceParams, "base_url", String.class, "");
        String apiKey = getParameterValue(serviceParams, "api_key", String.class, "");
        Integer timeout = getParameterValue(serviceParams, "timeout", Integer.class, 30);
        Integer maxRetries = getParameterValue(serviceParams, "max_retries", Integer.class, 3);

        // 固定参数名称列表（这些参数不会被拼接到请求中）
        Set<String> fixedParams = Set.of("baseUrl", "apiKey", "method", "headers", "body", "path", "url");
        
        // 从工具参数中获取具体的请求信息
        String path = (String) parameters.get("path"); // 相对路径
        String url = (String) parameters.get("url");   // 完整URL
        String method = (String) parameters.getOrDefault("method", "GET");
        String headers = (String) parameters.getOrDefault("headers", "{}");
        String body = (String) parameters.get("body");

        // 构建完整URL
        String finalUrl;
        if (StrUtil.isNotBlank(url)) {
            finalUrl = url; // 使用完整URL
        } else if (StrUtil.isNotBlank(path) && StrUtil.isNotBlank(baseUrl)) {
            finalUrl = baseUrl.endsWith("/") ? baseUrl + path.substring(path.startsWith("/") ? 1 : 0) : baseUrl + "/" + path;
        } else {
            return McpResponse.error("URL或路径不能为空");
        }

        // 提取动态参数（去掉固定参数）
        Map<String, Object> dynamicParams = new HashMap<>();
        for (Map.Entry<String, Object> entry : parameters.entrySet()) {
            if (!fixedParams.contains(entry.getKey()) && entry.getValue() != null) {
                dynamicParams.put(entry.getKey(), entry.getValue());
            }
        }

        try {
            HttpRequest request = HttpRequest.of(finalUrl)
                    .method(cn.hutool.http.Method.valueOf(method.toUpperCase()))
                    // 转换为毫秒
                    .timeout(timeout * 1000);

            // 添加API密钥到请求头
            if (StrUtil.isNotBlank(apiKey)) {
                request.header("Authorization", "Bearer " + apiKey);
            }

            // 添加其他请求头
            if (StrUtil.isNotBlank(headers)) {
                JSONObject headerObj = JSONUtil.parseObj(headers);
                for (String key : headerObj.keySet()) {
                    request.header(key, headerObj.getStr(key));
                }
            }

            // 处理动态参数
            if (!dynamicParams.isEmpty()) {
                if ("GET".equalsIgnoreCase(method) || "DELETE".equalsIgnoreCase(method)) {
                    // GET/DELETE请求：将动态参数作为查询参数
                    for (Map.Entry<String, Object> entry : dynamicParams.entrySet()) {
                        request.form(entry.getKey(), entry.getValue());
                    }
                } else if ("POST".equalsIgnoreCase(method) || "PUT".equalsIgnoreCase(method) || "PATCH".equalsIgnoreCase(method)) {
                    // POST/PUT/PATCH请求：处理请求体
                    if (StrUtil.isNotBlank(body)) {
                        // 如果已有body，直接使用
                        request.body(body);
                    } else {
                        // 否则将动态参数作为JSON请求体
                        request.header("Content-Type", "application/json");
                        request.body(JSONUtil.toJsonStr(dynamicParams));
                    }
                }
            } else if (StrUtil.isNotBlank(body)) {
                // 没有动态参数但有body时，直接使用body
                request.body(body);
            }

            log.info("执行HTTP请求: 方法={}, URL={}, 动态参数={}", method, finalUrl, dynamicParams);

            HttpResponse response = request.execute();

            Map<String, Object> result = new HashMap<>();
            result.put("status", response.getStatus());
            result.put("headers", response.headers());
            result.put("body", response.body());
            result.put("success", response.isOk());
            result.put("url", finalUrl);
            result.put("method", method);
            result.put("dynamicParams", dynamicParams);

            logToolResult("http_request", response.isOk(), response.isOk() ? null : "HTTP " + response.getStatus());
            return McpResponse.success(result);

        } catch (Exception e) {
            logToolResult("http_request", false, e.getMessage());
            return McpResponse.error("API调用失败: " + e.getMessage());
        }
    }
}
