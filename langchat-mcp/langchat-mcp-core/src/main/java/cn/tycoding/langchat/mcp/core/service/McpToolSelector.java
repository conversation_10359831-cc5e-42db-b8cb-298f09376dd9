/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.service;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.tycoding.langchat.mcp.core.entity.AigcMcpService;
import cn.tycoding.langchat.mcp.core.protocol.McpTool;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * MCP工具智能选择器
 * 根据用户的Prompt自动选择合适的MCP工具
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class McpToolSelector {

    private final AigcMcpServiceService mcpServiceService;

    /**
     * 工具选择结果
     */
    @Getter
    public static class ToolSelection {
        // Getters
        private String serviceName;
        private String toolName;
        private Map<String, Object> parameters;
        private double confidence;
        private String reason;

        public ToolSelection(String serviceName, String toolName, Map<String, Object> parameters, double confidence, String reason) {
            this.serviceName = serviceName;
            this.toolName = toolName;
            this.parameters = parameters;
            this.confidence = confidence;
            this.reason = reason;
        }

    }

    /**
     * 根据用户输入智能选择MCP工具
     */
    public List<ToolSelection> selectTools(String userInput, List<String> availableServiceIds) {
        if (CharSequenceUtil.isBlank(userInput) || availableServiceIds == null || availableServiceIds.isEmpty()) {
            return Collections.emptyList();
        }

        log.info("开始智能选择MCP工具，用户输入: {}, 可用服务数: {}", userInput, availableServiceIds.size());

        // 获取可用的MCP服务
        List<AigcMcpService> availableServices = availableServiceIds.stream()
                .map(mcpServiceService::findById)
                .filter(Objects::nonNull)
                .filter(AigcMcpService::getEnabled)
                .toList();

        if (availableServices.isEmpty()) {
            log.warn("没有可用的MCP服务");
            return Collections.emptyList();
        }

        List<ToolSelection> selections = new ArrayList<>();

        // 分析用户意图并匹配工具
        for (AigcMcpService service : availableServices) {
            List<ToolSelection> serviceSelections = analyzeServiceTools(userInput, service);
            selections.addAll(serviceSelections);
        }

        // 按置信度排序
        selections.sort((a, b) -> Double.compare(b.getConfidence(), a.getConfidence()));

        log.info("工具选择完成，找到 {} 个匹配的工具", selections.size());
        return selections;
    }

    /**
     * 分析单个服务的工具匹配度
     */
    private List<ToolSelection> analyzeServiceTools(String userInput, AigcMcpService service) {
        List<ToolSelection> selections = new ArrayList<>();

        if (StrUtil.isBlank(service.getTools())) {
            return selections;
        }

        try {
            JSONArray toolsArray = JSONUtil.parseArray(service.getTools());
            
            for (Object toolObj : toolsArray) {
                if (toolObj instanceof JSONObject) {
                    JSONObject tool = (JSONObject) toolObj;
                    String toolName = tool.getStr("name");
                    String toolDescription = tool.getStr("description");
                    String toolCategory = tool.getStr("category");

                    // 计算匹配度
                    double confidence = calculateConfidence(userInput, service, toolName, toolDescription, toolCategory);
                    
                    if (confidence > 0.3) { // 置信度阈值
                        // 提取参数
                        Map<String, Object> parameters = extractParameters(userInput, tool);
                        
                        String reason = buildReason(service.getDisplayName(), toolName, toolDescription, confidence);
                        
                        selections.add(new ToolSelection(service.getName(), toolName, parameters, confidence, reason));
                    }
                }
            }
        } catch (Exception e) {
            log.warn("解析服务工具失败: {}", service.getName(), e);
        }

        return selections;
    }

    /**
     * 计算工具匹配置信度
     */
    private double calculateConfidence(String userInput, AigcMcpService service, String toolName, String toolDescription, String toolCategory) {
        double confidence = 0.0;
        String input = userInput.toLowerCase();

        // 1. 关键词匹配
        confidence += calculateKeywordMatch(input, service, toolName, toolDescription, toolCategory);

        // 2. 意图识别
        confidence += calculateIntentMatch(input, service, toolName, toolCategory);

        // 3. 服务优先级加权
        confidence += (service.getPriority() / 10.0) * 0.1;

        return Math.min(confidence, 1.0);
    }

    /**
     * 关键词匹配计算
     */
    private double calculateKeywordMatch(String input, AigcMcpService service, String toolName, String toolDescription, String toolCategory) {
        double score = 0.0;

        // 图片生成相关
        if (isImageGenerationRequest(input)) {
            if ("wanx-image-generation".equals(service.getName()) || "text_to_image".equals(toolName)) {
                score += 0.8;
            }
        }

        // 搜索相关
        if (isSearchRequest(input)) {
            if ("brave-search".equals(service.getName()) || "web_search".equals(toolName)) {
                score += 0.8;
            }
        }

        // 代码相关
        if (isCodeRequest(input)) {
            if ("github-mcp".equals(service.getName()) || "code".equals(toolCategory)) {
                score += 0.7;
            }
        }

        // 文本分析相关
        if (isTextAnalysisRequest(input)) {
            if ("nlp".equals(toolCategory) || "text_analysis".equals(toolName)) {
                score += 0.7;
            }
        }

        // 工具描述匹配
        if (StrUtil.isNotBlank(toolDescription)) {
            String[] keywords = extractKeywords(toolDescription);
            for (String keyword : keywords) {
                if (input.contains(keyword.toLowerCase())) {
                    score += 0.2;
                }
            }
        }

        return score;
    }

    /**
     * 意图识别匹配
     */
    private double calculateIntentMatch(String input, AigcMcpService service, String toolName, String toolCategory) {
        double score = 0.0;

        // 创作意图
        if (isCreativeIntent(input)) {
            if ("image".equals(toolCategory) || "creative".equals(toolCategory)) {
                score += 0.6;
            }
        }

        // 查询意图
        if (isQueryIntent(input)) {
            if ("search".equals(toolCategory) || "query".equals(toolCategory)) {
                score += 0.6;
            }
        }

        // 分析意图
        if (isAnalysisIntent(input)) {
            if ("analysis".equals(toolCategory) || "nlp".equals(toolCategory)) {
                score += 0.6;
            }
        }

        return score;
    }

    /**
     * 提取工具参数
     */
    private Map<String, Object> extractParameters(String userInput, JSONObject tool) {
        Map<String, Object> parameters = new HashMap<>();

        try {
            JSONObject inputSchema = tool.getJSONObject("inputSchema");
            if (inputSchema != null && inputSchema.containsKey("properties")) {
                JSONObject properties = inputSchema.getJSONObject("properties");
                
                for (String paramName : properties.keySet()) {
                    Object value = extractParameterValue(userInput, paramName, properties.getJSONObject(paramName));
                    if (value != null) {
                        parameters.put(paramName, value);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("提取工具参数失败", e);
        }

        // 通用参数提取
        if (isImageGenerationRequest(userInput)) {
            String prompt = extractImagePrompt(userInput);
            if (StrUtil.isNotBlank(prompt)) {
                parameters.put("prompt", prompt);
            }
        }

        if (isSearchRequest(userInput)) {
            String query = extractSearchQuery(userInput);
            if (StrUtil.isNotBlank(query)) {
                parameters.put("q", query);
                parameters.put("query", query);
            }
        }

        return parameters;
    }

    /**
     * 提取单个参数值
     */
    private Object extractParameterValue(String userInput, String paramName, JSONObject paramSchema) {
        String type = paramSchema.getStr("type");
        String description = paramSchema.getStr("description");

        // 根据参数名称和描述智能提取
        switch (paramName.toLowerCase()) {
            case "prompt":
            case "text":
            case "content":
                return extractTextContent(userInput);
            case "query":
            case "q":
            case "keyword":
                return extractSearchQuery(userInput);
            case "style":
                return extractImageStyle(userInput);
            case "size":
                return extractImageSize(userInput);
            case "count":
            case "limit":
                return extractNumber(userInput, 10);
            default:
                return null;
        }
    }

    // ==================== 意图识别方法 ====================

    private boolean isImageGenerationRequest(String input) {
        String[] keywords = {"画", "生成图片", "图片", "图像", "绘制", "创作", "设计", "画一个", "生成一张", "制作图片"};
        return containsAnyKeyword(input, keywords);
    }

    private boolean isSearchRequest(String input) {
        String[] keywords = {"搜索", "查找", "搜", "找", "查询", "检索", "寻找", "搜一下", "查一下", "帮我找"};
        return containsAnyKeyword(input, keywords);
    }

    private boolean isCodeRequest(String input) {
        String[] keywords = {"代码", "编程", "程序", "开发", "github", "代码库", "仓库", "项目"};
        return containsAnyKeyword(input, keywords);
    }

    private boolean isTextAnalysisRequest(String input) {
        String[] keywords = {"分析", "解析", "理解", "总结", "提取", "识别", "分类", "情感分析"};
        return containsAnyKeyword(input, keywords);
    }

    private boolean isCreativeIntent(String input) {
        String[] keywords = {"创作", "创造", "设计", "制作", "生成", "画", "写", "编写"};
        return containsAnyKeyword(input, keywords);
    }

    private boolean isQueryIntent(String input) {
        String[] keywords = {"什么", "如何", "怎么", "为什么", "哪里", "谁", "什么时候", "多少"};
        return containsAnyKeyword(input, keywords);
    }

    private boolean isAnalysisIntent(String input) {
        String[] keywords = {"分析", "解释", "说明", "总结", "评价", "判断", "比较"};
        return containsAnyKeyword(input, keywords);
    }

    // ==================== 参数提取方法 ====================

    private String extractImagePrompt(String input) {
        // 提取图片描述
        String[] patterns = {"画一个(.+)", "生成(.+)图片", "制作(.+)的图片", "画(.+)", "生成一张(.+)"};
        for (String pattern : patterns) {
            String result = extractByPattern(input, pattern);
            if (StrUtil.isNotBlank(result)) {
                return result.trim();
            }
        }
        return input; // 如果没有匹配到特定模式，返回原始输入
    }

    private String extractSearchQuery(String input) {
        // 提取搜索关键词
        String[] patterns = {"搜索(.+)", "查找(.+)", "搜(.+)", "找(.+)", "查询(.+)"};
        for (String pattern : patterns) {
            String result = extractByPattern(input, pattern);
            if (StrUtil.isNotBlank(result)) {
                return result.trim();
            }
        }
        return input;
    }

    private String extractTextContent(String input) {
        return input.trim();
    }

    private String extractImageStyle(String input) {
        if (input.contains("摄影") || input.contains("照片")) return "photography";
        if (input.contains("卡通") || input.contains("动漫")) return "cartoon";
        if (input.contains("油画")) return "oil_painting";
        if (input.contains("水彩")) return "watercolor";
        return "photography"; // 默认风格
    }

    private String extractImageSize(String input) {
        if (input.contains("正方形") || input.contains("1:1")) return "1024*1024";
        if (input.contains("横屏") || input.contains("16:9")) return "1344*768";
        if (input.contains("竖屏") || input.contains("9:16")) return "768*1344";
        return "1024*1024"; // 默认尺寸
    }

    private Integer extractNumber(String input, int defaultValue) {
        Pattern pattern = Pattern.compile("(\\d+)");
        java.util.regex.Matcher matcher = pattern.matcher(input);
        if (matcher.find()) {
            try {
                return Integer.parseInt(matcher.group(1));
            } catch (NumberFormatException e) {
                return defaultValue;
            }
        }
        return defaultValue;
    }

    // ==================== 辅助方法 ====================

    private boolean containsAnyKeyword(String input, String[] keywords) {
        for (String keyword : keywords) {
            if (input.contains(keyword)) {
                return true;
            }
        }
        return false;
    }

    private String extractByPattern(String input, String pattern) {
        Pattern p = Pattern.compile(pattern);
        java.util.regex.Matcher matcher = p.matcher(input);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }

    private String[] extractKeywords(String text) {
        if (StrUtil.isBlank(text)) {
            return new String[0];
        }
        return text.split("[，,。.！!？?；;：:\\s]+");
    }

    private String buildReason(String serviceName, String toolName, String toolDescription, double confidence) {
        return String.format("选择 %s 的 %s 工具，匹配度: %.2f，原因: %s", 
                serviceName, toolName, confidence, toolDescription);
    }
}
