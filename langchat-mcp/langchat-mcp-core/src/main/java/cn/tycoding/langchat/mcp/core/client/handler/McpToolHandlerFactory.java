/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.client.handler;

import cn.tycoding.langchat.mcp.core.constants.McpServiceConstants;
import cn.tycoding.langchat.mcp.core.protocol.McpResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * MCP工具处理器工厂类
 * 负责根据服务名称路由到对应的处理器
 * 
 * <AUTHOR>
 * @since 2024/12/20
 */
@Slf4j
@Component
public class McpToolHandlerFactory {

    @Autowired
    private SearchMcpToolHandler searchHandler;

    @Autowired
    private CompanySearchMcpToolHandler companySearchHandler;

    @Autowired
    private ApiMcpToolHandler apiHandler;

    @Autowired
    private EmailMcpToolHandler emailHandler;
    @Autowired
    private IdentificationMcpToolHandler identificationHandler;


    /**
     * 处理工具调用（支持用户输入参数提取）
     */
    public McpResponse handleToolCall(String serviceName, String toolName
            , Map<String, Object> parameters, String userInput,String modelId) {
        log.info("工具调用路由: 服务={}, 工具={}, 参数数量={}", serviceName, toolName, parameters.size());

        try {
            AbstractMcpToolHandler handler = getHandler(serviceName);
            if (handler == null) {
                return McpResponse.error("未找到服务处理器: " + serviceName);
            }

            return handler.handleToolCall(serviceName, toolName, parameters, userInput,modelId);

        } catch (Exception e) {
            log.error("工具调用路由失败: 服务={}, 工具={}", serviceName, toolName, e);
            return McpResponse.error("工具调用路由异常: " + e.getMessage());
        }
    }

    /**
     * 根据服务名称获取对应的处理器
     */
    private AbstractMcpToolHandler getHandler(String serviceName) {
        return switch (serviceName) {
            case McpServiceConstants.BuiltinServices.BUILTIN_SEARCH -> searchHandler;
            case McpServiceConstants.BuiltinServices.BUILTIN_COMPANY_SEARCH -> companySearchHandler;
            case McpServiceConstants.BuiltinServices.BUILTIN_API -> apiHandler;
            case McpServiceConstants.BuiltinServices.BUILTIN_EMAIL -> emailHandler;
            case McpServiceConstants.BuiltinServices.BUILTIN_FILE -> createFileHandler();
            case McpServiceConstants.BuiltinServices.BUILTIN_TEXT -> createTextHandler();
            case McpServiceConstants.BuiltinServices.BUILTIN_ANALYTICS -> createAnalyticsHandler();
            case McpServiceConstants.BuiltinServices.BUILTIN_IDENTIFICATION -> identificationHandler;
            default -> {
                log.warn("未知的内置服务: {}", serviceName);
                yield null;
            }
        };
    }

    /**
     * 创建文件处理器（临时实现，后续可以拆分）
     */
    private AbstractMcpToolHandler createFileHandler() {
        return new AbstractMcpToolHandler() {
            @Override
            protected McpResponse handleSpecificTool(cn.tycoding.langchat.mcp.core.entity.AigcMcpService service, 
                                                   String toolName, 
                                                   Map<String, Object> parameters, 
                                                   Map<String, Object> serviceParams) {
                logToolCall(toolName, parameters);
                
                // 临时实现，返回模拟结果
                Map<String, Object> result = Map.of(
                    "success", true,
                    "message", "文件操作功能待实现",
                    "toolName", toolName,
                    "timestamp", System.currentTimeMillis()
                );
                
                logToolResult(toolName, true, null);
                return McpResponse.success(result);
            }
        };
    }

    /**
     * 创建文本处理器（临时实现，后续可以拆分）
     */
    private AbstractMcpToolHandler createTextHandler() {
        return new AbstractMcpToolHandler() {
            @Override
            protected McpResponse handleSpecificTool(cn.tycoding.langchat.mcp.core.entity.AigcMcpService service, 
                                                   String toolName, 
                                                   Map<String, Object> parameters, 
                                                   Map<String, Object> serviceParams) {
                logToolCall(toolName, parameters);
                
                // 临时实现，返回模拟结果
                Map<String, Object> result = Map.of(
                    "success", true,
                    "message", "文本处理功能待实现",
                    "toolName", toolName,
                    "timestamp", System.currentTimeMillis()
                );
                
                logToolResult(toolName, true, null);
                return McpResponse.success(result);
            }
        };
    }

    /**
     * 创建分析处理器（临时实现，后续可以拆分）
     */
    private AbstractMcpToolHandler createAnalyticsHandler() {
        return new AbstractMcpToolHandler() {
            @Override
            protected McpResponse handleSpecificTool(cn.tycoding.langchat.mcp.core.entity.AigcMcpService service, 
                                                   String toolName, 
                                                   Map<String, Object> parameters, 
                                                   Map<String, Object> serviceParams) {
                logToolCall(toolName, parameters);
                
                // 临时实现，返回模拟结果
                Map<String, Object> result = Map.of(
                    "success", true,
                    "message", "数据分析功能待实现",
                    "toolName", toolName,
                    "timestamp", System.currentTimeMillis()
                );
                
                logToolResult(toolName, true, null);
                return McpResponse.success(result);
            }
        };
    }

    /**
     * 获取支持的服务列表
     */
    public String[] getSupportedServices() {
        return new String[] {
            McpServiceConstants.BuiltinServices.BUILTIN_SEARCH,
            McpServiceConstants.BuiltinServices.BUILTIN_COMPANY_SEARCH,
            McpServiceConstants.BuiltinServices.BUILTIN_API,
            McpServiceConstants.BuiltinServices.BUILTIN_EMAIL,
            McpServiceConstants.BuiltinServices.BUILTIN_FILE,
            McpServiceConstants.BuiltinServices.BUILTIN_TEXT,
            McpServiceConstants.BuiltinServices.BUILTIN_ANALYTICS
        };
    }

    /**
     * 检查服务是否支持
     */
    public boolean isServiceSupported(String serviceName) {
        return getHandler(serviceName) != null;
    }
}
