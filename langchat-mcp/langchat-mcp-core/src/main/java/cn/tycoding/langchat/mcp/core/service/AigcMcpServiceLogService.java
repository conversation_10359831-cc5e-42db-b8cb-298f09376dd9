/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.service;

import cn.tycoding.langchat.mcp.core.entity.AigcMcpServiceLog;
import cn.tycoding.langchat.mcp.core.protocol.McpResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * MCP服务调用日志Service
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
public interface AigcMcpServiceLogService extends IService<AigcMcpServiceLog> {

    /**
     * 分页查询
     */
    IPage<AigcMcpServiceLog> list(AigcMcpServiceLog log, Map<String, Object> queryPage);

    /**
     * 记录工具调用日志
     */
    void recordToolCall(String serviceId, String serviceName, String toolName, 
                       Map<String, Object> parameters, McpResponse response, 
                       String userId, String conversationId, long executionTime);

    /**
     * 记录工具调用日志（异步）
     */
    void recordToolCallAsync(String serviceId, String serviceName, String toolName, 
                            Map<String, Object> parameters, McpResponse response, 
                            String userId, String conversationId, long executionTime);

    /**
     * 根据服务名称查询调用日志
     */
    List<AigcMcpServiceLog> getLogsByServiceName(String serviceName);

    /**
     * 根据用户ID查询调用日志
     */
    List<AigcMcpServiceLog> getLogsByUserId(String userId);

    /**
     * 根据对话ID查询调用日志
     */
    List<AigcMcpServiceLog> getLogsByConversationId(String conversationId);

    /**
     * 获取调用统计信息
     */
    Map<String, Object> getCallStatistics();

    /**
     * 获取服务调用统计
     */
    Map<String, Object> getServiceCallStatistics(String serviceName);

    /**
     * 清理过期日志
     */
    void cleanExpiredLogs(int days);
}
