/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.config;

import cn.tycoding.langchat.mcp.core.service.AigcMcpServiceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * MCP服务自动配置
 * 在应用启动时自动同步数据库中的MCP服务配置到MCP客户端
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class McpServiceAutoConfiguration {

    private final AigcMcpServiceService aigcMcpServiceService;

    /**
     * MCP服务自动同步
     */
    @Bean
    public CommandLineRunner mcpServiceAutoSync() {
        return args -> {
            try {
                log.info("开始自动同步数据库中的MCP服务配置...");
                
                // 同步所有启用的服务到MCP客户端
                aigcMcpServiceService.syncAllToMcpClient();
                
                log.info("MCP服务配置自动同步完成");
            } catch (Exception e) {
                log.error("MCP服务配置自动同步失败", e);
                // 不抛出异常，避免影响应用启动
            }
        };
    }
}
