/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.client.handler;

import cn.hutool.core.util.StrUtil;
import cn.tycoding.langchat.mcp.core.constants.McpServiceConstants;
import cn.tycoding.langchat.mcp.core.protocol.McpResponse;
import cn.tycoding.langchat.mcp.core.entity.AigcMcpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 邮件服务MCP工具处理器
 * 
 * <AUTHOR>
 * @since 2024/12/20
 */
@Slf4j
@Component
public class EmailMcpToolHandler extends AbstractMcpToolHandler {

    @Override
    protected McpResponse handleSpecificTool(AigcMcpService service, String toolName, 
                                           Map<String, Object> parameters, 
                                           Map<String, Object> serviceParams) {
        
        return switch (toolName) {
            case McpServiceConstants.ToolNames.SEND_EMAIL -> handleSendEmail(parameters, serviceParams);
            case McpServiceConstants.ToolNames.READ_EMAIL -> handleReadEmail(parameters, serviceParams);
            case McpServiceConstants.ToolNames.DELETE_EMAIL -> handleDeleteEmail(parameters, serviceParams);
            default -> McpResponse.error("未知的邮件工具: " + toolName);
        };
    }

    /**
     * 处理发送邮件
     */
    private McpResponse handleSendEmail(Map<String, Object> parameters, Map<String, Object> serviceParams) {
        logToolCall("send_email", parameters);

        if (!validateRequiredParameters(parameters, "to", "subject", "content")) {
            return createValidationError("to", "subject", "content");
        }

        try {
            String to = getParameterValue(parameters, "to", String.class, "");
            String cc = getParameterValue(parameters, "cc", String.class, "");
            String bcc = getParameterValue(parameters, "bcc", String.class, "");
            String subject = getParameterValue(parameters, "subject", String.class, "");
            String content = getParameterValue(parameters, "content", String.class, "");
            String contentType = getParameterValue(parameters, "contentType", String.class, "text/plain");
            String attachments = getParameterValue(parameters, "attachments", String.class, "");

            log.info("发送邮件: 收件人={}, 主题={}, 内容类型={}", to, subject, contentType);

            // 模拟邮件发送
            Map<String, Object> result = createMockEmailSendResult(to, cc, bcc, subject, content, contentType, attachments);

            logToolResult("send_email", true, null);
            return McpResponse.success(result);

        } catch (Exception e) {
            logToolResult("send_email", false, e.getMessage());
            return McpResponse.error("邮件发送失败: " + e.getMessage());
        }
    }

    /**
     * 处理读取邮件
     */
    private McpResponse handleReadEmail(Map<String, Object> parameters, Map<String, Object> serviceParams) {
        logToolCall("read_email", parameters);

        try {
            String folder = getParameterValue(parameters, "folder", String.class, "INBOX");
            Integer limit = getParameterValue(parameters, "limit", Integer.class, 10);
            String filter = getParameterValue(parameters, "filter", String.class, "");
            Boolean unreadOnly = getParameterValue(parameters, "unreadOnly", Boolean.class, false);

            log.info("读取邮件: 文件夹={}, 限制={}, 过滤器={}, 仅未读={}", folder, limit, filter, unreadOnly);

            Map<String, Object> result = createMockEmailReadResult(folder, limit, filter, unreadOnly);

            logToolResult("read_email", true, null);
            return McpResponse.success(result);

        } catch (Exception e) {
            logToolResult("read_email", false, e.getMessage());
            return McpResponse.error("邮件读取失败: " + e.getMessage());
        }
    }

    /**
     * 处理删除邮件
     */
    private McpResponse handleDeleteEmail(Map<String, Object> parameters, Map<String, Object> serviceParams) {
        logToolCall("delete_email", parameters);

        if (!validateRequiredParameters(parameters, "emailId")) {
            return createValidationError("emailId");
        }

        try {
            String emailId = getParameterValue(parameters, "emailId", String.class, "");
            Boolean permanent = getParameterValue(parameters, "permanent", Boolean.class, false);

            log.info("删除邮件: 邮件ID={}, 永久删除={}", emailId, permanent);

            Map<String, Object> result = createMockEmailDeleteResult(emailId, permanent);

            logToolResult("delete_email", true, null);
            return McpResponse.success(result);

        } catch (Exception e) {
            logToolResult("delete_email", false, e.getMessage());
            return McpResponse.error("邮件删除失败: " + e.getMessage());
        }
    }

    // ========== 模拟数据生成方法 ==========

    private Map<String, Object> createMockEmailSendResult(String to, String cc, String bcc, 
                                                          String subject, String content, 
                                                          String contentType, String attachments) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("messageId", "msg_" + System.currentTimeMillis());
        result.put("to", to);
        result.put("cc", cc);
        result.put("bcc", bcc);
        result.put("subject", subject);
        result.put("contentType", contentType);
        result.put("contentLength", content.length());
        result.put("hasAttachments", StrUtil.isNotBlank(attachments));
        result.put("sentTime", System.currentTimeMillis());
        result.put("status", "sent");
        return result;
    }

    private Map<String, Object> createMockEmailReadResult(String folder, Integer limit, String filter, Boolean unreadOnly) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("folder", folder);
        result.put("total", limit);
        result.put("filter", filter);
        result.put("unreadOnly", unreadOnly);
        
        // 模拟邮件列表
        java.util.List<Map<String, Object>> emails = new java.util.ArrayList<>();
        for (int i = 1; i <= Math.min(limit, 5); i++) {
            Map<String, Object> email = new HashMap<>();
            email.put("id", "email_" + i);
            email.put("from", "sender" + i + "@example.com");
            email.put("subject", "测试邮件 " + i);
            email.put("receivedTime", System.currentTimeMillis() - (i * 3600000L));
            email.put("isRead", i > 2);
            email.put("hasAttachments", i % 2 == 0);
            emails.add(email);
        }
        
        result.put("emails", emails);
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }

    private Map<String, Object> createMockEmailDeleteResult(String emailId, Boolean permanent) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("emailId", emailId);
        result.put("permanent", permanent);
        result.put("action", permanent ? "permanently_deleted" : "moved_to_trash");
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }
}
