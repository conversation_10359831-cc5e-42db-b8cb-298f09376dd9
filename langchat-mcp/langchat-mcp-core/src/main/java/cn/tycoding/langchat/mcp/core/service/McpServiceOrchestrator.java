/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.tycoding.langchat.ai.core.service.impl.LangChatServiceImpl;
import cn.tycoding.langchat.mcp.core.client.ExternalMcpToolHandler;
import cn.tycoding.langchat.mcp.core.entity.AigcMcpService;
import cn.tycoding.langchat.mcp.core.protocol.McpResponse;
import cn.tycoding.langchat.common.ai.dto.ChatReq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * MCP服务智能编排器
 * 根据用户的复杂需求，自动规划和执行多个MCP服务的调用链
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class McpServiceOrchestrator {

    private final AigcMcpServiceService mcpServiceService;
    private final ExternalMcpToolHandler mcpToolHandler;
    private final LangChatServiceImpl langChatService;

    /**
     * 执行计划步骤
     */
    public static class ExecutionStep {
        private String stepId;
        private String serviceName;
        private String toolName;
        private Map<String, Object> parameters;
        private String description;
        private List<String> dependencies; // 依赖的步骤ID
        private McpResponse result;
        private boolean completed;

        public ExecutionStep(String stepId, String serviceName, String toolName, 
                           Map<String, Object> parameters, String description) {
            this.stepId = stepId;
            this.serviceName = serviceName;
            this.toolName = toolName;
            this.parameters = parameters;
            this.description = description;
            this.dependencies = new ArrayList<>();
            this.completed = false;
        }

        // Getters and Setters
        public String getStepId() { return stepId; }
        public String getServiceName() { return serviceName; }
        public String getToolName() { return toolName; }
        public Map<String, Object> getParameters() { return parameters; }
        public String getDescription() { return description; }
        public List<String> getDependencies() { return dependencies; }
        public McpResponse getResult() { return result; }
        public boolean isCompleted() { return completed; }
        
        public void setResult(McpResponse result) { this.result = result; }
        public void setCompleted(boolean completed) { this.completed = completed; }
        public void addDependency(String stepId) { this.dependencies.add(stepId); }
        public void setParameters(Map<String, Object> parameters) { this.parameters = parameters; }
    }

    /**
     * 执行计划
     */
    public static class ExecutionPlan {
        private List<ExecutionStep> steps;
        private String planDescription;

        public ExecutionPlan() {
            this.steps = new ArrayList<>();
        }

        public List<ExecutionStep> getSteps() { return steps; }
        public String getPlanDescription() { return planDescription; }
        public void setPlanDescription(String planDescription) { this.planDescription = planDescription; }
        public void addStep(ExecutionStep step) { this.steps.add(step); }
    }

    /**
     * 根据用户需求智能生成执行计划
     */
    public ExecutionPlan generateExecutionPlan(String userPrompt, List<String> availableServiceIds) {
        log.info("开始生成执行计划，用户需求: {}", userPrompt);
        
        try {
            // 1. 获取可用的MCP服务
            List<AigcMcpService> availableServices = getAvailableServices(availableServiceIds);
            
            // 2. 使用AI分析用户需求并生成执行计划
            String planPrompt = buildPlanningPrompt(userPrompt, availableServices);
            
            ChatReq planReq = new ChatReq();
            planReq.setMessage(planPrompt);
            
            String aiResponse = langChatService.text(planReq);
            
            // 3. 解析AI生成的执行计划
            ExecutionPlan plan = parseExecutionPlan(aiResponse, availableServices);
            
            log.info("执行计划生成完成，包含 {} 个步骤", plan.getSteps().size());
            return plan;
            
        } catch (Exception e) {
            log.error("生成执行计划失败", e);
            return createFallbackPlan(userPrompt, availableServiceIds);
        }
    }

    /**
     * 执行计划
     */
    public Map<String, Object> executePlan(ExecutionPlan plan, String userId, String conversationId) {
        log.info("开始执行计划: {}", plan.getPlanDescription());
        
        Map<String, Object> executionResult = new HashMap<>();
        List<Map<String, Object>> stepResults = new ArrayList<>();
        
        try {
            // 按依赖关系排序步骤
            List<ExecutionStep> sortedSteps = topologicalSort(plan.getSteps());
            
            for (ExecutionStep step : sortedSteps) {
                log.info("执行步骤: {} - {}", step.getStepId(), step.getDescription());
                
                // 检查依赖是否完成
                if (!areDependenciesCompleted(step, plan.getSteps())) {
                    log.error("步骤 {} 的依赖未完成", step.getStepId());
                    continue;
                }
                
                // 更新参数（可能依赖前面步骤的结果）
                updateStepParameters(step, plan.getSteps());
                
                // 执行步骤
                McpResponse response = mcpToolHandler.handleToolCall(
                    step.getServiceName(),
                    step.getToolName(),
                    step.getParameters(),
                    userId,
                    conversationId
                );
                
                step.setResult(response);
                step.setCompleted(response.isSuccess());
                
                // 记录步骤结果
                Map<String, Object> stepResult = new HashMap<>();
                stepResult.put("stepId", step.getStepId());
                stepResult.put("description", step.getDescription());
                stepResult.put("success", response.isSuccess());
                stepResult.put("result", response.getResult());
                if (!response.isSuccess()) {
                    stepResult.put("error", response.getError());
                }
                stepResults.add(stepResult);
                
                log.info("步骤 {} 执行完成，成功: {}", step.getStepId(), response.isSuccess());
            }
            
            executionResult.put("success", true);
            executionResult.put("planDescription", plan.getPlanDescription());
            executionResult.put("steps", stepResults);
            executionResult.put("summary", generateExecutionSummary(plan));
            
        } catch (Exception e) {
            log.error("执行计划失败", e);
            executionResult.put("success", false);
            executionResult.put("error", e.getMessage());
            executionResult.put("steps", stepResults);
        }
        
        return executionResult;
    }

    /**
     * 构建规划提示词
     */
    private String buildPlanningPrompt(String userPrompt, List<AigcMcpService> availableServices) {
        StringBuilder prompt = new StringBuilder();
        
        prompt.append("你是一个智能的任务规划助手。请根据用户需求和可用的MCP服务，生成一个详细的执行计划。\n\n");
        prompt.append("用户需求：").append(userPrompt).append("\n\n");
        prompt.append("可用的MCP服务：\n");
        
        for (AigcMcpService service : availableServices) {
            prompt.append("- ").append(service.getDisplayName()).append(" (").append(service.getName()).append(")\n");
            prompt.append("  描述：").append(service.getDescription()).append("\n");
            if (StrUtil.isNotBlank(service.getTools())) {
                try {
                    String toolsJson = service.getTools();
                    // 简单解析工具信息用于提示
                    if (toolsJson.contains("name") && toolsJson.contains("description")) {
                        prompt.append("  工具：已配置");
                    }
                    prompt.append("\n");
                } catch (Exception e) {
                    log.warn("解析工具列表失败: {}", service.getName());
                }
            }
            prompt.append("\n");
        }
        
        prompt.append("请生成一个JSON格式的执行计划，包含以下结构：\n");
        prompt.append("{\n");
        prompt.append("  \"planDescription\": \"计划描述\",\n");
        prompt.append("  \"steps\": [\n");
        prompt.append("    {\n");
        prompt.append("      \"stepId\": \"step1\",\n");
        prompt.append("      \"serviceName\": \"服务名称\",\n");
        prompt.append("      \"toolName\": \"工具名称\",\n");
        prompt.append("      \"parameters\": {\"参数名\": \"参数值\"},\n");
        prompt.append("      \"description\": \"步骤描述\",\n");
        prompt.append("      \"dependencies\": [\"依赖的步骤ID\"]\n");
        prompt.append("    }\n");
        prompt.append("  ]\n");
        prompt.append("}\n\n");
        prompt.append("注意：\n");
        prompt.append("1. 步骤之间可能有依赖关系，后续步骤可能需要前面步骤的结果\n");
        prompt.append("2. 参数中可以使用 ${stepId.result} 来引用前面步骤的结果\n");
        prompt.append("3. 确保所有步骤都是必要的，能够完成用户的需求\n");
        
        return prompt.toString();
    }

    /**
     * 解析AI生成的执行计划
     */
    private ExecutionPlan parseExecutionPlan(String aiResponse, List<AigcMcpService> availableServices) {
        ExecutionPlan plan = new ExecutionPlan();
        
        try {
            // 提取JSON部分
            String jsonPart = extractJsonFromResponse(aiResponse);
            Map<String, Object> planData = JSONUtil.toBean(jsonPart, Map.class);
            
            plan.setPlanDescription((String) planData.get("planDescription"));
            
            List<Map<String, Object>> stepsData = (List<Map<String, Object>>) planData.get("steps");
            for (Map<String, Object> stepData : stepsData) {
                ExecutionStep step = new ExecutionStep(
                    (String) stepData.get("stepId"),
                    (String) stepData.get("serviceName"),
                    (String) stepData.get("toolName"),
                    (Map<String, Object>) stepData.get("parameters"),
                    (String) stepData.get("description")
                );
                
                List<String> dependencies = (List<String>) stepData.get("dependencies");
                if (dependencies != null) {
                    step.getDependencies().addAll(dependencies);
                }
                
                plan.addStep(step);
            }
            
        } catch (Exception e) {
            log.error("解析执行计划失败", e);
            throw new RuntimeException("解析执行计划失败", e);
        }
        
        return plan;
    }

    /**
     * 从AI响应中提取JSON
     */
    private String extractJsonFromResponse(String response) {
        // 查找JSON代码块
        Pattern pattern = Pattern.compile("```json\\s*([\\s\\S]*?)\\s*```", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(response);
        
        if (matcher.find()) {
            return matcher.group(1).trim();
        }
        
        // 如果没有代码块，尝试查找大括号包围的内容
        pattern = Pattern.compile("\\{[\\s\\S]*\\}");
        matcher = pattern.matcher(response);
        
        if (matcher.find()) {
            return matcher.group().trim();
        }
        
        throw new RuntimeException("无法从AI响应中提取JSON: " + response);
    }

    /**
     * 获取可用服务
     */
    private List<AigcMcpService> getAvailableServices(List<String> serviceIds) {
        return serviceIds.stream()
                .map(mcpServiceService::findById)
                .filter(Objects::nonNull)
                .filter(AigcMcpService::getEnabled)
                .collect(Collectors.toList());
    }

    /**
     * 拓扑排序（处理依赖关系）
     */
    private List<ExecutionStep> topologicalSort(List<ExecutionStep> steps) {
        Map<String, ExecutionStep> stepMap = new HashMap<>();
        for (ExecutionStep step : steps) {
            stepMap.put(step.getStepId(), step);
        }
        
        List<ExecutionStep> result = new ArrayList<>();
        Set<String> visited = new HashSet<>();
        Set<String> visiting = new HashSet<>();
        
        for (ExecutionStep step : steps) {
            if (!visited.contains(step.getStepId())) {
                topologicalSortDFS(step.getStepId(), stepMap, visited, visiting, result);
            }
        }
        
        return result;
    }

    private void topologicalSortDFS(String stepId, Map<String, ExecutionStep> stepMap, 
                                   Set<String> visited, Set<String> visiting, List<ExecutionStep> result) {
        if (visiting.contains(stepId)) {
            throw new RuntimeException("检测到循环依赖: " + stepId);
        }
        
        if (visited.contains(stepId)) {
            return;
        }
        
        visiting.add(stepId);
        ExecutionStep step = stepMap.get(stepId);
        
        for (String dependency : step.getDependencies()) {
            if (stepMap.containsKey(dependency)) {
                topologicalSortDFS(dependency, stepMap, visited, visiting, result);
            }
        }
        
        visiting.remove(stepId);
        visited.add(stepId);
        result.add(step);
    }

    /**
     * 检查依赖是否完成
     */
    private boolean areDependenciesCompleted(ExecutionStep step, List<ExecutionStep> allSteps) {
        Map<String, ExecutionStep> stepMap = new HashMap<>();
        for (ExecutionStep s : allSteps) {
            stepMap.put(s.getStepId(), s);
        }
        
        for (String depId : step.getDependencies()) {
            ExecutionStep depStep = stepMap.get(depId);
            if (depStep == null || !depStep.isCompleted()) {
                return false;
            }
        }
        return true;
    }

    /**
     * 更新步骤参数（替换依赖结果）
     */
    private void updateStepParameters(ExecutionStep step, List<ExecutionStep> allSteps) {
        Map<String, ExecutionStep> stepMap = new HashMap<>();
        for (ExecutionStep s : allSteps) {
            stepMap.put(s.getStepId(), s);
        }
        
        Map<String, Object> updatedParams = new HashMap<>(step.getParameters());
        
        for (Map.Entry<String, Object> entry : updatedParams.entrySet()) {
            if (entry.getValue() instanceof String) {
                String value = (String) entry.getValue();
                
                // 替换 ${stepId.result} 格式的引用
                Pattern pattern = Pattern.compile("\\$\\{(\\w+)\\.result\\}");
                Matcher matcher = pattern.matcher(value);
                
                while (matcher.find()) {
                    String refStepId = matcher.group(1);
                    ExecutionStep refStep = stepMap.get(refStepId);
                    
                    if (refStep != null && refStep.isCompleted() && refStep.getResult() != null) {
                        String replacement = extractResultValue(refStep.getResult());
                        value = value.replace(matcher.group(), replacement);
                    }
                }
                
                updatedParams.put(entry.getKey(), value);
            }
        }
        
        step.setParameters(updatedParams);
    }

    /**
     * 提取结果值
     */
    private String extractResultValue(McpResponse response) {
        if (response.getResult() instanceof String) {
            return (String) response.getResult();
        } else if (response.getResult() instanceof Map) {
            Map<String, Object> resultMap = (Map<String, Object>) response.getResult();
            // 尝试提取常见的结果字段
            if (resultMap.containsKey("url")) {
                return (String) resultMap.get("url");
            } else if (resultMap.containsKey("content")) {
                return (String) resultMap.get("content");
            } else if (resultMap.containsKey("text")) {
                return (String) resultMap.get("text");
            }
        }
        return JSONUtil.toJsonStr(response.getResult());
    }

    /**
     * 生成执行摘要
     */
    private String generateExecutionSummary(ExecutionPlan plan) {
        StringBuilder summary = new StringBuilder();
        summary.append("执行计划：").append(plan.getPlanDescription()).append("\n\n");
        
        for (ExecutionStep step : plan.getSteps()) {
            summary.append("✓ ").append(step.getDescription());
            if (step.isCompleted()) {
                summary.append(" - 完成");
            } else {
                summary.append(" - 失败");
            }
            summary.append("\n");
        }
        
        return summary.toString();
    }

    /**
     * 创建备用计划
     */
    private ExecutionPlan createFallbackPlan(String userPrompt, List<String> serviceIds) {
        ExecutionPlan plan = new ExecutionPlan();
        plan.setPlanDescription("简单执行计划");
        
        // 创建一个简单的单步计划
        if (!serviceIds.isEmpty()) {
            AigcMcpService firstService = mcpServiceService.findById(serviceIds.get(0));
            if (firstService != null) {
                ExecutionStep step = new ExecutionStep(
                    "step1",
                    firstService.getName(),
                    "default_tool",
                    Map.of("prompt", userPrompt),
                    "执行用户请求"
                );
                plan.addStep(step);
            }
        }
        
        return plan;
    }
}
