/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.agent;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 任务执行结果
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
@Data
@Builder
public class TaskExecutionResult {
    
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 子任务结果列表
     */
    private List<SubTaskResult> subTaskResults;
    
    /**
     * 执行摘要
     */
    private String summary;
    
    /**
     * 错误消息
     */
    private String errorMessage;
    
    /**
     * 开始时间
     */
    @Builder.Default
    private long startTime = System.currentTimeMillis();
    
    /**
     * 结束时间
     */
    private long endTime;
    
    /**
     * 总执行时间（毫秒）
     */
    private long totalExecutionTime;
    
    /**
     * 创建成功结果
     */
    public static TaskExecutionResult success(List<SubTaskResult> results, String summary) {
        long endTime = System.currentTimeMillis();
        return TaskExecutionResult.builder()
                .success(true)
                .subTaskResults(results)
                .summary(summary)
                .endTime(endTime)
                .totalExecutionTime(endTime - System.currentTimeMillis())
                .build();
    }
    
    /**
     * 创建失败结果
     */
    public static TaskExecutionResult failure(String errorMessage) {
        long endTime = System.currentTimeMillis();
        return TaskExecutionResult.builder()
                .success(false)
                .errorMessage(errorMessage)
                .endTime(endTime)
                .totalExecutionTime(endTime - System.currentTimeMillis())
                .build();
    }
    
    /**
     * 获取成功的子任务数量
     */
    public long getSuccessCount() {
        if (subTaskResults == null) return 0;
        return subTaskResults.stream().mapToLong(r -> r.isSuccess() ? 1 : 0).sum();
    }
    
    /**
     * 获取失败的子任务数量
     */
    public long getFailureCount() {
        if (subTaskResults == null) return 0;
        return subTaskResults.stream().mapToLong(r -> r.isSuccess() ? 0 : 1).sum();
    }
    
    /**
     * 获取总任务数量
     */
    public int getTotalTaskCount() {
        return subTaskResults != null ? subTaskResults.size() : 0;
    }
}
