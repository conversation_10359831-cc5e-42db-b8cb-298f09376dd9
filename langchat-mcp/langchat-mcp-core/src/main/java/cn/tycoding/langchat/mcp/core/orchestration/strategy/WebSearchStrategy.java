/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.orchestration.strategy;

import cn.tycoding.langchat.mcp.core.entity.AigcMcpService;
import cn.tycoding.langchat.mcp.core.orchestration.impl.FlexibleMcpOrchestrator;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

/**
 * 网络搜索编排策略
 * 处理网络搜索相关的用户请求
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
@Slf4j
@Component
public class WebSearchStrategy implements FlexibleMcpOrchestrator.OrchestrationStrategy {

    private final FlexibleMcpOrchestrator orchestrator;

    public WebSearchStrategy(FlexibleMcpOrchestrator orchestrator) {
        this.orchestrator = orchestrator;
    }
    @PostConstruct
    public void init() {
        orchestrator.registerStrategy(this);
        log.info("网络搜索编排策略已注册");
    }

    @Override
    public boolean canHandle(String userInput, List<AigcMcpService> availableServices) {
        // 检查是否有搜索服务
        boolean hasSearchService = availableServices.stream()
                .anyMatch(service -> "search".equalsIgnoreCase(service.getCategory()) || 
                                   service.getName().toLowerCase().contains("search") ||
                                   service.getName().toLowerCase().contains("brave"));

        if (!hasSearchService) {
            return false;
        }

        // 检查用户输入是否包含搜索意图
        String lowerInput = userInput.toLowerCase();
        String[] searchKeywords = {
            "搜索", "查找", "查询", "搜", "找", "检索", "寻找",
            "最新", "新闻", "资讯", "信息", "了解", "知道"
        };

        for (String keyword : searchKeywords) {
            if (lowerInput.contains(keyword)) {
                return true;
            }
        }

        return false;
    }

    @Override
    public FlexibleMcpOrchestrator.OrchestrationPlan createPlan(String userInput, List<AigcMcpService> availableServices) {
        FlexibleMcpOrchestrator.OrchestrationPlan plan = new FlexibleMcpOrchestrator.OrchestrationPlan();
        
        plan.setPlanId(generatePlanId());
        plan.setDescription("网络搜索任务");
        plan.setStrategyName(getStrategyName());
        
        // 添加元数据
        plan.putMetadata("userInput", userInput);
        plan.putMetadata("estimatedDuration", "5-10秒");
        plan.putMetadata("complexity", "simple");

        // 查找搜索服务
        AigcMcpService searchService = findSearchService(availableServices);
        if (searchService == null) {
            log.warn("未找到可用的搜索服务");
            return plan;
        }

        // 创建搜索步骤
        FlexibleMcpOrchestrator.ExecutionStep searchStep = new FlexibleMcpOrchestrator.ExecutionStep();
        searchStep.setStepId("web_search");
        searchStep.setServiceName(searchService.getName());
        searchStep.setToolName("web_search");
        searchStep.setDescription("在网络上搜索相关信息");
        
        // 提取搜索关键词
        String searchQuery = extractSearchQuery(userInput);
        searchStep.putParameter("query", searchQuery);
        searchStep.putParameter("count", 5);
        
        plan.addStep(searchStep);

        log.info("创建网络搜索编排计划，查询: {}", searchQuery);
        return plan;
    }

    @Override
    public String getStrategyName() {
        return "WebSearchStrategy";
    }

    @Override
    public int getPriority() {
        return 70; // 中高优先级
    }

    /**
     * 查找搜索服务
     */
    private AigcMcpService findSearchService(List<AigcMcpService> services) {
        return services.stream()
                .filter(service -> "search".equalsIgnoreCase(service.getCategory()) || 
                                 service.getName().toLowerCase().contains("search") ||
                                 service.getName().toLowerCase().contains("brave"))
                .findFirst()
                .orElse(null);
    }

    /**
     * 提取搜索关键词
     */
    private String extractSearchQuery(String userInput) {
        // 移除常见的指令词，提取核心查询
        String query = userInput;
        
        String[] removeWords = {
            "帮我", "请", "搜索", "查找", "查询", "搜", "找一下", "找", "检索", 
            "我想", "想要", "需要", "了解", "知道", "关于", "的信息", "的资料"
        };
        
        for (String word : removeWords) {
            query = query.replace(word, "");
        }
        
        query = query.trim();
        
        // 如果提取后为空，使用原始输入
        if (query.isEmpty()) {
            query = userInput;
        }
        
        return query;
    }

    /**
     * 生成计划ID
     */
    private String generatePlanId() {
        return "search_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }
}
