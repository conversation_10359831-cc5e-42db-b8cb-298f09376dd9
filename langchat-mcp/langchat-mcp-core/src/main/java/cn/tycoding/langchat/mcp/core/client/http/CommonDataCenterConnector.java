
package cn.tycoding.langchat.mcp.core.client.http;


import cn.hutool.core.map.MapUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.tycoding.langchat.common.core.exception.ServiceException;
import cn.tycoding.langchat.mcp.core.constants.RiskPublicDataServiceEnum;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.jfinal.kit.Kv;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.Duration;
import java.util.Iterator;
import java.util.Map;

/**
 * 对接工具类
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class CommonDataCenterConnector {
    private final static String USER_NAME = "username";
    private final static String PASSWORD = "password";
    private final static String TYPE = "type";
    private final static String TENANT_ID = "tenantId";
    private final static String HOST = "host";
    private final static String GRANT_TYPE = "grant_type";
    private final static String DATA = "data";
    private final static String CODE = "code";
    private final static String MSG = "msg";
    private final static String REDIS_TOKEN_PRE_KEY = "common_data_center_token:";
    private final static Integer SUCCESS = 200;
    private final static Integer TOKEN_EXPIRE = 3000;

    private final static String BASE_HOST = "http://**************:16013/api/";

    private final StringRedisTemplate redisTemplate;

    /**
     * 获取token请求头
     *
     * @return 请求头
     */
    public Kv createWebTokenParams() {
        Kv kv = Kv.create();
        kv.set(TENANT_ID, "693490")
                .set(USER_NAME, "admin")
                .set(PASSWORD, "gyLx8Ig5zp+zq4nr21zN+J1jPL4u3c8KF+mUPGKgGQi9LakvCSORT7Pkq5YzrsHy")
                .set(TYPE, "account")
                .set("grantType", "password")
                .set(GRANT_TYPE, "password");
        return kv;
    }

    public Map<String, String> createWebTokenHeader() {
        Map<String, String> kv = MapUtil.newHashMap();
        kv.put("Authorization", "Basic c2FiZXI6c2FiZXJfc2VjcmV0");
        kv.put("grant_type", "password");
        return kv;
    }

    /**
     * 创建请求头
     *
     * @param accessToken authToken
     * @return 请求头
     */
    public Map<String, String> createRequestHeader(String accessToken) {
        Map<String, String> header = MapUtil.newHashMap();
        header.put("Blade-Auth", accessToken);
        header.put("Content-Type", "application/json");
        return header;
    }

    /**
     * 通用post发送请求
     *
     * @param path        请求url
     * @param content     明文
     * @param postRequest 是否为post请求
     * @return 返回解密数据
     */
    public CommonDataReturn sendRequest(RiskPublicDataServiceEnum path, JSONObject content, boolean postRequest) {
        return sendRequest(path, content, postRequest, true);
    }

    /**
     * 通用post发送请求
     *
     * @param path        请求url
     * @param content     明文
     * @param postRequest 是否为post请求
     * @param deal        处理异常
     * @return 返回解密数据
     */
    public CommonDataReturn sendRequest(RiskPublicDataServiceEnum path, JSONObject content, boolean postRequest, boolean deal) {

        //获取登录token
        String accessToken = getRedisToken();
        //请求
        CommonDataReturn returnBody = send(path.getPath(), content, postRequest, accessToken);
        //若未授权 则重新获取token请求一次
        if (401 == returnBody.getCode()) {
            accessToken = getToken();
            returnBody = send(path.getPath(), content, postRequest, accessToken);
        }
        if (!SUCCESS.equals(returnBody.getCode())) {
            if (deal) {
                throw new ServiceException(returnBody.getMsg());
            }
        }
        saveLog();
        return returnBody;
    }

    //日志记录
    private void saveLog() {

    }


    public CommonDataReturn send(String url, JSONObject content, boolean postRequest, String accessToken) {
        Map<String, String> requestHeader = createRequestHeader(accessToken);
        String returnBody = null;
        if (postRequest) {
            returnBody = sendPost(url, content, requestHeader);
        } else {
            returnBody = sendGet(url, content, requestHeader);
        }
        return getData(returnBody);
    }

    private CommonDataReturn getData(String returnBody) {
        return JSONUtil.toBean(returnBody, CommonDataReturn.class);
    }

    /**
     * 发送post请求
     *
     * @param url           请求地址
     * @param body          请求体
     * @param requestHeader 请求头
     * @return
     */
    private String sendPost(String url, Map<String, Object> body, Map<String, String> requestHeader) {
        return HttpRequest.post(BASE_HOST + url).body(JSONUtil.toJsonStr(body)).addHeaders(requestHeader).execute().body();
    }

    /**
     * 发送get请求
     *
     * @param url           请求地址
     * @param content       请求参数
     * @param requestHeader 请求头
     * @return
     */
    private String sendGet(String url, JSONObject content, Map<String, String> requestHeader) {
        String body = HttpRequest.get(BASE_HOST + url + asUrlParams(content)).addHeaders(requestHeader).execute().body();
        return body;
    }

    /**
     * 将map转为url路径
     */
    public static String asUrlParams(JSONObject source) {
        if (source.size() == 0) {
            return "";
        }
        Iterator<String> it = source.keySet().iterator();
        StringBuilder paramStr = new StringBuilder();
        while (it.hasNext()) {
            String key = it.next();
            String value = source.get(key, String.class);
            if (StringUtils.isBlank(value)) {
                continue;
            }
            try {
                // URL 编码
                value = URLEncoder.encode(value, "utf-8");
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            paramStr.append("&").append(key).append("=").append(value);
        }
        System.out.println(paramStr);
        // 去掉第一个&
        return "?" + paramStr.substring(1);
    }

    /**
     * 获取缓存token
     *
     * @return
     */
    private String getRedisToken() {
        String token = redisTemplate.opsForValue().get(REDIS_TOKEN_PRE_KEY);
        if (StringUtils.isNotBlank(token)) {
            return token;
        }
        return getToken();
    }

    private String getToken() {
        Object accessToken = null;
        String body = null;
        try {
            body = HttpUtil.createPost(BASE_HOST + "/blade-auth/oauth/token")
                    .header("authorization", "Basic c2FiZXI6c2FiZXJfc2VjcmV0")
                    .header("Content-Type", "application/json")
                    .body(JSONUtil.toJsonStr(createWebTokenParams()))
                    .execute().body();

        } catch (Exception exception) {
            exception.printStackTrace();
        }
        accessToken = JSONUtil.parseObj(body).get("access_token");

        String tokenStr = accessToken.toString();
        //保存token到redis 过期时间为3000
        redisTemplate.opsForValue().set(REDIS_TOKEN_PRE_KEY,tokenStr, Duration.ofSeconds(TOKEN_EXPIRE));
        return tokenStr;
    }
}
