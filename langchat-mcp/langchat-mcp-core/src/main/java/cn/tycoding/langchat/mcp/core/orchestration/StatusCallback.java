/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.orchestration;

import cn.tycoding.langchat.mcp.core.orchestration.impl.FlexibleMcpOrchestrator;
import cn.tycoding.langchat.mcp.core.protocol.McpResponse;

/**
 * MCP编排执行状态回调接口
 * 用于实时向前端推送执行状态
 * 
 * <AUTHOR>
 * @since 2025/06/15
 */
public interface StatusCallback {

    /**
     * 编排计划开始执行
     */
    void onPlanStart(FlexibleMcpOrchestrator.OrchestrationPlan plan);

    /**
     * 步骤开始执行
     */
    void onStepStart(FlexibleMcpOrchestrator.ExecutionStep step);

    /**
     * 步骤执行完成
     */
    void onStepCompleted(FlexibleMcpOrchestrator.ExecutionStep step, McpResponse response);

    /**
     * 步骤执行失败
     */
    void onStepFailed(FlexibleMcpOrchestrator.ExecutionStep step, String error);

    /**
     * 步骤被跳过
     */
    void onStepSkipped(FlexibleMcpOrchestrator.ExecutionStep step, String reason);

    /**
     * 编排计划执行完成
     */
    void onPlanCompleted(FlexibleMcpOrchestrator.OrchestrationPlan plan, boolean success);

    /**
     * 编排计划执行失败
     */
    void onPlanFailed(FlexibleMcpOrchestrator.OrchestrationPlan plan, String error);

    /**
     * 发送进度更新
     */
    void onProgressUpdate(int currentStep, int totalSteps, String message);
}
