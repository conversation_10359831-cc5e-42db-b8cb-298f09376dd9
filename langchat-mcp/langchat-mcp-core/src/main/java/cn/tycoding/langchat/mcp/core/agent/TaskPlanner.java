/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.agent;

import cn.tycoding.langchat.mcp.core.protocol.McpClient;
import cn.tycoding.langchat.mcp.core.protocol.McpResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 任务规划器
 * 将复杂任务分解为多个子任务，并协调执行
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TaskPlanner {

    private final McpClient mcpClient;
    private final ToolSelector toolSelector;
    private final ExecutorService executorService = Executors.newFixedThreadPool(10);

    /**
     * 执行复杂任务
     */
    public TaskExecutionResult executeComplexTask(String taskDescription, Map<String, Object> context) {
        log.info("开始执行复杂任务: {}", taskDescription);
        
        try {
            // 1. 任务分析和分解
            List<SubTask> subTasks = analyzeAndDecomposeTask(taskDescription, context);
            
            // 2. 创建执行计划
            ExecutionPlan plan = createExecutionPlan(subTasks);
            
            // 3. 执行任务
            return executeTaskPlan(plan, context);
            
        } catch (Exception e) {
            log.error("任务执行失败", e);
            return TaskExecutionResult.failure("任务执行失败: " + e.getMessage());
        }
    }

    /**
     * 任务分析和分解
     */
    private List<SubTask> analyzeAndDecomposeTask(String taskDescription, Map<String, Object> context) {
        List<SubTask> subTasks = new ArrayList<>();
        
        // 简单的任务分解逻辑（实际应用中可以使用更复杂的NLP分析）
        String lowerTask = taskDescription.toLowerCase();
        
        // 搜索相关任务
        if (lowerTask.contains("搜索") || lowerTask.contains("查找") || lowerTask.contains("search")) {
            subTasks.add(SubTask.builder()
                    .id(UUID.randomUUID().toString())
                    .name("网络搜索")
                    .description("搜索相关信息")
                    .toolCategory("search")
                    .priority(1)
                    .build());
        }
        
        // 邮件相关任务
        if (lowerTask.contains("邮件") || lowerTask.contains("发送") || lowerTask.contains("email")) {
            subTasks.add(SubTask.builder()
                    .id(UUID.randomUUID().toString())
                    .name("发送邮件")
                    .description("发送电子邮件")
                    .toolCategory("email")
                    .priority(2)
                    .dependencies(getSearchTaskIds(subTasks))
                    .build());
        }
        
        // 文档相关任务
        if (lowerTask.contains("文档") || lowerTask.contains("报告") || lowerTask.contains("document")) {
            subTasks.add(SubTask.builder()
                    .id(UUID.randomUUID().toString())
                    .name("生成文档")
                    .description("创建或编辑文档")
                    .toolCategory("document")
                    .priority(3)
                    .dependencies(getSearchTaskIds(subTasks))
                    .build());
        }
        
        // 数据分析任务
        if (lowerTask.contains("分析") || lowerTask.contains("统计") || lowerTask.contains("数据")) {
            subTasks.add(SubTask.builder()
                    .id(UUID.randomUUID().toString())
                    .name("数据分析")
                    .description("执行数据分析")
                    .toolCategory("database")
                    .priority(1)
                    .build());
        }
        
        // 如果没有识别出具体任务，创建一个通用任务
        if (subTasks.isEmpty()) {
            subTasks.add(SubTask.builder()
                    .id(UUID.randomUUID().toString())
                    .name("通用任务处理")
                    .description(taskDescription)
                    .toolCategory("api")
                    .priority(1)
                    .build());
        }
        
        return subTasks;
    }

    /**
     * 创建执行计划
     */
    private ExecutionPlan createExecutionPlan(List<SubTask> subTasks) {
        // 按优先级和依赖关系排序
        List<SubTask> sortedTasks = new ArrayList<>(subTasks);
        sortedTasks.sort(Comparator.comparingInt(SubTask::getPriority));
        
        // 创建执行阶段
        List<ExecutionStage> stages = new ArrayList<>();
        Map<Integer, List<SubTask>> tasksByPriority = new HashMap<>();
        
        for (SubTask task : sortedTasks) {
            tasksByPriority.computeIfAbsent(task.getPriority(), k -> new ArrayList<>()).add(task);
        }
        
        for (Map.Entry<Integer, List<SubTask>> entry : tasksByPriority.entrySet()) {
            stages.add(ExecutionStage.builder()
                    .stageNumber(entry.getKey())
                    .tasks(entry.getValue())
                    .canRunInParallel(entry.getValue().size() > 1)
                    .build());
        }
        
        return ExecutionPlan.builder()
                .stages(stages)
                .totalTasks(subTasks.size())
                .estimatedDuration(calculateEstimatedDuration(subTasks))
                .build();
    }

    /**
     * 执行任务计划
     */
    private TaskExecutionResult executeTaskPlan(ExecutionPlan plan, Map<String, Object> context) {
        List<SubTaskResult> results = new ArrayList<>();
        Map<String, Object> sharedContext = new HashMap<>(context);
        
        for (ExecutionStage stage : plan.getStages()) {
            log.info("执行阶段 {}, 包含 {} 个任务", stage.getStageNumber(), stage.getTasks().size());
            
            if (stage.isCanRunInParallel()) {
                // 并行执行
                List<CompletableFuture<SubTaskResult>> futures = new ArrayList<>();
                
                for (SubTask task : stage.getTasks()) {
                    futures.add(CompletableFuture.supplyAsync(() -> 
                            executeSubTask(task, sharedContext), executorService));
                }
                
                // 等待所有任务完成
                for (CompletableFuture<SubTaskResult> future : futures) {
                    try {
                        SubTaskResult result = future.get();
                        results.add(result);
                        
                        // 更新共享上下文
                        if (result.isSuccess() && result.getOutput() != null) {
                            sharedContext.put(result.getTaskId() + "_result", result.getOutput());
                        }
                    } catch (Exception e) {
                        log.error("子任务执行失败", e);
                        results.add(SubTaskResult.failure(UUID.randomUUID().toString(), 
                                "任务执行异常: " + e.getMessage()));
                    }
                }
            } else {
                // 串行执行
                for (SubTask task : stage.getTasks()) {
                    SubTaskResult result = executeSubTask(task, sharedContext);
                    results.add(result);
                    
                    // 更新共享上下文
                    if (result.isSuccess() && result.getOutput() != null) {
                        sharedContext.put(result.getTaskId() + "_result", result.getOutput());
                    }
                    
                    // 如果关键任务失败，停止执行
                    if (!result.isSuccess() && task.isCritical()) {
                        return TaskExecutionResult.failure("关键任务失败: " + result.getErrorMessage());
                    }
                }
            }
        }
        
        return TaskExecutionResult.success(results, generateSummary(results));
    }

    /**
     * 执行子任务
     */
    private SubTaskResult executeSubTask(SubTask task, Map<String, Object> context) {
        log.info("执行子任务: {}", task.getName());
        
        try {
            // 选择最佳服务
            String serviceName = toolSelector.selectBestService(task.getToolCategory(), task.getDescription());
            if (serviceName == null) {
                return SubTaskResult.failure(task.getId(), "未找到合适的服务");
            }
            
            // 准备参数
            Map<String, Object> params = prepareTaskParameters(task, context);
            
            // 调用工具
            McpResponse response = mcpClient.callTool(serviceName,
                    getToolNameForCategory(task.getToolCategory()), params);
            
            if (response.isSuccess()) {
                return SubTaskResult.success(task.getId(), task.getName(), response.getResult());
            } else {
                return SubTaskResult.failure(task.getId(), response.getError().getMessage());
            }
            
        } catch (Exception e) {
            log.error("子任务执行异常", e);
            return SubTaskResult.failure(task.getId(), "执行异常: " + e.getMessage());
        }
    }

    /**
     * 准备任务参数
     */
    private Map<String, Object> prepareTaskParameters(SubTask task, Map<String, Object> context) {
        Map<String, Object> params = new HashMap<>();
        
        // 根据任务类型准备不同的参数
        switch (task.getToolCategory()) {
            case "search":
                params.put("query", task.getDescription());
                params.put("limit", 5);
                break;
            case "email":
                params.put("subject", "任务执行结果");
                params.put("content", task.getDescription());
                break;
            case "document":
                params.put("title", task.getName());
                params.put("content", task.getDescription());
                break;
            default:
                params.put("input", task.getDescription());
                break;
        }
        
        // 添加上下文信息
        params.putAll(context);
        
        return params;
    }

    /**
     * 获取工具名称
     */
    private String getToolNameForCategory(String category) {
        return switch (category) {
            case "search" -> "web_search";
            case "email" -> "send_email";
            case "document" -> "create_document";
            case "database" -> "execute_query";
            case "api" -> "http_request";
            default -> "generic_tool";
        };
    }

    /**
     * 获取搜索任务ID列表
     */
    private List<String> getSearchTaskIds(List<SubTask> tasks) {
        return tasks.stream()
                .filter(task -> "search".equals(task.getToolCategory()))
                .map(SubTask::getId)
                .toList();
    }

    /**
     * 计算预估执行时间
     */
    private int calculateEstimatedDuration(List<SubTask> tasks) {
        return tasks.size() * 30; // 每个任务预估30秒
    }

    /**
     * 生成执行摘要
     */
    private String generateSummary(List<SubTaskResult> results) {
        long successCount = results.stream().mapToLong(r -> r.isSuccess() ? 1 : 0).sum();
        long failureCount = results.size() - successCount;
        
        StringBuilder summary = new StringBuilder();
        summary.append(String.format("任务执行完成。成功: %d, 失败: %d\n", successCount, failureCount));
        
        for (SubTaskResult result : results) {
            summary.append(String.format("- %s: %s\n", 
                    result.getTaskName(), 
                    result.isSuccess() ? "成功" : "失败(" + result.getErrorMessage() + ")"));
        }
        
        return summary.toString();
    }
}
