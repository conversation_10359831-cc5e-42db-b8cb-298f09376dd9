/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.orchestration.strategy;

import cn.tycoding.langchat.mcp.core.entity.AigcMcpService;
import cn.tycoding.langchat.mcp.core.orchestration.impl.FlexibleMcpOrchestrator;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

/**
 * 复合编排策略
 * 处理需要多个步骤的复杂任务，如"搜索信息然后生成图片"
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
@Slf4j
@Component
public class CompositeStrategy implements FlexibleMcpOrchestrator.OrchestrationStrategy {

    private final FlexibleMcpOrchestrator orchestrator;

    public CompositeStrategy(FlexibleMcpOrchestrator orchestrator) {
        this.orchestrator = orchestrator;
    }

    @PostConstruct
    public void init() {
        orchestrator.registerStrategy(this);
        log.info("复合编排策略已注册");
    }

    @Override
    public boolean canHandle(String userInput, List<AigcMcpService> availableServices) {
        // 检查是否包含多个动作意图
        String lowerInput = userInput.toLowerCase();
        
        // 检查连接词
        String[] connectors = {"然后", "接着", "之后", "再", "并且", "同时", "先", "后"};
        boolean hasConnector = false;
        for (String connector : connectors) {
            if (lowerInput.contains(connector)) {
                hasConnector = true;
                break;
            }
        }

        // 检查复合动作模式
        String[] compositePatterns = {
            "搜索.*生成", "查找.*画", "搜索.*制作", "查询.*创建",
            "生成.*发布", "创建.*部署", "制作.*上传"
        };
        
        boolean hasCompositePattern = false;
        for (String pattern : compositePatterns) {
            if (lowerInput.matches(".*" + pattern + ".*")) {
                hasCompositePattern = true;
                break;
            }
        }

        // 检查多个动作词
        String[] actionWords = {"搜索", "查找", "生成", "创建", "制作", "画", "发布", "部署", "上传"};
        int actionCount = 0;
        for (String action : actionWords) {
            if (lowerInput.contains(action)) {
                actionCount++;
            }
        }

        // 需要有连接词或复合模式，且有多个动作
        return (hasConnector || hasCompositePattern) && actionCount >= 2;
    }

    @Override
    public FlexibleMcpOrchestrator.OrchestrationPlan createPlan(String userInput, List<AigcMcpService> availableServices) {
        FlexibleMcpOrchestrator.OrchestrationPlan plan = new FlexibleMcpOrchestrator.OrchestrationPlan();
        
        plan.setPlanId(generatePlanId());
        plan.setDescription("复合任务编排");
        plan.setStrategyName(getStrategyName());
        
        // 添加元数据
        plan.putMetadata("userInput", userInput);
        plan.putMetadata("estimatedDuration", "60-120秒");
        plan.putMetadata("complexity", "complex");

        String lowerInput = userInput.toLowerCase();

        // 分析任务类型并创建步骤
        if (lowerInput.matches(".*搜索.*生成.*") || lowerInput.matches(".*查找.*画.*")) {
            createSearchThenImagePlan(plan, userInput, availableServices);
        } else if (lowerInput.matches(".*生成.*发布.*") || lowerInput.matches(".*创建.*部署.*")) {
            createImageThenPublishPlan(plan, userInput, availableServices);
        } else {
            // 通用复合任务处理
            createGenericCompositePlan(plan, userInput, availableServices);
        }

        log.info("创建复合编排计划，步骤数: {}", plan.getSteps().size());
        return plan;
    }

    @Override
    public String getStrategyName() {
        return "CompositeStrategy";
    }

    @Override
    public int getPriority() {
        return 90; // 最高优先级，因为处理复杂任务
    }

    /**
     * 创建"搜索然后生成图片"的计划
     */
    private void createSearchThenImagePlan(FlexibleMcpOrchestrator.OrchestrationPlan plan, 
                                          String userInput, List<AigcMcpService> availableServices) {
        
        // 查找搜索服务
        AigcMcpService searchService = findServiceByCategory(availableServices, "search");
        if (searchService != null) {
            FlexibleMcpOrchestrator.ExecutionStep searchStep = new FlexibleMcpOrchestrator.ExecutionStep();
            searchStep.setStepId("search_info");
            searchStep.setServiceName(searchService.getName());
            searchStep.setToolName("web_search");
            searchStep.setDescription("搜索相关信息");
            
            String searchQuery = extractSearchQuery(userInput);
            searchStep.putParameter("query", searchQuery);
            searchStep.putParameter("count", 3);
            
            plan.addStep(searchStep);
        }

        // 查找图片生成服务
        AigcMcpService imageService = findServiceByCategory(availableServices, "image");
        if (imageService != null) {
            FlexibleMcpOrchestrator.ExecutionStep imageStep = new FlexibleMcpOrchestrator.ExecutionStep();
            imageStep.setStepId("generate_image");
            imageStep.setServiceName(imageService.getName());
            imageStep.setToolName("text_to_image");
            imageStep.setDescription("基于搜索结果生成图片");
            
            // 依赖搜索步骤
            if (searchService != null) {
                imageStep.addDependency("search_info");
                // 使用搜索结果作为图片提示词
                imageStep.putParameter("prompt", "${search_info.result.content}");
            } else {
                String imagePrompt = extractImagePrompt(userInput);
                imageStep.putParameter("prompt", imagePrompt);
            }
            
            imageStep.putParameter("style", "auto");
            imageStep.putParameter("size", "1024*1024");
            
            plan.addStep(imageStep);
        }
    }

    /**
     * 创建"生成图片然后发布"的计划
     */
    private void createImageThenPublishPlan(FlexibleMcpOrchestrator.OrchestrationPlan plan,
                                           String userInput, List<AigcMcpService> availableServices) {
        
        // 查找图片生成服务
        AigcMcpService imageService = findServiceByCategory(availableServices, "image");
        if (imageService != null) {
            FlexibleMcpOrchestrator.ExecutionStep imageStep = new FlexibleMcpOrchestrator.ExecutionStep();
            imageStep.setStepId("generate_image");
            imageStep.setServiceName(imageService.getName());
            imageStep.setToolName("text_to_image");
            imageStep.setDescription("生成图片");
            
            String imagePrompt = extractImagePrompt(userInput);
            imageStep.putParameter("prompt", imagePrompt);
            imageStep.putParameter("style", "auto");
            imageStep.putParameter("size", "1024*1024");
            
            plan.addStep(imageStep);
        }

        // 查找发布服务（如果有的话）
        AigcMcpService publishService = findServiceByCategory(availableServices, "publish");
        if (publishService != null) {
            FlexibleMcpOrchestrator.ExecutionStep publishStep = new FlexibleMcpOrchestrator.ExecutionStep();
            publishStep.setStepId("publish_content");
            publishStep.setServiceName(publishService.getName());
            publishStep.setToolName("publish");
            publishStep.setDescription("发布生成的图片");
            
            // 依赖图片生成步骤
            if (imageService != null) {
                publishStep.addDependency("generate_image");
                publishStep.putParameter("imageUrl", "${generate_image.result.url}");
            }
            
            plan.addStep(publishStep);
        }
    }

    /**
     * 创建通用复合计划
     */
    private void createGenericCompositePlan(FlexibleMcpOrchestrator.OrchestrationPlan plan,
                                           String userInput, List<AigcMcpService> availableServices) {
        // 简单的通用处理：按服务类型顺序执行
        String[] serviceOrder = {"search", "image", "text", "file", "publish"};
        
        for (String category : serviceOrder) {
            AigcMcpService service = findServiceByCategory(availableServices, category);
            if (service != null && shouldIncludeService(userInput, category)) {
                FlexibleMcpOrchestrator.ExecutionStep step = new FlexibleMcpOrchestrator.ExecutionStep();
                step.setStepId(category + "_step");
                step.setServiceName(service.getName());
                step.setToolName(getDefaultToolForCategory(category));
                step.setDescription("执行" + category + "相关任务");
                
                // 添加基本参数
                addDefaultParameters(step, category, userInput);
                
                plan.addStep(step);
            }
        }
    }

    // 辅助方法
    private AigcMcpService findServiceByCategory(List<AigcMcpService> services, String category) {
        return services.stream()
                .filter(service -> category.equalsIgnoreCase(service.getCategory()) ||
                                 service.getName().toLowerCase().contains(category))
                .findFirst()
                .orElse(null);
    }

    private boolean shouldIncludeService(String userInput, String category) {
        String lowerInput = userInput.toLowerCase();
        switch (category) {
            case "search": return lowerInput.contains("搜索") || lowerInput.contains("查找");
            case "image": return lowerInput.contains("图") || lowerInput.contains("画");
            case "text": return lowerInput.contains("文本") || lowerInput.contains("写");
            case "file": return lowerInput.contains("文件") || lowerInput.contains("下载");
            case "publish": return lowerInput.contains("发布") || lowerInput.contains("部署");
            default: return false;
        }
    }

    private String getDefaultToolForCategory(String category) {
        switch (category) {
            case "search": return "web_search";
            case "image": return "text_to_image";
            case "text": return "generate_text";
            case "file": return "process_file";
            case "publish": return "publish";
            default: return "default_tool";
        }
    }

    private void addDefaultParameters(FlexibleMcpOrchestrator.ExecutionStep step, String category, String userInput) {
        switch (category) {
            case "search":
                step.putParameter("query", extractSearchQuery(userInput));
                step.putParameter("count", 3);
                break;
            case "image":
                step.putParameter("prompt", extractImagePrompt(userInput));
                step.putParameter("style", "auto");
                step.putParameter("size", "1024*1024");
                break;
        }
    }

    private String extractSearchQuery(String userInput) {
        // 简化的搜索词提取
        return userInput.replaceAll("搜索|查找|然后.*", "").trim();
    }

    private String extractImagePrompt(String userInput) {
        // 简化的图片提示词提取
        return userInput.replaceAll(".*生成|.*画|图片|图像", "").trim();
    }

    private String generatePlanId() {
        return "composite_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }
}
