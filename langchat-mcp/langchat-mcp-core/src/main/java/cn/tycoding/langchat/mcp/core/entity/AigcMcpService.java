/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * MCP服务管理实体
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
@Data
@Accessors(chain = true)
@TableName("aigc_mcp_service")
public class AigcMcpService implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 服务名称
     */
    private String name;

    /**
     * 服务显示名称
     */
    private String displayName;

    /**
     * 服务描述
     */
    private String description;

    /**
     * 服务端点URL
     */
    private String endpoint;

    /**
     * 服务版本
     */
    private String version;

    /**
     * 服务类型：HTTP, WEBSOCKET, GRPC
     */
    private String type;

    /**
     * 服务状态：ACTIVE, INACTIVE, ERROR, UNKNOWN
     */
    private String status;

    /**
     * 服务分类：builtin(内置), external(外部)
     */
    private String category;

    /**
     * 认证类型：bearer, basic, api_key, none
     */
    private String authType;

    /**
     * 认证配置JSON
     */
    private String authConfig;

    /**
     * 服务配置JSON
     */
    private String config;

    /**
     * 支持的工具列表JSON
     */
    private String tools;

    /**
     * 服务参数定义JSON
     */
    private String parameters;

    /**
     * 服务参数值JSON
     */
    private String parameterValues;

    /**
     * 服务图标
     */
    private String icon;

    /**
     * 服务标签
     */
    private String tags;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 是否需要确认
     */
    private Boolean requiresConfirmation;

    /**
     * 超时时间（秒）
     */
    private Integer timeout;

    /**
     * 最大重试次数
     */
    private Integer maxRetries;

    /**
     * 健康检查URL
     */
    private String healthCheckUrl;

    /**
     * 健康检查间隔（秒）
     */
    private Integer healthCheckInterval;

    /**
     * 最后健康检查时间
     */
    private Date lastHealthCheck;
    /**
     * 健康状态
     */
    private String healthStatus;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 备注
     */
    private String remark;

}
