/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.orchestration.strategy;

import cn.tycoding.langchat.mcp.core.entity.AigcMcpService;
import cn.tycoding.langchat.mcp.core.orchestration.impl.FlexibleMcpOrchestrator;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.regex.Pattern;

/**
 * 图片生成并发布策略
 * 处理"生成图片然后发布"类型的请求
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
@Component
public class ImagePublishStrategy implements FlexibleMcpOrchestrator.OrchestrationStrategy {

    private static final Pattern IMAGE_PUBLISH_PATTERN = Pattern.compile(
        ".*(生成|画|制作).*(图片|图像|图).*(然后|接着|之后|再).*(发布|上传|部署).*",
        Pattern.CASE_INSENSITIVE
    );

    @Override
    public boolean canHandle(String userInput, List<AigcMcpService> availableServices) {
        // 1. 检查用户输入是否匹配模式
        if (!IMAGE_PUBLISH_PATTERN.matcher(userInput).matches()) {
            return false;
        }
        
        // 2. 检查是否有图片生成服务
        boolean hasImageService = availableServices.stream()
                .anyMatch(service -> 
                    service.getName().contains("image") || 
                    service.getName().contains("wanx") ||
                    service.getDescription().contains("图片") ||
                    service.getDescription().contains("生图")
                );
        
        // 3. 检查是否有发布服务
        boolean hasPublishService = availableServices.stream()
                .anyMatch(service -> 
                    service.getName().contains("publish") || 
                    service.getName().contains("deploy") ||
                    service.getName().contains("edgeone") ||
                    service.getDescription().contains("发布") ||
                    service.getDescription().contains("部署")
                );
        
        return hasImageService && hasPublishService;
    }

    @Override
    public FlexibleMcpOrchestrator.OrchestrationPlan createPlan(String userInput, List<AigcMcpService> availableServices) {
        FlexibleMcpOrchestrator.OrchestrationPlan plan = new FlexibleMcpOrchestrator.OrchestrationPlan();
        plan.setDescription("图片生成并发布流程");
        
        // 提取图片描述
        String imagePrompt = extractImagePrompt(userInput);
        
        // 查找图片生成服务
        AigcMcpService imageService = findImageService(availableServices);
        if (imageService != null) {
            FlexibleMcpOrchestrator.ExecutionStep imageStep = new FlexibleMcpOrchestrator.ExecutionStep();
            imageStep.setStepId("generate_image");
            imageStep.setServiceName(imageService.getName());
            imageStep.setToolName("text_to_image");
            imageStep.setDescription("生成图片: " + imagePrompt);
            imageStep.putParameter("prompt", imagePrompt);
            imageStep.putParameter("style", extractImageStyle(userInput));
            imageStep.putParameter("size", extractImageSize(userInput));
            
            plan.addStep(imageStep);
        }
        
        // 查找发布服务
        AigcMcpService publishService = findPublishService(availableServices);
        if (publishService != null) {
            FlexibleMcpOrchestrator.ExecutionStep publishStep = new FlexibleMcpOrchestrator.ExecutionStep();
            publishStep.setStepId("publish_image");
            publishStep.setServiceName(publishService.getName());
            publishStep.setToolName(determinePublishTool(publishService));
            publishStep.setDescription("发布图片到平台");
            publishStep.putParameter("file_url", "${generate_image.result.url}");
            publishStep.putParameter("title", imagePrompt);
            publishStep.addDependency("generate_image");
            
            // 添加条件：只有图片生成成功才发布
            publishStep.putCondition("require_success", "generate_image");
            
            plan.addStep(publishStep);
        }
        
        // 添加元数据
        plan.putMetadata("image_prompt", imagePrompt);
        plan.putMetadata("estimated_time", "60秒");
        plan.putMetadata("complexity", "medium");
        
        return plan;
    }

    @Override
    public String getStrategyName() {
        return "ImagePublishStrategy";
    }

    @Override
    public int getPriority() {
        return 80; // 高优先级
    }

    /**
     * 提取图片描述
     */
    private String extractImagePrompt(String userInput) {
        // 使用正则表达式提取图片描述
        Pattern promptPattern = Pattern.compile(".*(生成|画|制作).*?([^，。！？]+).*(图片|图像|图).*");
        java.util.regex.Matcher matcher = promptPattern.matcher(userInput);
        
        if (matcher.find()) {
            String prompt = matcher.group(2).trim();
            // 清理提取的文本
            prompt = prompt.replaceAll("(一张|一个|的)", "").trim();
            return prompt.isEmpty() ? "图片" : prompt;
        }
        
        // 如果正则匹配失败，尝试简单的关键词提取
        String[] keywords = {"可爱", "美丽", "漂亮", "小猫", "小狗", "风景", "人物", "卡通"};
        for (String keyword : keywords) {
            if (userInput.contains(keyword)) {
                return keyword;
            }
        }
        
        return "图片";
    }

    /**
     * 提取图片风格
     */
    private String extractImageStyle(String userInput) {
        if (userInput.contains("摄影") || userInput.contains("照片") || userInput.contains("真实")) {
            return "photography";
        } else if (userInput.contains("卡通") || userInput.contains("动漫") || userInput.contains("可爱")) {
            return "cartoon";
        } else if (userInput.contains("油画") || userInput.contains("艺术")) {
            return "oil_painting";
        } else if (userInput.contains("水彩")) {
            return "watercolor";
        }
        return "photography"; // 默认风格
    }

    /**
     * 提取图片尺寸
     */
    private String extractImageSize(String userInput) {
        if (userInput.contains("正方形") || userInput.contains("1:1")) {
            return "1024*1024";
        } else if (userInput.contains("横屏") || userInput.contains("宽屏") || userInput.contains("16:9")) {
            return "1344*768";
        } else if (userInput.contains("竖屏") || userInput.contains("9:16")) {
            return "768*1344";
        }
        return "1024*1024"; // 默认尺寸
    }

    /**
     * 查找图片生成服务
     */
    private AigcMcpService findImageService(List<AigcMcpService> services) {
        return services.stream()
                .filter(service -> 
                    service.getName().contains("image") || 
                    service.getName().contains("wanx") ||
                    service.getDescription().contains("图片") ||
                    service.getDescription().contains("生图")
                )
                .findFirst()
                .orElse(null);
    }

    /**
     * 查找发布服务
     */
    private AigcMcpService findPublishService(List<AigcMcpService> services) {
        return services.stream()
                .filter(service -> 
                    service.getName().contains("publish") || 
                    service.getName().contains("deploy") ||
                    service.getName().contains("edgeone") ||
                    service.getDescription().contains("发布") ||
                    service.getDescription().contains("部署")
                )
                .findFirst()
                .orElse(null);
    }

    /**
     * 确定发布工具
     */
    private String determinePublishTool(AigcMcpService publishService) {
        if (publishService.getName().contains("edgeone")) {
            return "upload_file";
        } else if (publishService.getName().contains("blog")) {
            return "publish_image";
        } else if (publishService.getName().contains("cdn")) {
            return "upload_to_cdn";
        }
        return "upload_file"; // 默认工具
    }
}
