/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.tycoding.langchat.ai.core.service.impl.LangChatServiceImpl;
import cn.tycoding.langchat.mcp.core.client.ExternalMcpToolHandler;
import cn.tycoding.langchat.mcp.core.protocol.McpResponse;
import cn.tycoding.langchat.ai.core.provider.EmbeddingProvider;
import cn.tycoding.langchat.ai.core.provider.ModelProvider;
import cn.tycoding.langchat.mcp.core.orchestration.dto.OrchestrationExecutionResult;
import cn.tycoding.langchat.mcp.core.orchestration.impl.FlexibleMcpOrchestrator;
import cn.tycoding.langchat.mcp.core.orchestration.impl.StreamingStatusCallback;
import cn.tycoding.langchat.mcp.core.service.AsyncTaskHandler;
import cn.tycoding.langchat.mcp.core.service.McpServiceOrchestrator;
import cn.tycoding.langchat.mcp.core.service.McpToolSelector;
import cn.tycoding.langchat.common.ai.dto.ChatReq;
import cn.tycoding.langchat.common.ai.properties.ChatProps;
import cn.tycoding.langchat.common.core.exception.ServiceException;
import com.agentsflex.core.util.StringUtil;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.model.chat.response.ChatResponse;
import dev.langchain4j.model.output.Response;
import dev.langchain4j.rag.content.Content;
import dev.langchain4j.service.TokenStream;
import dev.langchain4j.service.tool.ToolExecution;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * 集成MCP工具的增强LangChat服务实现
 *
 * <AUTHOR>
 * @since 2024/12/18
 */
@Slf4j
@Service("mcpEnhancedLangChatService")
public class McpEnhancedLangChatServiceImpl extends LangChatServiceImpl {

    private final ExternalMcpToolHandler mcpToolHandler;
    private final McpToolSelector mcpToolSelector;
    private final McpServiceOrchestrator mcpOrchestrator;
    private final FlexibleMcpOrchestrator flexibleMcpOrchestrator;
    private final AsyncTaskHandler asyncTaskHandler;

    // 防止重复调用的ThreadLocal标记
    private static final ThreadLocal<Boolean> MCP_PROCESSING = new ThreadLocal<>();

    public McpEnhancedLangChatServiceImpl(ModelProvider modelProvider,
                                          EmbeddingProvider embeddingProvider,
                                          ChatProps chatProps,
                                          ExternalMcpToolHandler mcpToolHandler,
                                          McpToolSelector mcpToolSelector,
                                          McpServiceOrchestrator mcpOrchestrator,
                                          FlexibleMcpOrchestrator flexibleMcpOrchestrator,
                                          AsyncTaskHandler asyncTaskHandler) {
        super(modelProvider, embeddingProvider, chatProps);
        this.mcpToolHandler = mcpToolHandler;
        this.mcpToolSelector = mcpToolSelector;
        this.mcpOrchestrator = mcpOrchestrator;
        this.flexibleMcpOrchestrator = flexibleMcpOrchestrator;
        this.asyncTaskHandler = asyncTaskHandler;
    }

    /**
     * 增强的聊天方法，集成MCP工具调用
     */
    @Override
    public TokenStream chat(ChatReq req) {
        try {
            // 1. 检查是否需要调用MCP工具
            if (shouldUseMcpTools(req)) {
                return chatWithMcpOrchestration(req);
            }

            // 2. 如果不需要MCP工具，使用原有逻辑
            return super.chat(req);

        } catch (Exception e) {
            log.error("MCP增强聊天失败", e);
            throw new ServiceException("聊天处理失败: " + e.getMessage());
        }
    }

    /**
     * 判断是否需要使用MCP工具
     */
    private boolean shouldUseMcpTools(ChatReq req) {
        // 检查是否配置了MCP服务
        if (req.getMcpServiceIds() == null || req.getMcpServiceIds().isEmpty()) {
            return false;
        }

        // 检查用户输入是否包含工具调用意图
        String userMessage = req.getMessage();
        if (StrUtil.isBlank(userMessage)) {
            return false;
        }

        // 重要：如果消息中已经包含工具执行结果，说明是MCP调用后的增强请求，不再进行MCP调用
        if (userMessage.contains("工具执行结果:") || userMessage.contains("智能编排执行结果")) {
            log.debug("检测到工具执行结果，跳过MCP调用");
            return false;
        }

        // 额外保护：检查是否正在处理MCP调用
        if (Boolean.TRUE.equals(MCP_PROCESSING.get())) {
            log.debug("检测到MCP正在处理中，跳过重复调用");
            return false;
        }

        // 简单的意图识别
        return containsToolIntent(userMessage);
    }

    /**
     * 检查是否包含工具调用意图
     */
    private boolean containsToolIntent(String message) {
        String[] toolKeywords = {
                "画", "生成图片", "搜索", "查找", "分析", "翻译",
                "计算", "查询", "获取", "下载", "上传", "处理",
                "发布", "部署", "创建", "制作", "编写", "生成"
        };

        String lowerMessage = message.toLowerCase();
        for (String keyword : toolKeywords) {
            if (lowerMessage.contains(keyword)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查是否需要复杂编排（多个步骤）
     */
    private boolean needsOrchestration(String message) {
        String[] orchestrationKeywords = {
                "然后", "接着", "之后", "再", "并且", "同时", "先", "后",
                "生成.*发布", "创建.*部署", "制作.*上传", "写.*发布"
        };

        for (String keyword : orchestrationKeywords) {
            if (message.matches(".*" + keyword + ".*")) {
                return true;
            }
        }

        // 检查是否包含多个动作词
        String[] actionWords = {"生成", "创建", "制作", "发布", "部署", "上传", "下载", "搜索", "分析"};
        int actionCount = 0;
        for (String action : actionWords) {
            if (message.contains(action)) {
                actionCount++;
            }
        }

        return actionCount >= 2;
    }

    /**
     * 使用MCP编排的聊天处理
     */
    private TokenStream chatWithMcpOrchestration(ChatReq req) {
        try {
            // 设置MCP处理标记
            MCP_PROCESSING.set(true);

            log.info("使用MCP智能编排处理用户请求: {}", req.getMessage());

            if (ObjectUtil.isNotEmpty(req.getMessage())) {
                req.setMessage(req.getMessage() + " urls : " + req.getUrl());
            }
            // 1. 生成编排计划（使用系统提示词和模型ID）
            FlexibleMcpOrchestrator.OrchestrationPlan plan = flexibleMcpOrchestrator.orchestrate(
                    req.getMessage(), req.getMcpServiceIds(), req.getPromptText(), req.getModelId()
            );

            if (plan == null || plan.getSteps().isEmpty()) {
                log.warn("未生成有效的编排计划，使用简单工具调用");
                return chatWithSimpleTools(req);
            }

            // 2. 创建状态回调（用于实时推送执行状态）
            StreamingStatusCallback statusCallback = new StreamingStatusCallback(req.getEmitter());

            // 3. 执行编排计划（带状态回调和模型ID）
            OrchestrationExecutionResult executionResult = flexibleMcpOrchestrator.execute(
                    plan, req.getUserId(), req.getConversationId(), statusCallback, req.getModelId(), req.getMessage()
            );

            // 4. 构建包含执行结果的请求
            String resultSummary = formatFlexibleOrchestrationResult(executionResult);
            ChatReq enhancedReq = buildEnhancedRequest(req, resultSummary);

            log.info("MCP编排完成，调用基础聊天服务生成最终回答");
            log.debug("增强请求内容: {}", enhancedReq.getMessage());

            try {
                return super.chat(enhancedReq);
            } catch (Exception e) {
                log.error("调用基础聊天服务失败，直接返回MCP执行结果", e);
                // 如果基础聊天服务调用失败，直接返回MCP执行结果
                return createDirectResponse(resultSummary);
            }

        } catch (Exception e) {
            log.error("MCP智能编排处理失败", e);
            return chatWithSimpleTools(req);
        } finally {
            // 清除MCP处理标记
            MCP_PROCESSING.remove();
        }
    }

    /**
     * 复杂编排模式的聊天处理
     */
    private TokenStream chatWithOrchestration(ChatReq req) {
        try {
            log.info("使用智能编排模式处理用户请求: {}", req.getMessage());

            // 1. 生成执行计划
            McpServiceOrchestrator.ExecutionPlan plan = mcpOrchestrator.generateExecutionPlan(
                    req.getMessage(), req.getMcpServiceIds()
            );

            // 2. 执行计划
            Map<String, Object> executionResult = mcpOrchestrator.executePlan(
                    plan, req.getUserId(), req.getConversationId()
            );

            // 3. 构建包含执行结果的请求
            String resultSummary = formatOrchestrationResult(executionResult);
            ChatReq enhancedReq = buildEnhancedRequest(req, resultSummary);

            return super.chat(enhancedReq);

        } catch (Exception e) {
            log.error("智能编排处理失败", e);
            return chatWithSimpleTools(req);
        }
    }

    /**
     * 简单工具调用模式
     */
    private TokenStream chatWithSimpleTools(ChatReq req) {
        try {
            // 1. 智能选择MCP工具
            List<McpToolSelector.ToolSelection> toolSelections = mcpToolSelector.selectTools(
                    req.getMessage(), req.getMcpServiceIds()
            );

            if (toolSelections.isEmpty()) {
                // 没有匹配的工具，使用普通聊天
                log.info("未找到匹配的MCP工具，使用普通聊天模式");
                return super.chat(req);
            }

            // 2. 执行工具调用
            StringBuilder toolResults = new StringBuilder();
            for (McpToolSelector.ToolSelection selection : toolSelections) {
                if (selection.getConfidence() > 0.6) { // 只执行高置信度的工具
                    McpResponse response = mcpToolHandler.handleToolCall(
                            selection.getServiceName(),
                            selection.getToolName(),
                            selection.getParameters(),
                            req.getUserId(),
                            req.getConversationId()
                    );

                    if (response.isSuccess()) {
                        String result = formatToolResult(selection, response);
                        toolResults.append(result).append("\n\n");
                    }
                }
            }

            // 3. 如果有工具结果，让AI基于结果进行总结
            if (toolResults.length() > 0) {
                // 构建包含工具结果的新请求
                ChatReq enhancedReq = buildEnhancedRequest(req, toolResults.toString());
                return super.chat(enhancedReq);
            } else {
                // 没有成功的工具调用，使用普通聊天
                return super.chat(req);
            }

        } catch (Exception e) {
            log.error("简单工具调用失败", e);
            return super.chat(req);
        }
    }

    /**
     * 构建包含工具结果的增强请求
     */
    private ChatReq buildEnhancedRequest(ChatReq originalReq, String toolResults) {
        ChatReq enhancedReq = new ChatReq();

        // 复制原始请求的属性
        enhancedReq.setModelId(originalReq.getModelId());
        enhancedReq.setConversationId(originalReq.getConversationId());
        enhancedReq.setUserId(originalReq.getUserId());
        enhancedReq.setKnowledgeIds(originalReq.getKnowledgeIds());
        enhancedReq.setPromptText(originalReq.getPromptText());
        enhancedReq.setRole(originalReq.getRole());
        enhancedReq.setEmitter(originalReq.getEmitter());

        // 重要：清空MCP服务配置，避免重复调用
        enhancedReq.setMcpServiceIds(null);

        // 构建新的消息，包含原始问题和工具结果
        String enhancedMessage = String.format(
                "用户问题: %s\n\n工具执行结果:\n%s\n\n请基于以上工具执行结果，为用户提供完整、准确的回答。",
                originalReq.getMessage(),
                toolResults
        );

        enhancedReq.setMessage(enhancedMessage);

        return enhancedReq;
    }

    /**
     * 格式化工具执行结果
     */
    private String formatToolResult(McpToolSelector.ToolSelection selection, McpResponse response) {
        StringBuilder result = new StringBuilder();

        result.append("✅ ").append(selection.getReason()).append("\n");

        // 根据工具类型格式化结果
        if ("text_to_image".equals(selection.getToolName())) {
            result.append(formatImageGenerationResult(response));
        } else if ("web_search".equals(selection.getToolName())) {
            result.append(formatSearchResult(response));
        } else {
            result.append(formatGenericResult(response));
        }

        return result.toString();
    }

    /**
     * 格式化图片生成结果
     */
    private String formatImageGenerationResult(McpResponse response) {
        try {
            Map<String, Object> data = (Map<String, Object>) response.getResult();

            // 检查是否包含错误信息
            if (data.containsKey("success") && !(Boolean) data.get("success")) {
                String error = (String) data.get("error");
                return "❌ 图片生成失败: " + error;
            }

            // 处理正常结果（异步任务应该已经在ExternalMcpToolHandler中处理完成）
            if (data != null && data.containsKey("output")) {
                Map<String, Object> output = (Map<String, Object>) data.get("output");
                if (output.containsKey("results")) {
                    List<Map<String, Object>> results = (List<Map<String, Object>>) output.get("results");
                    if (!results.isEmpty() && results.get(0).containsKey("url")) {
                        String imageUrl = (String) results.get(0).get("url");
                        return "🖼️ 图片生成成功！\n图片地址: " + imageUrl;
                    }
                }

                // 检查是否还有任务状态信息（可能是异步任务的最终状态）
                if (output.containsKey("task_status")) {
                    String taskStatus = (String) output.get("task_status");
                    if ("SUCCEEDED".equals(taskStatus)) {
                        return "🖼️ 图片生成任务已完成，但未找到图片URL。详细信息: " + data;
                    } else if ("FAILED".equals(taskStatus)) {
                        return "❌ 图片生成任务失败，状态: " + taskStatus;
                    }
                }
            }

            // 检查是否为任务完成状态
            if (asyncTaskHandler.isTaskCompleted(data)) {
                if (asyncTaskHandler.isTaskSuccessful(data)) {
                    return "🖼️ 图片生成任务已完成，请查看详细结果: " + data;
                } else {
                    return "❌ 图片生成任务失败: " + data.get("error");
                }
            }

            // 如果到这里，说明可能是未处理的异步任务状态
            AsyncTaskHandler.TaskStatus status = asyncTaskHandler.extractTaskStatus(data);
            if (status == AsyncTaskHandler.TaskStatus.PENDING || status == AsyncTaskHandler.TaskStatus.RUNNING) {
                log.warn("检测到未处理的异步任务状态: {}", status.getCode());
                return String.format("⏳ 图片生成任务状态: %s (%s)\n任务ID: %s\n注意：异步任务应该已经在工具处理器中完成",
                        status.getCode(),
                        status.getDescription(),
                        extractTaskId(data)
                );
            }

        } catch (Exception e) {
            log.warn("格式化图片生成结果失败", e);
        }
        return "🖼️ 图片生成完成，详细信息: " + response.getResult();
    }

    /**
     * 从响应中提取任务ID
     */
    private String extractTaskId(Map<String, Object> response) {
        String[] possibleFields = {"task_id", "taskId", "id", "request_id", "requestId"};

        for (String field : possibleFields) {
            Object value = response.get(field);
            if (value != null) {
                return value.toString();
            }
        }

        Object output = response.get("output");
        if (output instanceof Map) {
            Map<String, Object> outputMap = (Map<String, Object>) output;
            for (String field : possibleFields) {
                Object value = outputMap.get(field);
                if (value != null) {
                    return value.toString();
                }
            }
        }

        return "unknown";
    }

    /**
     * 格式化搜索结果
     */
    private String formatSearchResult(McpResponse response) {
        try {
            Map<String, Object> data = (Map<String, Object>) response.getResult();
            if (data != null && data.containsKey("web")) {
                Map<String, Object> web = (Map<String, Object>) data.get("web");
                if (web.containsKey("results")) {
                    List<Map<String, Object>> results = (List<Map<String, Object>>) web.get("results");
                    StringBuilder searchResult = new StringBuilder("🔍 搜索结果:\n");

                    int count = Math.min(results.size(), 3); // 只显示前3个结果
                    for (int i = 0; i < count; i++) {
                        Map<String, Object> item = results.get(i);
                        searchResult.append(String.format("%d. %s\n   %s\n\n",
                                i + 1,
                                item.get("title"),
                                item.get("description")
                        ));
                    }
                    return searchResult.toString();
                }
            }
        } catch (Exception e) {
            log.warn("格式化搜索结果失败", e);
        }
        return "🔍 搜索完成，详细信息: " + response.getResult();
    }

    /**
     * 格式化通用结果
     */
    private String formatGenericResult(McpResponse response) {
        return "📋 工具执行结果: " + response.getResult();
    }

    /**
     * 格式化编排执行结果
     */
    private String formatOrchestrationResult(Map<String, Object> executionResult) {
        StringBuilder result = new StringBuilder();

        result.append("🎯 智能编排执行结果\n\n");
        result.append("执行计划: ").append(executionResult.get("planDescription")).append("\n\n");

        List<Map<String, Object>> steps = (List<Map<String, Object>>) executionResult.get("steps");
        if (steps != null) {
            result.append("执行步骤:\n");
            for (int i = 0; i < steps.size(); i++) {
                Map<String, Object> step = steps.get(i);
                boolean success = (Boolean) step.get("success");

                result.append(String.format("%d. %s - %s\n",
                        i + 1,
                        step.get("description"),
                        success ? "✅ 成功" : "❌ 失败"
                ));

                if (success && step.containsKey("result")) {
                    Object stepResult = step.get("result");
                    if (stepResult instanceof Map) {
                        Map<String, Object> resultMap = (Map<String, Object>) stepResult;
                        if (resultMap.containsKey("url")) {
                            result.append("   📎 生成链接: ").append(resultMap.get("url")).append("\n");
                        } else if (resultMap.containsKey("content")) {
                            result.append("   📄 生成内容: ").append(resultMap.get("content")).append("\n");
                        }
                    }
                }
                result.append("\n");
            }
        }

        if (executionResult.containsKey("summary")) {
            result.append("📋 执行摘要:\n").append(executionResult.get("summary"));
        }

        return result.toString();
    }

    /**
     * 格式化灵活编排执行结果
     */
    private String formatFlexibleOrchestrationResult(OrchestrationExecutionResult executionResult) {
        StringBuilder result = new StringBuilder();

        result.append("🎯 智能编排执行结果\n\n");

        // 显示策略信息
        result.append("编排策略: ").append(executionResult.getStrategyName()).append("\n");
        result.append("执行计划: ").append(executionResult.getDescription()).append("\n");
        result.append("执行状态: ").append(executionResult.isSuccess() ? "✅ 成功" : "❌ 失败").append("\n");

        // 显示统计信息
        if (executionResult.getTotalSteps() != null) {
            result.append("执行摘要: ").append(executionResult.getExecutionSummary()).append("\n");
        }
        result.append("\n");

        // 显示执行步骤
        if (executionResult.getSteps() != null && !executionResult.getSteps().isEmpty()) {
            result.append("执行步骤:\n");
            for (int i = 0; i < executionResult.getSteps().size(); i++) {
                var step = executionResult.getSteps().get(i);

                result.append(String.format("%d. %s - %s\n",
                        i + 1,
                        step.getDescription(),
                        step.getStatusDescription()
                ));

                // 显示步骤结果
                if (step.isSuccess() && step.hasResult()) {
                    String formattedResult = formatStepResult(step.getResult());
                    if (formattedResult != null && !formattedResult.isEmpty()) {
                        result.append("   ").append(formattedResult).append("\n");
                    }
                }

                // 显示错误信息
                if (!step.isSuccess() && !step.isSkipped() && step.getError() != null) {
                    result.append("   ❌ 错误: ").append(step.getError()).append("\n");
                }

                // 显示跳过原因
                if (step.isSkipped() && step.getError() != null) {
                    result.append("   ⏭️ 跳过: ").append(step.getError()).append("\n");
                }

                result.append("\n");
            }
        }

        // 显示元数据
        if (executionResult.getMetadata() != null && !executionResult.getMetadata().isEmpty()) {
            result.append("📋 执行信息:\n");
            executionResult.getMetadata().forEach((key, value) -> {
                result.append("   ").append(key).append(": ").append(value).append("\n");
            });
        }

        return result.toString();
    }

    /**
     * 格式化步骤结果
     */
    private String formatStepResult(Object stepResult) {
        if (stepResult instanceof Map) {
            Map<String, Object> resultMap = (Map<String, Object>) stepResult;

            // 图片URL
            if (resultMap.containsKey("url")) {
                return "📎 生成链接: " + resultMap.get("url");
            }

            // 文本内容
            if (resultMap.containsKey("content")) {
                String content = String.valueOf(resultMap.get("content"));
                if (content.length() > 100) {
                    content = content.substring(0, 100) + "...";
                }
                return "📄 生成内容: " + content;
            }

            // 搜索结果
            if (resultMap.containsKey("results")) {
                List<?> results = (List<?>) resultMap.get("results");
                return "🔍 搜索到 " + results.size() + " 条结果";
            }

            // 通用结果
            return "📋 执行结果: " + JSONUtil.toJsonStr(resultMap);
        }

        return stepResult != null ? stepResult.toString() : "";
    }

    /**
     * 创建直接响应（当基础聊天服务失败时使用）
     */
    private TokenStream createDirectResponse(String content) {
        return new TokenStream() {
            @Override
            public TokenStream onPartialResponse(Consumer<String> partialResponseHandler) {
                return null;
            }

            @Override
            public TokenStream onNext(Consumer<String> consumer) {
                // 直接发送完整内容
                consumer.accept(content);
                return this;
            }

            @Override
            public TokenStream onRetrieved(Consumer<List<Content>> contentHandler) {
                return null;
            }

            @Override
            public TokenStream onToolExecuted(Consumer<ToolExecution> toolExecuteHandler) {
                return null;
            }

            @Override
            public TokenStream onCompleteResponse(Consumer<ChatResponse> completeResponseHandler) {
                return null;
            }

            @Override
            public TokenStream onComplete(Consumer<Response<AiMessage>> consumer) {
                // 创建一个简单的响应
                AiMessage aiMessage = new AiMessage(content);
                Response<AiMessage> response = Response.from(aiMessage);
                consumer.accept(response);
                return this;
            }

            @Override
            public TokenStream onError(Consumer<Throwable> consumer) {
                return this;
            }

            @Override
            public TokenStream ignoreErrors() {
                return null;
            }

            @Override
            public void start() {

            }
        };
    }

    /**
     * 增强的文本生成方法
     */
    @Override
    public String text(ChatReq req) {
        try {
            // 检查是否需要使用MCP工具
            if (shouldUseMcpTools(req)) {
                return textWithMcpTools(req);
            }

            return super.text(req);
        } catch (Exception e) {
            log.error("MCP增强文本生成失败", e);
            throw new ServiceException("文本生成失败: " + e.getMessage());
        }
    }

    /**
     * 使用MCP工具的文本生成
     */
    private String textWithMcpTools(ChatReq req) {
        try {
            // 设置MCP处理标记
            MCP_PROCESSING.set(true);

            log.info("使用MCP编排进行文本生成: {}", req.getMessage());

            // 1. 生成编排计划（使用系统提示词和模型ID）
            FlexibleMcpOrchestrator.OrchestrationPlan plan = flexibleMcpOrchestrator.orchestrate(
                    req.getMessage(), req.getMcpServiceIds(), req.getPromptText(), req.getModelId()
            );

            if (plan == null || plan.getSteps().isEmpty()) {
                log.warn("未生成有效的编排计划，使用普通文本生成");
                return super.text(req);
            }

            // 2. 创建状态回调（文本模式下不推送状态，传null）
            // 注意：text方法没有emitter，所以不推送实时状态

            // 3. 执行编排计划（传递模型ID）
            OrchestrationExecutionResult executionResult = flexibleMcpOrchestrator.execute(
                    plan, req.getUserId(), req.getConversationId(), null, req.getModelId(), req.getMessage()
            );

            // 4. 构建包含执行结果的请求
            String resultSummary = formatFlexibleOrchestrationResult(executionResult);
            ChatReq enhancedReq = buildEnhancedRequest(req, resultSummary);

            log.info("MCP编排完成，调用基础文本生成服务");
            log.debug("增强请求内容: {}", enhancedReq.getMessage());

            try {
                return super.text(enhancedReq);
            } catch (Exception e) {
                log.error("调用基础文本生成服务失败，直接返回MCP执行结果", e);
                // 如果基础文本生成服务调用失败，直接返回MCP执行结果
                return resultSummary;
            }

        } catch (Exception e) {
            log.error("MCP编排文本生成失败", e);
            return super.text(req);
        } finally {
            // 清除MCP处理标记
            MCP_PROCESSING.remove();
        }
    }
}
