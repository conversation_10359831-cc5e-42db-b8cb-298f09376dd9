/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.service;

import cn.tycoding.langchat.mcp.core.entity.AigcMcpService;
import cn.tycoding.langchat.mcp.core.entity.McpService;
import cn.tycoding.langchat.mcp.core.protocol.McpRequest;
import cn.tycoding.langchat.mcp.core.protocol.McpResponse;
import cn.tycoding.langchat.mcp.core.protocol.McpTool;

import java.util.List;
import java.util.Map;

/**
 * MCP服务管理器接口
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
public interface McpServiceManager {

    /**
     * 注册MCP服务
     */
    void registerService(AigcMcpService service);

    /**
     * 注销MCP服务
     */
    void unregisterService(String serviceName);

    /**
     * 获取服务
     */
    AigcMcpService getService(String serviceName);

    /**
     * 获取所有启用的服务
     */
    List<AigcMcpService> getEnabledServices();

    /**
     * 获取服务列表（按分类）
     */
    List<AigcMcpService> getServicesByCategory(String category);

    /**
     * 检查服务是否可用
     */
    boolean isServiceAvailable(String serviceName);

    /**
     * 获取服务的工具列表
     */
    List<McpTool> getServiceTools(String serviceName);

    /**
     * 调用MCP工具
     */
    McpResponse callTool(String serviceName, String toolName, Map<String, Object> parameters);

    /**
     * 调用MCP工具（带上下文）
     */
    McpResponse callTool(String serviceName, String toolName, Map<String, Object> parameters, 
                        String userId, String conversationId);

    /**
     * 发送MCP请求
     */
    McpResponse sendRequest(String serviceName, McpRequest request);

    /**
     * 健康检查
     */
    boolean healthCheck(String serviceName);

    /**
     * 批量健康检查
     */
    Map<String, Boolean> batchHealthCheck();

    /**
     * 刷新服务配置
     */
    void refreshService(String serviceName);

    /**
     * 刷新所有服务配置
     */
    void refreshAllServices();

    /**
     * 启用服务
     */
    void enableService(String serviceName);

    /**
     * 禁用服务
     */
    void disableService(String serviceName);

    /**
     * 获取服务统计信息
     */
    Map<String, Object> getServiceStats(String serviceName);

    /**
     * 获取所有服务统计信息
     */
    Map<String, Object> getAllServicesStats();
}
