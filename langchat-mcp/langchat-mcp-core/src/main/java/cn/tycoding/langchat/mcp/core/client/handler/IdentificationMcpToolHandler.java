/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.client.handler;

import cn.hutool.core.util.StrUtil;
import cn.tycoding.consts.OrcTemplateType;
import cn.tycoding.langchat.mcp.core.entity.AigcMcpService;
import cn.tycoding.langchat.mcp.core.protocol.McpResponse;
import cn.tycoding.langchat.mcp.core.utils.FileTypeDetector;
import cn.tycoding.service.DocumentRecognitionService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.File;
import java.net.URL;
import java.util.*;

/**
 * 图片识别MCP工具处理器
 * 
 * <AUTHOR>
 * @since 2024/12/20
 */
@Slf4j
@Component
@AllArgsConstructor
public class IdentificationMcpToolHandler extends AbstractMcpToolHandler {

    private final DocumentRecognitionService documentRecognitionService;

    @Override
    protected McpResponse handleSpecificTool(AigcMcpService service, String toolName, 
                                           Map<String, Object> parameters, 
                                           Map<String, Object> serviceParams) {
        
        return handleIdentification(parameters, serviceParams);
    }

    /**
     * 处理文档/图片识别
     */
    private McpResponse handleIdentification(Map<String, Object> parameters, Map<String, Object> serviceParams) {
        logToolCall("builtin_identification", parameters);

        String path = getParameterValue(parameters, "path", String.class, "");
        if (StrUtil.isBlank(path)) {
            return McpResponse.error("文件路径不能为空");
        }

        try {
            // 检查用户是否手动指定了模板类型
            String userSpecifiedType = getParameterValue(parameters, "templateType", String.class, null);

            if (StrUtil.isNotBlank(userSpecifiedType)) {
                // 用户指定了类型，使用统一处理
                return handleSingleTypeRecognition(path, userSpecifiedType);
            } else {
                // 自动检测并分组处理
                return handleMultiTypeRecognition(path);
            }
        } catch (Exception e) {
            logToolResult("builtin_identification", false, e.getMessage());
            return McpResponse.error("识别任务失败: " + e.getMessage());
        }
    }

    /**
     * 处理单一类型识别（用户指定类型）
     */
    private McpResponse handleSingleTypeRecognition(String path, String templateType) {
        List<Object> inputs = convertPathsToInputs(path);

        String recognitionResult = documentRecognitionService.recognizeDocument(templateType, inputs);

        Map<String, Object> result = new HashMap<>();
        result.put("results", recognitionResult);
        result.put("templateType", templateType);
        result.put("fileCount", inputs.size());
        result.put("processingMode", "single_type");
        result.put("timestamp", System.currentTimeMillis());

        logToolResult("builtin_identification", true,
            String.format("成功识别%d个文件，用户指定类型: %s", inputs.size(), templateType));

        return McpResponse.success(result);
    }

    /**
     * 处理多类型识别（自动检测并分组）
     */
    private McpResponse handleMultiTypeRecognition(String path) {
        // 1. 检测文件类型并分组
        List<String> pathList = Arrays.asList(path.split(","));
        FileTypeDetector.FileTypeDetectionResult detectionResult =
            FileTypeDetector.detectMultipleFiles(pathList);

        // 2. 按类型分组文件
        Map<OrcTemplateType, List<String>> groupedFiles = groupFilesByType(detectionResult);

        // 3. 分别处理每种类型的文件
        List<RecognitionResult> recognitionResults = new ArrayList<>();
        int totalFileCount = 0;

        for (Map.Entry<OrcTemplateType, List<String>> entry : groupedFiles.entrySet()) {
            OrcTemplateType templateType = entry.getKey();
            List<String> filePaths = entry.getValue();

            if (!filePaths.isEmpty()) {
                RecognitionResult groupResult = processFileGroup(templateType, filePaths);
                recognitionResults.add(groupResult);
                totalFileCount += filePaths.size();
            }
        }

        // 4. 合并结果
        Map<String, Object> finalResult = mergeRecognitionResults(recognitionResults, totalFileCount, detectionResult);

        logToolResult("builtin_identification", true,
            String.format("成功识别%d个文件，分%d组处理", totalFileCount, recognitionResults.size()));

        return McpResponse.success(finalResult.get("results"));
    }



    /**
     * 将路径字符串转换为输入对象列表
     */
    private List<Object> convertPathsToInputs(String path) {
        List<Object> inputs = new ArrayList<>();
        String[] paths = path.split(",");

        for (String singlePath : paths) {
            singlePath = singlePath.trim();
            if (StrUtil.isBlank(singlePath)) {
                continue;
            }

            try {
                Object input = convertSinglePathToInput(singlePath);
                inputs.add(input);
                log.debug("成功转换路径: {} -> {}", singlePath, input.getClass().getSimpleName());
            } catch (Exception e) {
                log.warn("转换路径失败，跳过: {}", singlePath, e);
            }
        }

        if (inputs.isEmpty()) {
            throw new IllegalArgumentException("没有有效的文件路径");
        }

        return inputs;
    }

    /**
     * 将单个路径转换为输入对象
     */
    private Object convertSinglePathToInput(String path) throws Exception {
        // 判断是URL还是本地文件路径
        if (path.startsWith("http://") || path.startsWith("https://")) {
            // URL路径
            return new URL(path);
        } else if (path.startsWith("file://")) {
            // 文件协议URL
            return new URL(path);
        } else {
            // 本地文件路径
            File file = new File(path);
            if (!file.exists()) {
                throw new IllegalArgumentException("文件不存在: " + path);
            }
            if (!file.canRead()) {
                throw new IllegalArgumentException("文件不可读: " + path);
            }
            return file;
        }
    }

    /**
     * 按文件类型分组
     */
    private Map<OrcTemplateType, List<String>> groupFilesByType(FileTypeDetector.FileTypeDetectionResult detectionResult) {
        Map<OrcTemplateType, List<String>> groupedFiles = new HashMap<>();

        for (FileTypeDetector.FileTypeInfo fileInfo : detectionResult.getFiles()) {
            OrcTemplateType type = fileInfo.getType();
            String filePath = fileInfo.getFilePath();

            groupedFiles.computeIfAbsent(type, k -> new ArrayList<>()).add(filePath);
        }

        log.info("文件分组结果: {}", groupedFiles.entrySet().stream()
            .map(entry -> String.format("%s: %d个文件", entry.getKey(), entry.getValue().size()))
            .reduce((a, b) -> a + ", " + b).orElse("无文件"));

        return groupedFiles;
    }

    /**
     * 处理单个文件组
     */
    private RecognitionResult processFileGroup(OrcTemplateType templateType, List<String> filePaths) {
        try {
            log.info("开始处理文件组: 类型={}, 文件数={}", templateType, filePaths.size());

            // 转换路径为输入对象
            List<Object> inputs = new ArrayList<>();
            List<String> successfulPaths = new ArrayList<>();
            List<String> failedPaths = new ArrayList<>();

            for (String path : filePaths) {
                try {
                    Object input = convertSinglePathToInput(path.trim());
                    inputs.add(input);
                    successfulPaths.add(path);
                } catch (Exception e) {
                    log.warn("转换路径失败，跳过: {}", path, e);
                    failedPaths.add(path);
                }
            }

            if (inputs.isEmpty()) {
                return new RecognitionResult(templateType, "", successfulPaths, failedPaths,
                    "所有文件路径转换失败", false);
            }

            // 调用识别服务
            String recognitionResult = documentRecognitionService.recognizeDocument(templateType.name(), inputs);

            log.info("文件组处理完成: 类型={}, 成功={}个, 失败={}个",
                templateType, successfulPaths.size(), failedPaths.size());

            return new RecognitionResult(templateType, recognitionResult, successfulPaths, failedPaths,
                "识别成功", true);

        } catch (Exception e) {
            log.error("处理文件组失败: 类型={}", templateType, e);
            return new RecognitionResult(templateType, "", filePaths, new ArrayList<>(),
                "识别失败: " + e.getMessage(), false);
        }
    }

    /**
     * 合并识别结果
     */
    private Map<String, Object> mergeRecognitionResults(List<RecognitionResult> recognitionResults,
                                                       int totalFileCount,
                                                       FileTypeDetector.FileTypeDetectionResult detectionResult) {
        Map<String, Object> finalResult = new HashMap<>();

        // 基本信息
        finalResult.put("fileCount", totalFileCount);
        finalResult.put("processingMode", "multi_type_grouped");
        finalResult.put("timestamp", System.currentTimeMillis());

        // 分组处理统计
        Map<String, Object> processingStats = new HashMap<>();
        processingStats.put("totalGroups", recognitionResults.size());
        processingStats.put("imageCount", detectionResult.getImageCount());
        processingStats.put("documentCount", detectionResult.getDocumentCount());
        processingStats.put("hasMixedTypes", detectionResult.hasMixedTypes());
        processingStats.put("processingSuggestion", detectionResult.getProcessingSuggestion());
        finalResult.put("processingStats", processingStats);

        // 分组结果
        List<Map<String, Object>> groupResults = new ArrayList<>();
        StringBuilder combinedResults = new StringBuilder();
        boolean hasAnySuccess = false;
        List<String> allFailedPaths = new ArrayList<>();

        for (RecognitionResult result : recognitionResults) {
            // 构建分组结果信息
            Map<String, Object> groupInfo = new HashMap<>();
            groupInfo.put("templateType", result.getTemplateType().name());
            groupInfo.put("fileCount", result.getSuccessfulPaths().size() + result.getFailedPaths().size());
            groupInfo.put("successCount", result.getSuccessfulPaths().size());
            groupInfo.put("failedCount", result.getFailedPaths().size());
            groupInfo.put("success", result.isSuccess());
            groupInfo.put("message", result.getMessage());
            groupInfo.put("successfulPaths", result.getSuccessfulPaths());
            groupInfo.put("failedPaths", result.getFailedPaths());

            if (result.isSuccess() && StrUtil.isNotBlank(result.getRecognitionResult())) {
                groupInfo.put("results", result.getRecognitionResult());

                // 拼接到总结果中
                if (combinedResults.length() > 0) {
                    combinedResults.append("\n\n--- ").append(result.getTemplateType().name()).append(" 识别结果 ---\n");
                } else {
                    combinedResults.append("--- ").append(result.getTemplateType().name()).append(" 识别结果 ---\n");
                }
                combinedResults.append(result.getRecognitionResult());
                hasAnySuccess = true;
            } else {
                groupInfo.put("error", result.getMessage());
            }

            groupResults.add(groupInfo);
            allFailedPaths.addAll(result.getFailedPaths());
        }

        finalResult.put("groupResults", groupResults);
        finalResult.put("results", combinedResults.toString());
        finalResult.put("overallSuccess", hasAnySuccess);

        // 失败文件汇总
        if (!allFailedPaths.isEmpty()) {
            finalResult.put("failedPaths", allFailedPaths);
            finalResult.put("failedCount", allFailedPaths.size());
        }

        return finalResult;
    }

    /**
     * 识别结果封装类
     */
    private static class RecognitionResult {
        private final OrcTemplateType templateType;
        private final String recognitionResult;
        private final List<String> successfulPaths;
        private final List<String> failedPaths;
        private final String message;
        private final boolean success;

        public RecognitionResult(OrcTemplateType templateType, String recognitionResult,
                               List<String> successfulPaths, List<String> failedPaths,
                               String message, boolean success) {
            this.templateType = templateType;
            this.recognitionResult = recognitionResult;
            this.successfulPaths = new ArrayList<>(successfulPaths);
            this.failedPaths = new ArrayList<>(failedPaths);
            this.message = message;
            this.success = success;
        }

        public OrcTemplateType getTemplateType() { return templateType; }
        public String getRecognitionResult() { return recognitionResult; }
        public List<String> getSuccessfulPaths() { return successfulPaths; }
        public List<String> getFailedPaths() { return failedPaths; }
        public String getMessage() { return message; }
        public boolean isSuccess() { return success; }
    }

}
