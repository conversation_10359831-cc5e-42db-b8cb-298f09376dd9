/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.tycoding.langchat.mcp.core.dto.McpServiceConfig;
import cn.tycoding.langchat.mcp.core.dto.McpServiceParameter;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.regex.Pattern;

/**
 * MCP参数工具类
 * 
 * <AUTHOR>
 * @since 2024/12/19
 */
@Slf4j
public class McpParameterUtils {

    /**
     * 解析参数定义JSON
     */
    public static List<McpServiceParameter> parseParameters(String parametersJson) {
        if (StrUtil.isBlank(parametersJson)) {
            return new ArrayList<>();
        }
        
        try {
            return JSONUtil.toList(parametersJson, McpServiceParameter.class);
        } catch (Exception e) {
            log.error("解析参数定义失败: {}", parametersJson, e);
            return new ArrayList<>();
        }
    }

    /**
     * 验证参数值
     */
    public static List<String> validateParameters(List<McpServiceParameter> parameters, 
                                                 Map<String, Object> parameterValues) {
        List<String> errors = new ArrayList<>();
        
        if (parameters == null || parameters.isEmpty()) {
            return errors;
        }
        
        for (McpServiceParameter parameter : parameters) {
            String paramName = parameter.getName();
            Object value = parameterValues != null ? parameterValues.get(paramName) : null;
            
            // 验证必填参数
            if (Boolean.TRUE.equals(parameter.getRequired())) {
                if (value == null || (value instanceof String && StrUtil.isBlank((String) value))) {
                    errors.add(String.format("参数 '%s' 是必填的", parameter.getDisplayName()));
                    continue;
                }
            }
            
            // 如果值为空且不是必填，跳过验证
            if (value == null) {
                continue;
            }
            
            // 类型验证
            String typeError = validateParameterType(parameter, value);
            if (typeError != null) {
                errors.add(typeError);
                continue;
            }
            
            // 验证规则验证
            String validationError = validateParameterRules(parameter, value);
            if (validationError != null) {
                errors.add(validationError);
            }
        }
        
        return errors;
    }

    /**
     * 验证参数类型
     */
    private static String validateParameterType(McpServiceParameter parameter, Object value) {
        String type = parameter.getType();
        String paramName = parameter.getDisplayName();
        
        switch (type) {
            case "string":
                if (!(value instanceof String)) {
                    return String.format("参数 '%s' 必须是字符串类型", paramName);
                }
                break;
            case "number":
                if (!(value instanceof Number) && !isNumericString(value)) {
                    return String.format("参数 '%s' 必须是数字类型", paramName);
                }
                break;
            case "boolean":
                if (!(value instanceof Boolean) && !isBooleanString(value)) {
                    return String.format("参数 '%s' 必须是布尔类型", paramName);
                }
                break;
            case "array":
                if (!(value instanceof List) && !(value instanceof Object[])) {
                    return String.format("参数 '%s' 必须是数组类型", paramName);
                }
                break;
            case "object":
                if (!(value instanceof Map) && !isJsonString(value)) {
                    return String.format("参数 '%s' 必须是对象类型", paramName);
                }
                break;
        }
        
        return null;
    }

    /**
     * 验证参数规则
     */
    private static String validateParameterRules(McpServiceParameter parameter, Object value) {
        McpServiceParameter.ParameterValidation validation = parameter.getValidation();
        if (validation == null) {
            return null;
        }
        
        String paramName = parameter.getDisplayName();
        String customError = validation.getErrorMessage();
        
        // 数字范围验证
        if (value instanceof Number || isNumericString(value)) {
            double numValue = value instanceof Number ? ((Number) value).doubleValue() : 
                             Double.parseDouble(value.toString());
            
            if (validation.getMin() != null && numValue < validation.getMin().doubleValue()) {
                return customError != null ? customError : 
                       String.format("参数 '%s' 不能小于 %s", paramName, validation.getMin());
            }
            
            if (validation.getMax() != null && numValue > validation.getMax().doubleValue()) {
                return customError != null ? customError : 
                       String.format("参数 '%s' 不能大于 %s", paramName, validation.getMax());
            }
        }
        
        // 字符串长度验证
        if (value instanceof String) {
            String strValue = (String) value;
            
            if (validation.getMinLength() != null && strValue.length() < validation.getMinLength()) {
                return customError != null ? customError : 
                       String.format("参数 '%s' 长度不能少于 %d 个字符", paramName, validation.getMinLength());
            }
            
            if (validation.getMaxLength() != null && strValue.length() > validation.getMaxLength()) {
                return customError != null ? customError : 
                       String.format("参数 '%s' 长度不能超过 %d 个字符", paramName, validation.getMaxLength());
            }
            
            // 正则表达式验证
            if (StrUtil.isNotBlank(validation.getPattern())) {
                try {
                    if (!Pattern.matches(validation.getPattern(), strValue)) {
                        return customError != null ? customError : 
                               String.format("参数 '%s' 格式不正确", paramName);
                    }
                } catch (Exception e) {
                    log.warn("正则表达式验证失败: {}", validation.getPattern(), e);
                }
            }
        }
        
        // 枚举值验证
        if (validation.getEnumValues() != null && !validation.getEnumValues().isEmpty()) {
            if (!validation.getEnumValues().contains(value)) {
                return customError != null ? customError : 
                       String.format("参数 '%s' 的值不在允许的选项中", paramName);
            }
        }
        
        return null;
    }

    /**
     * 合并参数值（用户配置值覆盖默认值）
     */
    public static Map<String, Object> mergeParameterValues(List<McpServiceParameter> parameters, 
                                                          Map<String, Object> userValues) {
        Map<String, Object> mergedValues = new HashMap<>();
        
        if (parameters != null) {
            for (McpServiceParameter parameter : parameters) {
                String paramName = parameter.getName();
                
                // 优先使用用户配置的值
                if (userValues != null && userValues.containsKey(paramName)) {
                    mergedValues.put(paramName, userValues.get(paramName));
                } else if (parameter.getDefaultValue() != null) {
                    // 使用默认值
                    mergedValues.put(paramName, parameter.getDefaultValue());
                }
            }
        }
        
        return mergedValues;
    }

    /**
     * 脱敏参数值
     */
    public static Map<String, Object> maskSensitiveParameters(List<McpServiceParameter> parameters, 
                                                             Map<String, Object> parameterValues) {
        if (parameterValues == null || parameterValues.isEmpty()) {
            return new HashMap<>();
        }
        
        Map<String, Object> maskedValues = new HashMap<>(parameterValues);
        
        if (parameters != null) {
            for (McpServiceParameter parameter : parameters) {
                if (Boolean.TRUE.equals(parameter.getSensitive()) && 
                    maskedValues.containsKey(parameter.getName())) {
                    maskedValues.put(parameter.getName(), "***");
                }
            }
        }
        
        return maskedValues;
    }

    /**
     * 获取参数分组
     */
    public static Map<String, List<McpServiceParameter>> groupParameters(List<McpServiceParameter> parameters) {
        Map<String, List<McpServiceParameter>> groups = new LinkedHashMap<>();
        
        if (parameters != null) {
            for (McpServiceParameter parameter : parameters) {
                String group = StrUtil.isNotBlank(parameter.getGroup()) ? parameter.getGroup() : "default";
                groups.computeIfAbsent(group, k -> new ArrayList<>()).add(parameter);
            }
            
            // 按order排序
            groups.values().forEach(list -> 
                list.sort(Comparator.comparing(p -> p.getOrder() != null ? p.getOrder() : 0)));
        }
        
        return groups;
    }

    /**
     * 检查依赖关系
     */
    public static boolean checkParameterDependencies(McpServiceParameter parameter, 
                                                    Map<String, Object> parameterValues) {
        if (parameter.getDependencies() == null || parameter.getDependencies().isEmpty()) {
            return true;
        }
        
        for (McpServiceParameter.ParameterDependency dependency : parameter.getDependencies()) {
            Object dependentValue = parameterValues.get(dependency.getParameterName());
            
            boolean conditionMet = switch (dependency.getCondition()) {
                case "equals" -> Objects.equals(dependentValue, dependency.getValue());
                case "not_equals" -> !Objects.equals(dependentValue, dependency.getValue());
                case "in" -> dependency.getValue() instanceof List && 
                           ((List<?>) dependency.getValue()).contains(dependentValue);
                case "not_in" -> !(dependency.getValue() instanceof List && 
                                 ((List<?>) dependency.getValue()).contains(dependentValue));
                case "greater_than" -> dependentValue instanceof Number && 
                                     dependency.getValue() instanceof Number &&
                                     ((Number) dependentValue).doubleValue() > 
                                     ((Number) dependency.getValue()).doubleValue();
                case "less_than" -> dependentValue instanceof Number && 
                                   dependency.getValue() instanceof Number &&
                                   ((Number) dependentValue).doubleValue() < 
                                   ((Number) dependency.getValue()).doubleValue();
                default -> true;
            };
            
            if (!conditionMet) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 转换参数值类型
     */
    public static Object convertParameterValue(McpServiceParameter parameter, Object value) {
        if (value == null) {
            return null;
        }
        
        String type = parameter.getType();
        
        try {
            return switch (type) {
                case "string" -> value.toString();
                case "number" -> {
                    if (value instanceof Number) {
                        yield value;
                    } else {
                        yield Double.parseDouble(value.toString());
                    }
                }
                case "boolean" -> {
                    if (value instanceof Boolean) {
                        yield value;
                    } else {
                        yield Boolean.parseBoolean(value.toString());
                    }
                }
                case "array" -> {
                    if (value instanceof List) {
                        yield value;
                    } else if (value instanceof String) {
                        yield JSONUtil.parseArray((String) value);
                    } else {
                        yield Arrays.asList(value);
                    }
                }
                case "object" -> {
                    if (value instanceof Map) {
                        yield value;
                    } else if (value instanceof String) {
                        yield JSONUtil.parseObj((String) value);
                    } else {
                        yield value;
                    }
                }
                default -> value;
            };
        } catch (Exception e) {
            log.warn("参数值类型转换失败: {} -> {}", value, type, e);
            return value;
        }
    }

    // 辅助方法
    private static boolean isNumericString(Object value) {
        if (!(value instanceof String)) {
            return false;
        }
        try {
            Double.parseDouble((String) value);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    private static boolean isBooleanString(Object value) {
        if (!(value instanceof String)) {
            return false;
        }
        String str = ((String) value).toLowerCase();
        return "true".equals(str) || "false".equals(str);
    }

    private static boolean isJsonString(Object value) {
        if (!(value instanceof String)) {
            return false;
        }
        try {
            JSONUtil.parseObj((String) value);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 解析参数值JSON（简化方案专用）
     */
    public static Map<String, Object> parseParameterValues(String parameterValuesJson) {
        if (StrUtil.isBlank(parameterValuesJson)) {
            return new HashMap<>();
        }

        try {
            return JSONUtil.toBean(parameterValuesJson, Map.class);
        } catch (Exception e) {
            log.error("解析参数值失败: {}", parameterValuesJson, e);
            return new HashMap<>();
        }
    }

    /**
     * 获取参数值（指定类型）
     */
    public static <T> T getParameterValue(Map<String, Object> values, String paramName, Class<T> type, T defaultValue) {
        Object value = values.get(paramName);
        if (value == null) {
            return defaultValue;
        }

        try {
            if (type == String.class) {
                return type.cast(value.toString());
            } else if (type == Integer.class) {
                if (value instanceof Number) {
                    return type.cast(((Number) value).intValue());
                } else {
                    return type.cast(Integer.parseInt(value.toString()));
                }
            } else if (type == Long.class) {
                if (value instanceof Number) {
                    return type.cast(((Number) value).longValue());
                } else {
                    return type.cast(Long.parseLong(value.toString()));
                }
            } else if (type == Double.class) {
                if (value instanceof Number) {
                    return type.cast(((Number) value).doubleValue());
                } else {
                    return type.cast(Double.parseDouble(value.toString()));
                }
            } else if (type == Boolean.class) {
                if (value instanceof Boolean) {
                    return type.cast(value);
                } else {
                    return type.cast(Boolean.parseBoolean(value.toString()));
                }
            } else {
                return type.cast(value);
            }
        } catch (Exception e) {
            log.warn("参数值类型转换失败: {} -> {}", paramName, type.getSimpleName(), e);
            return defaultValue;
        }
    }

    /**
     * 检查参数是否已配置（简化方案专用）
     */
    public static boolean isParametersConfigured(List<McpServiceParameter> parameters, Map<String, Object> values) {
        if (parameters == null || parameters.isEmpty()) {
            return true; // 无参数需要配置
        }

        for (McpServiceParameter param : parameters) {
            if (Boolean.TRUE.equals(param.getRequired())) {
                Object value = values.get(param.getName());
                if (value == null || (value instanceof String && StrUtil.isBlank((String) value))) {
                    return false; // 必填参数未配置
                }
            }
        }

        return true;
    }
}
