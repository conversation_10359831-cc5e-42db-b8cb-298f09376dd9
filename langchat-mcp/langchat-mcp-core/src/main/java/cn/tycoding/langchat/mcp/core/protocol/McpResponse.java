/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.protocol;

import lombok.Builder;
import lombok.Data;

/**
 * MCP响应对象
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
@Data
@Builder
public class McpResponse {
    
    /**
     * JSON-RPC版本
     */
    @Builder.Default
    private String jsonrpc = "2.0";
    
    /**
     * 请求ID
     */
    private String id;
    
    /**
     * 响应结果
     */
    private Object result;
    
    /**
     * 错误信息
     */
    private McpError error;
    
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 创建成功响应
     */
    public static McpResponse success(Object result) {
        return McpResponse.builder()
                .success(true)
                .result(result)
                .build();
    }
    
    /**
     * 创建错误响应
     */
    public static McpResponse error(String message) {
        return McpResponse.builder()
                .success(false)
                .error(McpError.builder()
                        .code(-1)
                        .message(message)
                        .build())
                .build();
    }
    
    /**
     * 创建错误响应
     */
    public static McpResponse error(int code, String message) {
        return McpResponse.builder()
                .success(false)
                .error(McpError.builder()
                        .code(code)
                        .message(message)
                        .build())
                .build();
    }

    /**
     * 创建错误响应
     */
    public static McpResponse error(String requestId, int code, String message) {
        return McpResponse.builder()
                .success(false)
                .id(requestId)
                .error(McpError.builder()
                        .code(code)
                        .message(message)
                        .build())
                .build();
    }
    
    @Data
    @Builder
    public static class McpError {
        private int code;
        private String message;
        private Object data;
    }
}
