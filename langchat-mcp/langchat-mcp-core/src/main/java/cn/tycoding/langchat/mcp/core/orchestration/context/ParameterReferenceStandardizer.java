/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.orchestration.context;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 参数引用格式标准化工具
 * 负责将AI生成的各种参数引用格式标准化为统一的解析格式
 * 
 * <AUTHOR>
 * @since 2025/06/24
 */
@Slf4j
@Component
public class ParameterReferenceStandardizer {

    // 参数引用模式
    private static final Pattern REFERENCE_PATTERN = Pattern.compile("\\$\\{([^}]+)\\}");
    
    // 标准化映射规则
    private static final Map<String, String> STANDARDIZATION_RULES = new HashMap<>();
    
    static {
        // 常见的非标准格式映射到标准格式
        STANDARDIZATION_RULES.put("outputPrompt", "result.outputPrompt");
        STANDARDIZATION_RULES.put("optimizedPrompt", "result.optimizedPrompt");
        STANDARDIZATION_RULES.put("optimized_prompt", "result.optimized_prompt");
        STANDARDIZATION_RULES.put("imageUrl", "result.imageUrl");
        STANDARDIZATION_RULES.put("image_url", "result.image_url");
        STANDARDIZATION_RULES.put("url", "result.url");
        STANDARDIZATION_RULES.put("content", "result.content");
        STANDARDIZATION_RULES.put("text", "result.text");
        STANDARDIZATION_RULES.put("data", "result.data");
        STANDARDIZATION_RULES.put("value", "result.value");
        STANDARDIZATION_RULES.put("response", "result.response");
        STANDARDIZATION_RULES.put("output", "result.output");
        STANDARDIZATION_RULES.put("result", "result");
    }

    /**
     * 标准化参数引用格式
     */
    public Object standardizeParameterReferences(Object value) {
        if (value == null) {
            return null;
        }

        if (value instanceof String) {
            return standardizeStringReferences((String) value);
        } else if (value instanceof Map) {
            return standardizeMapReferences((Map<String, Object>) value);
        } else if (value instanceof List) {
            return standardizeListReferences((List<Object>) value);
        }

        return value;
    }

    /**
     * 标准化字符串中的参数引用
     */
    private String standardizeStringReferences(String value) {
        if (StrUtil.isBlank(value) || !value.contains("${")) {
            return value;
        }

        Matcher matcher = REFERENCE_PATTERN.matcher(value);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String reference = matcher.group(1);
            String standardized = standardizeReference(reference);
            matcher.appendReplacement(sb, Matcher.quoteReplacement("${" + standardized + "}"));
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 标准化Map中的参数引用
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> standardizeMapReferences(Map<String, Object> map) {
        Map<String, Object> standardizedMap = new HashMap<>();
        
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            Object standardizedValue = standardizeParameterReferences(entry.getValue());
            standardizedMap.put(entry.getKey(), standardizedValue);
        }
        
        return standardizedMap;
    }

    /**
     * 标准化List中的参数引用
     */
    private List<Object> standardizeListReferences(List<Object> list) {
        List<Object> standardizedList = new ArrayList<>();
        
        for (Object item : list) {
            Object standardizedItem = standardizeParameterReferences(item);
            standardizedList.add(standardizedItem);
        }
        
        return standardizedList;
    }

    /**
     * 标准化单个引用
     */
    private String standardizeReference(String reference) {
        if (StrUtil.isBlank(reference)) {
            return reference;
        }

        String[] parts = reference.split("\\.");
        if (parts.length < 2) {
            // 如果只有一个部分，可能是stepId，保持原样
            return reference;
        }

        String stepId = parts[0];
        String field = parts[1];

        // 检查是否是已知的标准格式
        if (isStandardFormat(reference)) {
            return reference;
        }

        // 检查是否需要标准化
        String standardizedField = STANDARDIZATION_RULES.get(field);
        if (standardizedField != null) {
            // 构建标准化的引用
            StringBuilder standardized = new StringBuilder(stepId).append(".").append(standardizedField);
            
            // 如果原引用有更多层级，保留它们
            if (parts.length > 2) {
                for (int i = 2; i < parts.length; i++) {
                    standardized.append(".").append(parts[i]);
                }
            }
            
            String result = standardized.toString();
            log.debug("标准化参数引用: {} -> {}", reference, result);
            return result;
        }

        // 如果不在标准化规则中，检查是否缺少result前缀
        if (!field.equals("result") && !field.equals("parameters") && !field.equals("context") && 
            !field.equals("duration") && !field.equals("success") &&
            !stepId.equals("global") && !stepId.equals("shared") && !stepId.equals("context")) {
            
            // 可能是直接访问结果字段，添加result前缀
            StringBuilder standardized = new StringBuilder(stepId).append(".result.").append(field);
            
            // 添加剩余的层级
            if (parts.length > 2) {
                for (int i = 2; i < parts.length; i++) {
                    standardized.append(".").append(parts[i]);
                }
            }
            
            String result = standardized.toString();
            log.debug("添加result前缀: {} -> {}", reference, result);
            return result;
        }

        // 保持原样
        return reference;
    }

    /**
     * 检查是否是标准格式
     */
    private boolean isStandardFormat(String reference) {
        String[] parts = reference.split("\\.");
        if (parts.length < 2) {
            return false;
        }

        String stepId = parts[0];
        String field = parts[1];

        // 全局上下文引用
        if ("global".equals(stepId) || "shared".equals(stepId) || "context".equals(stepId)) {
            return true;
        }

        // 步骤引用的标准字段
        return "result".equals(field) || "parameters".equals(field) || "context".equals(field) || 
               "duration".equals(field) || "success".equals(field);
    }

    /**
     * 获取标准化规则
     */
    public Map<String, String> getStandardizationRules() {
        return new HashMap<>(STANDARDIZATION_RULES);
    }

    /**
     * 添加自定义标准化规则
     */
    public void addStandardizationRule(String nonStandardField, String standardField) {
        STANDARDIZATION_RULES.put(nonStandardField, standardField);
        log.info("添加标准化规则: {} -> {}", nonStandardField, standardField);
    }

    /**
     * 移除标准化规则
     */
    public void removeStandardizationRule(String nonStandardField) {
        String removed = STANDARDIZATION_RULES.remove(nonStandardField);
        if (removed != null) {
            log.info("移除标准化规则: {} -> {}", nonStandardField, removed);
        }
    }

    /**
     * 验证参数引用格式
     */
    public boolean isValidReference(String reference) {
        if (StrUtil.isBlank(reference)) {
            return false;
        }

        String[] parts = reference.split("\\.");
        if (parts.length < 2) {
            return false;
        }

        String stepId = parts[0];
        String field = parts[1];

        // 检查stepId是否有效（不能包含特殊字符）
        if (!stepId.matches("[a-zA-Z0-9_-]+")) {
            return false;
        }

        // 检查是否是有效的引用格式
        return isStandardFormat(reference) || STANDARDIZATION_RULES.containsKey(field);
    }

    /**
     * 获取参数引用格式说明
     */
    public String getReferenceFormatGuide() {
        StringBuilder guide = new StringBuilder();
        guide.append("参数引用格式说明:\n\n");
        
        guide.append("标准格式:\n");
        guide.append("- ${stepId.result} - 获取步骤的完整结果\n");
        guide.append("- ${stepId.result.field} - 获取结果中的特定字段\n");
        guide.append("- ${stepId.parameters.paramName} - 获取步骤参数\n");
        guide.append("- ${stepId.context.contextKey} - 获取步骤上下文\n");
        guide.append("- ${stepId.duration} - 获取步骤执行时长\n");
        guide.append("- ${stepId.success} - 获取步骤执行状态\n");
        guide.append("- ${global.variableName} - 获取全局变量\n");
        guide.append("- ${shared.dataKey} - 获取共享数据\n");
        guide.append("- ${context.userId} - 获取上下文信息\n\n");
        
        guide.append("自动标准化的简化格式:\n");
        for (Map.Entry<String, String> rule : STANDARDIZATION_RULES.entrySet()) {
            guide.append("- ${stepId.").append(rule.getKey()).append("} -> ${stepId.").append(rule.getValue()).append("}\n");
        }
        
        return guide.toString();
    }
}
