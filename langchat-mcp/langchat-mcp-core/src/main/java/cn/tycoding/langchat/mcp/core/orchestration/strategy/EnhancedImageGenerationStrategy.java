/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.orchestration.strategy;

import cn.tycoding.langchat.mcp.core.entity.AigcMcpService;
import cn.tycoding.langchat.mcp.core.orchestration.impl.FlexibleMcpOrchestrator;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


import java.util.List;
import java.util.UUID;

/**
 * 增强图片生成编排策略
 * 在图片生成前先用AI优化提示词，提升生成效果
 * 
 * <AUTHOR>
 * @since 2025/06/15
 */
@Slf4j
@Component
public class EnhancedImageGenerationStrategy implements FlexibleMcpOrchestrator.OrchestrationStrategy {

    private final FlexibleMcpOrchestrator orchestrator;

    public EnhancedImageGenerationStrategy(FlexibleMcpOrchestrator orchestrator) {
        this.orchestrator = orchestrator;
    }

    @PostConstruct
    public void init() {
        orchestrator.registerStrategy(this);
        log.info("增强图片生成编排策略已注册");
    }

    @Override
    public boolean canHandle(String userInput, List<AigcMcpService> availableServices) {
        // 检查是否有图片生成服务
        boolean hasImageService = availableServices.stream()
                .anyMatch(service -> "image".equalsIgnoreCase(service.getCategory()) || 
                                   service.getName().toLowerCase().contains("image") ||
                                   service.getName().toLowerCase().contains("wanx"));

        if (!hasImageService) {
            return false;
        }

        // 检查用户输入是否包含图片生成意图
        String lowerInput = userInput.toLowerCase();
        String[] imageKeywords = {
            "画", "生成图片", "生成图像", "画一张", "画个", "制作图片", 
            "创建图片", "图片生成", "画出", "绘制", "设计图片", "生成一张"
        };

        for (String keyword : imageKeywords) {
            if (lowerInput.contains(keyword)) {
                return true;
            }
        }

        return false;
    }

    @Override
    public FlexibleMcpOrchestrator.OrchestrationPlan createPlan(String userInput, List<AigcMcpService> availableServices) {
        FlexibleMcpOrchestrator.OrchestrationPlan plan = new FlexibleMcpOrchestrator.OrchestrationPlan();
        
        plan.setPlanId(generatePlanId());
        plan.setDescription("AI增强图片生成任务");
        plan.setStrategyName(getStrategyName());
        
        // 添加元数据
        plan.putMetadata("userInput", userInput);
        plan.putMetadata("estimatedDuration", "45-90秒");
        plan.putMetadata("complexity", "enhanced");
        plan.putMetadata("useAiOptimization", true);

        // 查找图片生成服务
        AigcMcpService imageService = findImageGenerationService(availableServices);
        if (imageService == null) {
            log.warn("未找到可用的图片生成服务");
            return plan;
        }

        // 提取原始图片描述
        String originalPrompt = extractImagePrompt(userInput);

        // 步骤1: AI提示词优化
        FlexibleMcpOrchestrator.ExecutionStep promptOptimizationStep = new FlexibleMcpOrchestrator.ExecutionStep();
        promptOptimizationStep.setStepId("optimize_prompt");
        promptOptimizationStep.setServiceName("ai-prompt-optimizer");
        promptOptimizationStep.setToolName("optimize_image_prompt");
        promptOptimizationStep.setDescription("AI优化图片生成提示词");
        promptOptimizationStep.putParameter("original_prompt", originalPrompt);
        promptOptimizationStep.putParameter("optimization_type", "image_generation");
        
        plan.addStep(promptOptimizationStep);

        // 步骤2: 图片生成（使用优化后的提示词）
        FlexibleMcpOrchestrator.ExecutionStep imageStep = new FlexibleMcpOrchestrator.ExecutionStep();
        imageStep.setStepId("generate_image");
        imageStep.setServiceName(imageService.getName());
        imageStep.setToolName("text_to_image");
        imageStep.setDescription("使用优化提示词生成高质量图片");
        
        // 使用优化后的提示词
        imageStep.putParameter("prompt", "${optimize_prompt.result.optimized_prompt}");
        imageStep.putParameter("style", "auto");
        imageStep.putParameter("size", "1024*1024");
        
        // 设置依赖关系
        imageStep.addDependency("optimize_prompt");
        
        plan.addStep(imageStep);

        log.info("创建AI增强图片生成编排计划，原始提示词: {}", originalPrompt);
        return plan;
    }

    @Override
    public String getStrategyName() {
        return "EnhancedImageGenerationStrategy";
    }

    @Override
    public int getPriority() {
        return 85; // 比普通图片生成策略优先级更高
    }

    /**
     * 查找图片生成服务
     */
    private AigcMcpService findImageGenerationService(List<AigcMcpService> services) {
        return services.stream()
                .filter(service -> "image".equalsIgnoreCase(service.getCategory()) || 
                                 service.getName().toLowerCase().contains("image") ||
                                 service.getName().toLowerCase().contains("wanx"))
                .findFirst()
                .orElse(null);
    }

    /**
     * 提取图片描述
     */
    private String extractImagePrompt(String userInput) {
        // 移除常见的指令词，提取核心描述
        String prompt = userInput;
        
        String[] removeWords = {
            "帮我", "请", "画一张", "画个", "生成", "制作", "创建", "图片", "图像", 
            "的图片", "的图像", "生成一张", "画出", "绘制", "设计"
        };
        
        for (String word : removeWords) {
            prompt = prompt.replace(word, "");
        }
        
        prompt = prompt.trim();
        
        // 如果提取后为空，使用原始输入
        if (prompt.isEmpty()) {
            prompt = userInput;
        }
        
        return prompt;
    }

    /**
     * 生成计划ID
     */
    private String generatePlanId() {
        return "enhanced_img_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }
}
