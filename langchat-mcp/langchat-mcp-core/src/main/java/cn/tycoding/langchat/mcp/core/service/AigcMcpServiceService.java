/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.service;

import cn.tycoding.langchat.mcp.core.entity.AigcMcpService;
import cn.tycoding.langchat.common.core.utils.QueryPage;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * MCP服务管理Service
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
public interface AigcMcpServiceService extends IService<AigcMcpService> {

    /**
     * 分页查询
     */
    IPage<AigcMcpService> list(AigcMcpService aigcMcpService, QueryPage queryPage);

    /**
     * 根据ID查询
     */
    AigcMcpService findById(String id);

    /**
     * 新增
     */
    void add(AigcMcpService aigcMcpService);

    /**
     * 修改
     */
    void update(AigcMcpService aigcMcpService);

    /**
     * 删除
     */
    void delete(List<String> ids);

    /**
     * 启用/禁用服务
     */
    void toggleEnabled(String id, Boolean enabled);

    /**
     * 测试服务连接
     */
    boolean testConnection(String id);

    /**
     * 同步服务到MCP客户端
     */
    void syncToMcpClient(String id);

    /**
     * 批量同步服务
     */
    void syncAllToMcpClient();

    /**
     * 获取服务健康状态
     */
    String getHealthStatus(String id);

    /**
     * 刷新服务工具列表
     */
    void refreshTools(String id);

    /**
     * 根据名称查询服务
     */
    AigcMcpService findByName(String name);

    /**
     * 获取启用的服务列表
     */
    List<AigcMcpService> getEnabledServices();

    /**
     * 根据分类获取服务列表
     */
    List<AigcMcpService> getServicesByCategory(String category);
}
