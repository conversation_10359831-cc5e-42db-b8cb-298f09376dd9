/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.protocol;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * MCP (Model Context Protocol) 客户端
 * 用于与MCP服务进行通信，实现工具调用和服务发现
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
@Slf4j
@Component
public class McpClient {

    private final Map<String, McpService> registeredServices = new ConcurrentHashMap<>();

    @Autowired
    private ApplicationContext applicationContext;
    
    /**
     * 注册MCP服务
     */
    public void registerService(McpService service) {
        registeredServices.put(service.getName(), service);
        log.info("MCP服务已注册: {}", service.getName());
    }
    
    /**
     * 获取所有已注册的服务
     */
    public Map<String, McpService> getRegisteredServices() {
        return registeredServices;
    }
    
    /**
     * 调用MCP工具
     */
    public McpResponse callTool(String serviceName, String toolName, Map<String, Object> parameters) {
        McpService service = registeredServices.get(serviceName);
        if (service == null) {
            return McpResponse.error("服务未找到: " + serviceName);
        }

        try {
            // 检查是否为内置服务
            if (serviceName.startsWith("builtin-")) {
                try {
                    var builtinToolHandler = applicationContext.getBean("builtinMcpToolHandler");
                    // 使用反射调用，避免循环依赖
                    return (McpResponse) builtinToolHandler.getClass()
                            .getMethod("handleToolCall", String.class, String.class, Map.class)
                            .invoke(builtinToolHandler, serviceName, toolName, parameters);
                } catch (Exception e) {
                    log.warn("内置工具处理器不可用，使用默认HTTP调用", e);
                    return callExternalMcpService(service, toolName, parameters);
                }
            }

            // 调用外部MCP服务
            return callExternalMcpService(service, toolName, parameters);

        } catch (Exception e) {
            log.error("MCP工具调用失败", e);
            return McpResponse.error("调用异常: " + e.getMessage());
        }
    }

    /**
     * 调用外部MCP服务
     */
    private McpResponse callExternalMcpService(McpService service, String toolName, Map<String, Object> parameters) {
        try {
            // 优先使用专门的外部工具处理器
            if (isWellKnownExternalService(service.getName())) {
                try {
                    var externalToolHandler = applicationContext.getBean("externalMcpToolHandler");
                    // 使用反射调用，避免循环依赖
                    return (McpResponse) externalToolHandler.getClass()
                            .getMethod("handleExternalToolCall", McpService.class, String.class, Map.class)
                            .invoke(externalToolHandler, service, toolName, parameters);
                } catch (Exception e) {
                    log.warn("外部工具处理器不可用，使用默认HTTP调用", e);
                }
            }

            // 根据服务类型选择不同的调用方式
            return switch (service.getType()) {
                case HTTP -> callHttpMcpService(service, toolName, parameters);
                case WEBSOCKET -> callWebSocketMcpService(service, toolName, parameters);
                case GRPC -> callGrpcMcpService(service, toolName, parameters);
                default -> McpResponse.error("不支持的服务类型: " + service.getType());
            };
        } catch (Exception e) {
            log.error("调用外部MCP服务失败: {}", service.getName(), e);
            return McpResponse.error("外部服务调用失败: " + e.getMessage());
        }
    }

    /**
     * 检查是否为知名外部服务
     */
    private boolean isWellKnownExternalService(String serviceName) {
        return serviceName.equals("wanx-image-generation") ||
               serviceName.equals("sequential-thinking") ||
               serviceName.equals("github-mcp") ||
               serviceName.equals("brave-search");
    }

    /**
     * 调用HTTP类型的MCP服务
     */
    private McpResponse callHttpMcpService(McpService service, String toolName, Map<String, Object> parameters) {
        // 构建标准MCP请求
        McpRequest request = McpRequest.create(String.valueOf(System.currentTimeMillis()), "tools/call", Map.of(
                "name", toolName,
                "arguments", parameters
        ));

        HttpRequest httpRequest = HttpRequest.post(service.getEndpoint())
                .header("Content-Type", "application/json")
                .timeout(30000);

        // 添加认证信息
        if (service.getAuthInfo() != null) {
            addAuthHeaders(httpRequest, service.getAuthInfo());
        }

        HttpResponse response = httpRequest.body(JSONUtil.toJsonStr(request)).execute();

        if (response.isOk()) {
            JSONObject result = JSONUtil.parseObj(response.body());
            if (result.containsKey("error")) {
                JSONObject error = result.getJSONObject("error");
                return McpResponse.error(error.getInt("code"), error.getStr("message"));
            }
            return McpResponse.success(result.get("result"));
        } else {
            return McpResponse.error("HTTP调用失败: " + response.getStatus() + " " + response.body());
        }
    }

    /**
     * 调用WebSocket类型的MCP服务（暂时返回错误，后续可扩展）
     */
    private McpResponse callWebSocketMcpService(McpService service, String toolName, Map<String, Object> parameters) {
        // TODO: 实现WebSocket MCP调用
        return McpResponse.error("WebSocket MCP服务调用暂未实现");
    }

    /**
     * 调用gRPC类型的MCP服务（暂时返回错误，后续可扩展）
     */
    private McpResponse callGrpcMcpService(McpService service, String toolName, Map<String, Object> parameters) {
        // TODO: 实现gRPC MCP调用
        return McpResponse.error("gRPC MCP服务调用暂未实现");
    }

    /**
     * 添加认证头
     */
    private void addAuthHeaders(HttpRequest request, McpService.AuthInfo authInfo) {
        switch (authInfo.getType().toLowerCase()) {
            case "bearer":
                request.header("Authorization", "Bearer " + authInfo.getToken());
                break;
            case "basic":
                String credentials = authInfo.getUsername() + ":" + authInfo.getPassword();
                String encoded = java.util.Base64.getEncoder().encodeToString(credentials.getBytes());
                request.header("Authorization", "Basic " + encoded);
                break;
            case "api_key":
                request.header("X-API-Key", authInfo.getApiKey());
                break;
            default:
                log.warn("未知的认证类型: {}", authInfo.getType());
        }
    }
    
    /**
     * 获取服务的可用工具列表
     */
    public List<McpTool> getAvailableTools(String serviceName) {
        McpService service = registeredServices.get(serviceName);
        if (service == null) {
            return List.of();
        }
        
        try {
            McpRequest request = McpRequest.create(String.valueOf(System.currentTimeMillis()), "tools/list", Map.of());
            
            HttpResponse response = HttpRequest.post(service.getEndpoint() + "/mcp")
                    .header("Content-Type", "application/json")
                    .body(JSONUtil.toJsonStr(request))
                    .timeout(10000)
                    .execute();
            
            if (response.isOk()) {
                JSONObject result = JSONUtil.parseObj(response.body());
                return JSONUtil.toList(result.getJSONArray("tools"), McpTool.class);
            }
            
        } catch (Exception e) {
            log.error("获取工具列表失败", e);
        }
        
        return List.of();
    }
    
    /**
     * 服务健康检查
     */
    public boolean isServiceHealthy(String serviceName) {
        McpService service = registeredServices.get(serviceName);
        if (service == null) {
            return false;
        }
        
        try {
            HttpResponse response = HttpRequest.get(service.getEndpoint())
                    .timeout(5000)
                    .execute();
            return response.isOk();
        } catch (Exception e) {
            log.warn("服务健康检查失败: {}", serviceName);
            return false;
        }
    }
    
    /**
     * 自动发现并注册MCP服务
     */
    public void discoverServices(List<String> serviceUrls) {
        for (String url : serviceUrls) {
            try {
                HttpResponse response = HttpRequest.get(url + "/mcp/info")
                        .timeout(5000)
                        .execute();
                
                if (response.isOk()) {
                    JSONObject info = JSONUtil.parseObj(response.body());
                    McpService service = McpService.builder()
                            .name(info.getStr("name"))
                            .endpoint(url)
                            .description(info.getStr("description"))
                            .version(info.getStr("version"))
                            .build();
                    
                    registerService(service);
                }
            } catch (Exception e) {
                log.warn("服务发现失败: {}", url, e);
            }
        }
    }
}
