/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.tycoding.langchat.mcp.core.entity.AigcMcpService;
import cn.tycoding.langchat.mcp.core.mapper.AigcMcpServiceMapper;
import cn.tycoding.langchat.mcp.core.protocol.McpClient;
import cn.tycoding.langchat.mcp.core.protocol.McpService;
import cn.tycoding.langchat.mcp.core.protocol.McpTool;
import cn.tycoding.langchat.mcp.core.service.AigcMcpServiceService;
import cn.tycoding.langchat.common.core.utils.MybatisUtil;
import cn.tycoding.langchat.common.core.utils.QueryPage;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * MCP服务管理Service实现
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AigcMcpServiceServiceImpl extends ServiceImpl<AigcMcpServiceMapper, AigcMcpService> implements AigcMcpServiceService {

    private final McpClient mcpClient;

    @Override
    public IPage<AigcMcpService> list(AigcMcpService aigcMcpService, QueryPage queryPage) {

        
        IPage<AigcMcpService> pageObj = MybatisUtil.wrap(aigcMcpService, queryPage);
        LambdaQueryWrapper<AigcMcpService> queryWrapper = Wrappers.<AigcMcpService>lambdaQuery()
                .like(StrUtil.isNotBlank(aigcMcpService.getName()), AigcMcpService::getName, aigcMcpService.getName())
                .like(StrUtil.isNotBlank(aigcMcpService.getDisplayName()), AigcMcpService::getDisplayName, aigcMcpService.getDisplayName())
                .eq(StrUtil.isNotBlank(aigcMcpService.getCategory()), AigcMcpService::getCategory, aigcMcpService.getCategory())
                .eq(StrUtil.isNotBlank(aigcMcpService.getStatus()), AigcMcpService::getStatus, aigcMcpService.getStatus())
                .eq(StrUtil.isNotBlank(aigcMcpService.getType()), AigcMcpService::getType, aigcMcpService.getType())
                .eq(aigcMcpService.getEnabled() != null, AigcMcpService::getEnabled, aigcMcpService.getEnabled())
                .orderByDesc(AigcMcpService::getCreateTime);
        return baseMapper.selectPage(pageObj, queryWrapper);
    }

    @Override
    public AigcMcpService findById(String id) {
        return baseMapper.selectById(id);
    }

    @Override
    @Transactional
    public void add(AigcMcpService aigcMcpService) {
        // 检查服务名称是否重复
        if (findByName(aigcMcpService.getName()) != null) {
            throw new RuntimeException("服务名称已存在");
        }
        
        aigcMcpService.setCreateTime(new Date());
        aigcMcpService.setUpdateTime(new Date());
        
        // 设置默认值
        if (aigcMcpService.getEnabled() == null) {
            aigcMcpService.setEnabled(true);
        }
        if (aigcMcpService.getTimeout() == null) {
            aigcMcpService.setTimeout(30);
        }
        if (aigcMcpService.getMaxRetries() == null) {
            aigcMcpService.setMaxRetries(3);
        }
        if (aigcMcpService.getPriority() == null) {
            aigcMcpService.setPriority(5);
        }
        if (StrUtil.isBlank(aigcMcpService.getStatus())) {
            aigcMcpService.setStatus("UNKNOWN");
        }
        
        baseMapper.insert(aigcMcpService);
        
        // 如果启用，同步到MCP客户端
        if (aigcMcpService.getEnabled()) {
            syncToMcpClient(aigcMcpService.getId());
        }
    }

    @Override
    @Transactional
    public void update(AigcMcpService aigcMcpService) {
        AigcMcpService existing = findById(aigcMcpService.getId());
        if (existing == null) {
            throw new RuntimeException("服务不存在");
        }
        
        // 检查服务名称是否重复（排除自己）
        AigcMcpService nameCheck = findByName(aigcMcpService.getName());
        if (nameCheck != null && !nameCheck.getId().equals(aigcMcpService.getId())) {
            throw new RuntimeException("服务名称已存在");
        }
        
        aigcMcpService.setUpdateTime(new Date());
        baseMapper.updateById(aigcMcpService);
        
        // 重新同步到MCP客户端
        if (aigcMcpService.getEnabled()) {
            syncToMcpClient(aigcMcpService.getId());
        }
    }

    @Override
    @Transactional
    public void delete(List<String> ids) {
        for (String id : ids) {
            AigcMcpService service = findById(id);
            if (service != null) {
                // 从MCP客户端移除
                try {
                    // TODO: 实现从McpClient中移除服务的方法
                    log.info("从MCP客户端移除服务: {}", service.getName());
                } catch (Exception e) {
                    log.error("移除MCP服务失败", e);
                }
            }
        }
        baseMapper.deleteBatchIds(ids);
    }

    @Override
    @Transactional
    public void toggleEnabled(String id, Boolean enabled) {
        AigcMcpService service = findById(id);
        if (service == null) {
            throw new RuntimeException("服务不存在");
        }
        
        service.setEnabled(enabled);
        service.setUpdateTime(new Date());
        baseMapper.updateById(service);
        
        if (enabled) {
            syncToMcpClient(id);
        } else {
            // TODO: 从MCP客户端移除服务
            log.info("禁用MCP服务: {}", service.getName());
        }
    }

    @Override
    public boolean testConnection(String id) {
        AigcMcpService service = findById(id);
        if (service == null) {
            return false;
        }
        
        try {
            // 使用MCP客户端测试连接
            boolean healthy = mcpClient.isServiceHealthy(service.getName());
            
            // 更新健康检查时间和状态
            service.setLastHealthCheck(new Date());
            service.setStatus(healthy ? "ACTIVE" : "ERROR");
            baseMapper.updateById(service);
            
            return healthy;
        } catch (Exception e) {
            log.error("测试MCP服务连接失败: {}", service.getName(), e);
            service.setLastHealthCheck(new Date());
            service.setStatus("ERROR");
            baseMapper.updateById(service);
            return false;
        }
    }

    @Override
    public void syncToMcpClient(String id) {
        AigcMcpService service = findById(id);
        if (service == null || !service.getEnabled()) {
            return;
        }
        
        try {
            // 转换为MCP服务对象
            McpService mcpService = convertToMcpService(service);
            
            // 注册到MCP客户端
            mcpClient.registerService(mcpService);
            
            // 更新状态
            service.setStatus("ACTIVE");
            service.setUpdateTime(new Date());
            baseMapper.updateById(service);
            
            log.info("MCP服务同步成功: {}", service.getName());
        } catch (Exception e) {
            log.error("MCP服务同步失败: {}", service.getName(), e);
            service.setStatus("ERROR");
            service.setUpdateTime(new Date());
            baseMapper.updateById(service);
            throw new RuntimeException("服务同步失败: " + e.getMessage());
        }
    }

    @Override
    public void syncAllToMcpClient() {
        List<AigcMcpService> enabledServices = getEnabledServices();
        for (AigcMcpService service : enabledServices) {
            try {
                syncToMcpClient(service.getId());
            } catch (Exception e) {
                log.error("同步MCP服务失败: {}", service.getName(), e);
            }
        }
    }

    @Override
    public String getHealthStatus(String id) {
        AigcMcpService service = findById(id);
        if (service == null) {
            return "NOT_FOUND";
        }
        
        if (!service.getEnabled()) {
            return "DISABLED";
        }
        
        try {
            boolean healthy = mcpClient.isServiceHealthy(service.getName());
            return healthy ? "HEALTHY" : "UNHEALTHY";
        } catch (Exception e) {
            return "ERROR";
        }
    }

    @Override
    public void refreshTools(String id) {
        AigcMcpService service = findById(id);
        if (service == null || !service.getEnabled()) {
            return;
        }
        
        try {
            // 获取服务的工具列表
            List<McpTool> tools = mcpClient.getAvailableTools(service.getName());
            
            // 更新工具列表
            service.setTools(JSONUtil.toJsonStr(tools));
            service.setUpdateTime(new Date());
            baseMapper.updateById(service);
            
            log.info("刷新MCP服务工具列表成功: {}", service.getName());
        } catch (Exception e) {
            log.error("刷新MCP服务工具列表失败: {}", service.getName(), e);
            throw new RuntimeException("刷新工具列表失败: " + e.getMessage());
        }
    }

    @Override
    public AigcMcpService findByName(String name) {
        return baseMapper.selectOne(Wrappers.<AigcMcpService>lambdaQuery()
                .eq(AigcMcpService::getName, name));
    }

    @Override
    public List<AigcMcpService> getEnabledServices() {
        return baseMapper.selectList(Wrappers.<AigcMcpService>lambdaQuery()
                .eq(AigcMcpService::getEnabled, true)
                .orderByAsc(AigcMcpService::getPriority));
    }

    @Override
    public List<AigcMcpService> getServicesByCategory(String category) {
        return baseMapper.selectList(Wrappers.<AigcMcpService>lambdaQuery()
                .eq(AigcMcpService::getCategory, category)
                .eq(AigcMcpService::getEnabled, true)
                .orderByAsc(AigcMcpService::getPriority));
    }

    /**
     * 转换为MCP服务对象
     */
    private McpService convertToMcpService(AigcMcpService service) {
        McpService.McpServiceBuilder builder = McpService.builder()
                .name(service.getName())
                .endpoint(service.getEndpoint())
                .description(service.getDescription())
                .version(service.getVersion())
                .type(McpService.ServiceType.valueOf(service.getType()))
                .status(McpService.ServiceStatus.valueOf(service.getStatus()));
        
        // 设置认证信息
        if (StrUtil.isNotBlank(service.getAuthConfig())) {
            try {
                McpService.AuthInfo authInfo = JSONUtil.toBean(service.getAuthConfig(), McpService.AuthInfo.class);
                builder.authInfo(authInfo);
            } catch (Exception e) {
                log.warn("解析认证配置失败: {}", service.getName(), e);
            }
        }
        
        // 设置工具列表
        if (StrUtil.isNotBlank(service.getTools())) {
            try {
                List<McpTool> tools = JSONUtil.toList(service.getTools(), McpTool.class);
                builder.tools(tools);
            } catch (Exception e) {
                log.warn("解析工具列表失败: {}", service.getName(), e);
            }
        }
        
        return builder.build();
    }
}
