/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.agent;

import cn.tycoding.langchat.mcp.core.protocol.McpTool;
import lombok.Builder;
import lombok.Data;

/**
 * 推荐工具
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
@Data
@Builder
public class RecommendedTool {
    
    /**
     * 服务名称
     */
    private String serviceName;
    
    /**
     * 工具信息
     */
    private McpTool tool;
    
    /**
     * 相关性分数
     */
    private double relevanceScore;
    
    /**
     * 推荐理由
     */
    private String reason;
    
    /**
     * 使用示例
     */
    private String example;
    
    /**
     * 预估执行时间（秒）
     */
    private int estimatedTime;
    
    /**
     * 是否需要用户确认
     */
    private boolean requiresConfirmation;
}
