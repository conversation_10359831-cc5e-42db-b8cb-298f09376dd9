/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * MCP服务调用日志实体
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
@Data
@Accessors(chain = true)
@TableName("aigc_mcp_service_log")
public class AigcMcpServiceLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 服务ID
     */
    private String serviceId;

    /**
     * 服务名称
     */
    private String serviceName;

    /**
     * 工具名称
     */
    private String toolName;

    /**
     * 请求参数JSON
     */
    private String requestParams;

    /**
     * 响应数据JSON
     */
    private String responseData;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 执行是否成功
     */
    private Boolean success;

    /**
     * 执行时间（毫秒）
     */
    private Long executionTime;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 对话ID
     */
    private String conversationId;

    /**
     * 客户端IP
     */
    private String clientIp;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 调用时间
     */
    private Date createTime;

    /**
     * 备注
     */
    private String remark;
}
