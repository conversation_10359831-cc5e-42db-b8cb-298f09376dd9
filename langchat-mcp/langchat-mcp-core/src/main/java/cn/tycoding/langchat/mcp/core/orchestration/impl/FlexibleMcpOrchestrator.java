/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.orchestration.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.tycoding.langchat.mcp.core.client.McpToolRouter;
import cn.tycoding.langchat.mcp.core.constants.McpServiceConstants;
import cn.tycoding.langchat.mcp.core.entity.AigcMcpService;
import cn.tycoding.langchat.mcp.core.orchestration.AiOrchestrationPlanner;
import cn.tycoding.langchat.mcp.core.orchestration.StatusCallback;
import cn.tycoding.langchat.mcp.core.orchestration.context.AdvancedParameterResolver;
import cn.tycoding.langchat.mcp.core.orchestration.context.McpExecutionContextManager;
import cn.tycoding.langchat.mcp.core.orchestration.context.ParameterReferenceStandardizer;
import cn.tycoding.langchat.mcp.core.orchestration.dto.OrchestrationExecutionResult;
import cn.tycoding.langchat.mcp.core.orchestration.dto.StepExecutionResult;
import cn.tycoding.langchat.mcp.core.protocol.McpResponse;
import cn.tycoding.langchat.mcp.core.service.AigcMcpServiceService;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 灵活的MCP编排器
 * 基于配置驱动的智能服务编排，支持动态策略和可扩展规则
 *
 * <AUTHOR>
 * @since 2024/12/18
 */
@Slf4j
@Service
public class FlexibleMcpOrchestrator {

    private final AigcMcpServiceService mcpServiceService;
    private final McpToolRouter mcpToolRouter;
    private final AiOrchestrationPlanner aiPlanner;

    @Autowired
    private McpExecutionContextManager contextManager;

    @Autowired
    private AdvancedParameterResolver parameterResolver;

    @Autowired
    private ParameterReferenceStandardizer referenceStandardizer;

    public FlexibleMcpOrchestrator(AigcMcpServiceService mcpServiceService,
                                   McpToolRouter mcpToolRouter,
                                   AiOrchestrationPlanner aiPlanner) {
        this.mcpServiceService = mcpServiceService;
        this.mcpToolRouter = mcpToolRouter;
        this.aiPlanner = aiPlanner;
    }

    /**
     * 编排策略接口
     */
    public interface OrchestrationStrategy {
        boolean canHandle(String userInput, List<AigcMcpService> availableServices);
        OrchestrationPlan createPlan(String userInput, List<AigcMcpService> availableServices);
        String getStrategyName();
        // 优先级，数字越大优先级越高
        int getPriority();
    }

    /**
     * 编排计划
     */
    @Setter
    @Getter
    public static class OrchestrationPlan {
        // Getters and Setters
        private String planId;
        private String description;
        private String strategyName;
        private List<ExecutionStep> steps;
        private Map<String, Object> metadata;

        public OrchestrationPlan() {
            this.steps = new ArrayList<>();
            this.metadata = new HashMap<>();
        }

        public void addStep(ExecutionStep step) { this.steps.add(step); }
        public void putMetadata(String key, Object value) { this.metadata.put(key, value); }
    }

    /**
     * 执行步骤
     */
    @Setter
    @Getter
    public static class ExecutionStep {

        private String stepId;
        private String serviceName;
        private String toolName;
        private Map<String, Object> parameters;
        private String description;
        private List<String> dependencies;
        private Map<String, Object> conditions; // 执行条件
        private Map<String, Object> transformers; // 结果转换器
        private McpResponse result;
        private boolean completed;
        private Long startTime; // 执行开始时间
        private Long endTime; // 执行结束时间
        private Map<String, Object> executionContext; // 执行上下文

        public ExecutionStep() {
            this.parameters = new HashMap<>();
            this.dependencies = new ArrayList<>();
            this.conditions = new HashMap<>();
            this.transformers = new HashMap<>();
            this.executionContext = new HashMap<>();
        }

        public void addDependency(String stepId) { this.dependencies.add(stepId); }
        public void putParameter(String key, Object value) { this.parameters.put(key, value); }
        public void putCondition(String key, Object value) { this.conditions.put(key, value); }
        public void putExecutionContext(String key, Object value) { this.executionContext.put(key, value); }

        /**
         * 标记步骤开始执行
         */
        public void markStarted() {
            this.startTime = System.currentTimeMillis();
        }

        /**
         * 标记步骤执行完成
         */
        public void markCompleted() {
            this.endTime = System.currentTimeMillis();
        }

        /**
         * 获取执行时长
         */
        public Long getDuration() {
            if (startTime != null && endTime != null) {
                return endTime - startTime;
            }
            return null;
        }
    }

    // 注册的策略列表
    private final List<OrchestrationStrategy> strategies = new ArrayList<>();

    /**
     * 全局执行上下文
     */
    @Setter
    @Getter
    public static class GlobalExecutionContext {
        private String planId;
        private String userId;
        private String conversationId;
        private String userInput;
        private String modelId;
        private Map<String, Object> globalVariables; // 全局变量
        private Map<String, Object> sharedData; // 共享数据
        private Long startTime;
        private Long endTime;

        public GlobalExecutionContext() {
            this.globalVariables = new HashMap<>();
            this.sharedData = new HashMap<>();
            this.startTime = System.currentTimeMillis();
        }

        public void putGlobalVariable(String key, Object value) {
            this.globalVariables.put(key, value);
        }

        public Object getGlobalVariable(String key) {
            return this.globalVariables.get(key);
        }

        public void putSharedData(String key, Object value) {
            this.sharedData.put(key, value);
        }

        public Object getSharedData(String key) {
            return this.sharedData.get(key);
        }

        public void markCompleted() {
            this.endTime = System.currentTimeMillis();
        }

        public Long getDuration() {
            if (startTime != null && endTime != null) {
                return endTime - startTime;
            }
            return null;
        }
    }

    /**
     * 注册编排策略
     */
    public void registerStrategy(OrchestrationStrategy strategy) {
        strategies.add(strategy);
        // 按优先级排序
        strategies.sort((a, b) -> Integer.compare(b.getPriority(), a.getPriority()));
        log.info("注册编排策略: {} (优先级: {})", strategy.getStrategyName(), strategy.getPriority());
    }


    /**
     * 智能编排（完整参数）
     */
    public OrchestrationPlan orchestrate(String userInput, List<String> availableServiceIds,
                                       String systemPrompt, String modelId) {
        log.info("开始智能编排，用户输入: {}", userInput);

        try {
            // 1. 获取可用服务
            List<AigcMcpService> availableServices = getAvailableServices(availableServiceIds);

            // 2. 优先使用AI编排计划生成器
            if (aiPlanner != null) {
                log.info("使用AI生成编排计划，模型ID: {}", modelId);
                OrchestrationPlan aiPlan = aiPlanner.generatePlan(userInput, availableServices, systemPrompt, modelId);
                if (aiPlan != null && !aiPlan.getSteps().isEmpty()) {
                    log.info("AI编排计划生成成功，策略: {}, 步骤数: {}",
                        aiPlan.getStrategyName(), aiPlan.getSteps().size());
                    return aiPlan;
                } else {
                    log.warn("AI编排计划生成失败，回退到策略模式");
                }
            }

            // 3. 回退到传统策略选择
            OrchestrationStrategy selectedStrategy = selectBestStrategy(userInput, availableServices);

            if (selectedStrategy == null) {
                log.warn("未找到合适的编排策略");
                return createFallbackPlan(userInput, availableServices);
            }

            // 4. 创建编排计划
            OrchestrationPlan plan = selectedStrategy.createPlan(userInput, availableServices);
            plan.setStrategyName(selectedStrategy.getStrategyName());
            plan.setPlanId(generatePlanId());

            log.info("编排计划创建完成，策略: {}, 步骤数: {}",
                    selectedStrategy.getStrategyName(), plan.getSteps().size());

            return plan;

        } catch (Exception e) {
            log.error("智能编排失败", e);
            return createFallbackPlan(userInput, getAvailableServices(availableServiceIds));
        }
    }

    /**
     * 执行编排计划（完整参数）
     */
    public OrchestrationExecutionResult execute(OrchestrationPlan plan, String userId, String conversationId,
                                               StatusCallback statusCallback, String modelId,
                                               String userInput) {
        log.info("开始执行编排计划: {} ({})", plan.getDescription(), plan.getStrategyName());

        long startTime = System.currentTimeMillis();
        List<StepExecutionResult> stepResults = new ArrayList<>();

        // 使用上下文管理器创建全局执行上下文
        GlobalExecutionContext globalContext = contextManager.createContext(
            plan.getPlanId(), userId, conversationId, userInput, modelId);

        // 从计划元数据中提取用户输入
        log.debug("从编排计划中提取用户输入: {}", userInput != null ? userInput.substring(0, Math.min(50, userInput.length())) + "..." : "无");

        try {
            // 发送计划开始状态
            if (statusCallback != null) {
                statusCallback.onPlanStart(plan);
            }

            // 1. 验证计划
            validatePlan(plan);

            // 2. 按依赖关系排序
            List<ExecutionStep> sortedSteps = topologicalSort(plan.getSteps());

            // 3. 执行步骤
            int currentStepIndex = 0;
            int totalSteps = sortedSteps.size();

            for (ExecutionStep step : sortedSteps) {
                currentStepIndex++;
                log.info("执行步骤 {}/{}: {} - {}", currentStepIndex, totalSteps, step.getStepId(), step.getDescription());

                // 标记步骤开始
                step.markStarted();

                // 发送步骤开始状态
                if (statusCallback != null) {
                    statusCallback.onStepStart(step);
                    statusCallback.onProgressUpdate(currentStepIndex, totalSteps,
                        String.format("正在执行: %s", step.getDescription()));
                }

                // 检查执行条件
//                if (!checkExecutionConditions(step, plan.getSteps(), globalContext)) {
//                    log.info("步骤 {} 不满足执行条件，跳过", step.getStepId());
//                    if (statusCallback != null) {
//                        statusCallback.onStepSkipped(step, "不满足执行条件");
//                    }
//                    continue;
//                }

                // 检查依赖
                if (!areDependenciesCompleted(step, plan.getSteps())) {
                    log.error("步骤 {} 的依赖未完成", step.getStepId());
                    if (statusCallback != null) {
                        statusCallback.onStepSkipped(step, "依赖步骤未完成");
                    }
                    continue;
                }

                // 先标准化参数引用格式，再使用高级参数解析器动态更新参数
                standardizeStepParameters(step);
                updateStepParametersWithResolver(step, plan.getSteps(), globalContext);

                // 关键修复：将前面步骤的结果添加到当前步骤的parameters中
                addStepResultsToParameters(step, plan.getSteps(), globalContext);

                // 执行步骤（通过路由器传递模型ID和用户输入）
                McpResponse response = mcpToolRouter.routeToolCall(
                    step.getServiceName(),
                    step.getToolName(),
                    step.getParameters(),
                    userId,
                    conversationId,
                    modelId,
                    userInput
                );

                // 应用结果转换器
                response = applyResultTransformers(response, step.getTransformers());

                step.setResult(response);
                step.setCompleted(response.isSuccess());
                step.markCompleted();

                // 使用上下文管理器更新步骤结果
                contextManager.updateStepResult(plan.getPlanId(), step.getStepId(), response);

                // 更新全局上下文
                updateGlobalContext(step, response, globalContext);

                // 记录步骤结果
                StepExecutionResult stepResult = StepExecutionResult.fromMcpResponse(
                    step.getStepId(), step.getServiceName(), step.getToolName(),
                    step.getDescription(), response, step.getParameters(), step.getDependencies()
                );
                stepResults.add(stepResult);

                // 发送步骤完成状态
                if (statusCallback != null) {
                    if (response.isSuccess()) {
                        statusCallback.onStepCompleted(step, response);
                    } else {
                        statusCallback.onStepFailed(step, response.getError().getMessage());
                    }
                }

                log.info("步骤 {} 执行完成，成功: {}", step.getStepId(), response.isSuccess());
            }
            
            // 创建成功结果
            OrchestrationExecutionResult result = OrchestrationExecutionResult.success(
                plan.getPlanId(), plan.getStrategyName(), plan.getDescription(),
                stepResults, plan.getMetadata()
            );
            result.setStartTime(startTime);
            result.calculateStatistics();

            // 发送计划完成状态
            if (statusCallback != null) {
                statusCallback.onPlanCompleted(plan, true);
            }

            // 标记全局上下文完成
            globalContext.markCompleted();

            return result;

        } catch (Exception e) {
            log.error("执行编排计划失败", e);

            // 创建失败结果
            OrchestrationExecutionResult result = OrchestrationExecutionResult.failure(
                plan.getPlanId(), plan.getStrategyName(), e.getMessage(), stepResults
            );
            result.setStartTime(startTime);
            result.calculateStatistics();

            // 发送计划失败状态
            if (statusCallback != null) {
                statusCallback.onPlanFailed(plan, e.getMessage());
            }

            // 清理失败的上下文
            contextManager.removeContext(plan.getPlanId());

            return result;
        }
    }

    /**
     * 选择最佳策略
     */
    private OrchestrationStrategy selectBestStrategy(String userInput, List<AigcMcpService> availableServices) {
        for (OrchestrationStrategy strategy : strategies) {
            if (strategy.canHandle(userInput, availableServices)) {
                log.info("选择编排策略: {}", strategy.getStrategyName());
                return strategy;
            }
        }
        return null;
    }

    /**
     * 获取可用服务
     */
    private List<AigcMcpService> getAvailableServices(List<String> serviceIds) {
        List<AigcMcpService> services = new ArrayList<>();

        for (String serviceId : serviceIds) {
            // 处理数据库中的服务
            AigcMcpService dbService = mcpServiceService.findById(serviceId);
            if (dbService != null && dbService.getEnabled()) {
                services.add(dbService);
            }
        }

        return services;
    }

    /**
     * 验证计划
     */
    private void validatePlan(OrchestrationPlan plan) {
        if (plan.getSteps().isEmpty()) {
            throw new IllegalArgumentException("编排计划不能为空");
        }
        
        // 检查步骤ID唯一性
        Set<String> stepIds = new HashSet<>();
        for (ExecutionStep step : plan.getSteps()) {
            if (!stepIds.add(step.getStepId())) {
                throw new IllegalArgumentException("步骤ID重复: " + step.getStepId());
            }
        }
        
        // 检查依赖关系
        for (ExecutionStep step : plan.getSteps()) {
            for (String depId : step.getDependencies()) {
                if (!stepIds.contains(depId)) {
                    throw new IllegalArgumentException("步骤 " + step.getStepId() + " 依赖不存在的步骤: " + depId);
                }
            }
        }
    }


    /**
     * 检查执行条件（支持全局上下文）
     */
    private boolean checkExecutionConditions(ExecutionStep step, List<ExecutionStep> allSteps, GlobalExecutionContext globalContext) {
        if (step.getConditions().isEmpty()) {
            return true;
        }

        return true;
    }

    /**
     * 检查依赖是否完成
     */
    private boolean areDependenciesCompleted(ExecutionStep step, List<ExecutionStep> allSteps) {
        Map<String, ExecutionStep> stepMap = allSteps.stream()
                .collect(Collectors.toMap(ExecutionStep::getStepId, Function.identity()));
        
        for (String depId : step.getDependencies()) {
            ExecutionStep depStep = stepMap.get(depId);
            if (depStep == null || !depStep.isCompleted()) {
                return false;
            }
        }
        return true;
    }


    /**
     * 更新步骤参数（支持全局上下文）
     */
    private void updateStepParametersWithContext(ExecutionStep step, List<ExecutionStep> allSteps, GlobalExecutionContext globalContext) {
        Map<String, ExecutionStep> stepMap = allSteps.stream()
                .collect(Collectors.toMap(ExecutionStep::getStepId, Function.identity()));

        Map<String, Object> updatedParams = new HashMap<>(step.getParameters());

        // 递归处理所有参数值，支持嵌套对象和数组
        updatedParams = (Map<String, Object>)processParameterValueWithContext(updatedParams, stepMap, globalContext);

        step.setParameters(updatedParams);
    }

    /**
     * 标准化步骤参数引用格式
     */
    private void standardizeStepParameters(ExecutionStep step) {
        if (referenceStandardizer == null) {
            log.warn("参数引用标准化器不可用，跳过标准化");
            return;
        }

        Object standardizedParams = referenceStandardizer.standardizeParameterReferences(step.getParameters());
        if (standardizedParams instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> paramMap = (Map<String, Object>) standardizedParams;
            step.setParameters(paramMap);

            log.debug("步骤 {} 参数引用格式标准化完成", step.getStepId());
        }
    }

    /**
     * 使用高级参数解析器更新步骤参数
     */
    private void updateStepParametersWithResolver(ExecutionStep step, List<ExecutionStep> allSteps, GlobalExecutionContext globalContext) {
        if (parameterResolver == null) {
            // 回退到原有方法
            updateStepParametersWithContext(step, allSteps, globalContext);
            return;
        }

        Map<String, ExecutionStep> stepMap = allSteps.stream()
                .collect(Collectors.toMap(ExecutionStep::getStepId, Function.identity()));

        // 使用高级参数解析器处理参数
        Object resolvedParams = parameterResolver.resolveParameterValue(
                step.getParameters(), stepMap, globalContext);

        if (resolvedParams instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> paramMap = (Map<String, Object>) resolvedParams;
            step.setParameters(paramMap);

            log.debug("步骤 {} 参数解析完成，参数数量: {}", step.getStepId(), paramMap.size());
        }
    }

    /**
     * 将前面步骤的结果添加到当前步骤的parameters中
     * 这是关键修复：确保ExternalMcpToolHandler能够访问到前面步骤的结果
     */
    private void addStepResultsToParameters(ExecutionStep currentStep, List<ExecutionStep> allSteps, GlobalExecutionContext globalContext) {
        Map<String, Object> currentParams = new HashMap<>(currentStep.getParameters());

        // 添加所有已完成步骤的结果到parameters中
        for (ExecutionStep step : allSteps) {
            if (step.isCompleted() && step.getResult() != null && step.getResult().isSuccess()) {
                String stepId = step.getStepId();
                Object result = step.getResult().getResult();
                JSONObject json = JSONUtil.parseObj(result);
                if(json != null && json.get("output") != null){
                    JSONObject output = (JSONObject) json.get("output");
                    currentParams.put(stepId, output.get("results"));
                }else{
                    currentParams.put(stepId, result);
                }
                // 只添加步骤结果，使用 stepId_result 格式


                log.debug("添加步骤 {} 的结果到当前步骤 {} 的参数中", stepId, currentStep.getStepId());
            }
        }

        currentStep.setParameters(currentParams);

        log.info("步骤 {} 参数已增强，新增 {} 个步骤结果", currentStep.getStepId(),
            currentParams.size() - currentStep.getParameters().size());

    }


    /**
     * 递归处理参数值，支持复杂数据结构和全局上下文
     */
    @SuppressWarnings("unchecked")
    private Object processParameterValueWithContext(Object value, Map<String, ExecutionStep> stepMap, GlobalExecutionContext globalContext) {
        if (value instanceof String) {
            return replaceParameterReferencesWithContext((String) value, stepMap, globalContext);
        } else if (value instanceof Map) {
            Map<String, Object> map = (Map<String, Object>) value;
            Map<String, Object> processedMap = new HashMap<>();
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                processedMap.put(entry.getKey(), processParameterValueWithContext(entry.getValue(), stepMap, globalContext));
            }
            return processedMap;
        } else if (value instanceof List) {
            List<Object> list = (List<Object>) value;
            List<Object> processedList = new ArrayList<>();
            for (Object item : list) {
                processedList.add(processParameterValueWithContext(item, stepMap, globalContext));
            }
            return processedList;
        }
        return value;
    }

    /**
     * 替换参数引用（支持全局上下文）
     */
    private String replaceParameterReferencesWithContext(String value, Map<String, ExecutionStep> stepMap, GlobalExecutionContext globalContext) {
        // 支持多种引用格式：${stepId.result}, ${stepId.result.field}, ${global.varName}, ${shared.dataKey}
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("\\$\\{([^}]+)\\}");
        java.util.regex.Matcher matcher = pattern.matcher(value);

        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            String reference = matcher.group(1);
            String replacement = resolveReferenceWithContext(reference, stepMap, globalContext);
            if (replacement != null) {
                matcher.appendReplacement(sb, java.util.regex.Matcher.quoteReplacement(replacement));
            } else {
                // 保留原始引用，便于调试
                log.warn("无法解析参数引用: {}", reference);
                matcher.appendReplacement(sb, java.util.regex.Matcher.quoteReplacement(matcher.group()));
            }
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 解析引用（支持全局上下文）
     */
    private String resolveReferenceWithContext(String reference, Map<String, ExecutionStep> stepMap, GlobalExecutionContext globalContext) {
        String[] parts = reference.split("\\.");
        if (parts.length < 2) {
            log.warn("参数引用格式错误: {}, 应为 stepId.result[.field] 或 global.varName 或 shared.dataKey", reference);
            return null;
        }

        String firstPart = parts[0];

        // 处理全局变量引用：${global.varName}
        if ("global".equals(firstPart) && globalContext != null && parts.length >= 2) {
            Object value = globalContext.getGlobalVariable(parts[1]);
            return value != null ? String.valueOf(value) : null;
        }

        // 处理共享数据引用：${shared.dataKey}
        if ("shared".equals(firstPart) && globalContext != null && parts.length >= 2) {
            Object value = globalContext.getSharedData(parts[1]);
            return value != null ? String.valueOf(value) : null;
        }

        // 处理步骤引用
        String stepId = firstPart;
        ExecutionStep step = stepMap.get(stepId);
        if (step == null) {
            log.warn("未找到步骤: {}", stepId);
            return null;
        }

        if (!step.isCompleted()) {
            log.warn("步骤 {} 尚未完成，无法获取结果", stepId);
            return null;
        }

        if ("result".equals(parts[1])) {
            if (parts.length == 2) {
                return extractResultValue(step.getResult());
            } else {
                // 支持多级字段访问：stepId.result.field1.field2
                return extractNestedResultField(step.getResult(), Arrays.copyOfRange(parts, 2, parts.length));
            }
        } else if ("parameters".equals(parts[1]) && parts.length > 2) {
            // 支持访问步骤的参数：stepId.parameters.paramName
            Object paramValue = step.getParameters().get(parts[2]);
            return paramValue != null ? String.valueOf(paramValue) : null;
        } else if ("context".equals(parts[1]) && parts.length > 2) {
            // 支持访问步骤的执行上下文：stepId.context.contextKey
            Object contextValue = step.getExecutionContext().get(parts[2]);
            return contextValue != null ? String.valueOf(contextValue) : null;
        }

        return null;
    }

    /**
     * 提取结果值
     */
    private String extractResultValue(McpResponse response) {
        if (response == null || response.getResult() == null) {
            return null;
        }

        if (response.getResult() instanceof String) {
            return (String) response.getResult();
        } else if (response.getResult() instanceof Map) {
            Map<String, Object> resultMap = (Map<String, Object>) response.getResult();
            // 尝试提取常见字段
            for (String field : Arrays.asList("url", "content", "text", "data", "value", "result")) {
                if (resultMap.containsKey(field)) {
                    Object value = resultMap.get(field);
                    return value != null ? String.valueOf(value) : null;
                }
            }
            // 如果没有找到常见字段，返回整个结果的JSON字符串
            return JSONUtil.toJsonStr(resultMap);
        } else if (response.getResult() instanceof List) {
            // 对于列表结果，返回JSON字符串
            return JSONUtil.toJsonStr(response.getResult());
        }
        return String.valueOf(response.getResult());
    }


    /**
     * 提取嵌套结果字段，支持多级访问
     */
    @SuppressWarnings("unchecked")
    private String extractNestedResultField(McpResponse response, String[] fieldPath) {
        if (response == null || response.getResult() == null || fieldPath.length == 0) {
            return null;
        }

        Object current = response.getResult();

        for (String field : fieldPath) {
            if (current instanceof Map) {
                Map<String, Object> map = (Map<String, Object>) current;
                current = map.get(field);
                if (current == null) {
                    return null;
                }
            } else if (current instanceof List && field.matches("\\d+")) {
                // 支持数组索引访问
                List<Object> list = (List<Object>) current;
                int index = Integer.parseInt(field);
                if (index >= 0 && index < list.size()) {
                    current = list.get(index);
                } else {
                    return null;
                }
            } else {
                return null;
            }
        }

        return current != null ? String.valueOf(current) : null;
    }

    /**
     * 更新全局上下文
     */
    private void updateGlobalContext(ExecutionStep step, McpResponse response, GlobalExecutionContext globalContext) {
        if (globalContext == null) {
            return;
        }

        // 自动提取常见结果字段到全局变量
        if (response.isSuccess() && response.getResult() != null) {
            String stepId = step.getStepId();

            // 将整个结果存储为全局变量
            globalContext.putGlobalVariable(stepId + "_result", response.getResult());

            // 如果结果是Map，提取常见字段
            if (response.getResult() instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> resultMap = (Map<String, Object>) response.getResult();

                // 提取常见字段到全局变量
                for (String commonField : Arrays.asList("url", "content", "text", "data", "value", "id", "name")) {
                    if (resultMap.containsKey(commonField)) {
                        globalContext.putGlobalVariable(stepId + "_" + commonField, resultMap.get(commonField));
                    }
                }
            }

            // 根据步骤类型进行特殊处理
            String serviceName = step.getServiceName();
            if ("builtin-search".equals(serviceName)) {
                // 搜索服务的结果可能需要特殊处理
                globalContext.putSharedData("last_search_result", response.getResult());
            } else if ("wanx-image".equals(serviceName)) {
                // 图像生成服务的结果
                globalContext.putSharedData("last_generated_image", response.getResult());
            } else if ("builtin-api".equals(serviceName)) {
                // API调用的结果
                globalContext.putSharedData("last_api_result", response.getResult());
            }
        }
        // 记录步骤执行信息
        step.putExecutionContext("execution_time", step.getDuration());
        step.putExecutionContext("success", response.isSuccess());
        if (!response.isSuccess() && response.getError() != null) {
            step.putExecutionContext("error", response.getError().getMessage());
        }
    }

    /**
     * 应用结果转换器
     */
    private McpResponse applyResultTransformers(McpResponse response, Map<String, Object> transformers) {
        if (transformers.isEmpty()) {
            return response;
        }
        
        // 这里可以实现各种结果转换逻辑
        // 例如：格式转换、数据过滤、字段映射等
        return response;
    }

    /**
     * 拓扑排序
     */
    private List<ExecutionStep> topologicalSort(List<ExecutionStep> steps) {
        Map<String, ExecutionStep> stepMap = steps.stream()
                .collect(Collectors.toMap(ExecutionStep::getStepId, Function.identity()));
        
        List<ExecutionStep> result = new ArrayList<>();
        Set<String> visited = new HashSet<>();
        Set<String> visiting = new HashSet<>();
        
        for (ExecutionStep step : steps) {
            if (!visited.contains(step.getStepId())) {
                topologicalSortDfs(step.getStepId(), stepMap, visited, visiting, result);
            }
        }
        
        return result;
    }

    private void topologicalSortDfs(String stepId, Map<String, ExecutionStep> stepMap,
                                   Set<String> visited, Set<String> visiting, List<ExecutionStep> result) {
        if (visiting.contains(stepId)) {
            throw new RuntimeException("检测到循环依赖: " + stepId);
        }
        
        if (visited.contains(stepId)) {
            return;
        }
        
        visiting.add(stepId);
        ExecutionStep step = stepMap.get(stepId);
        
        for (String dependency : step.getDependencies()) {
            if (stepMap.containsKey(dependency)) {
                topologicalSortDfs(dependency, stepMap, visited, visiting, result);
            }
        }
        
        visiting.remove(stepId);
        visited.add(stepId);
        result.add(step);
    }



    /**
     * 创建备用计划
     */
    private OrchestrationPlan createFallbackPlan(String userInput, List<AigcMcpService> availableServices) {
        OrchestrationPlan plan = new OrchestrationPlan();
        plan.setPlanId(generatePlanId());
        plan.setDescription("简单执行计划");
        plan.setStrategyName("fallback");
        
        if (!availableServices.isEmpty()) {
            ExecutionStep step = new ExecutionStep();
            step.setStepId("step1");
            step.setServiceName(availableServices.get(0).getName());
            step.setToolName("default_tool");
            step.putParameter("prompt", userInput);
            step.setDescription("执行用户请求");
            
            plan.addStep(step);
        }
        
        return plan;
    }

    /**
     * 生成计划ID
     */
    private String generatePlanId() {
        return "plan_" + System.currentTimeMillis() + "_" + (int)(Math.random() * 1000);
    }
}
