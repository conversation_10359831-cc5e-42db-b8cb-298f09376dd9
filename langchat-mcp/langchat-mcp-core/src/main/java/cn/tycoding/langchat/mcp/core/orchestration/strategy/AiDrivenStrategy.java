/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.orchestration.strategy;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.tycoding.langchat.ai.core.service.impl.LangChatServiceImpl;
import cn.tycoding.langchat.mcp.core.entity.AigcMcpService;
import cn.tycoding.langchat.common.ai.dto.ChatReq;
import cn.tycoding.langchat.mcp.core.orchestration.impl.FlexibleMcpOrchestrator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * AI驱动的通用编排策略
 * 使用AI来分析用户需求并生成执行计划，适用于复杂和未预定义的场景
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AiDrivenStrategy implements FlexibleMcpOrchestrator.OrchestrationStrategy {

    private final LangChatServiceImpl langChatService;

    @Override
    public boolean canHandle(String userInput, List<AigcMcpService> availableServices) {
        // AI驱动策略作为兜底策略，可以处理任何复杂请求
        // 但优先级较低，只有在其他策略都无法处理时才使用
        
        // 检查是否包含多个动作词
        String[] actionWords = {
            "生成", "创建", "制作", "发布", "部署", "上传", "下载", 
            "搜索", "查找", "分析", "处理", "转换", "翻译", "总结"
        };
        
        int actionCount = 0;
        for (String action : actionWords) {
            if (userInput.contains(action)) {
                actionCount++;
            }
        }
        
        // 如果包含2个或以上动作词，且有足够的服务支持，则可以处理
        return actionCount >= 2 && availableServices.size() >= 2;
    }

    @Override
    public FlexibleMcpOrchestrator.OrchestrationPlan createPlan(String userInput, List<AigcMcpService> availableServices) {
        try {
            log.info("使用AI驱动策略生成执行计划");
            
            // 构建AI提示词
            String prompt = buildAiPrompt(userInput, availableServices);
            
            // 调用AI生成计划
            ChatReq chatReq = new ChatReq();
            chatReq.setMessage(prompt);
            
            String aiResponse = langChatService.text(chatReq);
            
            // 解析AI响应
            return parseAiResponse(aiResponse, userInput);
            
        } catch (Exception e) {
            log.error("AI驱动策略生成计划失败", e);
            return createSimplePlan(userInput, availableServices);
        }
    }

    @Override
    public String getStrategyName() {
        return "AiDrivenStrategy";
    }

    @Override
    public int getPriority() {
        // 最低优先级，作为兜底策略
        return 10;
    }

    /**
     * 构建AI提示词
     */
    private String buildAiPrompt(String userInput, List<AigcMcpService> availableServices) {
        StringBuilder prompt = new StringBuilder();
        
        prompt.append("你是一个智能的任务编排专家。请根据用户需求和可用的MCP服务，生成一个详细的执行计划。\n\n");
        prompt.append("用户需求：").append(userInput).append("\n\n");
        prompt.append("可用的MCP服务：\n");
        
        for (AigcMcpService service : availableServices) {
            prompt.append("- ").append(service.getDisplayName()).append(" (").append(service.getName()).append(")\n");
            prompt.append("  描述：").append(service.getDescription()).append("\n");
            prompt.append("  类型：").append(service.getType()).append("\n");
            
            if (StrUtil.isNotBlank(service.getTools())) {
                prompt.append("  工具：已配置\n");
            }
            prompt.append("\n");
        }
        
        prompt.append("请生成一个JSON格式的执行计划，严格按照以下结构：\n");
        prompt.append("{\n");
        prompt.append("  \"description\": \"计划的简短描述\",\n");
        prompt.append("  \"steps\": [\n");
        prompt.append("    {\n");
        prompt.append("      \"stepId\": \"step1\",\n");
        prompt.append("      \"serviceName\": \"服务名称（必须是上面列出的服务名称）\",\n");
        prompt.append("      \"toolName\": \"工具名称\",\n");
        prompt.append("      \"description\": \"步骤描述\",\n");
        prompt.append("      \"parameters\": {\n");
        prompt.append("        \"参数名\": \"参数值\"\n");
        prompt.append("      },\n");
        prompt.append("      \"dependencies\": [\"依赖的步骤ID\"]\n");
        prompt.append("    }\n");
        prompt.append("  ]\n");
        prompt.append("}\n\n");
        
        prompt.append("重要规则：\n");
        prompt.append("1. stepId必须唯一，建议使用step1, step2, step3格式\n");
        prompt.append("2. serviceName必须是上面列出的服务名称之一\n");
        prompt.append("3. 步骤之间的依赖关系要合理，后续步骤可以使用前面步骤的结果\n");
        prompt.append("4. 参数中可以使用${stepId.result}来引用前面步骤的结果\n");
        prompt.append("5. 确保所有步骤都是完成用户需求所必需的\n");
        prompt.append("6. 只返回JSON，不要包含其他解释文字\n");
        
        return prompt.toString();
    }

    /**
     * 解析AI响应
     */
    private FlexibleMcpOrchestrator.OrchestrationPlan parseAiResponse(String aiResponse, String userInput) {
        try {
            // 提取JSON部分
            String jsonPart = extractJsonFromResponse(aiResponse);
            
            // 解析JSON
            Map<String, Object> planData = JSONUtil.toBean(jsonPart, Map.class);
            
            FlexibleMcpOrchestrator.OrchestrationPlan plan = new FlexibleMcpOrchestrator.OrchestrationPlan();
            plan.setDescription((String) planData.get("description"));
            
            // 解析步骤
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> stepsData = (List<Map<String, Object>>) planData.get("steps");
            
            for (Map<String, Object> stepData : stepsData) {
                FlexibleMcpOrchestrator.ExecutionStep step = new FlexibleMcpOrchestrator.ExecutionStep();
                step.setStepId((String) stepData.get("stepId"));
                step.setServiceName((String) stepData.get("serviceName"));
                step.setToolName((String) stepData.get("toolName"));
                step.setDescription((String) stepData.get("description"));
                
                // 设置参数
                @SuppressWarnings("unchecked")
                Map<String, Object> parameters = (Map<String, Object>) stepData.get("parameters");
                if (parameters != null) {
                    step.setParameters(parameters);
                }
                
                // 设置依赖
                @SuppressWarnings("unchecked")
                List<String> dependencies = (List<String>) stepData.get("dependencies");
                if (dependencies != null) {
                    step.setDependencies(dependencies);
                }
                
                plan.addStep(step);
            }
            
            // 添加元数据
            plan.putMetadata("generated_by", "ai");
            plan.putMetadata("user_input", userInput);
            plan.putMetadata("complexity", "dynamic");
            
            return plan;
            
        } catch (Exception e) {
            log.error("解析AI响应失败: {}", aiResponse, e);
            throw new RuntimeException("解析AI响应失败", e);
        }
    }

    /**
     * 从响应中提取JSON
     */
    private String extractJsonFromResponse(String response) {
        // 首先尝试查找JSON代码块
        Pattern codeBlockPattern = Pattern.compile("```json\\s*([\\s\\S]*?)\\s*```", Pattern.CASE_INSENSITIVE);
        Matcher matcher = codeBlockPattern.matcher(response);
        
        if (matcher.find()) {
            return matcher.group(1).trim();
        }
        
        // 如果没有代码块，查找大括号包围的内容
        Pattern jsonPattern = Pattern.compile("\\{[\\s\\S]*\\}");
        matcher = jsonPattern.matcher(response);
        
        if (matcher.find()) {
            return matcher.group().trim();
        }
        
        // 如果都没找到，尝试清理响应并直接解析
        String cleaned = response.trim();
        if (cleaned.startsWith("{") && cleaned.endsWith("}")) {
            return cleaned;
        }
        
        throw new RuntimeException("无法从AI响应中提取JSON: " + response);
    }

    /**
     * 创建简单计划（备用方案）
     */
    private FlexibleMcpOrchestrator.OrchestrationPlan createSimplePlan(String userInput, List<AigcMcpService> availableServices) {
        FlexibleMcpOrchestrator.OrchestrationPlan plan = new FlexibleMcpOrchestrator.OrchestrationPlan();
        plan.setDescription("简单执行计划（AI生成失败时的备用方案）");
        
        // 创建一个使用第一个可用服务的简单步骤
        if (!availableServices.isEmpty()) {
            AigcMcpService firstService = availableServices.get(0);
            
            FlexibleMcpOrchestrator.ExecutionStep step = new FlexibleMcpOrchestrator.ExecutionStep();
            step.setStepId("step1");
            step.setServiceName(firstService.getName());
            step.setToolName("default_tool");
            step.setDescription("执行用户请求");
            step.putParameter("prompt", userInput);
            
            plan.addStep(step);
        }
        
        plan.putMetadata("generated_by", "fallback");
        plan.putMetadata("reason", "ai_generation_failed");
        
        return plan;
    }
}
