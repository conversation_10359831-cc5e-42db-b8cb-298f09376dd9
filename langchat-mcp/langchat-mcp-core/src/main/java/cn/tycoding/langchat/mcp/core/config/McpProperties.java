/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * MCP配置属性
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
@Data
@ConfigurationProperties(prefix = "langchat.mcp")
public class McpProperties {

    /**
     * 是否启用MCP模块
     */
    private boolean enabled = true;

    /**
     * 默认超时时间（毫秒）
     */
    private int defaultTimeout = 30000;

    /**
     * 默认重试次数
     */
    private int defaultRetries = 3;

    /**
     * 健康检查间隔（秒）
     */
    private int healthCheckInterval = 60;

    /**
     * 是否启用健康检查
     */
    private boolean healthCheckEnabled = true;

    /**
     * 连接池配置
     */
    private ConnectionPool connectionPool = new ConnectionPool();

    /**
     * 编排配置
     */
    private Orchestration orchestration = new Orchestration();

    /**
     * 日志配置
     */
    private Logging logging = new Logging();

    /**
     * 缓存配置
     */
    private Cache cache = new Cache();

    /**
     * 安全配置
     */
    private Security security = new Security();

    /**
     * 自定义服务配置
     */
    private Map<String, ServiceConfig> services = new HashMap<>();

    @Data
    public static class ConnectionPool {
        private int maxTotal = 200;
        private int maxPerRoute = 50;
        private int connectionTimeout = 5000;
        private int socketTimeout = 30000;
        private int connectionRequestTimeout = 5000;
    }

    @Data
    public static class Orchestration {
        private boolean enabled = true;
        private int maxSteps = 10;
        private int maxExecutionTime = 300000; // 5分钟
        private boolean aiDrivenEnabled = true;
        private String aiModel = "gpt-3.5-turbo";
    }

    @Data
    public static class Logging {
        private boolean enabled = true;
        private boolean logRequests = true;
        private boolean logResponses = true;
        private boolean logErrors = true;
        private int maxLogSize = 10000; // 字符数
        private int retentionDays = 30;
    }

    @Data
    public static class Cache {
        private boolean enabled = true;
        private String type = "memory"; // memory, redis
        private int ttl = 3600; // 秒
        private int maxSize = 1000;
    }

    @Data
    public static class Security {
        private boolean enabled = true;
        private boolean rateLimitEnabled = true;
        private int rateLimitPerMinute = 100;
        private boolean authRequired = false;
        private String[] allowedOrigins = {"*"};
    }

    @Data
    public static class ServiceConfig {
        private String endpoint;
        private String type;
        private String authType;
        private Map<String, String> authConfig = new HashMap<>();
        private Map<String, Object> config = new HashMap<>();
        private boolean enabled = true;
        private int priority = 50;
        private int timeout = 30000;
        private int maxRetries = 3;
    }
}
