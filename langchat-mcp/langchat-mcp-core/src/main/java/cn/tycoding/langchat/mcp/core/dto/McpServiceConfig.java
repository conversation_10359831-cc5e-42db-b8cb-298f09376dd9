/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * MCP服务配置
 * 
 * <AUTHOR>
 * @since 2024/12/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class McpServiceConfig {

    /**
     * 服务参数定义
     */
    private List<McpServiceParameter> parameters;

    /**
     * 服务参数值（用户配置的实际值）
     */
    private Map<String, Object> parameterValues;

    /**
     * 连接配置
     */
    private ConnectionConfig connection;

    /**
     * 认证配置
     */
    private AuthConfig auth;

    /**
     * 高级配置
     */
    private AdvancedConfig advanced;

    /**
     * 连接配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ConnectionConfig {
        /** 连接超时时间（毫秒） */
        private Integer connectTimeout;
        
        /** 读取超时时间（毫秒） */
        private Integer readTimeout;
        
        /** 写入超时时间（毫秒） */
        private Integer writeTimeout;
        
        /** 最大重试次数 */
        private Integer maxRetries;
        
        /** 重试间隔（毫秒） */
        private Integer retryInterval;
        
        /** 连接池大小 */
        private Integer poolSize;
        
        /** 保持连接时间（毫秒） */
        private Integer keepAliveTime;
    }

    /**
     * 认证配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AuthConfig {
        /** 认证类型 */
        private String type;
        
        /** API Key */
        private String apiKey;
        
        /** API Key Header名称 */
        private String apiKeyHeader;
        
        /** Bearer Token */
        private String bearerToken;
        
        /** 用户名（Basic Auth） */
        private String username;
        
        /** 密码（Basic Auth） */
        private String password;
        
        /** OAuth2配置 */
        private Map<String, Object> oauth2Config;
        
        /** 自定义Headers */
        private Map<String, String> customHeaders;
    }

    /**
     * 高级配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AdvancedConfig {
        /** 是否启用缓存 */
        private Boolean enableCache;
        
        /** 缓存过期时间（秒） */
        private Integer cacheExpireTime;
        
        /** 是否启用限流 */
        private Boolean enableRateLimit;
        
        /** 限流配置：每秒请求数 */
        private Integer rateLimit;
        
        /** 是否启用熔断器 */
        private Boolean enableCircuitBreaker;
        
        /** 熔断器配置 */
        private Map<String, Object> circuitBreakerConfig;
        
        /** 是否启用监控 */
        private Boolean enableMonitoring;
        
        /** 自定义配置 */
        private Map<String, Object> customConfig;
    }

    /**
     * 获取参数值
     */
    public Object getParameterValue(String parameterName) {
        return parameterValues != null ? parameterValues.get(parameterName) : null;
    }

    /**
     * 获取参数值（带默认值）
     */
    public Object getParameterValue(String parameterName, Object defaultValue) {
        Object value = getParameterValue(parameterName);
        return value != null ? value : defaultValue;
    }

    /**
     * 设置参数值
     */
    public void setParameterValue(String parameterName, Object value) {
        if (parameterValues == null) {
            parameterValues = new java.util.HashMap<>();
        }
        parameterValues.put(parameterName, value);
    }

    /**
     * 获取字符串类型参数值
     */
    public String getStringParameter(String parameterName) {
        Object value = getParameterValue(parameterName);
        return value != null ? value.toString() : null;
    }

    /**
     * 获取整数类型参数值
     */
    public Integer getIntegerParameter(String parameterName) {
        Object value = getParameterValue(parameterName);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }

    /**
     * 获取布尔类型参数值
     */
    public Boolean getBooleanParameter(String parameterName) {
        Object value = getParameterValue(parameterName);
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        if (value instanceof String) {
            return Boolean.parseBoolean((String) value);
        }
        return null;
    }

    /**
     * 验证必填参数
     */
    public List<String> validateRequiredParameters() {
        List<String> missingParameters = new java.util.ArrayList<>();
        
        if (parameters != null) {
            for (McpServiceParameter parameter : parameters) {
                if (Boolean.TRUE.equals(parameter.getRequired())) {
                    Object value = getParameterValue(parameter.getName());
                    if (value == null || (value instanceof String && ((String) value).trim().isEmpty())) {
                        missingParameters.add(parameter.getName());
                    }
                }
            }
        }
        
        return missingParameters;
    }

    /**
     * 获取敏感参数列表
     */
    public List<String> getSensitiveParameters() {
        List<String> sensitiveParams = new java.util.ArrayList<>();
        
        if (parameters != null) {
            for (McpServiceParameter parameter : parameters) {
                if (Boolean.TRUE.equals(parameter.getSensitive())) {
                    sensitiveParams.add(parameter.getName());
                }
            }
        }
        
        return sensitiveParams;
    }

    /**
     * 获取脱敏后的配置（用于日志记录等）
     */
    public McpServiceConfig getMaskedConfig() {
        McpServiceConfig maskedConfig = new McpServiceConfig();
        maskedConfig.setParameters(this.parameters);
        maskedConfig.setConnection(this.connection);
        maskedConfig.setAdvanced(this.advanced);
        
        // 脱敏参数值
        if (this.parameterValues != null) {
            Map<String, Object> maskedValues = new java.util.HashMap<>();
            List<String> sensitiveParams = getSensitiveParameters();
            
            for (Map.Entry<String, Object> entry : this.parameterValues.entrySet()) {
                if (sensitiveParams.contains(entry.getKey())) {
                    maskedValues.put(entry.getKey(), "***");
                } else {
                    maskedValues.put(entry.getKey(), entry.getValue());
                }
            }
            maskedConfig.setParameterValues(maskedValues);
        }
        
        // 脱敏认证配置
        if (this.auth != null) {
            AuthConfig maskedAuth = new AuthConfig();
            maskedAuth.setType(this.auth.getType());
            maskedAuth.setApiKeyHeader(this.auth.getApiKeyHeader());
            maskedAuth.setUsername(this.auth.getUsername());
            maskedAuth.setCustomHeaders(this.auth.getCustomHeaders());
            
            // 脱敏敏感信息
            maskedAuth.setApiKey(this.auth.getApiKey() != null ? "***" : null);
            maskedAuth.setBearerToken(this.auth.getBearerToken() != null ? "***" : null);
            maskedAuth.setPassword(this.auth.getPassword() != null ? "***" : null);
            
            maskedConfig.setAuth(maskedAuth);
        }
        
        return maskedConfig;
    }
}
