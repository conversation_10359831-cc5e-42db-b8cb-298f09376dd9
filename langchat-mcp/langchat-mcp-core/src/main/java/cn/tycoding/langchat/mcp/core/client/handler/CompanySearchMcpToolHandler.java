/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.client.handler;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.tycoding.langchat.mcp.core.client.http.CommonDataCenterConnector;
import cn.tycoding.langchat.mcp.core.client.http.CommonDataReturn;
import cn.tycoding.langchat.mcp.core.constants.McpServiceConstants;
import cn.tycoding.langchat.mcp.core.constants.RiskPublicDataServiceEnum;
import cn.tycoding.langchat.mcp.core.protocol.McpResponse;
import cn.tycoding.langchat.mcp.core.entity.AigcMcpService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 企业查询服务MCP工具处理器
 * 
 * <AUTHOR>
 * @since 2024/12/20
 */
@Slf4j
@Component
public class CompanySearchMcpToolHandler extends AbstractMcpToolHandler {

    @Autowired
    private CommonDataCenterConnector connector;
    @Override
    protected McpResponse handleSpecificTool(AigcMcpService service, String toolName, 
                                           Map<String, Object> parameters, 
                                           Map<String, Object> serviceParams) {
        
        log.info("处理企业查询服务: toolName={}, parameters={}", toolName, parameters);
        
        // 如果toolName为空或不匹配，根据参数智能判断使用哪个工具
        if (StrUtil.isBlank(toolName) || !isValidCompanyTool(toolName)) {
            toolName = StrUtil.toString(parameters.getOrDefault("queryType",McpServiceConstants.ToolNames.COMPANY_SEARCH));
            log.info("智能判断企业查询工具: {}", toolName);
        }
        
        return switch (toolName) {
            case McpServiceConstants.ToolNames.COMPANY_SEARCH -> handleCompanySearch(parameters, serviceParams);
            case McpServiceConstants.ToolNames.COMPANY_DETAIL -> handleCompanyDetail(parameters, serviceParams);
            case McpServiceConstants.ToolNames.COMPANY_FINANCIAL -> handleCompanyFinancial(parameters, serviceParams);
            case McpServiceConstants.ToolNames.COMPANY_RISK_ASSESSMENT -> handleCompanyRiskAssessment(parameters, serviceParams);
            default -> {
                log.warn("未知的企业查询工具: {}, 使用默认的企业搜索功能", toolName);
                yield handleCompanySearch(parameters, serviceParams);
            }
        };
    }

    /**
     * 检查是否为有效的企业查询工具
     */
    private boolean isValidCompanyTool(String toolName) {
        return McpServiceConstants.ToolNames.COMPANY_SEARCH.equals(toolName) ||
               McpServiceConstants.ToolNames.COMPANY_DETAIL.equals(toolName) ||
               McpServiceConstants.ToolNames.COMPANY_FINANCIAL.equals(toolName) ||
               McpServiceConstants.ToolNames.COMPANY_RISK_ASSESSMENT.equals(toolName);
    }

    /**
     * 根据参数智能判断使用哪个企业查询工具
     */
    private String determineCompanyTool(Map<String, Object> parameters) {
        // 1. 如果有companyId，优先使用详情查询
        if (parameters.containsKey("companyId") && StrUtil.isNotBlank((String) parameters.get("companyId"))) {
            String queryType = (String) parameters.get("queryType");
            if ("financial".equals(queryType)) {
                return McpServiceConstants.ToolNames.COMPANY_FINANCIAL;
            } else if ("risk".equals(queryType)) {
                return McpServiceConstants.ToolNames.COMPANY_RISK_ASSESSMENT;
            } else {
                return McpServiceConstants.ToolNames.COMPANY_DETAIL;
            }
        }

        // 2. 根据queryType判断
        String queryType = (String) parameters.get("queryType");
        if ("financial".equals(queryType)) {
            return McpServiceConstants.ToolNames.COMPANY_FINANCIAL;
        } else if ("risk".equals(queryType)) {
            return McpServiceConstants.ToolNames.COMPANY_RISK_ASSESSMENT;
        } else if ("basic".equals(queryType) || "detail".equals(queryType)) {
            return McpServiceConstants.ToolNames.COMPANY_DETAIL;
        }

        // 3. 智能分析查询内容，提取查询意图
        String detectedQueryType = analyzeQueryIntent(parameters);
        if (StrUtil.isNotBlank(detectedQueryType)) {
            log.info("智能分析查询意图: {}", detectedQueryType);
            return switch (detectedQueryType) {
                case "financial" -> McpServiceConstants.ToolNames.COMPANY_FINANCIAL;
                case "risk" -> McpServiceConstants.ToolNames.COMPANY_RISK_ASSESSMENT;
                case "detail" -> McpServiceConstants.ToolNames.COMPANY_DETAIL;
                default -> McpServiceConstants.ToolNames.COMPANY_SEARCH;
            };
        }

        // 4. 默认使用企业搜索
        return McpServiceConstants.ToolNames.COMPANY_SEARCH;
    }

    /**
     * 智能分析查询意图
     */
    private String analyzeQueryIntent(Map<String, Object> parameters) {
        // 收集所有可能包含查询意图的文本
        StringBuilder queryText = new StringBuilder();

        // 添加各种可能的查询参数
        String[] textFields = {"query", "companyName", "description", "content", "prompt"};
        for (String field : textFields) {
            Object value = parameters.get(field);
            if (value instanceof String && StrUtil.isNotBlank((String) value)) {
                queryText.append(value).append(" ");
            }
        }

        String fullText = queryText.toString().toLowerCase();
        if (StrUtil.isBlank(fullText)) {
            return null;
        }

        log.debug("分析查询文本: {}", fullText);

        // 财务相关关键词
        String[] financialKeywords = {
            "财务", "财报", "营收", "收入", "利润", "资产", "负债", "现金流", "盈利", "亏损",
            "financial", "revenue", "profit", "assets", "liabilities", "cash", "income",
            "年报", "季报", "财务报表", "资产负债表", "利润表", "现金流量表", "财务状况",
            "经营状况", "盈利能力", "偿债能力", "营运能力", "成长能力", "财务指标"
        };

        // 风险相关关键词
        String[] riskKeywords = {
            "风险", "风险评估", "信用", "违约", "诉讼", "处罚", "黑名单", "失信", "经营异常",
            "risk", "credit", "lawsuit", "penalty", "blacklist", "violation", "abnormal",
            "法律风险", "经营风险", "财务风险", "信用风险", "合规风险", "市场风险",
            "风险等级", "风险预警", "风险监控", "风险管理", "风险控制", "风险分析"
        };

        // 详情相关关键词
        String[] detailKeywords = {
            "详情", "基本信息", "企业信息", "公司信息", "注册信息", "工商信息", "详细资料",
            "detail", "info", "information", "profile", "overview", "basic",
            "法人", "注册资本", "成立时间", "经营范围", "注册地址", "企业类型", "经营状态",
            "股东信息", "高管信息", "分支机构", "对外投资", "企业年报", "知识产权"
        };

        // 计算关键词匹配度
        int financialScore = countKeywordMatches(fullText, financialKeywords);
        int riskScore = countKeywordMatches(fullText, riskKeywords);
        int detailScore = countKeywordMatches(fullText, detailKeywords);

        log.debug("关键词匹配分数 - 财务: {}, 风险: {}, 详情: {}", financialScore, riskScore, detailScore);

        // 返回得分最高的查询类型
        if (financialScore > 0 && financialScore >= riskScore && financialScore >= detailScore) {
            return "financial";
        } else if (riskScore > 0 && riskScore >= detailScore) {
            return "risk";
        } else if (detailScore > 0) {
            return "detail";
        }

        return null; // 无法确定具体类型，使用默认搜索
    }

    /**
     * 计算关键词匹配数量
     */
    private int countKeywordMatches(String text, String[] keywords) {
        int count = 0;
        for (String keyword : keywords) {
            if (text.contains(keyword.toLowerCase())) {
                count++;
            }
        }
        return count;
    }

    /**
     * 处理企业搜索
     */
    private McpResponse handleCompanySearch(Map<String, Object> parameters, Map<String, Object> serviceParams) {
        logToolCall("company_search", parameters);

        if (!validateRequiredParameters(parameters, "companyName")) {
            return createValidationError("companyName");
        }

        try {
            String companyName = getParameterValue(parameters, "companyName", String.class, "");
            String searchType = getParameterValue(parameters, "searchType", String.class, "fuzzy");
            String region = getParameterValue(parameters, "region", String.class, "全国");
            Integer resultLimit = getParameterValue(parameters, "resultLimit", Integer.class, 10);

            log.info("执行企业搜索: 企业名称={}, 搜索类型={}, 地区={}, 结果限制={}", 
                    companyName, searchType, region, resultLimit);

            Map<String, Object> searchResult = createMockCompanySearchResult(companyName, searchType, region, resultLimit);

            logToolResult("company_search", true, null);
            return McpResponse.success(searchResult);

        } catch (Exception e) {
            logToolResult("company_search", false, e.getMessage());
            return McpResponse.error("企业搜索失败: " + e.getMessage());
        }
    }

    /**
     * 处理企业详情查询
     */
    private McpResponse handleCompanyDetail(Map<String, Object> parameters, Map<String, Object> serviceParams) {
        logToolCall("company_detail", parameters);

        String companyId = (String) parameters.get("companyId");
        String companyName = (String) parameters.get("companyName");
        
        if (StrUtil.isBlank(companyId) && StrUtil.isBlank(companyName)) {
            return createValidationError("companyId", "companyName");
        }

        try {
            String infoType = getParameterValue(parameters, "infoType", String.class, "basic");

            log.info("查询企业详情: 企业ID={}, 企业名称={}, 信息类型={}", companyId, companyName, infoType);

            Map<String, Object> detailResult = createMockCompanyDetailResult(companyId, companyName, infoType);

            logToolResult("company_detail", true, null);
            return McpResponse.success(detailResult);

        } catch (Exception e) {
            logToolResult("company_detail", false, e.getMessage());
            return McpResponse.error("企业详情查询失败: " + e.getMessage());
        }
    }

    /**
     * 处理企业财务查询
     */
    private McpResponse handleCompanyFinancial(Map<String, Object> parameters, Map<String, Object> serviceParams) {
        logToolCall("company_financial", parameters);

        String companyId = (String) parameters.get("companyId");
        String companyName = (String) parameters.get("companyName");
        
        if (StrUtil.isBlank(companyId) && StrUtil.isBlank(companyName)) {
            return createValidationError("companyId", "companyName");
        }

        try {
            Integer year = getParameterValue(parameters, "year", Integer.class, 2023);
            String reportType = getParameterValue(parameters, "reportType", String.class, "annual");

            log.info("查询企业财务: 企业ID={}, 企业名称={}, 年份={}, 报告类型={}", 
                    companyId, companyName, year, reportType);

            Map<String, Object> financialResult = createMockCompanyFinancialResult(companyId, companyName, year, reportType);

            logToolResult("company_financial", true, null);
            return McpResponse.success(financialResult);

        } catch (Exception e) {
            logToolResult("company_financial", false, e.getMessage());
            return McpResponse.error("企业财务查询失败: " + e.getMessage());
        }
    }

    /**
     * 处理企业风险评估
     */
    private McpResponse handleCompanyRiskAssessment(Map<String, Object> parameters, Map<String, Object> serviceParams) {
        logToolCall("company_risk_assessment", parameters);

        String companyId = (String) parameters.get("companyId");
        String companyName = (String) parameters.get("companyName");
        
        if (StrUtil.isBlank(companyId) && StrUtil.isBlank(companyName)) {
            return createValidationError("companyId", "companyName");
        }

        try {
            @SuppressWarnings("unchecked")
            List<String> riskTypes = (List<String>) parameters.getOrDefault("riskTypes", 
                    List.of("legal", "financial", "operational"));

            log.info("企业风险评估: 企业ID={}, 企业名称={}, 风险类型={}", companyId, companyName, riskTypes);

            Map<String, Object> riskResult = createMockCompanyRiskResult(companyId, companyName, riskTypes);

            logToolResult("company_risk_assessment", true, null);
            return McpResponse.success(riskResult);

        } catch (Exception e) {
            logToolResult("company_risk_assessment", false, e.getMessage());
            return McpResponse.error("企业风险评估失败: " + e.getMessage());
        }
    }

    private Map<String, Object> createMockCompanySearchResult(String companyName, String searchType, String region, Integer resultLimit) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("searchType", searchType);
        result.put("region", region);
        result.put("query", companyName);

        JSONObject params = new JSONObject();
        params.set("searchKey", companyName);
        params.set("validPeriod",24*30);

        CommonDataReturn commonDataReturn = connector.sendRequest(RiskPublicDataServiceEnum.SUPPLIER_CUSTOMER,
                        params, false);

        result.put("data", commonDataReturn.getData());
        result.put("timestamp", System.currentTimeMillis());

        return result;
    }

    private Map<String, Object> createMockCompanyDetailResult(String companyId, String companyName, String infoType) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("companyId", companyId);
        result.put("infoType", infoType);

        Map<String, Object> data = new HashMap<>();
        Map<String, Object> basicInfo = new HashMap<>();
        basicInfo.put("name", companyName != null ? companyName + "有限公司" : "示例企业有限公司");
        basicInfo.put("creditCode", companyId != null ? companyId : "91330000MA28F3E96U");
        basicInfo.put("legalPerson", "张三");
        basicInfo.put("registeredCapital", "1000万人民币");
        basicInfo.put("establishDate", "2020-01-01");
        basicInfo.put("status", "存续");
        basicInfo.put("address", "浙江省杭州市高新技术开发区创新大道123号");
        basicInfo.put("businessScope", "软件开发、技术咨询、技术服务、技术转让");
        basicInfo.put("industry", "软件和信息技术服务业");
        basicInfo.put("companyType", "有限责任公司");
        basicInfo.put("employees", "50-100人");
        data.put("basicInfo", basicInfo);

        result.put("data", data);
        result.put("timestamp", System.currentTimeMillis());

        return result;
    }

    private Map<String, Object> createMockCompanyFinancialResult(String companyId, String companyName, Integer year, String reportType) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("companyId", companyId);
        result.put("companyName", companyName);
        result.put("year", year);
        result.put("reportType", reportType);

        Map<String, Object> financialData = new HashMap<>();
        financialData.put("revenue", "5000万元");
        financialData.put("profit", "800万元");
        financialData.put("assets", "3000万元");
        financialData.put("liabilities", "1200万元");
        financialData.put("netAssets", "1800万元");
        financialData.put("cashFlow", "600万元");
        financialData.put("growthRate", "15.5%");
        financialData.put("profitMargin", "16%");
        financialData.put("debtRatio", "40%");

        result.put("data", financialData);
        result.put("timestamp", System.currentTimeMillis());

        return result;
    }

    /**
     * 企业风险信息
     * <AUTHOR>
     * @date 2025/6/21 16:16
     * @param companyId 企业ID
     * @param companyName   企业名称
     * @param riskTypes 风险类型
     * @return java.util.Map<java.lang.String,java.lang.Object>
     */
    private Map<String, Object> createMockCompanyRiskResult(String companyId, String companyName, List<String> riskTypes) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("companyId", companyId);
        result.put("companyName", companyName);
        result.put("riskTypes", riskTypes);

        JSONObject params = new JSONObject();
        params.set("searchKey", companyName);
        params.set("validPeriod",24*30);

        CommonDataReturn cooperationRisk = connector.sendRequest(RiskPublicDataServiceEnum.BUSINESS_INFO_COOPERATION_RISK,
                params, false);
        params.putOnce("pageSize", 1);
        params.putOnce("pageNum", 20);

        CommonDataReturn guarantees = connector.sendRequest(RiskPublicDataServiceEnum.BUSINESS_INFO_GUARANTEES,
                params, false);

        result.put("cooperationRisk", cooperationRisk);
        //result.put("guarantees", guarantees);

        result.put("timestamp", System.currentTimeMillis());

        return result;
    }


}
