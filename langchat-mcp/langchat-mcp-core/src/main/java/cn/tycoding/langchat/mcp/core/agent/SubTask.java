/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.agent;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 子任务定义
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
@Data
@Builder
public class SubTask {
    
    /**
     * 任务ID
     */
    private String id;
    
    /**
     * 任务名称
     */
    private String name;
    
    /**
     * 任务描述
     */
    private String description;
    
    /**
     * 工具类别
     */
    private String toolCategory;
    
    /**
     * 任务优先级（数字越小优先级越高）
     */
    private int priority;
    
    /**
     * 依赖的任务ID列表
     */
    private List<String> dependencies;
    
    /**
     * 任务参数
     */
    private Map<String, Object> parameters;
    
    /**
     * 是否为关键任务（失败时停止整个流程）
     */
    @Builder.Default
    private boolean critical = false;
    
    /**
     * 预估执行时间（秒）
     */
    @Builder.Default
    private int estimatedTime = 30;
    
    /**
     * 最大重试次数
     */
    @Builder.Default
    private int maxRetries = 3;
    
    /**
     * 任务状态
     */
    @Builder.Default
    private TaskStatus status = TaskStatus.PENDING;
    
    public enum TaskStatus {
        PENDING,    // 待执行
        RUNNING,    // 执行中
        SUCCESS,    // 成功
        FAILED,     // 失败
        SKIPPED     // 跳过
    }
}
