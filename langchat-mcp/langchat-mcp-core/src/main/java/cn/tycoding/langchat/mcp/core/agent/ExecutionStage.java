/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.agent;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 执行阶段
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
@Data
@Builder
public class ExecutionStage {
    
    /**
     * 阶段编号
     */
    private int stageNumber;
    
    /**
     * 阶段中的任务列表
     */
    private List<SubTask> tasks;
    
    /**
     * 是否可以并行执行
     */
    private boolean canRunInParallel;
    
    /**
     * 阶段描述
     */
    private String description;
    
    /**
     * 阶段状态
     */
    @Builder.Default
    private StageStatus status = StageStatus.PENDING;
    
    /**
     * 开始时间
     */
    private long startTime;
    
    /**
     * 结束时间
     */
    private long endTime;
    
    public enum StageStatus {
        PENDING,    // 待执行
        RUNNING,    // 执行中
        COMPLETED,  // 已完成
        FAILED      // 失败
    }
    
    /**
     * 获取执行时间（毫秒）
     */
    public long getExecutionTime() {
        if (startTime > 0 && endTime > 0) {
            return endTime - startTime;
        }
        return 0;
    }
}
