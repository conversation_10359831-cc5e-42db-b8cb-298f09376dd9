/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.orchestration.impl;

import cn.tycoding.langchat.common.ai.dto.ChatRes;
import cn.tycoding.langchat.common.ai.utils.StreamEmitter;
import cn.tycoding.langchat.mcp.core.orchestration.StatusCallback;
import cn.tycoding.langchat.mcp.core.orchestration.impl.FlexibleMcpOrchestrator;
import cn.tycoding.langchat.mcp.core.protocol.McpResponse;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * 流式状态回调实现
 * 将MCP执行状态实时推送到前端
 * 
 * <AUTHOR>
 * @since 2025/06/15
 */
@Slf4j
public class StreamingStatusCallback implements StatusCallback {

    private final StreamEmitter emitter;

    public StreamingStatusCallback(StreamEmitter emitter) {
        this.emitter = emitter;
    }

    @Override
    public void onPlanStart(FlexibleMcpOrchestrator.OrchestrationPlan plan) {
        String message = String.format("🎯 开始执行智能编排\n策略: %s\n计划: %s\n步骤数: %d\n\n",
            plan.getStrategyName(), 
            plan.getDescription(),
            plan.getSteps().size()
        );
        sendMessage(message);
    }

    @Override
    public void onStepStart(FlexibleMcpOrchestrator.ExecutionStep step) {
        String message = String.format("⏳ 正在执行: %s\n服务: %s\n", 
            step.getDescription(),
            step.getServiceName()
        );
        sendMessage(message);
    }

    @Override
    public void onStepCompleted(FlexibleMcpOrchestrator.ExecutionStep step, McpResponse response) {
        String result = formatStepResult(step, response);
        String message = String.format("✅ %s - 完成\n%s\n", 
            step.getDescription(),
            result
        );
        sendMessage(message);
    }

    @Override
    public void onStepFailed(FlexibleMcpOrchestrator.ExecutionStep step, String error) {
        String message = String.format("❌ %s - 失败\n错误: %s\n\n", 
            step.getDescription(),
            error
        );
        sendMessage(message);
    }

    @Override
    public void onStepSkipped(FlexibleMcpOrchestrator.ExecutionStep step, String reason) {
        String message = String.format("⏭️ %s - 跳过\n原因: %s\n\n", 
            step.getDescription(),
            reason
        );
        sendMessage(message);
    }

    @Override
    public void onPlanCompleted(FlexibleMcpOrchestrator.OrchestrationPlan plan, boolean success) {
        String message = success ? 
            "🎉 智能编排执行完成！\n\n" : 
            "⚠️ 智能编排执行完成，但部分步骤失败\n\n";
        sendMessage(message);
    }

    @Override
    public void onPlanFailed(FlexibleMcpOrchestrator.OrchestrationPlan plan, String error) {
        String message = String.format("💥 智能编排执行失败\n错误: %s\n\n", error);
        sendMessage(message);
    }

    @Override
    public void onProgressUpdate(int currentStep, int totalSteps, String message) {
        String progressMessage = String.format("📊 进度: %d/%d - %s\n", 
            currentStep, totalSteps, message
        );
        sendMessage(progressMessage);
    }

    /**
     * 发送消息到前端
     */
    private void sendMessage(String message) {
        try {
            if (emitter != null) {
                emitter.send(new ChatRes(message));
            }
        } catch (Exception e) {
            log.error("发送状态消息失败", e);
        }
    }

    /**
     * 格式化步骤结果
     */
    private String formatStepResult(FlexibleMcpOrchestrator.ExecutionStep step, McpResponse response) {
        if (!response.isSuccess()) {
            return "执行失败: " + response.getError();
        }

        Object result = response.getResult();
        if (result instanceof Map) {
            Map<String, Object> resultMap = (Map<String, Object>) result;
            
            // 图片生成结果
            if ("text_to_image".equals(step.getToolName())) {
                return formatImageResult(resultMap);
            }
            
            // 搜索结果
            if ("web_search".equals(step.getToolName())) {
                return formatSearchResult(resultMap);
            }
            
            // 通用结果
            return formatGenericResult(resultMap);
        }
        
        return "执行成功";
    }

    /**
     * 格式化图片生成结果
     */
    private String formatImageResult(Map<String, Object> result) {
        if (result.containsKey("output")) {
            Map<String, Object> output = (Map<String, Object>) result.get("output");
            if (output.containsKey("results")) {
                return "🖼️ 图片生成成功";
            }
        }
        return "🖼️ 图片生成完成";
    }

    /**
     * 格式化搜索结果
     */
    private String formatSearchResult(Map<String, Object> result) {
        if (result.containsKey("web")) {
            Map<String, Object> web = (Map<String, Object>) result.get("web");
            if (web.containsKey("results")) {
                Object results = web.get("results");
                if (results instanceof java.util.List) {
                    int count = ((java.util.List<?>) results).size();
                    return String.format("🔍 搜索完成，找到 %d 条结果", count);
                }
            }
        }
        return "🔍 搜索完成";
    }

    /**
     * 格式化通用结果
     */
    private String formatGenericResult(Map<String, Object> result) {
        if (result.containsKey("success") && Boolean.TRUE.equals(result.get("success"))) {
            return "✨ 执行成功";
        }
        return "📋 执行完成";
    }
}
