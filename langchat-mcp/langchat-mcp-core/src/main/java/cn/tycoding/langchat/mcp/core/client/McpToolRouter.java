/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.client;

import cn.tycoding.langchat.mcp.core.client.handler.McpToolHandlerFactory;
import cn.tycoding.langchat.mcp.core.constants.McpServiceConstants;
import cn.tycoding.langchat.mcp.core.protocol.McpResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * MCP工具路由器
 * 统一路由MCP工具调用到相应的处理器
 * 
 * <AUTHOR>
 * @since 2025/06/15
 */
@Slf4j
@Component
public class McpToolRouter {

    @Autowired
    private McpToolHandlerFactory handlerFactory;
    
    @Autowired
    private ExternalMcpToolHandler externalToolHandler;


    /**
     * 路由工具调用到相应的处理器（支持用户输入）
     */
    public McpResponse routeToolCall(String serviceName, String toolName, Map<String, Object> parameters,
                                    String userId, String conversationId, String modelId, String userInput) {
        try {
            log.info("路由MCP工具调用: 服务={}, 工具={}, 用户={}, 用户输入={}", serviceName, toolName, userId,
                    userInput != null ? userInput.substring(0, Math.min(50, userInput.length())) + "..." : "无");

            // 1. 检查服务类型并路由到相应的处理器
            if (McpServiceConstants.isBuiltinService(serviceName)) {
                log.info("路由到内置工具处理器: {}", serviceName);
                return handlerFactory.handleToolCall(serviceName, toolName, parameters, userInput,modelId);
                
            } else if (McpServiceConstants.isVirtualService(serviceName) ||
                      McpServiceConstants.isExternalService(serviceName)) {
                log.info("路由到外部工具处理器: {}", serviceName);
                return externalToolHandler.handleToolCall(serviceName, toolName, parameters, userId, conversationId, modelId, userInput);

            } else {
                // 未知服务类型，尝试外部处理器
                log.info("未知服务类型，尝试外部工具处理器: {}", serviceName);
                return externalToolHandler.handleToolCall(serviceName, toolName, parameters, userId, conversationId, modelId, userInput);
            }
            
        } catch (Exception e) {
            log.error("MCP工具路由失败: 服务={}, 工具={}", serviceName, toolName, e);
            return McpResponse.error("工具路由异常: " + e.getMessage());
        }
    }


    /**
     * 路由工具调用（支持用户输入，无模型ID）
     */
    public McpResponse routeToolCall(String serviceName, String toolName, Map<String, Object> parameters,
                                    String userId, String conversationId, String userInput) {
        return routeToolCall(serviceName, toolName, parameters, userId, conversationId, null, userInput);
    }


    /**
     * 检查服务是否可用
     */
    public boolean isServiceAvailable(String serviceName) {
        try {
            if (McpServiceConstants.isBuiltinService(serviceName)) {
                return handlerFactory != null;
            } else {
                return externalToolHandler != null;
            }
        } catch (Exception e) {
            log.error("检查服务可用性失败: {}", serviceName, e);
            return false;
        }
    }

    /**
     * 获取服务类型
     */
    public String getServiceType(String serviceName) {
        if (McpServiceConstants.isBuiltinService(serviceName)) {
            return McpServiceConstants.ServiceTypes.BUILTIN;
        } else if (McpServiceConstants.isVirtualService(serviceName)) {
            return McpServiceConstants.ServiceTypes.VIRTUAL;
        } else if (McpServiceConstants.isExternalService(serviceName)) {
            return McpServiceConstants.ServiceTypes.EXTERNAL;
        } else {
            return "unknown";
        }
    }

    /**
     * 获取服务显示名称
     */
    public String getServiceDisplayName(String serviceName) {
        return McpServiceConstants.getServiceDisplayName(serviceName);
    }
}
