/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.protocol;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * MCP工具定义
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
@Data
@Builder
public class McpTool {
    
    /**
     * 工具名称
     */
    private String name;
    
    /**
     * 工具描述
     */
    private String description;
    
    /**
     * 输入参数定义
     */
    private ParameterSchema inputSchema;
    
    /**
     * 工具类别
     */
    private String category;
    
    /**
     * 工具标签
     */
    private List<String> tags;
    
    /**
     * 是否需要确认
     */
    private boolean requiresConfirmation = false;
    
    /**
     * 工具优先级
     */
    private int priority = 0;
    
    /**
     * 参数模式定义
     */
    @Data
    @Builder
    public static class ParameterSchema {
        private String type = "object";
        private Map<String, ParameterProperty> properties;
        private List<String> required;
        
        @Data
        @Builder
        public static class ParameterProperty {
            private String type;
            private String description;
            private Object defaultValue;
            private List<Object> enumValues;
            private String format;
            private Number minimum;
            private Number maximum;
            private Integer minLength;
            private Integer maxLength;
        }
    }
}
