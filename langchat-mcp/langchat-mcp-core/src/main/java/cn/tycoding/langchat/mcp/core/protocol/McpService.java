/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.protocol;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * MCP服务定义
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
@Data
@Builder
public class McpService {
    
    /**
     * 服务名称
     */
    private String name;
    
    /**
     * 服务端点URL
     */
    private String endpoint;
    
    /**
     * 服务描述
     */
    private String description;
    
    /**
     * 服务版本
     */
    private String version;
    
    /**
     * 支持的工具列表
     */
    private List<McpTool> tools;
    
    /**
     * 服务状态
     */
    private ServiceStatus status = ServiceStatus.UNKNOWN;
    
    /**
     * 服务类型
     */
    private ServiceType type = ServiceType.HTTP;
    
    /**
     * 认证信息
     */
    private AuthInfo authInfo;
    
    public enum ServiceStatus {
        ACTIVE,     // 活跃
        INACTIVE,   // 非活跃
        ERROR,      // 错误
        UNKNOWN     // 未知
    }
    
    public enum ServiceType {
        HTTP,       // HTTP服务
        WEBSOCKET,  // WebSocket服务
        GRPC        // gRPC服务
    }
    
    @Data
    @Builder
    public static class AuthInfo {
        private String type;        // 认证类型: bearer, basic, api_key
        private String token;       // 认证令牌
        private String username;    // 用户名
        private String password;    // 密码
        private String apiKey;      // API密钥
    }
}
