/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.utils;

import cn.hutool.core.util.StrUtil;
import cn.tycoding.consts.OrcTemplateType;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

/**
 * 文件类型检测工具类
 * 用于识别文件路径是文档还是图片，并返回相应的模板类型
 * 
 * <AUTHOR>
 * @since 2025/06/24
 */
@Slf4j
public class FileTypeDetector {

    // 支持的图片文件扩展名
    private static final Set<String> IMAGE_EXTENSIONS = Set.of(
        "jpg", "jpeg", "png", "gif", "bmp", "webp", "tiff", "tif", "svg"
    );

    // 支持的文档文件扩展名
    private static final Set<String> DOCUMENT_EXTENSIONS = Set.of(
        "pdf", "doc", "docx", "txt", "rtf", "odt"
    );

    // 图片MIME类型
    private static final Set<String> IMAGE_MIME_TYPES = Set.of(
        "image/jpeg", "image/jpg", "image/png", "image/gif", "image/bmp", 
        "image/webp", "image/tiff", "image/svg+xml"
    );

    // 文档MIME类型
    private static final Set<String> DOCUMENT_MIME_TYPES = Set.of(
        "application/pdf", "application/msword", 
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "text/plain", "application/rtf", "application/vnd.oasis.opendocument.text"
    );

    /**
     * 检测文件类型并返回相应的模板类型
     * 
     * @param filePath 文件路径（可以是URL或本地路径）
     * @return OrcTemplateType 模板类型
     */
    public static OrcTemplateType detectFileType(String filePath) {
        if (StrUtil.isBlank(filePath)) {
            throw new IllegalArgumentException("文件路径不能为空");
        }

        try {
            // 1. 首先尝试通过文件扩展名判断
            OrcTemplateType typeByExtension = detectByExtension(filePath);
            if (typeByExtension != null) {
                log.debug("通过扩展名检测到文件类型: {} -> {}", filePath, typeByExtension);
                return typeByExtension;
            }

            // 2. 如果是URL，尝试通过MIME类型判断
            if (isUrl(filePath)) {
                OrcTemplateType typeByMime = detectByMimeType(filePath);
                if (typeByMime != null) {
                    log.debug("通过MIME类型检测到文件类型: {} -> {}", filePath, typeByMime);
                    return typeByMime;
                }
            }

            // 3. 如果是本地文件，尝试通过文件内容判断
            if (isLocalFile(filePath)) {
                OrcTemplateType typeByContent = detectByContent(filePath);
                if (typeByContent != null) {
                    log.debug("通过文件内容检测到文件类型: {} -> {}", filePath, typeByContent);
                    return typeByContent;
                }
            }

            // 4. 默认返回图片类型（因为OCR主要处理图片）
            log.warn("无法确定文件类型，默认使用图片类型: {}", filePath);
            return OrcTemplateType.ID_CARD;

        } catch (Exception e) {
            log.error("检测文件类型失败: {}", filePath, e);
            // 发生异常时默认返回图片类型
            return OrcTemplateType.ID_CARD;
        }
    }

    /**
     * 批量检测文件类型
     * 
     * @param filePaths 文件路径列表
     * @return 检测结果，如果所有文件都是同一类型则返回该类型，否则返回混合类型的处理建议
     */
    public static FileTypeDetectionResult detectMultipleFiles(List<String> filePaths) {
        if (filePaths == null || filePaths.isEmpty()) {
            throw new IllegalArgumentException("文件路径列表不能为空");
        }

        FileTypeDetectionResult result = new FileTypeDetectionResult();
        
        for (String filePath : filePaths) {
            OrcTemplateType type = detectFileType(filePath);
            result.addFile(filePath, type);
        }

        return result;
    }

    /**
     * 通过文件扩展名检测类型
     */
    private static OrcTemplateType detectByExtension(String filePath) {
        String extension = getFileExtension(filePath);
        if (StrUtil.isBlank(extension)) {
            return null;
        }

        extension = extension.toLowerCase();
        
        if (IMAGE_EXTENSIONS.contains(extension)) {
            return OrcTemplateType.ID_CARD;
        } else if (DOCUMENT_EXTENSIONS.contains(extension)) {
            return OrcTemplateType.DOCUMENT;
        }

        return null;
    }

    /**
     * 通过MIME类型检测（适用于URL）
     */
    private static OrcTemplateType detectByMimeType(String urlString) {
        try {
            URL url = new URL(urlString);
            URLConnection connection = url.openConnection();
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(5000);
            
            String mimeType = connection.getContentType();
            if (StrUtil.isBlank(mimeType)) {
                return null;
            }

            // 移除可能的字符集信息
            mimeType = mimeType.split(";")[0].trim().toLowerCase();

            if (IMAGE_MIME_TYPES.contains(mimeType)) {
                return OrcTemplateType.ID_CARD;
            } else if (DOCUMENT_MIME_TYPES.contains(mimeType)) {
                return OrcTemplateType.DOCUMENT;
            }

        } catch (Exception e) {
            log.warn("获取URL MIME类型失败: {}", urlString, e);
        }

        return null;
    }

    /**
     * 通过文件内容检测（适用于本地文件）
     */
    private static OrcTemplateType detectByContent(String filePath) {
        try {
            Path path = Paths.get(filePath);
            if (!Files.exists(path)) {
                return null;
            }

            String mimeType = Files.probeContentType(path);
            if (StrUtil.isBlank(mimeType)) {
                return null;
            }

            mimeType = mimeType.toLowerCase();

            if (IMAGE_MIME_TYPES.contains(mimeType)) {
                return OrcTemplateType.ID_CARD;
            } else if (DOCUMENT_MIME_TYPES.contains(mimeType)) {
                return OrcTemplateType.DOCUMENT;
            }

        } catch (Exception e) {
            log.warn("检测文件内容类型失败: {}", filePath, e);
        }

        return null;
    }

    /**
     * 获取文件扩展名
     */
    private static String getFileExtension(String filePath) {
        if (StrUtil.isBlank(filePath)) {
            return null;
        }

        // 移除URL参数
        String cleanPath = filePath.split("\\?")[0];
        
        int lastDotIndex = cleanPath.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < cleanPath.length() - 1) {
            return cleanPath.substring(lastDotIndex + 1);
        }

        return null;
    }

    /**
     * 判断是否是URL
     */
    private static boolean isUrl(String path) {
        return path.startsWith("http://") || path.startsWith("https://") || path.startsWith("ftp://");
    }

    /**
     * 判断是否是本地文件路径
     */
    private static boolean isLocalFile(String path) {
        return !isUrl(path) && (path.startsWith("/") || path.matches("^[A-Za-z]:\\\\.*"));
    }

    /**
     * 文件类型检测结果
     */
    public static class FileTypeDetectionResult {
        private int imageCount = 0;
        private int documentCount = 0;
        private final List<FileTypeInfo> files = new java.util.ArrayList<>();

        public void addFile(String filePath, OrcTemplateType type) {
            files.add(new FileTypeInfo(filePath, type));
            
            if (type == OrcTemplateType.ID_CARD) {
                imageCount++;
            } else if (type == OrcTemplateType.DOCUMENT) {
                documentCount++;
            }
        }

        /**
         * 获取推荐的处理策略
         */
        public OrcTemplateType getRecommendedType() {
            if (documentCount > 0 && imageCount == 0) {
                return OrcTemplateType.DOCUMENT;
            } else {
                // 默认使用图片处理（包括混合类型的情况）
                return OrcTemplateType.ID_CARD;
            }
        }

        /**
         * 是否包含混合类型
         */
        public boolean hasMixedTypes() {
            return imageCount > 0 && documentCount > 0;
        }

        /**
         * 获取处理建议
         */
        public String getProcessingSuggestion() {
            if (hasMixedTypes()) {
                return String.format("检测到混合文件类型（%d个图片，%d个文档），建议分别处理", 
                    imageCount, documentCount);
            } else if (documentCount > 0) {
                return String.format("检测到%d个文档文件，使用文档识别模式", documentCount);
            } else {
                return String.format("检测到%d个图片文件，使用图片识别模式", imageCount);
            }
        }

        // Getters
        public int getImageCount() { return imageCount; }
        public int getDocumentCount() { return documentCount; }
        public List<FileTypeInfo> getFiles() { return files; }
    }

    /**
     * 文件类型信息
     */
    public static class FileTypeInfo {
        private final String filePath;
        private final OrcTemplateType type;

        public FileTypeInfo(String filePath, OrcTemplateType type) {
            this.filePath = filePath;
            this.type = type;
        }

        public String getFilePath() { return filePath; }
        public OrcTemplateType getType() { return type; }
    }
}
