/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.config;

import cn.tycoding.langchat.mcp.core.orchestration.impl.FlexibleMcpOrchestrator;
import cn.tycoding.langchat.mcp.core.orchestration.strategy.AiDrivenStrategy;
import cn.tycoding.langchat.mcp.core.orchestration.strategy.ContentCreationStrategy;
import cn.tycoding.langchat.mcp.core.orchestration.strategy.ImagePublishStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Configuration;

/**
 * MCP编排配置
 * 自动注册所有编排策略
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class McpOrchestrationConfig implements CommandLineRunner {

    private final FlexibleMcpOrchestrator flexibleMcpOrchestrator;
    private final ImagePublishStrategy imagePublishStrategy;
    private final ContentCreationStrategy contentCreationStrategy;
    private final AiDrivenStrategy aiDrivenStrategy;

    @Override
    public void run(String... args) throws Exception {
        log.info("开始注册MCP编排策略...");
        
        // 注册所有策略（按优先级自动排序）
        flexibleMcpOrchestrator.registerStrategy(imagePublishStrategy);
        flexibleMcpOrchestrator.registerStrategy(contentCreationStrategy);
        flexibleMcpOrchestrator.registerStrategy(aiDrivenStrategy);
        
        log.info("MCP编排策略注册完成");
    }
}
