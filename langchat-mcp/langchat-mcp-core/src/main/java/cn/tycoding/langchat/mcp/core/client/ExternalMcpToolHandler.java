/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.client;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.tycoding.langchat.mcp.core.constants.McpServiceConstants;
import cn.tycoding.langchat.mcp.core.entity.AigcMcpService;
import cn.tycoding.langchat.mcp.core.orchestration.AiOrchestrationPlanner;
import cn.tycoding.langchat.mcp.core.protocol.McpResponse;
import cn.tycoding.langchat.mcp.core.service.AigcMcpServiceService;
import cn.tycoding.langchat.mcp.core.service.AsyncTaskHandler;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 外部MCP工具处理器
 * 通过前端传递的MCP服务信息动态执行工具调用
 *
 * <AUTHOR>
 * @since 2024/12/18
 */
@Slf4j
@Component
public class ExternalMcpToolHandler {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private AsyncTaskHandler asyncTaskHandler;

    @Autowired
    private AiOrchestrationPlanner aiPlanner;
    @Autowired
    private AigcMcpServiceService aigcMcpServiceService;

    /**
     * 处理MCP工具调用（无模型ID）
     */
    public McpResponse handleToolCall(String serviceName, String toolName, Map<String, Object> parameters,
                                      String userId, String conversationId) {
        return handleToolCall(serviceName, toolName, parameters, userId, conversationId, null, null);
    }

    /**
     * 处理MCP工具调用（完整参数，支持用户输入）
     */
    public McpResponse handleToolCall(String serviceName, String toolName, Map<String, Object> parameters,
                                      String userId, String conversationId, String modelId, String userInput) {
        try {
            log.info("开始执行MCP工具调用: 服务={}, 工具={}, 用户输入={}", serviceName, toolName,
                    userInput != null ? userInput.substring(0, Math.min(50, userInput.length())) + "..." : "无");

            // 1. 特殊处理虚拟服务
            if (McpServiceConstants.VirtualServices.AI_PROMPT_OPTIMIZER.equals(serviceName)) {
                log.info("处理虚拟AI提示词优化服务");
                return handleAiPromptOptimization(null, toolName, parameters, userId, conversationId, modelId);
            }

            // 2. 查询服务配置
            AigcMcpService dbService = null;
            try {
                List<AigcMcpService> aigcMcpServiceList = aigcMcpServiceService.list(Wrappers.<AigcMcpService>lambdaQuery()
                        .eq(AigcMcpService::getName, serviceName).or()
                        .eq(AigcMcpService::getId, serviceName));
                if (CollUtil.isNotEmpty(aigcMcpServiceList)) {
                    dbService = aigcMcpServiceList.get(0);
                }
                if (dbService == null) {
                    log.error("未找到MCP服务: {}", serviceName);
                    return McpResponse.error("未找到MCP服务: " + serviceName);
                }

                if (!dbService.getEnabled()) {
                    log.error("MCP服务已禁用: {}", serviceName);
                    return McpResponse.error("MCP服务已禁用: " + serviceName);
                }

            } catch (Exception e) {
                log.error("获取MCP服务失败: {}", serviceName, e);
                return McpResponse.error("获取MCP服务失败: " + e.getMessage());
            }

            // 2. 执行工具调用
            McpResponse response = executeToolCall(dbService, toolName, parameters, userId, conversationId, modelId, userInput);

            log.info("MCP工具调用完成: 服务={}, 工具={}, 成功={}",
                    serviceName, toolName, response.isSuccess());

            return response;

        } catch (Exception e) {
            log.error("MCP工具调用异常: 服务={}, 工具={}", serviceName, toolName, e);
            return McpResponse.error("工具调用异常: " + e.getMessage());
        }
    }

    /**
     * 执行工具调用
     */
    private McpResponse executeToolCall(AigcMcpService service, String toolName, Map<String, Object> parameters,
                                        String userId, String conversationId, String modelId, String userInput) {
        try {
            // 根据服务名称选择特定的处理逻辑
            return switch (service.getName()) {
                case McpServiceConstants.VirtualServices.AI_PROMPT_OPTIMIZER ->
                        handleAiPromptOptimization(service, toolName, parameters, userId, conversationId, modelId);
                case McpServiceConstants.ExternalServices.WANX_IMAGE_GENERATION ->
                        handleWanxImageGeneration(service, toolName, parameters, userInput);
                case McpServiceConstants.ExternalServices.BRAVE_SEARCH ->
                        handleBraveSearch(service, toolName, parameters, userInput);
                case McpServiceConstants.ExternalServices.EDGEONE_PAGES ->
                        handleEdgeOnePages(service, toolName, parameters, userInput);
                default -> handleGenericMcpService(service, toolName, parameters, userInput);
            };
        } catch (Exception e) {
            log.error("执行工具调用失败: 服务={}, 工具={}", service.getName(), toolName, e);
            return McpResponse.error("工具调用执行失败: " + e.getMessage());
        }
    }

    private String getPromptFromParameters(Map<String, Object> parameters) {
        String[] promptKeys = {
                McpServiceConstants.ParameterNames.ORIGINAL_PROMPT,
                McpServiceConstants.ParameterNames.INPUT_PROMPT,
                McpServiceConstants.ParameterNames.PROMPT
        };

        for (String key : promptKeys) {
            String value = (String) parameters.get(key);
            if (value != null && !value.trim().isEmpty()) {
                return value.trim();
            }
        }
        return null;
    }

    // 使用：

    /**
     * 处理AI提示词优化
     */
    private McpResponse handleAiPromptOptimization(AigcMcpService service, String toolName,
                                                   Map<String, Object> parameters,
                                                   String userId, String conversationId, String modelId) {

        String originalPrompt = getPromptFromParameters(parameters);

        if (StrUtil.isBlank(originalPrompt)) {
            return McpResponse.error("原始提示词不能为空");
        }

        try {
            String optimizationType = (String) parameters.getOrDefault("optimization_type", McpServiceConstants.ParameterValues.OPTIMIZATION_TYPE_GENERAL);
            log.info("开始AI提示词优化: {}，类型: {}，使用模型ID: {}", originalPrompt, optimizationType, modelId);

            // 调用AI优化提示词
            String optimizedPrompt = aiPlanner.optimizePrompt(originalPrompt, optimizationType, modelId);

            // 构建响应
            Map<String, Object> result = new HashMap<>();
            result.put(McpServiceConstants.ParameterNames.ORIGINAL_PROMPT, originalPrompt);
            result.put(McpServiceConstants.ParameterNames.OPTIMIZED_PROMPT, optimizedPrompt);
            result.put("optimization_type", parameters.getOrDefault("optimization_type", McpServiceConstants.ParameterValues.OPTIMIZATION_TYPE_GENERAL));
            result.put("timestamp", System.currentTimeMillis());

            log.info("AI提示词优化完成: {} -> {}", originalPrompt, optimizedPrompt);
            return McpResponse.success(result);

        } catch (Exception e) {
            log.error("AI提示词优化失败", e);
            // 降级处理：返回原始提示词
            Map<String, Object> fallbackResult = new HashMap<>();
            fallbackResult.put(McpServiceConstants.ParameterNames.ORIGINAL_PROMPT, originalPrompt);
            fallbackResult.put(McpServiceConstants.ParameterNames.OPTIMIZED_PROMPT, originalPrompt);
            fallbackResult.put("optimization_failed", true);
            fallbackResult.put("error", e.getMessage());

            return McpResponse.success(fallbackResult);
        }
    }


    /**
     * 处理Wanx文生图服务
     */
    private McpResponse handleWanxImageGeneration(AigcMcpService service, String toolName, Map<String, Object> parameters, String userInput) {

        String prompt = (String) parameters.get(McpServiceConstants.ParameterNames.PROMPT);
        if (StrUtil.isBlank(prompt) && StrUtil.isBlank(userInput)) {
            return McpResponse.error("图片生成提示词不能为空");
        }

        // 记录用户输入信息（用于后续的参数提取或优化）
        if (StrUtil.isNotBlank(userInput)) {
            log.debug("Wanx图片生成 - 用户输入: {}", userInput);
        }

        try {
            // 构建请求
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", "wanx2.1-t2i-plus");

            Map<String, Object> input = new HashMap<>();
            input.put(McpServiceConstants.ParameterNames.PROMPT, prompt == null ? userInput : prompt);
            requestBody.put("input", input);
            Map<String, Object> params = new HashMap<>();

            params.put("n", 1);
            params.put(McpServiceConstants.ParameterNames.SIZE, parameters.getOrDefault(McpServiceConstants.ParameterNames.SIZE, McpServiceConstants.ParameterValues.SIZE_DEFAULT));
            requestBody.put("parameters", params);

            // 获取API Key
            String apiKey = getApiKeyFromAuthConfig(service.getAuthConfig());
            if (StrUtil.isBlank(apiKey)) {
                return McpResponse.error("API Key未配置");
            }

            HttpResponse response = HttpRequest.post(service.getEndpoint())
                    .header("Authorization", "Bearer " + apiKey)
                    .header("Content-Type", "application/json")
                    .header("X-DashScope-Async", "enable")
                    .body(JSONUtil.toJsonStr(requestBody))
                    .timeout(60000)
                    .execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("Wanx API响应: {}", responseBody);

                JSONObject result = JSONUtil.parseObj(responseBody);
                Map<String, Object> resultMap = result.toBean(Map.class);

                // 检查是否为异步任务
                if (asyncTaskHandler.isAsyncTask(resultMap)) {
                    log.info("检测到Wanx异步任务，开始处理，任务ID: {}", asyncTaskHandler.extractTaskId(resultMap));

                    // 处理异步任务
                    Map<String, Object> finalResult = asyncTaskHandler.handleWanxImageTask(resultMap, apiKey);

                    // 检查最终结果是否为错误
                    if (finalResult.containsKey("success") && !(Boolean) finalResult.get("success")) {
                        log.error("异步任务处理失败: {}", finalResult.get("error"));
                        return McpResponse.error((String) finalResult.get("error"));
                    }

                    log.info("异步任务处理完成");
                    return McpResponse.success(finalResult);
                } else {
                    // 同步结果或已完成的任务
                    log.info("同步结果或已完成的任务");
                    return McpResponse.success(resultMap);
                }
            } else {
                String errorBody = response.body();
                log.error("Wanx API调用失败，状态码: {}, 响应: {}", response.getStatus(), errorBody);
                return McpResponse.error("API调用失败: " + response.getStatus() + ", " + errorBody);
            }

        } catch (Exception e) {
            log.error("调用Wanx服务失败", e);
            return McpResponse.error("Wanx服务异常: " + e.getMessage());
        }
    }

    /**
     * 处理Brave Search服务
     */
    private McpResponse handleBraveSearch(AigcMcpService service, String toolName, Map<String, Object> parameters, String userInput) {
        if (!McpServiceConstants.ToolNames.WEB_SEARCH.equals(toolName)) {
            return McpResponse.error("未知的搜索工具: " + toolName);
        }

        String query = (String) parameters.get(McpServiceConstants.ParameterNames.QUERY);
        if (StrUtil.isBlank(query)) {
            return McpResponse.error("搜索查询不能为空");
        }

        // 记录用户输入信息（用于后续的参数提取或优化）
        if (StrUtil.isNotBlank(userInput)) {
            log.debug("Brave搜索 - 用户输入: {}", userInput);
        }

        try {
            String apiKey = getApiKeyFromAuthConfig(service.getAuthConfig());
            if (StrUtil.isBlank(apiKey)) {
                return McpResponse.error("API Key未配置");
            }

            String url = String.format("%s?q=%s&count=%d",
                    service.getEndpoint(), query,
                    (Integer) parameters.getOrDefault(McpServiceConstants.ParameterNames.COUNT, 10));

            HttpResponse response = HttpRequest.get(url)
                    .header("X-Subscription-Token", apiKey)
                    .header("Accept", "application/json")
                    .timeout(30000)
                    .execute();

            if (response.isOk()) {
                JSONObject result = JSONUtil.parseObj(response.body());
                return McpResponse.success(result);
            } else {
                return McpResponse.error("搜索API调用失败: " + response.getStatus());
            }

        } catch (Exception e) {
            log.error("调用搜索服务失败", e);
            return McpResponse.error("搜索服务异常: " + e.getMessage());
        }
    }

    /**
     * 处理通用MCP服务
     */
    private McpResponse handleGenericMcpService(AigcMcpService service, String toolName, Map<String, Object> parameters, String userInput) {
        try {
            // 记录用户输入信息（用于后续的参数提取或优化）
            if (StrUtil.isNotBlank(userInput)) {
                log.debug("通用MCP服务 {} - 用户输入: {}", service.getName(), userInput);
            }
            // 构建标准MCP请求
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("jsonrpc", "2.0");
            requestBody.put("id", String.valueOf(System.currentTimeMillis()));
            requestBody.put("method", "tools/call");

            Map<String, Object> params = new HashMap<>();
            params.put("name", toolName);
            params.put("arguments", parameters);
            requestBody.put("params", params);

            HttpRequest request = HttpRequest.post(service.getEndpoint())
                    .header("Content-Type", "application/json")
                    .timeout(30000);

            // 添加认证信息
            String apiKey = getApiKeyFromAuthConfig(service.getAuthConfig());
            if (StrUtil.isNotBlank(apiKey)) {
                request.header("Authorization", "Bearer " + apiKey);
            }

            HttpResponse response = request.body(JSONUtil.toJsonStr(requestBody)).execute();

            if (response.isOk()) {
                JSONObject result = JSONUtil.parseObj(response.body());
                if (result.containsKey("error")) {
                    JSONObject error = result.getJSONObject("error");
                    return McpResponse.error(error.getStr("message"));
                }
                return McpResponse.success(result.get("result"));
            } else {
                return McpResponse.error("MCP服务调用失败: " + response.getStatus());
            }

        } catch (Exception e) {
            log.error("调用通用MCP服务失败: {}", service.getName(), e);
            return McpResponse.error("MCP服务异常: " + e.getMessage());
        }
    }

    /**
     * 从认证配置中获取API Key
     */
    private String getApiKeyFromAuthConfig(String authConfig) {
        if (StrUtil.isBlank(authConfig)) {
            return null;
        }

        try {
            JSONObject authJson = JSONUtil.parseObj(authConfig);
            // 优先尝试 apiKey，然后尝试 api_token（用于EdgeOne Pages）
            String apiKey = authJson.getStr("apiKey");
            if (StrUtil.isNotBlank(apiKey)) {
                return apiKey;
            }
            return authJson.getStr("api_token");
        } catch (Exception e) {
            log.warn("解析认证配置失败", e);
            return null;
        }
    }

    /**
     * 处理EdgeOne Pages服务
     * 基于官方EdgeOne Pages MCP源码实现
     */
    private McpResponse handleEdgeOnePages(AigcMcpService service, String toolName, Map<String, Object> parameters, String userInput) {
        log.info("处理EdgeOne Pages服务调用: 工具={}, 参数={}", toolName, parameters);

        try {
            return handleEdgeOneDeployHtml(service, parameters);
        } catch (Exception e) {
            log.error("EdgeOne Pages服务调用失败", e);
            return McpResponse.error("EdgeOne Pages服务调用失败: " + e.getMessage());
        }
    }

    /**
     * 部署HTML内容到EdgeOne Pages
     * 基于官方MCP源码实现 - 直接通过EdgeOne Pages Functions部署
     */
    private McpResponse handleEdgeOneDeployHtml(AigcMcpService service, Map<String, Object> parameters) {
        String htmlContent = (String) parameters.get("html_content");
        String projectName = (String) parameters.get("project_name");

        if (StrUtil.isBlank(htmlContent)) {
            return McpResponse.error("HTML内容不能为空");
        }

        // 处理参数替换 - 替换HTML内容中的${变量}占位符
        htmlContent = processParameterReferences(htmlContent, parameters);

        log.info("EdgeOne Pages部署 - 原始HTML长度: {}, 处理后HTML长度: {}",
            ((String) parameters.get("html_content")).length(), htmlContent.length());

        try {
            // 获取API Token（可选，用于私有项目）
            String apiToken = getApiKeyFromAuthConfig(service.getAuthConfig());

            // 构建MCP协议请求 - 使用正确的方法名
            Map<String, Object> mcpRequest = new HashMap<>();
            mcpRequest.put("jsonrpc", "2.0");
            mcpRequest.put("id", System.currentTimeMillis());
            mcpRequest.put("method", "tools/call");

            Map<String, Object> params = new HashMap<>();
            // 正确的工具名称（带连字符）
            params.put("name", "deploy-html");

            Map<String, Object> arguments = new HashMap<>();
            // EdgeOne Pages使用 "value" 参数
            arguments.put("value", htmlContent);
            params.put("arguments", arguments);

            mcpRequest.put("params", params);

            // 添加调试日志
            log.info("发送EdgeOne Pages MCP请求: {}", JSONUtil.toJsonStr(mcpRequest));

            // 调用EdgeOne Pages MCP服务器
            String deployUrl = "https://mcp-on-edge.edgeone.app/mcp-server";

            HttpRequest request = HttpRequest.post(deployUrl)
                    .header("Content-Type", "application/json");

            if (StrUtil.isNotBlank(apiToken)) {
                request.header("Authorization", "Bearer " + apiToken);
            }

            HttpResponse response = request
                    .body(JSONUtil.toJsonStr(mcpRequest))
                    // 2分钟超时
                    .timeout(120000)
                    .execute();

            log.info("EdgeOne Pages MCP响应: status={}, body={}", response.getStatus(), response.body());

            if (response.isOk()) {
                JSONObject mcpResponse = JSONUtil.parseObj(response.body());

                // 处理EdgeOne Pages MCP协议响应
                if (mcpResponse.containsKey("result")) {
                    JSONObject result = mcpResponse.getJSONObject("result");

                    // EdgeOne Pages返回格式: result.content[0].text 包含部署URL
                    if (result.containsKey("content")) {
                        JSONArray content = result.getJSONArray("content");
                        if (content != null && content.size() > 0) {
                            JSONObject firstContent = content.getJSONObject(0);
                            String deploymentUrl = firstContent.getStr("text");

                            Map<String, Object> responseData = new HashMap<>();
                            responseData.put("success", true);
                            responseData.put("url", deploymentUrl);
                            responseData.put("preview_url", deploymentUrl);
                            responseData.put("deployment_id", extractDeploymentId(deploymentUrl));
                            responseData.put("project_name", extractProjectName(deploymentUrl));
                            responseData.put("status", "deployed");
                            responseData.put("message", "HTML内容已成功部署到EdgeOne Pages");
                            responseData.put("raw_response", deploymentUrl);

                            log.info("EdgeOne Pages HTML部署成功: {}", responseData);
                            return McpResponse.success(responseData);
                        }
                    }

                    // 如果没有content字段，尝试直接解析result
                    Map<String, Object> responseData = new HashMap<>();
                    responseData.put("success", true);
                    responseData.put("result", result);
                    responseData.put("message", "EdgeOne Pages部署完成");
                    return McpResponse.success(responseData);
                } else if (mcpResponse.containsKey("error")) {
                    String errorMsg = mcpResponse.getJSONObject("error").getStr("message");
                    log.error("EdgeOne Pages MCP调用失败: {}", errorMsg);
                    return McpResponse.error("MCP调用失败: " + errorMsg);
                } else {
                    log.error("EdgeOne Pages MCP响应格式异常: {}", response.body());
                    return McpResponse.error("MCP响应格式异常");
                }
            } else {
                log.error("EdgeOne Pages MCP服务器调用失败: status={}, body={}", response.getStatus(), response.body());
                return McpResponse.error("MCP服务器调用失败: " + response.body());
            }

        } catch (Exception e) {
            log.error("EdgeOne Pages HTML部署异常", e);
            return McpResponse.error("HTML部署异常: " + e.getMessage());
        }
    }

    /**
     * 部署文件夹到EdgeOne Pages
     * 基于官方MCP源码实现 - 需要API Token
     */
    private McpResponse handleEdgeOneDeployFolder(AigcMcpService service, Map<String, Object> parameters) {
        String folderPath = (String) parameters.get("folder_path");
        String projectName = (String) parameters.get("project_name");

        if (StrUtil.isBlank(folderPath)) {
            return McpResponse.error("文件夹路径不能为空");
        }

        try {
            // 获取API Token（文件夹部署需要Token）
            String apiToken = getApiKeyFromAuthConfig(service.getAuthConfig());

            if (StrUtil.isBlank(apiToken)) {
                return McpResponse.error("部署文件夹需要EdgeOne Pages API Token");
            }

            // 读取文件夹内容
            Map<String, String> files = readFolderContents(folderPath);
            if (files.isEmpty()) {
                return McpResponse.error("文件夹为空或无法读取");
            }

            // 构建部署请求
            Map<String, Object> deployRequest = new HashMap<>();
            if (StrUtil.isNotBlank(projectName)) {
                deployRequest.put("projectName", projectName);
            }
            deployRequest.put("files", files);

            // 自动检测框架类型
            String framework = detectFramework(files);
            Map<String, Object> config = new HashMap<>();
            config.put("framework", framework);
            config.put("buildCommand", getBuildCommand(framework));
            config.put("outputDirectory", getOutputDirectory(framework));
            deployRequest.put("config", config);

            // 调用EdgeOne Pages MCP服务器
            String deployUrl = "https://mcp-on-edge.edgeone.app/mcp-server";

            HttpResponse response = HttpRequest.post(deployUrl)
                    .header("Authorization", "Bearer " + apiToken)
                    .header("Content-Type", "application/json")
                    .body(JSONUtil.toJsonStr(deployRequest))
                    // 5分钟超时，文件夹部署可能需要构建
                    .timeout(300000)
                    .execute();

            if (response.isOk()) {
                JSONObject result = JSONUtil.parseObj(response.body());

                Map<String, Object> responseData = new HashMap<>();
                responseData.put("success", true);
                responseData.put("deployment_id", result.getStr("deploymentId"));
                responseData.put("project_name", result.getStr("projectName"));
                responseData.put("url", result.getStr("url"));
                responseData.put("preview_url", result.getStr("previewUrl"));
                responseData.put("status", "building");
                responseData.put("framework", framework);
                responseData.put("message", "文件夹已提交部署，正在构建中");

                return McpResponse.success(responseData);
            } else {
                return McpResponse.error("文件夹部署失败: " + response.body());
            }

        } catch (Exception e) {
            log.error("EdgeOne Pages文件夹部署异常", e);
            return McpResponse.error("文件夹部署异常: " + e.getMessage());
        }
    }

    /**
     * 读取文件夹内容
     */
    private Map<String, String> readFolderContents(String folderPath) {
        Map<String, String> files = new HashMap<>();
        try {
            // 这里需要实现文件夹读取逻辑
            // 由于安全考虑，实际实现中可能需要限制可访问的路径
            log.warn("文件夹读取功能需要根据实际安全策略实现: {}", folderPath);

            // 示例实现（实际使用时需要根据安全策略调整）
            java.io.File folder = new java.io.File(folderPath);
            if (folder.exists() && folder.isDirectory()) {
                readFolderRecursive(folder, folder.getAbsolutePath(), files);
            }
        } catch (Exception e) {
            log.error("读取文件夹失败: {}", folderPath, e);
        }
        return files;
    }

    /**
     * 递归读取文件夹
     */
    private void readFolderRecursive(java.io.File folder, String basePath, Map<String, String> files) {
        java.io.File[] fileList = folder.listFiles();
        if (fileList != null) {
            for (java.io.File file : fileList) {
                if (file.isDirectory()) {
                    readFolderRecursive(file, basePath, files);
                } else {
                    try {
                        String relativePath = file.getAbsolutePath().substring(basePath.length() + 1);
                        String content = java.nio.file.Files.readString(file.toPath());
                        files.put(relativePath, content);
                    } catch (Exception e) {
                        log.warn("读取文件失败: {}", file.getAbsolutePath(), e);
                    }
                }
            }
        }
    }

    /**
     * 检测框架类型
     */
    private String detectFramework(Map<String, String> files) {
        if (files.containsKey("package.json")) {
            String packageJson = files.get("package.json");
            if (packageJson.contains("\"react\"")) return "react";
            if (packageJson.contains("\"vue\"")) return "vue";
            if (packageJson.contains("\"next\"")) return "nextjs";
            if (packageJson.contains("\"nuxt\"")) return "nuxtjs";
            return "nodejs";
        }
        if (files.containsKey("index.html")) {
            return "static";
        }
        return "static";
    }

    /**
     * 获取构建命令
     */
    private String getBuildCommand(String framework) {
        return switch (framework) {
            case "react", "vue" -> "npm run build";
            case "nextjs" -> "npm run build && npm run export";
            case "nuxtjs" -> "npm run generate";
            default -> "";
        };
    }

    /**
     * 获取输出目录
     */
    private String getOutputDirectory(String framework) {
        return switch (framework) {
            case "react" -> "build";
            case "vue" -> "dist";
            case "nextjs" -> "out";
            case "nuxtjs" -> "dist";
            default -> ".";
        };
    }



    /**
     * 获取MCP服务器支持的工具列表
     */
    private McpResponse listMcpTools(String serverUrl) {
        try {
            Map<String, Object> mcpRequest = new HashMap<>();
            mcpRequest.put("jsonrpc", "2.0");
            mcpRequest.put("id", System.currentTimeMillis());
            mcpRequest.put("method", "tools/list");
            mcpRequest.put("params", new HashMap<>());

            HttpResponse response = HttpRequest.post(serverUrl)
                    .header("Content-Type", "application/json")
                    .body(JSONUtil.toJsonStr(mcpRequest))
                    .timeout(30000)
                    .execute();

            if (response.isOk()) {
                log.info("MCP工具列表响应: {}", response.body());
                return McpResponse.success(JSONUtil.parseObj(response.body()));
            } else {
                log.error("获取MCP工具列表失败: {}", response.body());
                return McpResponse.error("获取工具列表失败: " + response.body());
            }
        } catch (Exception e) {
            log.error("获取MCP工具列表异常", e);
            return McpResponse.error("获取工具列表异常: " + e.getMessage());
        }
    }

    /**
     * 处理参数引用替换
     * 替换字符串中的 ${key} 占位符为实际值
     */
    private String processParameterReferences(String content, Map<String, Object> parameters) {
        if (StrUtil.isBlank(content) || !content.contains("${")) {
            return content;
        }

        String result = content;

        // 处理简单的参数替换 ${parameterName}
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("\\$\\{([^}]+)\\}");
        java.util.regex.Matcher matcher = pattern.matcher(content);

        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            String reference = matcher.group(1);
            String replacement = resolveParameterReference(reference, parameters);

            if (replacement != null) {
                matcher.appendReplacement(sb, java.util.regex.Matcher.quoteReplacement(replacement));
                log.debug("参数替换: ${} -> {}", reference, replacement.length() > 100 ?
                    replacement.substring(0, 100) + "..." : replacement);
            } else {
                log.warn("无法解析参数引用: ${}", reference);
                matcher.appendReplacement(sb, java.util.regex.Matcher.quoteReplacement(matcher.group()));
            }
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 解析单个参数引用
     */
    private String resolveParameterReference(String reference, Map<String, Object> parameters) {
        // 处理直接参数引用
        if (parameters.containsKey(reference)) {
            Object value = parameters.get(reference);
            return value != null ? String.valueOf(value) : null;
        }

        // 处理嵌套引用 stepId.result.field
        String[] parts = reference.split("\\.");
        if (parts.length >= 2) {
            String stepId = parts[0];
            String field = parts[1];

            // 查找步骤结果
            //String stepResultKey = stepId ;
            if (parameters.containsKey(stepId)) {
                Object stepResult = parameters.get(stepId);

                if (stepResult instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> resultMap = (Map<String, Object>) stepResult;

                    if ("result".equals(field) && parts.length > 2) {
                        // stepId.result.field 格式
                        String targetField = parts[2];
                        Object fieldValue = resultMap.get(targetField);
                        return fieldValue != null ? String.valueOf(fieldValue) : null;
                    } else {
                        // stepId.field 格式
                        Object fieldValue = resultMap.get(field);
                        return fieldValue != null ? String.valueOf(fieldValue) : null;
                    }
                } else if (stepResult instanceof java.util.List) {
                    // 处理数组类型的结果
                    @SuppressWarnings("unchecked")
                    java.util.List<Object> resultList = (java.util.List<Object>) stepResult;

                    if ("result".equals(field) && parts.length > 2) {
                        // stepId.result.index 或 stepId.result.field 格式
                        String targetField = parts[2];
                        return handleArrayFieldAccess(resultList, targetField);
                    } else {
                        // stepId.index 或 stepId.field 格式
                        return handleArrayFieldAccess(resultList, field);
                    }
                } else if (stepResult.getClass().isArray()) {
                    // 处理原生数组类型
                    Object[] resultArray = (Object[]) stepResult;
                    java.util.List<Object> resultList = java.util.Arrays.asList(resultArray);

                    if ("result".equals(field) && parts.length > 2) {
                        String targetField = parts[2];
                        return handleArrayFieldAccess(resultList, targetField);
                    } else {
                        return handleArrayFieldAccess(resultList, field);
                    }
                }

                return stepResult != null ? String.valueOf(stepResult) : null;
            }

            // 尝试直接查找 stepId.field 格式的参数
            String directKey = stepId + "." + field;
            if (parameters.containsKey(directKey)) {
                Object value = parameters.get(directKey);
                return value != null ? String.valueOf(value) : null;
            }
        }

        return null;
    }

    /**
     * 处理数组字段访问
     * 支持索引访问和字段访问
     */
    private String handleArrayFieldAccess(java.util.List<Object> resultList, String field) {
        if (resultList == null || resultList.isEmpty()) {
            return null;
        }

        try {
            // 尝试作为数组索引解析
            int index = Integer.parseInt(field);
            if (index >= 0 && index < resultList.size()) {
                Object item = resultList.get(index);
                return item != null ? String.valueOf(item) : null;
            }
        } catch (NumberFormatException e) {
            // 不是数字索引，尝试作为字段名处理
        }

        // 特殊字段处理
        switch (field) {
            case "length", "size" -> {
                return String.valueOf(resultList.size());
            }
            case "first" -> {
                Object first = resultList.get(0);
                return first != null ? String.valueOf(first) : null;
            }
            case "last" -> {
                Object last = resultList.get(resultList.size() - 1);
                return last != null ? String.valueOf(last) : null;
            }
            default -> {
                // 尝试在数组的每个元素中查找字段
                for (Object item : resultList) {
                    if (item instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> itemMap = (Map<String, Object>) item;
                        if (itemMap.containsKey(field)) {
                            Object value = itemMap.get(field);
                            return value != null ? String.valueOf(value) : null;
                        }
                    }
                }
            }
        }

        return null;
    }

    /**
     * 从EdgeOne Pages URL中提取部署ID
     */
    private String extractDeploymentId(String url) {
        if (StrUtil.isBlank(url)) {
            return null;
        }

        try {
            // URL格式: https://mcp.edgeone.site/share/MKoopnZ-itHewysi5LbKv
            // 提取最后的部分作为部署ID
            String[] parts = url.split("/");
            if (parts.length > 0) {
                return parts[parts.length - 1];
            }
        } catch (Exception e) {
            log.warn("提取部署ID失败: {}", url, e);
        }

        return "unknown";
    }

    /**
     * 从EdgeOne Pages URL中提取项目名称
     */
    private String extractProjectName(String url) {
        if (StrUtil.isBlank(url)) {
            return null;
        }

        try {
            // 从URL中提取项目名称，如果无法提取则使用时间戳
            String deploymentId = extractDeploymentId(url);
            return "edgeone-project-" + deploymentId;
        } catch (Exception e) {
            log.warn("提取项目名称失败: {}", url, e);
        }

        return "edgeone-project-" + System.currentTimeMillis();
    }

    /**
     * 处理外部MCP工具调用（兼容旧接口）
     */
    @Deprecated
    public McpResponse handleExternalToolCall(Object service, String toolName, Map<String, Object> parameters) {
        log.warn("使用了已废弃的方法 handleExternalToolCall，请使用 handleToolCall");
        return McpResponse.error("方法已废弃");
    }
}
