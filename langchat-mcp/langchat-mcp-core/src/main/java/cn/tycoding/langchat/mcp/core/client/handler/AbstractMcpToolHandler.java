/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.client.handler;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.tycoding.langchat.mcp.core.protocol.McpResponse;
import cn.tycoding.langchat.mcp.core.entity.AigcMcpService;
import cn.tycoding.langchat.mcp.core.service.AigcMcpServiceService;
import cn.tycoding.langchat.mcp.core.utils.McpParameterExtractor;
import cn.tycoding.langchat.mcp.core.utils.McpParameterUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

import java.sql.Wrapper;
import java.util.HashMap;
import java.util.Map;

/**
 * MCP工具处理器抽象基类
 * 
 * <AUTHOR>
 * @since 2024/12/20
 */
@Slf4j
public abstract class AbstractMcpToolHandler {

    @Autowired
    protected McpParameterExtractor parameterExtractor;
    @Autowired
    private AigcMcpServiceService mcpServiceService;

    /**
     * 处理工具调用
     */
    public McpResponse handleToolCall(String serviceName, String toolName, Map<String, Object> parameters) {
        return handleToolCall(serviceName, toolName, parameters, null,null);
    }

    /**
     * 处理工具调用（支持用户输入参数提取）
     */
    public McpResponse handleToolCall(String serviceName, String toolName, Map<String, Object> parameters, String userInput,String modelId) {
        try {
            // 获取服务配置
            AigcMcpService service = getServiceConfig(serviceName);
            if (service == null) {
                return McpResponse.error("服务未找到: " + serviceName);
            }

            if (!service.getEnabled()) {
                return McpResponse.error("服务已禁用: " + serviceName);
            }

            // 获取服务参数配置
            Map<String, Object> serviceParams = getServiceParameters(service);
            
            // 从用户输入中提取参数（如果提供了用户输入）
            Map<String, Object> extractedParams = new HashMap<>();
            if (StrUtil.isNotBlank(userInput)) {
                var extractionConfigs = parameterExtractor.parseExtractionConfigs(service.getParameters());
                extractedParams = parameterExtractor.extractParametersFromInput(userInput, extractionConfigs,modelId);
                log.debug("从用户输入提取的参数: {}", extractedParams);
            }
            
            // 合并参数：应用配置 + 提取的参数 + 工具参数
            Map<String, Object> finalParams = parameterExtractor.mergeParameters(extractedParams, serviceParams, parameters);

            // 调用具体的处理方法
            return handleSpecificTool(service, toolName, extractedParams, finalParams);

        } catch (Exception e) {
            log.error("工具调用失败: 服务={}, 工具={}", serviceName, toolName, e);
            return McpResponse.error("工具调用异常: " + e.getMessage());
        }
    }

    /**
     * 处理具体的工具调用（由子类实现）
     */
    protected abstract McpResponse handleSpecificTool(AigcMcpService service, String toolName, 
                                                     Map<String, Object> parameters, 
                                                     Map<String, Object> serviceParams);

    /**
     * 获取服务配置
     */
    protected AigcMcpService getServiceConfig(String serviceName) {
        try {
            return mcpServiceService.getOne(Wrappers.<AigcMcpService>lambdaQuery()
                    .eq(AigcMcpService::getName, serviceName));
        } catch (Exception e) {
            log.error("获取服务配置失败: {}", serviceName, e);
            return null;
        }
    }

    /**
     * 获取服务参数配置
     */
    protected Map<String, Object> getServiceParameters(AigcMcpService service) {
        if (StrUtil.isBlank(service.getParameterValues())) {
            return new HashMap<>();
        }

        try {
            return JSONUtil.parseObj(service.getParameterValues()).toBean(Map.class);
        } catch (Exception e) {
            log.error("解析服务参数失败: {}", service.getName(), e);
            return new HashMap<>();
        }
    }

    /**
     * 验证必需参数
     */
    protected boolean validateRequiredParameters(Map<String, Object> parameters, String... requiredParams) {
        for (String param : requiredParams) {
            if (!parameters.containsKey(param) || parameters.get(param) == null) {
                log.warn("缺少必需参数: {}", param);
                return false;
            }
            
            if (parameters.get(param) instanceof String && StrUtil.isBlank((String) parameters.get(param))) {
                log.warn("必需参数为空: {}", param);
                return false;
            }
        }
        return true;
    }

    /**
     * 创建参数验证错误响应
     */
    protected McpResponse createValidationError(String... missingParams) {
        return McpResponse.error("缺少必需参数: " + String.join(", ", missingParams));
    }

    /**
     * 获取参数值（带默认值）
     */
    protected <T> T getParameterValue(Map<String, Object> parameters, String key, Class<T> type, T defaultValue) {
        return McpParameterUtils.getParameterValue(parameters, key, type, defaultValue);
    }

    /**
     * 记录工具调用日志
     */
    protected void logToolCall(String toolName, Map<String, Object> parameters) {
        log.info("执行工具调用: 工具={}, 参数={}", toolName, parameters);
    }

    /**
     * 记录工具调用结果
     */
    protected void logToolResult(String toolName, boolean success, String message) {
        if (success) {
            log.info("工具调用成功: 工具={}", toolName);
        } else {
            log.error("工具调用失败: 工具={}, 错误={}", toolName, message);
        }
    }
}
