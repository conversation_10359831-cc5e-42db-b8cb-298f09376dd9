/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.protocol;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * MCP请求协议
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
@Data
public class McpRequest {

    /**
     * JSON-RPC版本
     */
    private String jsonrpc = "2.0";

    /**
     * 请求ID
     */
    private String id;

    /**
     * 方法名
     */
    private String method;

    /**
     * 参数
     */
    private Map<String, Object> params;

    /**
     * 元数据
     */
    private Map<String, Object> meta;

    public static McpRequest create(String id, String method, Map<String, Object> params) {
        McpRequest request = new McpRequest();
        request.setId(id);
        request.setMethod(method);
        request.setParams(params);
        return request;
    }

    public static McpRequest toolCall(String id, String toolName, Map<String, Object> arguments) {
        Map<String, Object> params = Map.of(
            "name", toolName,
            "arguments", arguments
        );
        return create(id, "tools/call", params);
    }

    public static McpRequest listTools(String id) {
        return create(id, "tools/list", Map.of());
    }

    public static McpRequest listResources(String id) {
        return create(id, "resources/list", Map.of());
    }

    public static McpRequest getResource(String id, String uri) {
        Map<String, Object> params = Map.of("uri", uri);
        return create(id, "resources/read", params);
    }

    public static McpRequest listPrompts(String id) {
        return create(id, "prompts/list", Map.of());
    }

    public static McpRequest getPrompt(String id, String name, Map<String, Object> arguments) {
        Map<String, Object> params = Map.of(
            "name", name,
            "arguments", arguments != null ? arguments : Map.of()
        );
        return create(id, "prompts/get", params);
    }

    public static McpRequest initialize(String id, String clientName, String clientVersion) {
        Map<String, Object> params = Map.of(
            "protocolVersion", "2024-11-05",
            "capabilities", Map.of(
                "tools", Map.of(),
                "resources", Map.of(),
                "prompts", Map.of()
            ),
            "clientInfo", Map.of(
                "name", clientName,
                "version", clientVersion
            )
        );
        return create(id, "initialize", params);
    }

    public static McpRequest ping(String id) {
        return create(id, "ping", Map.of());
    }
}
