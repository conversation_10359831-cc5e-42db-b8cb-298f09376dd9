/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.agent;

import cn.tycoding.langchat.mcp.core.protocol.McpClient;
import cn.tycoding.langchat.mcp.core.protocol.McpService;
import cn.tycoding.langchat.mcp.core.protocol.McpTool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 工具选择器
 * 智能选择最适合的MCP服务和工具来完成任务
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ToolSelector {

    private final McpClient mcpClient;
    
    // 工具类别映射
    private static final Map<String, List<String>> TOOL_CATEGORY_MAPPING;

    static {
        Map<String, List<String>> mapping = new HashMap<>();
        mapping.put("search", List.of("web_search", "search_engine", "google_search", "bing_search"));
        mapping.put("email", List.of("send_email", "email_send", "mail_send"));
        mapping.put("file", List.of("file_operation", "read_file", "write_file", "file_manager"));
        mapping.put("database", List.of("execute_query", "db_query", "sql_execute"));
        mapping.put("api", List.of("http_request", "api_call", "rest_call"));
        mapping.put("calendar", List.of("create_event", "schedule_meeting", "calendar_add"));
        mapping.put("document", List.of("create_document", "edit_document", "pdf_generate"));
        mapping.put("image", List.of("text_to_image", "generate_image", "edit_image", "image_process"));
        mapping.put("translation", List.of("translate_text", "language_translate"));
        mapping.put("weather", List.of("get_weather", "weather_forecast"));
        mapping.put("reasoning", List.of("sequential_think", "think", "reason", "analyze"));
        mapping.put("code", List.of("search_repositories", "get_repository_info", "code_search", "github_search"));
        TOOL_CATEGORY_MAPPING = Collections.unmodifiableMap(mapping);
    }

    /**
     * 选择最佳服务
     */
    public String selectBestService(String category, String context) {
        List<ServiceScore> candidates = new ArrayList<>();
        
        // 获取所有注册的服务
        Map<String, McpService> services = mcpClient.getRegisteredServices();
        
        for (Map.Entry<String, McpService> entry : services.entrySet()) {
            String serviceName = entry.getKey();
            McpService service = entry.getValue();
            
            // 检查服务健康状态
            if (!mcpClient.isServiceHealthy(serviceName)) {
                continue;
            }
            
            // 计算服务匹配分数
            double score = calculateServiceScore(service, category, context);
            if (score > 0) {
                candidates.add(new ServiceScore(serviceName, score));
            }
        }
        
        // 按分数排序，选择最高分的服务
        return candidates.stream()
                .max(Comparator.comparingDouble(ServiceScore::getScore))
                .map(ServiceScore::getServiceName)
                .orElse(null);
    }

    /**
     * 选择最佳工具
     */
    public McpTool selectBestTool(String serviceName, String category, String context) {
        List<McpTool> tools = mcpClient.getAvailableTools(serviceName);
        if (tools.isEmpty()) {
            return null;
        }
        
        List<ToolScore> candidates = new ArrayList<>();
        
        for (McpTool tool : tools) {
            double score = calculateToolScore(tool, category, context);
            if (score > 0) {
                candidates.add(new ToolScore(tool, score));
            }
        }
        
        return candidates.stream()
                .max(Comparator.comparingDouble(ToolScore::getScore))
                .map(ToolScore::getTool)
                .orElse(null);
    }

    /**
     * 获取推荐工具列表
     */
    public List<RecommendedTool> getRecommendedTools(String userIntent) {
        List<RecommendedTool> recommendations = new ArrayList<>();
        
        // 分析用户意图，提取关键词
        Set<String> keywords = extractKeywords(userIntent);
        
        // 遍历所有服务和工具
        for (Map.Entry<String, McpService> entry : mcpClient.getRegisteredServices().entrySet()) {
            String serviceName = entry.getKey();
            List<McpTool> tools = mcpClient.getAvailableTools(serviceName);
            
            for (McpTool tool : tools) {
                double relevanceScore = calculateRelevanceScore(tool, keywords, userIntent);
                if (relevanceScore > 0.3) { // 阈值过滤
                    recommendations.add(RecommendedTool.builder()
                            .serviceName(serviceName)
                            .tool(tool)
                            .relevanceScore(relevanceScore)
                            .reason(generateRecommendationReason(tool, keywords))
                            .build());
                }
            }
        }
        
        // 按相关性分数排序
        return recommendations.stream()
                .sorted(Comparator.comparingDouble(RecommendedTool::getRelevanceScore).reversed())
                .limit(10) // 限制推荐数量
                .collect(Collectors.toList());
    }

    /**
     * 计算服务匹配分数
     */
    private double calculateServiceScore(McpService service, String category, String context) {
        double score = 0.0;
        
        // 基础分数
        score += 1.0;
        
        // 服务状态加分
        if (service.getStatus() == McpService.ServiceStatus.ACTIVE) {
            score += 2.0;
        }
        
        // 工具匹配度
        List<McpTool> tools = mcpClient.getAvailableTools(service.getName());
        List<String> expectedTools = TOOL_CATEGORY_MAPPING.get(category);
        
        if (expectedTools != null && !tools.isEmpty()) {
            long matchCount = tools.stream()
                    .mapToLong(tool -> expectedTools.stream()
                            .mapToLong(expected -> tool.getName().toLowerCase().contains(expected.toLowerCase()) ? 1 : 0)
                            .sum())
                    .sum();
            
            score += (double) matchCount / expectedTools.size() * 3.0;
        }
        
        // 服务描述匹配
        if (service.getDescription() != null && category != null) {
            if (service.getDescription().toLowerCase().contains(category.toLowerCase())) {
                score += 1.0;
            }
        }
        
        return score;
    }

    /**
     * 计算工具匹配分数
     */
    private double calculateToolScore(McpTool tool, String category, String context) {
        double score = 0.0;
        
        // 基础分数
        score += 1.0;
        
        // 工具优先级
        score += tool.getPriority() * 0.1;
        
        // 名称匹配
        List<String> expectedTools = TOOL_CATEGORY_MAPPING.get(category);
        if (expectedTools != null) {
            for (String expected : expectedTools) {
                if (tool.getName().toLowerCase().contains(expected.toLowerCase())) {
                    score += 2.0;
                    break;
                }
            }
        }
        
        // 描述匹配
        if (tool.getDescription() != null && context != null) {
            String[] contextWords = context.toLowerCase().split("\\s+");
            String description = tool.getDescription().toLowerCase();
            
            for (String word : contextWords) {
                if (description.contains(word)) {
                    score += 0.5;
                }
            }
        }
        
        return score;
    }

    /**
     * 提取关键词
     */
    private Set<String> extractKeywords(String text) {
        if (text == null) return Set.of();
        
        return Arrays.stream(text.toLowerCase().split("\\s+"))
                .filter(word -> word.length() > 2)
                .collect(Collectors.toSet());
    }

    /**
     * 计算相关性分数
     */
    private double calculateRelevanceScore(McpTool tool, Set<String> keywords, String userIntent) {
        double score = 0.0;
        
        // 工具名称匹配
        String toolName = tool.getName().toLowerCase();
        for (String keyword : keywords) {
            if (toolName.contains(keyword)) {
                score += 1.0;
            }
        }
        
        // 工具描述匹配
        if (tool.getDescription() != null) {
            String description = tool.getDescription().toLowerCase();
            for (String keyword : keywords) {
                if (description.contains(keyword)) {
                    score += 0.5;
                }
            }
        }
        
        // 标签匹配
        if (tool.getTags() != null) {
            for (String tag : tool.getTags()) {
                for (String keyword : keywords) {
                    if (tag.toLowerCase().contains(keyword)) {
                        score += 0.3;
                    }
                }
            }
        }
        
        return Math.min(score, 5.0); // 限制最大分数
    }

    /**
     * 生成推荐理由
     */
    private String generateRecommendationReason(McpTool tool, Set<String> keywords) {
        StringBuilder reason = new StringBuilder();
        
        reason.append("推荐理由: ");
        
        if (tool.getDescription() != null) {
            reason.append(tool.getDescription());
        }
        
        if (tool.getTags() != null && !tool.getTags().isEmpty()) {
            reason.append(" (标签: ").append(String.join(", ", tool.getTags())).append(")");
        }
        
        return reason.toString();
    }

    // 内部类
    private static class ServiceScore {
        private final String serviceName;
        private final double score;
        
        public ServiceScore(String serviceName, double score) {
            this.serviceName = serviceName;
            this.score = score;
        }
        
        public String getServiceName() { return serviceName; }
        public double getScore() { return score; }
    }
    
    private static class ToolScore {
        private final McpTool tool;
        private final double score;
        
        public ToolScore(McpTool tool, double score) {
            this.tool = tool;
            this.score = score;
        }
        
        public McpTool getTool() { return tool; }
        public double getScore() { return score; }
    }
}
