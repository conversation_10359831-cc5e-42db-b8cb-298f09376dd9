/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.agent;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 执行计划
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
@Data
@Builder
public class ExecutionPlan {
    
    /**
     * 执行阶段列表
     */
    private List<ExecutionStage> stages;
    
    /**
     * 总任务数
     */
    private int totalTasks;
    
    /**
     * 预估执行时间（秒）
     */
    private int estimatedDuration;
    
    /**
     * 计划创建时间
     */
    @Builder.Default
    private long createdTime = System.currentTimeMillis();
    
    /**
     * 计划状态
     */
    @Builder.Default
    private PlanStatus status = PlanStatus.CREATED;
    
    public enum PlanStatus {
        CREATED,    // 已创建
        EXECUTING,  // 执行中
        COMPLETED,  // 已完成
        FAILED,     // 失败
        CANCELLED   // 已取消
    }
}
