package cn.tycoding.langchat.mcp.core.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Description:
 * @date 2025年06月21日 14:59
 */
@Getter
@AllArgsConstructor
public enum RiskPublicDataServiceEnum {
    /**
     * 应收分页
     */
    LIST_PYABLE_ORDER("/riskmana/web-back/enterprise-order/payable/list", "应收分页"),
    /**
     * 结算分页
     */
    LIST_SETTLEMENT_ORDER("/riskmana/web-back/enterprise-order/settlement/list", "结算分页"),
    /**
     * 企业工商详情
     */
    SUPPLIER_CUSTOMER("/blade-resource/web-back/outer/business-info/supplier-customer", "企业工商详情"),
    /**
     * 合作风险
     */
    BUSINESS_INFO_COOPERATION_RISK("/blade-resource/web-back/outer/business-info/cooperationRisk", "合作风险"),
    /**
     * 对外担保
     */
    BUSINESS_INFO_GUARANTEES("/blade-resource/web-back/outer/business-info/getGuarantees", "对外担保"),
    /**
     * 税务原始数据
     */
    GET_ORIGINAL_TAX_MERGE("/blade-resource/web-back/outer/business-info/original/tax/merge", "税务原始数据");
    /**
     * 路径
     */
    private final String path;
    /**
     * 描述
     */
    private final String desc;
}
