/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.orchestration.dto;

import cn.tycoding.langchat.mcp.core.protocol.McpResponse;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Map;

/**
 * 步骤执行结果
 * 
 * <AUTHOR>
 * @since 2025/06/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StepExecutionResult {

    /**
     * 步骤ID
     */
    private String stepId;

    /**
     * 服务名称
     */
    private String serviceName;

    /**
     * 工具名称
     */
    private String toolName;

    /**
     * 步骤描述
     */
    private String description;

    /**
     * 执行是否成功
     */
    private boolean success;

    /**
     * 是否被跳过
     */
    private boolean skipped;

    /**
     * 执行结果数据
     */
    private Object result;

    /**
     * 错误信息（失败时）
     */
    private String error;

    /**
     * 执行开始时间
     */
    private Long startTime;

    /**
     * 执行结束时间
     */
    private Long endTime;

    /**
     * 执行时长（毫秒）
     */
    private Long duration;

    /**
     * 输入参数
     */
    private Map<String, Object> parameters;

    /**
     * 依赖的步骤ID列表
     */
    private java.util.List<String> dependencies;

    /**
     * 额外的元数据
     */
    private Map<String, Object> metadata;

    /**
     * 从McpResponse创建成功结果
     */
    public static StepExecutionResult fromMcpResponse(String stepId, String serviceName, String toolName,
                                                     String description, McpResponse response,
                                                     Map<String, Object> parameters,
                                                     java.util.List<String> dependencies) {
        StepExecutionResult result = StepExecutionResult.builder()
                .stepId(stepId)
                .serviceName(serviceName)
                .toolName(toolName)
                .description(description)
                .success(response.isSuccess())
                .skipped(false)
                .parameters(parameters)
                .dependencies(dependencies)
                .endTime(System.currentTimeMillis())
                .build();

        if (response.isSuccess()) {
            result.setResult(response.getResult());
        } else {
            result.setError(response.getError()==null? "未知错误": response.getError().getMessage());
        }

        return result;
    }

    /**
     * 创建跳过的步骤结果
     */
    public static StepExecutionResult skipped(String stepId, String serviceName, String toolName,
                                            String description, String reason,
                                            Map<String, Object> parameters,
                                            java.util.List<String> dependencies) {
        return StepExecutionResult.builder()
                .stepId(stepId)
                .serviceName(serviceName)
                .toolName(toolName)
                .description(description)
                .success(false)
                .skipped(true)
                .error(reason)
                .parameters(parameters)
                .dependencies(dependencies)
                .startTime(System.currentTimeMillis())
                .endTime(System.currentTimeMillis())
                .duration(0L)
                .build();
    }

    /**
     * 创建失败的步骤结果
     */
    public static StepExecutionResult failed(String stepId, String serviceName, String toolName,
                                           String description, String error,
                                           Map<String, Object> parameters,
                                           java.util.List<String> dependencies) {
        return StepExecutionResult.builder()
                .stepId(stepId)
                .serviceName(serviceName)
                .toolName(toolName)
                .description(description)
                .success(false)
                .skipped(false)
                .error(error)
                .parameters(parameters)
                .dependencies(dependencies)
                .endTime(System.currentTimeMillis())
                .build();
    }

    /**
     * 设置开始时间
     */
    public void markStarted() {
        this.startTime = System.currentTimeMillis();
    }

    /**
     * 设置结束时间并计算时长
     */
    public void markCompleted() {
        this.endTime = System.currentTimeMillis();
        if (this.startTime != null) {
            this.duration = this.endTime - this.startTime;
        }
    }

    /**
     * 获取执行状态描述
     */
    public String getStatusDescription() {
        if (skipped) {
            return "跳过";
        } else if (success) {
            return "成功";
        } else {
            return "失败";
        }
    }

    /**
     * 获取执行摘要
     */
    public String getExecutionSummary() {
        String status = getStatusDescription();
        String durationStr = duration != null ? String.format(" (耗时: %dms)", duration) : "";
        
        if (skipped) {
            return String.format("%s - %s: %s%s", stepId, status, error, durationStr);
        } else if (success) {
            return String.format("%s - %s%s", stepId, status, durationStr);
        } else {
            return String.format("%s - %s: %s%s", stepId, status, error, durationStr);
        }
    }

    /**
     * 是否有结果数据
     */
    public boolean hasResult() {
        return result != null;
    }

    /**
     * 获取结果数据的字符串表示
     */
    public String getResultAsString() {
        if (result == null) {
            return null;
        }
        return result.toString();
    }
}
