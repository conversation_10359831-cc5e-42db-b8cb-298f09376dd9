/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import cn.tycoding.langchat.mcp.core.entity.AigcMcpService;
import cn.tycoding.langchat.mcp.core.protocol.McpRequest;
import cn.tycoding.langchat.mcp.core.protocol.McpResponse;
import cn.tycoding.langchat.mcp.core.protocol.McpTool;
import cn.tycoding.langchat.mcp.core.service.AigcMcpServiceService;
import cn.tycoding.langchat.mcp.core.service.McpServiceManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * MCP服务管理器实现
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class McpServiceManagerImpl implements McpServiceManager {

    private final AigcMcpServiceService mcpServiceService;
    
    // 服务缓存
    private final Map<String, AigcMcpService> serviceCache = new ConcurrentHashMap<>();

    @Override
    public void registerService(AigcMcpService service) {
        log.info("注册MCP服务: {}", service.getName());
        serviceCache.put(service.getName(), service);
        mcpServiceService.save(service);
    }

    @Override
    public void unregisterService(String serviceName) {
        log.info("注销MCP服务: {}", serviceName);
        serviceCache.remove(serviceName);
        AigcMcpService service = mcpServiceService.findByName(serviceName);
        if (service != null) {
            service.setEnabled(false);
            mcpServiceService.updateById(service);
        }
    }

    @Override
    public AigcMcpService getService(String serviceName) {
        AigcMcpService service = serviceCache.get(serviceName);
        if (service == null) {
            service = mcpServiceService.findByName(serviceName);
            if (service != null && service.getEnabled()) {
                serviceCache.put(serviceName, service);
            }
        }
        return service;
    }

    @Override
    public List<AigcMcpService> getEnabledServices() {
        return mcpServiceService.list().stream()
                .filter(AigcMcpService::getEnabled)
                .collect(Collectors.toList());
    }

    @Override
    public List<AigcMcpService> getServicesByCategory(String category) {
        return mcpServiceService.list().stream()
                .filter(service -> service.getEnabled() && category.equals(service.getCategory()))
                .collect(Collectors.toList());
    }

    @Override
    public boolean isServiceAvailable(String serviceName) {
        AigcMcpService service = getService(serviceName);
        return service != null && service.getEnabled();
    }

    @Override
    public List<McpTool> getServiceTools(String serviceName) {
        AigcMcpService service = getService(serviceName);
        if (service == null || StrUtil.isBlank(service.getTools())) {
            return Collections.emptyList();
        }
        
        try {
            return JSONUtil.toList(service.getTools(), McpTool.class);
        } catch (Exception e) {
            log.error("解析服务工具失败: {}", serviceName, e);
            return Collections.emptyList();
        }
    }

    @Override
    public McpResponse callTool(String serviceName, String toolName, Map<String, Object> parameters) {
        return callTool(serviceName, toolName, parameters, null, null);
    }

    @Override
    public McpResponse callTool(String serviceName, String toolName, Map<String, Object> parameters, 
                               String userId, String conversationId) {
        try {
            AigcMcpService service = getService(serviceName);
            if (service == null) {
                return McpResponse.error( -32000, "服务不存在: " + serviceName);
            }

            if (!service.getEnabled()) {
                return McpResponse.error( -32007, "服务已禁用: " + serviceName);
            }

            // 构建MCP请求
            McpRequest request = McpRequest.toolCall(
                String.valueOf(System.currentTimeMillis()),
                toolName,
                parameters
            );

            return sendRequest(serviceName, request);
            
        } catch (Exception e) {
            log.error("调用MCP工具失败: 服务={}, 工具={}", serviceName, toolName, e);
            return McpResponse.error( -32001, "工具调用异常: " + e.getMessage());
        }
    }

    @Override
    public McpResponse sendRequest(String serviceName, McpRequest request) {
        AigcMcpService service = getService(serviceName);
        if (service == null) {
            return McpResponse.error(request.getId(), -32000, "服务不存在: " + serviceName);
        }

        try {
            // 根据服务类型处理请求
            return switch (service.getType().toUpperCase()) {
                case "HTTP" -> handleHttpRequest(service, request);
                case "WEBSOCKET" -> handleWebSocketRequest(service, request);
                case "GRPC" -> handleGrpcRequest(service, request);
                default -> McpResponse.error(request.getId(), -32601, "不支持的服务类型: " + service.getType());
            };
        } catch (Exception e) {
            log.error("发送MCP请求失败: 服务={}", serviceName, e);
            return McpResponse.error(request.getId(), -32603, "请求处理异常: " + e.getMessage());
        }
    }

    private McpResponse handleHttpRequest(AigcMcpService service, McpRequest request) {
        try {
            HttpRequest httpRequest = HttpRequest.post(service.getEndpoint())
                    .header("Content-Type", "application/json")
                    .timeout(service.getTimeout() != null ? service.getTimeout() : 30000);

            // 添加认证信息
            addAuthentication(httpRequest, service);

            HttpResponse response = httpRequest.body(JSONUtil.toJsonStr(request)).execute();

            if (response.isOk()) {
                String responseBody = response.body();
                return JSONUtil.toBean(responseBody, McpResponse.class);
            } else {
                return McpResponse.error(request.getId(), -32603, "HTTP请求失败: " + response.getStatus());
            }
        } catch (Exception e) {
            log.error("HTTP请求处理失败", e);
            return McpResponse.error(request.getId(), -32603, "HTTP请求异常: " + e.getMessage());
        }
    }

    private McpResponse handleWebSocketRequest(AigcMcpService service, McpRequest request) {
        // WebSocket实现
        return McpResponse.error(request.getId(), -32601, "WebSocket暂未实现");
    }

    private McpResponse handleGrpcRequest(AigcMcpService service, McpRequest request) {
        // gRPC实现
        return McpResponse.error(request.getId(), -32601, "gRPC暂未实现");
    }

    private void addAuthentication(HttpRequest request, AigcMcpService service) {
        if (StrUtil.isBlank(service.getAuthType()) || "none".equals(service.getAuthType())) {
            return;
        }

        try {
            Map<String, Object> authConfig = JSONUtil.toBean(service.getAuthConfig(), Map.class);
            
            switch (service.getAuthType()) {
                case "api_key" -> {
                    String apiKey = (String) authConfig.get("apiKey");
                    if (StrUtil.isNotBlank(apiKey)) {
                        request.header("Authorization", "Bearer " + apiKey);
                    }
                }
                case "bearer" -> {
                    String token = (String) authConfig.get("token");
                    if (StrUtil.isNotBlank(token)) {
                        request.header("Authorization", "Bearer " + token);
                    }
                }
                case "basic" -> {
                    String username = (String) authConfig.get("username");
                    String password = (String) authConfig.get("password");
                    if (StrUtil.isNotBlank(username) && StrUtil.isNotBlank(password)) {
                        request.basicAuth(username, password);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("添加认证信息失败: {}", service.getName(), e);
        }
    }

    @Override
    public boolean healthCheck(String serviceName) {
        AigcMcpService service = getService(serviceName);
        if (service == null) {
            return false;
        }

        try {
            String healthUrl = StrUtil.isNotBlank(service.getHealthCheckUrl()) 
                ? service.getHealthCheckUrl() 
                : service.getEndpoint() + "/health";

            HttpResponse response = HttpRequest.get(healthUrl)
                    .timeout(5000)
                    .execute();

            boolean healthy = response.isOk();
            
            // 更新健康状态
            service.setHealthStatus(healthy ? "healthy" : "unhealthy");
            service.setLastHealthCheck(new Date());
            mcpServiceService.updateById(service);
            
            return healthy;
        } catch (Exception e) {
            log.warn("健康检查失败: {}", serviceName, e);
            service.setHealthStatus("unhealthy");
            service.setLastHealthCheck(new Date());
            mcpServiceService.updateById(service);
            return false;
        }
    }

    @Override
    public Map<String, Boolean> batchHealthCheck() {
        List<AigcMcpService> services = getEnabledServices();
        Map<String, Boolean> results = new HashMap<>();
        
        for (AigcMcpService service : services) {
            results.put(service.getName(), healthCheck(service.getName()));
        }
        
        return results;
    }

    @Override
    public void refreshService(String serviceName) {
        serviceCache.remove(serviceName);
        log.info("刷新服务配置: {}", serviceName);
    }

    @Override
    public void refreshAllServices() {
        serviceCache.clear();
        log.info("刷新所有服务配置");
    }

    @Override
    public void enableService(String serviceName) {
        AigcMcpService service = mcpServiceService.findByName(serviceName);
        if (service != null) {
            service.setEnabled(true);
            mcpServiceService.updateById(service);
            serviceCache.put(serviceName, service);
            log.info("启用服务: {}", serviceName);
        }
    }

    @Override
    public void disableService(String serviceName) {
        AigcMcpService service = mcpServiceService.findByName(serviceName);
        if (service != null) {
            service.setEnabled(false);
            mcpServiceService.updateById(service);
            serviceCache.remove(serviceName);
            log.info("禁用服务: {}", serviceName);
        }
    }

    @Override
    public Map<String, Object> getServiceStats(String serviceName) {
        // 实现服务统计
        Map<String, Object> stats = new HashMap<>();
        stats.put("serviceName", serviceName);
        stats.put("available", isServiceAvailable(serviceName));
        // 可以添加更多统计信息
        return stats;
    }

    @Override
    public Map<String, Object> getAllServicesStats() {
        Map<String, Object> stats = new HashMap<>();
        List<AigcMcpService> services = getEnabledServices();
        
        stats.put("totalServices", services.size());
        stats.put("enabledServices", services.stream().filter(AigcMcpService::getEnabled).count());
        stats.put("healthyServices", services.stream().filter(s -> "healthy".equals(s.getHealthStatus())).count());
        
        return stats;
    }
}
