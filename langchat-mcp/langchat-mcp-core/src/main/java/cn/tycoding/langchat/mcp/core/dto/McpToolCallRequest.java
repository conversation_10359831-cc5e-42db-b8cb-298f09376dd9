/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.dto;

import lombok.Data;

import java.util.Map;

/**
 * MCP工具调用请求DTO
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
@Data
public class McpToolCallRequest {

    /**
     * 服务名称
     */
    private String serviceName;

    /**
     * 工具名称
     */
    private String toolName;

    /**
     * 工具参数
     */
    private Map<String, Object> parameters;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 对话ID
     */
    private String conversationId;

    /**
     * 用户输入内容（用于参数提取）
     */
    private String userInput;

    /**
     * 是否异步执行
     */
    private Boolean async = false;

    /**
     * 超时时间（毫秒）
     */
    private Integer timeout;

    /**
     * 最大重试次数
     */
    private Integer maxRetries;

    /**
     * 是否需要确认
     */
    private Boolean requiresConfirmation = false;

    /**
     * 客户端IP
     */
    private String clientIp;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 备注信息
     */
    private String remark;
}
