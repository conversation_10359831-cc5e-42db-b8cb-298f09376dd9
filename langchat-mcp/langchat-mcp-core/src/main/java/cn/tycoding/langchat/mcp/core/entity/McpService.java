/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * MCP服务实体
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
@Data
@Accessors(chain = true)
@TableName("mcp_service")
public class McpService implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 服务名称（唯一标识）
     */
    private String name;

    /**
     * 显示名称
     */
    private String displayName;

    /**
     * 服务描述
     */
    private String description;

    /**
     * 服务版本
     */
    private String version;

    /**
     * 服务端点URL
     */
    private String endpoint;

    /**
     * 服务类型：HTTP, WEBSOCKET, GRPC
     */
    private String type;

    /**
     * 服务分类：builtin, external, custom
     */
    private String category;

    /**
     * 认证类型：none, api_key, bearer, basic, oauth2
     */
    private String authType;

    /**
     * 认证配置JSON
     */
    private String authConfig;

    /**
     * 工具列表JSON
     */
    private String tools;

    /**
     * 服务配置JSON
     */
    private String config;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 优先级（数字越大优先级越高）
     */
    private Integer priority;

    /**
     * 超时时间（毫秒）
     */
    private Integer timeout;

    /**
     * 最大重试次数
     */
    private Integer maxRetries;

    /**
     * 健康检查URL
     */
    private String healthCheckUrl;

    /**
     * 健康状态：healthy, unhealthy, unknown
     */
    private String healthStatus;

    /**
     * 最后健康检查时间
     */
    private Date lastHealthCheck;

    /**
     * 标签（用于分组和过滤）
     */
    private String tags;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;
}
