/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.tycoding.langchat.ai.core.service.LangChatService;
import cn.tycoding.langchat.ai.core.service.impl.LangChatServiceImpl;
import cn.tycoding.langchat.common.ai.dto.ChatReq;
import cn.tycoding.langchat.mcp.core.dto.McpServiceParameter;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * MCP参数提取器
 * 从用户输入中智能提取参数值
 * 
 * <AUTHOR>
 * @since 2024/12/20
 */
@Slf4j
@Component
public class McpParameterExtractor {

    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private LangChatService langChatService;
    /**
     * 参数提取配置
     */
    @Data
    public static class ParameterExtractionConfig {
        private String name;                    // 参数名称
        private String displayName;             // 显示名称
        private String type;                    // 参数类型
        private boolean extractFromInput;       // 是否从用户输入提取
        private String extractionPrompt;        // 提取提示词
        private List<String> keywords;          // 关键词列表
        private String pattern;                 // 正则表达式模式
        private String defaultValue;            // 默认值
        private boolean required;               // 是否必填
    }

    /**
     * 从用户输入中提取参数
     */
    public Map<String, Object> extractParametersFromInput(String userInput, List<ParameterExtractionConfig> configs,String modelId) {
        Map<String, Object> extractedParams = new HashMap<>();
        
        if (StrUtil.isBlank(userInput) || configs == null || configs.isEmpty()) {
            return extractedParams;
        }

        for (ParameterExtractionConfig config : configs) {
            if (!config.isExtractFromInput()) {
                continue;
            }

            Object value = extractSingleParameter(userInput, config,modelId);
            if (value != null) {
                extractedParams.put(config.getName(), value);
                log.debug("提取参数: {} = {}", config.getName(), value);
            }
        }

        return extractedParams;
    }

    /**
     * 提取单个参数
     */
    private Object extractSingleParameter(String userInput, ParameterExtractionConfig config,String modelId) {
        try {
            // 1. 优先使用正则表达式提取
            if (StrUtil.isNotBlank(config.getPattern())) {
                Object value = extractByPattern(userInput, config);
                if (value != null) {
                    return value;
                }
            }

//            // 2. 使用关键词匹配提取
//            if (config.getKeywords() != null && !config.getKeywords().isEmpty()) {
//                Object value = extractByKeywords(userInput, config);
//                if (value != null) {
//                    return value;
//                }
//            }

            // 3. 使用AI模型提取（如果有提取提示词）
            if (StrUtil.isNotBlank(config.getExtractionPrompt())) {
                Object value = extractByAI(userInput, config,modelId);
                if (value != null) {
                    return value;
                }
            }

            // 4. 使用默认值
            if (StrUtil.isNotBlank(config.getDefaultValue())) {
                return convertValue(config.getDefaultValue(), config.getType());
            }

        } catch (Exception e) {
            log.error("提取参数失败: {}", config.getName(), e);
        }

        return null;
    }

    /**
     * 使用正则表达式提取
     */
    private Object extractByPattern(String userInput, ParameterExtractionConfig config) {
        try {
            Pattern pattern = Pattern.compile(config.getPattern(), Pattern.CASE_INSENSITIVE);
            Matcher matcher = pattern.matcher(userInput);
            
            if (matcher.find()) {
                String value = matcher.groupCount() > 0 ? matcher.group(1) : matcher.group(0);
                return convertValue(value.trim(), config.getType());
            }
        } catch (Exception e) {
            log.error("正则表达式提取失败: {}", config.getPattern(), e);
        }
        return null;
    }

    /**
     * 使用关键词匹配提取
     */
    private Object extractByKeywords(String userInput, ParameterExtractionConfig config) {
        String lowerInput = userInput.toLowerCase();

        for (String keyword : config.getKeywords()) {
            String lowerKeyword = keyword.toLowerCase();
            int index = lowerInput.indexOf(lowerKeyword);

            if (index != -1) {
                // 尝试提取关键词后面的值
                String afterKeyword = userInput.substring(index + keyword.length()).trim();

                // 简单的值提取逻辑
                if (afterKeyword.startsWith("：") || afterKeyword.startsWith(":")) {
                    afterKeyword = afterKeyword.substring(1).trim();
                }

                // 对于企业名称，尝试提取完整的企业名称（包括后缀）
                if ("companyName".equals(config.getName())) {
                    return extractCompanyNameFromText(afterKeyword);
                }

                // 提取第一个词或短语
                String[] words = afterKeyword.split("[\\s,，。；;]");
                if (words.length > 0 && StrUtil.isNotBlank(words[0])) {
                    return convertValue(words[0].trim(), config.getType());
                }
            }
        }

        return null;
    }

    /**
     * 从文本中提取企业名称（包括完整后缀）
     */
    private String extractCompanyNameFromText(String text) {
        // 企业名称后缀模式
        String[] companySuffixes = {
            "有限责任公司", "股份有限公司", "有限公司", "股份公司",
            "集团有限公司", "集团股份有限公司", "集团", "公司", "企业"
        };

        // 按后缀长度排序，优先匹配长后缀
        java.util.Arrays.sort(companySuffixes, (a, b) -> b.length() - a.length());

        for (String suffix : companySuffixes) {
            int suffixIndex = text.indexOf(suffix);
            if (suffixIndex != -1) {
                // 提取包含后缀的完整企业名称
                String companyName = text.substring(0, suffixIndex + suffix.length()).trim();
                if (companyName.length() > suffix.length()) {
                    return companyName;
                }
            }
        }

        // 如果没有找到标准后缀，返回第一个词
        String[] words = text.split("[\\s,，。；;的]");
        if (words.length > 0 && StrUtil.isNotBlank(words[0])) {
            return words[0].trim();
        }

        return null;
    }

    /**
     * 使用AI模型提取
     */
    private Object extractByAI(String userInput, ParameterExtractionConfig config,String modelId) {
        try {
            // 构建提取提示词
            String prompt = buildExtractionPrompt(userInput, config);
            
            // 调用AI模型进行提取
            String aiResponse = callAIModel(prompt,modelId);
            
            if (StrUtil.isNotBlank(aiResponse)) {
                // 解析AI响应
                return parseAIResponse(aiResponse, config);
            }
            
        } catch (Exception e) {
            log.error("AI提取失败: {}", config.getName(), e);
        }
        
        return null;
    }

    /**
     * 构建提取提示词
     */
    private String buildExtractionPrompt(String userInput, ParameterExtractionConfig config) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请从以下用户输入中提取").append(config.getDisplayName()).append("：\n\n");
        prompt.append("用户输入：").append(userInput).append("\n\n");
        
        if (StrUtil.isNotBlank(config.getExtractionPrompt())) {
            prompt.append("提取要求：").append(config.getExtractionPrompt()).append("\n\n");
        }
        
        prompt.append("请只返回提取到的值，如果没有找到相关信息，请返回'null'。");
        
        return prompt.toString();
    }

    /**
     * 调用AI模型
     */
    private String callAIModel(String prompt,String modelId) {
        try {
            // 这里需要集成实际的AI模型调用
            // 可以调用现有的聊天模型服务
            ChatReq req = new ChatReq();
            req.setModelId(modelId);
            req.setMessage(prompt);

           return langChatService.text(req);
        } catch (Exception e) {
            log.error("调用AI模型失败", e);
        }
        
        return null;
    }

    /**
     * 解析AI响应
     */
    private Object parseAIResponse(String aiResponse, ParameterExtractionConfig config) {
        if ("null".equalsIgnoreCase(aiResponse.trim())) {
            return null;
        }
        
        // 清理AI响应
        String cleanResponse = aiResponse.trim()
                .replaceAll("^[\"']|[\"']$", "") // 去掉首尾引号
                .trim();
        
        if (StrUtil.isBlank(cleanResponse)) {
            return null;
        }
        
        return convertValue(cleanResponse, config.getType());
    }

    /**
     * 类型转换
     */
    private Object convertValue(String value, String type) {
        if (StrUtil.isBlank(value)) {
            return null;
        }
        
        try {
            return switch (type.toLowerCase()) {
                case "string" -> value;
                case "number", "integer" -> Integer.parseInt(value);
                case "double", "float" -> Double.parseDouble(value);
                case "boolean" -> Boolean.parseBoolean(value);
                case "object", "json" -> JSONUtil.parseObj(value);
                default -> value;
            };
        } catch (Exception e) {
            log.warn("类型转换失败: {} -> {}", value, type);
            return value; // 转换失败时返回原始字符串
        }
    }

    /**
     * 解析参数提取配置
     */
    public List<ParameterExtractionConfig> parseExtractionConfigs(String parametersJson) {
        List<ParameterExtractionConfig> configs = new ArrayList<>();
        
        if (StrUtil.isBlank(parametersJson)) {
            return configs;
        }
        
        try {
            var parameters = McpParameterUtils.parseParameters(parametersJson);
            
            for (var param : parameters) {
                ParameterExtractionConfig config = new ParameterExtractionConfig();
                config.setName(param.getName());
                config.setDisplayName(param.getDisplayName());
                config.setType(param.getType());
                config.setRequired(param.getRequired() != null && param.getRequired());
                
                // 从参数定义中解析提取配置
                if (param.getValidation() != null) {
                    var validation = param.getValidation();
                    config.setExtractFromInput(Boolean.TRUE.equals(validation.getExtractFromInput()));
                    config.setExtractionPrompt(validation.getExtractionPrompt());
                    config.setPattern(validation.getPattern());
                    config.setDefaultValue(validation.getDefaultValue());
                    config.setKeywords(validation.getKeywords());
                }
                
                configs.add(config);
            }
            
        } catch (Exception e) {
            log.error("解析提取配置失败", e);
        }
        
        return configs;
    }

    /**
     * 合并参数（提取的参数 + 应用配置的参数）
     */
    public Map<String, Object> mergeParameters(Map<String, Object> extractedParams, 
                                             Map<String, Object> appConfigParams,
                                             Map<String, Object> toolParams) {
        Map<String, Object> mergedParams = new HashMap<>();
        
        // 1. 先添加应用配置的参数
        if (appConfigParams != null) {
            mergedParams.putAll(appConfigParams);
        }

        // 2. 最后添加工具调用时传入的参数（优先级最高）
        if (toolParams != null) {
            mergedParams.putAll(toolParams);
        }

        // 3. 再添加从用户输入提取的参数（覆盖应用配置）
        if (extractedParams != null) {
            mergedParams.putAll(extractedParams);
        }

        return mergedParams;
    }
}
