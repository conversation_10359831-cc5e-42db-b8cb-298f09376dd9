/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.orchestration.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * MCP编排执行结果
 * 
 * <AUTHOR>
 * @since 2025/06/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrchestrationExecutionResult {

    /**
     * 执行是否成功
     */
    private boolean success;

    /**
     * 计划ID
     */
    private String planId;

    /**
     * 策略名称
     */
    private String strategyName;

    /**
     * 计划描述
     */
    private String description;

    /**
     * 步骤执行结果列表
     */
    private List<StepExecutionResult> steps;

    /**
     * 元数据
     */
    private Map<String, Object> metadata;

    /**
     * 错误信息（失败时）
     */
    private String error;

    /**
     * 执行开始时间
     */
    private Long startTime;

    /**
     * 执行结束时间
     */
    private Long endTime;

    /**
     * 总执行时长（毫秒）
     */
    private Long duration;

    /**
     * 成功步骤数
     */
    private Integer successSteps;

    /**
     * 失败步骤数
     */
    private Integer failedSteps;

    /**
     * 跳过步骤数
     */
    private Integer skippedSteps;

    /**
     * 总步骤数
     */
    private Integer totalSteps;

    /**
     * 创建成功结果
     */
    public static OrchestrationExecutionResult success(String planId, String strategyName, String description,
                                                      List<StepExecutionResult> steps, Map<String, Object> metadata) {
        return OrchestrationExecutionResult.builder()
                .success(true)
                .planId(planId)
                .strategyName(strategyName)
                .description(description)
                .steps(steps)
                .metadata(metadata)
                .endTime(System.currentTimeMillis())
                .build();
    }

    /**
     * 创建失败结果
     */
    public static OrchestrationExecutionResult failure(String planId, String strategyName, String error,
                                                      List<StepExecutionResult> steps) {
        return OrchestrationExecutionResult.builder()
                .success(false)
                .planId(planId)
                .strategyName(strategyName)
                .error(error)
                .steps(steps)
                .endTime(System.currentTimeMillis())
                .build();
    }

    /**
     * 计算统计信息
     */
    public void calculateStatistics() {
        if (steps == null || steps.isEmpty()) {
            this.totalSteps = 0;
            this.successSteps = 0;
            this.failedSteps = 0;
            this.skippedSteps = 0;
            return;
        }

        this.totalSteps = steps.size();
        this.successSteps = (int) steps.stream().filter(StepExecutionResult::isSuccess).count();
        this.failedSteps = (int) steps.stream().filter(step -> !step.isSuccess() && !step.isSkipped()).count();
        this.skippedSteps = (int) steps.stream().filter(StepExecutionResult::isSkipped).count();

        if (startTime != null && endTime != null) {
            this.duration = endTime - startTime;
        }
    }

    /**
     * 获取成功率
     */
    public double getSuccessRate() {
        if (totalSteps == null || totalSteps == 0) {
            return 0.0;
        }
        return (double) (successSteps != null ? successSteps : 0) / totalSteps;
    }

    /**
     * 是否有失败的步骤
     */
    public boolean hasFailures() {
        return failedSteps != null && failedSteps > 0;
    }

    /**
     * 是否有跳过的步骤
     */
    public boolean hasSkipped() {
        return skippedSteps != null && skippedSteps > 0;
    }

    /**
     * 获取执行摘要
     */
    public String getExecutionSummary() {
        if (!success) {
            return String.format("执行失败: %s", error);
        }

        return String.format("执行成功 - 总计: %d, 成功: %d, 失败: %d, 跳过: %d, 成功率: %.1f%%",
                totalSteps != null ? totalSteps : 0,
                successSteps != null ? successSteps : 0,
                failedSteps != null ? failedSteps : 0,
                skippedSteps != null ? skippedSteps : 0,
                getSuccessRate() * 100);
    }
}
