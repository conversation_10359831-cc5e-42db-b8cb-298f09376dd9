/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package cn.tycoding.langchat.mcp.core.orchestration.strategy;

import cn.tycoding.langchat.mcp.core.entity.AigcMcpService;
import cn.tycoding.langchat.mcp.core.orchestration.impl.FlexibleMcpOrchestrator;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.regex.Pattern;

/**
 * 内容创作策略
 * 处理"搜索资料、写文章、配图、发布"类型的复杂内容创作流程
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
@Component
public class ContentCreationStrategy implements FlexibleMcpOrchestrator.OrchestrationStrategy {

    private static final Pattern CONTENT_CREATION_PATTERN = Pattern.compile(
        ".*(搜索|查找).*(写|创作|生成).*(文章|内容|博客).*(发布|上传).*",
        Pattern.CASE_INSENSITIVE
    );

    @Override
    public boolean canHandle(String userInput, List<AigcMcpService> availableServices) {
        // 1. 检查用户输入是否匹配内容创作模式
        if (!CONTENT_CREATION_PATTERN.matcher(userInput).matches()) {
            // 也检查其他可能的表达方式
            if (!userInput.contains("搜索") || !userInput.contains("文章") || !userInput.contains("发布")) {
                return false;
            }
        }
        
        // 2. 检查必需的服务
        boolean hasSearchService = hasServiceByType(availableServices, "search");
        boolean hasTextService = hasServiceByType(availableServices, "text");
        boolean hasPublishService = hasServiceByType(availableServices, "publish");
        
        return hasSearchService && (hasTextService || hasPublishService);
    }

    @Override
    public FlexibleMcpOrchestrator.OrchestrationPlan createPlan(String userInput, List<AigcMcpService> availableServices) {
        FlexibleMcpOrchestrator.OrchestrationPlan plan = new FlexibleMcpOrchestrator.OrchestrationPlan();
        plan.setDescription("内容创作发布流程");
        
        // 提取搜索关键词
        String searchQuery = extractSearchQuery(userInput);
        String contentTopic = extractContentTopic(userInput);
        
        // 步骤1：搜索资料
        AigcMcpService searchService = findServiceByType(availableServices, "search");
        if (searchService != null) {
            FlexibleMcpOrchestrator.ExecutionStep searchStep = new FlexibleMcpOrchestrator.ExecutionStep();
            searchStep.setStepId("search_materials");
            searchStep.setServiceName(searchService.getName());
            searchStep.setToolName("web_search");
            searchStep.setDescription("搜索相关资料: " + searchQuery);
            searchStep.putParameter("q", searchQuery);
            searchStep.putParameter("count", 10);
            
            plan.addStep(searchStep);
        }
        
        // 步骤2：生成文章内容
        AigcMcpService textService = findServiceByType(availableServices, "text");
        if (textService != null) {
            FlexibleMcpOrchestrator.ExecutionStep contentStep = new FlexibleMcpOrchestrator.ExecutionStep();
            contentStep.setStepId("generate_content");
            contentStep.setServiceName(textService.getName());
            contentStep.setToolName("generate_article");
            contentStep.setDescription("基于搜索结果生成文章");
            contentStep.putParameter("topic", contentTopic);
            contentStep.putParameter("reference_data", "${search_materials.result}");
            contentStep.putParameter("word_count", extractWordCount(userInput));
            contentStep.addDependency("search_materials");
            
            plan.addStep(contentStep);
        }
        
        // 步骤3：生成配图（可选）
        AigcMcpService imageService = findServiceByType(availableServices, "image");
        if (imageService != null && userInput.contains("配图")) {
            FlexibleMcpOrchestrator.ExecutionStep imageStep = new FlexibleMcpOrchestrator.ExecutionStep();
            imageStep.setStepId("generate_image");
            imageStep.setServiceName(imageService.getName());
            imageStep.setToolName("text_to_image");
            imageStep.setDescription("生成文章配图");
            imageStep.putParameter("prompt", contentTopic + " 专业配图");
            imageStep.putParameter("style", "professional");
            // 可以并行执行，不依赖文章生成
            
            plan.addStep(imageStep);
        }
        
        // 步骤4：发布内容
        AigcMcpService publishService = findServiceByType(availableServices, "publish");
        if (publishService != null) {
            FlexibleMcpOrchestrator.ExecutionStep publishStep = new FlexibleMcpOrchestrator.ExecutionStep();
            publishStep.setStepId("publish_content");
            publishStep.setServiceName(publishService.getName());
            publishStep.setToolName(determinePublishTool(publishService, userInput));
            publishStep.setDescription("发布文章内容");
            publishStep.putParameter("title", contentTopic);
            publishStep.putParameter("content", "${generate_content.result}");
            publishStep.putParameter("category", extractCategory(userInput));
            publishStep.addDependency("generate_content");
            
            // 如果有配图，也依赖配图步骤
            if (imageService != null && userInput.contains("配图")) {
                publishStep.putParameter("featured_image", "${generate_image.result.url}");
                publishStep.addDependency("generate_image");
            }
            
            plan.addStep(publishStep);
        }
        
        // 添加元数据
        plan.putMetadata("search_query", searchQuery);
        plan.putMetadata("content_topic", contentTopic);
        plan.putMetadata("estimated_time", "3-5分钟");
        plan.putMetadata("complexity", "high");
        plan.putMetadata("requires_review", true);
        
        return plan;
    }

    @Override
    public String getStrategyName() {
        return "ContentCreationStrategy";
    }

    @Override
    public int getPriority() {
        return 90; // 最高优先级
    }

    /**
     * 检查是否有指定类型的服务
     */
    private boolean hasServiceByType(List<AigcMcpService> services, String type) {
        return services.stream().anyMatch(service -> isServiceOfType(service, type));
    }

    /**
     * 查找指定类型的服务
     */
    private AigcMcpService findServiceByType(List<AigcMcpService> services, String type) {
        return services.stream()
                .filter(service -> isServiceOfType(service, type))
                .findFirst()
                .orElse(null);
    }

    /**
     * 判断服务是否为指定类型
     */
    private boolean isServiceOfType(AigcMcpService service, String type) {
        String name = service.getName().toLowerCase();
        String desc = service.getDescription().toLowerCase();
        
        return switch (type) {
            case "search" -> name.contains("search") || name.contains("brave") || 
                           desc.contains("搜索") || desc.contains("查找");
            case "text" -> name.contains("text") || name.contains("gpt") || name.contains("claude") ||
                         desc.contains("文本") || desc.contains("文章") || desc.contains("生成");
            case "image" -> name.contains("image") || name.contains("wanx") ||
                          desc.contains("图片") || desc.contains("生图");
            case "publish" -> name.contains("publish") || name.contains("blog") || name.contains("cms") ||
                            desc.contains("发布") || desc.contains("博客");
            default -> false;
        };
    }

    /**
     * 提取搜索查询
     */
    private String extractSearchQuery(String userInput) {
        // 尝试提取搜索关键词
        Pattern searchPattern = Pattern.compile(".*(搜索|查找)([^，。！？]+).*");
        java.util.regex.Matcher matcher = searchPattern.matcher(userInput);
        
        if (matcher.find()) {
            String query = matcher.group(2).trim();
            // 清理提取的文本
            query = query.replaceAll("(关于|的|资料|信息|内容)", "").trim();
            return query.isEmpty() ? extractContentTopic(userInput) : query;
        }
        
        return extractContentTopic(userInput);
    }

    /**
     * 提取内容主题
     */
    private String extractContentTopic(String userInput) {
        // 尝试提取文章主题
        String[] topics = {"人工智能", "AI", "机器学习", "深度学习", "科技", "技术", "发展", "趋势"};
        
        for (String topic : topics) {
            if (userInput.contains(topic)) {
                return topic;
            }
        }
        
        // 如果没有找到特定主题，使用通用提取
        Pattern topicPattern = Pattern.compile(".*(写|创作|生成).*?([^，。！？]+).*(文章|内容|博客).*");
        java.util.regex.Matcher matcher = topicPattern.matcher(userInput);
        
        if (matcher.find()) {
            String topic = matcher.group(2).trim();
            topic = topic.replaceAll("(一篇|关于|的)", "").trim();
            return topic.isEmpty() ? "技术文章" : topic;
        }
        
        return "技术文章";
    }

    /**
     * 提取字数要求
     */
    private int extractWordCount(String userInput) {
        Pattern wordPattern = Pattern.compile("(\\d+)字");
        java.util.regex.Matcher matcher = wordPattern.matcher(userInput);
        
        if (matcher.find()) {
            return Integer.parseInt(matcher.group(1));
        }
        
        // 根据文章类型推断字数
        if (userInput.contains("详细") || userInput.contains("深入")) {
            return 3000;
        } else if (userInput.contains("简短") || userInput.contains("简介")) {
            return 800;
        }
        
        return 1500; // 默认字数
    }

    /**
     * 提取文章分类
     */
    private String extractCategory(String userInput) {
        if (userInput.contains("技术") || userInput.contains("AI") || userInput.contains("人工智能")) {
            return "技术";
        } else if (userInput.contains("新闻") || userInput.contains("资讯")) {
            return "新闻";
        } else if (userInput.contains("教程") || userInput.contains("指南")) {
            return "教程";
        }
        return "综合";
    }

    /**
     * 确定发布工具
     */
    private String determinePublishTool(AigcMcpService publishService, String userInput) {
        String name = publishService.getName().toLowerCase();
        
        if (name.contains("blog")) {
            return "publish_post";
        } else if (name.contains("cms")) {
            return "create_article";
        } else if (name.contains("wordpress")) {
            return "wp_publish";
        }
        
        return "publish_content"; // 默认工具
    }
}
