/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.service;

import cn.tycoding.langchat.mcp.core.dto.McpServiceConfig;
import cn.tycoding.langchat.mcp.core.entity.AigcMcpServiceInstance;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * MCP服务实例管理Service
 * 
 * <AUTHOR>
 * @since 2024/12/19
 */
public interface AigcMcpServiceInstanceService extends IService<AigcMcpServiceInstance> {

    /**
     * 根据用户ID和服务名称获取实例列表
     */
    List<AigcMcpServiceInstance> getInstancesByUserAndService(String userId, String serviceName);

    /**
     * 根据用户ID获取所有实例
     */
    List<AigcMcpServiceInstance> getInstancesByUser(String userId);

    /**
     * 获取用户的默认实例
     */
    AigcMcpServiceInstance getDefaultInstance(String userId, String serviceName);

    /**
     * 创建服务实例
     */
    AigcMcpServiceInstance createInstance(String userId, String serviceName, String instanceName, 
                                        McpServiceConfig config);

    /**
     * 更新服务实例配置
     */
    AigcMcpServiceInstance updateInstanceConfig(String instanceId, McpServiceConfig config);

    /**
     * 设置默认实例
     */
    void setDefaultInstance(String userId, String instanceId);

    /**
     * 删除服务实例
     */
    void deleteInstance(String instanceId);

    /**
     * 验证实例配置
     */
    List<String> validateInstanceConfig(String serviceId, McpServiceConfig config);

    /**
     * 获取实例的完整配置
     */
    McpServiceConfig getInstanceConfig(String instanceId);

    /**
     * 测试实例连接
     */
    Map<String, Object> testInstanceConnection(String instanceId);

    /**
     * 更新实例使用统计
     */
    void updateUsageStatistics(String instanceId);

    /**
     * 获取公开的实例列表
     */
    List<AigcMcpServiceInstance> getPublicInstances(String serviceName);

    /**
     * 复制实例
     */
    AigcMcpServiceInstance cloneInstance(String sourceInstanceId, String userId, String newInstanceName);

    /**
     * 导出实例配置
     */
    String exportInstanceConfig(String instanceId);

    /**
     * 导入实例配置
     */
    AigcMcpServiceInstance importInstanceConfig(String userId, String configJson);

    /**
     * 批量创建实例
     */
    List<AigcMcpServiceInstance> batchCreateInstances(String userId, List<Map<String, Object>> instanceConfigs);

    /**
     * 获取实例使用统计
     */
    Map<String, Object> getInstanceStatistics(String userId);

    /**
     * 搜索实例
     */
    List<AigcMcpServiceInstance> searchInstances(String userId, String keyword, String serviceName, 
                                                String status, String accessLevel);
}
