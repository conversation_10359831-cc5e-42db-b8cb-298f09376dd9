/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.constants;

/**
 * MCP服务常量定义
 * 
 * <AUTHOR>
 * @since 2025/06/15
 */
public final class McpServiceConstants {

    private McpServiceConstants() {
        // 私有构造函数，防止实例化
    }

    /**
     * 内置服务名称
     */
    public static final class BuiltinServices {
        /** 内置搜索服务 */
        public static final String BUILTIN_SEARCH = "builtin-search";

        /** 内置文件处理服务 */
        public static final String BUILTIN_FILE = "builtin-file";

        /** 内置文本处理服务 */
        public static final String BUILTIN_TEXT = "builtin-text";

        /** 内置数据分析服务 */
        public static final String BUILTIN_ANALYTICS = "builtin-analytics";

        /** 内置邮件服务 */
        public static final String BUILTIN_EMAIL = "builtin-email";

        /** 内置API服务 */
        public static final String BUILTIN_API = "builtin-api";

        /** 内置企业查询服务 */
        public static final String BUILTIN_COMPANY_SEARCH = "builtin-company-search";
        /** 内置图片或文档识别 */
        public static final String BUILTIN_IDENTIFICATION = "builtin-identification";

    }

    /**
     * 外部服务名称
     */
    public static final class ExternalServices {
        /** Wanx文生图服务 */
        public static final String WANX_IMAGE_GENERATION = "wanx-image-generation";
        
        /** Wanx服务简称 */
        public static final String WANX = "wanx";
        
        /** Brave搜索服务 */
        public static final String BRAVE_SEARCH = "brave-search";
        
        /** Brave服务简称 */
        public static final String BRAVE = "brave";

        /** EdgeOne Pages服务 */
        public static final String EDGEONE_PAGES = "edgeone-pages-mcp-server";
    }

    /**
     * 虚拟服务名称
     */
    public static final class VirtualServices {
        /** AI提示词优化器 */
        public static final String AI_PROMPT_OPTIMIZER = "ai-prompt-optimizer";
        
        /** AI文本生成器 */
        public static final String AI_TEXT_GENERATOR = "ai-text-generator";
        
        /** AI分析器 */
        public static final String AI_ANALYZER = "ai-analyzer";
    }

    /**
     * 工具名称
     */
    public static final class ToolNames {
        /** 网络搜索 */
        public static final String WEB_SEARCH = "web_search";

        /** 图片搜索 */
        public static final String IMAGE_SEARCH = "image_search";

        /** 新闻搜索 */
        public static final String NEWS_SEARCH = "news_search";

        /** 文本转图片 */
        public static final String TEXT_TO_IMAGE = "text_to_image";
        
        /** 优化图片提示词 */
        public static final String OPTIMIZE_IMAGE_PROMPT = "optimize_image_prompt";
        
        /** 文件处理 */
        public static final String PROCESS_FILE = "file_operation";

        /** 读取文件 */
        public static final String READ_FILE = "read_file";

        /** 写入文件 */
        public static final String WRITE_FILE = "write_file";

        /** 文件管理 */
        public static final String FILE_MANAGER = "file_manager";

        /** 文本生成 */
        public static final String GENERATE_TEXT = "generate_text";

        /** 数据分析 */
        public static final String ANALYZE_DATA = "analyze_data";

        /** 发送邮件 */
        public static final String SEND_EMAIL = "send_email";

        /** 读取邮件 */
        public static final String READ_EMAIL = "read_email";

        /** 删除邮件 */
        public static final String DELETE_EMAIL = "delete_email";

        /** HTTP请求 */
        public static final String HTTP_REQUEST = "http_request";

        /** 企业搜索 */
        public static final String COMPANY_SEARCH = "company_search";

        /** 企业详情查询 */
        public static final String COMPANY_DETAIL = "company_detail";

        /** 企业财务查询 */
        public static final String COMPANY_FINANCIAL = "company_financial";

        /** 企业风险评估 */
        public static final String COMPANY_RISK_ASSESSMENT = "company_risk_assessment";
    }

    /**
     * 服务类型
     */
    public static final class ServiceTypes {
        /** 内置服务 */
        public static final String BUILTIN = "builtin";
        
        /** 外部服务 */
        public static final String EXTERNAL = "external";
        
        /** 虚拟服务 */
        public static final String VIRTUAL = "virtual";
        
        /** 内部服务 */
        public static final String INTERNAL = "internal";
    }

    /**
     * 服务类别
     */
    public static final class ServiceCategories {
        /** 搜索类服务 */
        public static final String SEARCH = "search";
        
        /** 图片类服务 */
        public static final String IMAGE = "image";
        
        /** 文本类服务 */
        public static final String TEXT = "text";
        
        /** 文件类服务 */
        public static final String FILE = "file";
        
        /** AI类服务 */
        public static final String AI = "ai";
        
        /** 分析类服务 */
        public static final String ANALYTICS = "analytics";
        
        /** 发布类服务 */
        public static final String PUBLISH = "publish";
    }

    /**
     * 常用参数名称
     */
    public static final class ParameterNames {
        /** 查询参数 */
        public static final String QUERY = "query";
        
        /** 提示词参数 */
        public static final String PROMPT = "prompt";
        
        /** 原始提示词 */
        public static final String ORIGINAL_PROMPT = "original_prompt";
        /** 用户输入的提示词 */
        public static final String INPUT_PROMPT = "input_prompt";

        /** 输出提示词 */
        public static final String OUTPUT_PROMPT = "outputPrompt";

        /** 优化后的提示词 */
        public static final String OPTIMIZED_PROMPT = "optimized_prompt";
        
        /** 数量参数 */
        public static final String COUNT = "count";
        
        /** 大小参数 */
        public static final String SIZE = "size";
        
        /** 风格参数 */
        public static final String STYLE = "style";
        
        /** 文件路径 */
        public static final String FILE_PATH = "file_path";
        
        /** 内容参数 */
        public static final String CONTENT = "content";
    }

    /**
     * 常用参数值
     */
    public static final class ParameterValues {
        /** 自动风格 */
        public static final String STYLE_AUTO = "auto";
        
        /** 默认图片大小 */
        public static final String SIZE_DEFAULT = "1024*1024";
        
        /** 默认搜索数量 */
        public static final int COUNT_DEFAULT = 5;
        
        /** 通用优化类型 */
        public static final String OPTIMIZATION_TYPE_GENERAL = "general";

        /** 图片生成优化类型 */
        public static final String OPTIMIZATION_TYPE_IMAGE = "image_generation";

        /** 文本生成优化类型 */
        public static final String OPTIMIZATION_TYPE_TEXT = "text_generation";

        /** 创意写作优化类型 */
        public static final String OPTIMIZATION_TYPE_CREATIVE = "creative";

        /** 技术文档优化类型 */
        public static final String OPTIMIZATION_TYPE_TECHNICAL = "technical";
    }

    /**
     * 检查是否为内置服务
     */
    public static boolean isBuiltinService(String serviceName) {
        return serviceName != null && (
            BuiltinServices.BUILTIN_SEARCH.equals(serviceName) ||
            BuiltinServices.BUILTIN_FILE.equals(serviceName) ||
            BuiltinServices.BUILTIN_TEXT.equals(serviceName) ||
            BuiltinServices.BUILTIN_ANALYTICS.equals(serviceName) ||
            BuiltinServices.BUILTIN_EMAIL.equals(serviceName) ||
            BuiltinServices.BUILTIN_API.equals(serviceName) ||
            BuiltinServices.BUILTIN_COMPANY_SEARCH.equals(serviceName)||
            BuiltinServices.BUILTIN_IDENTIFICATION.equals(serviceName)
        );
    }

    /**
     * 检查是否为外部服务
     */
    public static boolean isExternalService(String serviceName) {
        return serviceName != null && (
            ExternalServices.WANX_IMAGE_GENERATION.equals(serviceName) ||
            ExternalServices.WANX.equals(serviceName) ||
            ExternalServices.BRAVE_SEARCH.equals(serviceName) ||
            ExternalServices.BRAVE.equals(serviceName) ||
            ExternalServices.EDGEONE_PAGES.equals(serviceName)
        );
    }

    /**
     * 检查是否为虚拟服务
     */
    public static boolean isVirtualService(String serviceName) {
        return serviceName != null && (
            VirtualServices.AI_PROMPT_OPTIMIZER.equals(serviceName) ||
            VirtualServices.AI_TEXT_GENERATOR.equals(serviceName) ||
            VirtualServices.AI_ANALYZER.equals(serviceName)
        );
    }

    /**
     * 获取服务的显示名称
     */
    public static String getServiceDisplayName(String serviceName) {
        if (serviceName == null) return "未知服务";
        
        return switch (serviceName) {
            case BuiltinServices.BUILTIN_SEARCH -> "内置搜索服务";
            case BuiltinServices.BUILTIN_FILE -> "内置文件处理服务";
            case BuiltinServices.BUILTIN_TEXT -> "内置文本处理服务";
            case BuiltinServices.BUILTIN_ANALYTICS -> "内置数据分析服务";
            case BuiltinServices.BUILTIN_EMAIL -> "内置邮件服务";
            case BuiltinServices.BUILTIN_API -> "内置API服务";
            case BuiltinServices.BUILTIN_COMPANY_SEARCH -> "内置企业查询服务";
            case ExternalServices.WANX_IMAGE_GENERATION, ExternalServices.WANX -> "Wanx文生图服务";
            case ExternalServices.BRAVE_SEARCH, ExternalServices.BRAVE -> "Brave搜索服务";
            case ExternalServices.EDGEONE_PAGES -> "EdgeOne Pages发布服务";
            case VirtualServices.AI_PROMPT_OPTIMIZER -> "AI提示词优化器";
            case VirtualServices.AI_TEXT_GENERATOR -> "AI文本生成器";
            case VirtualServices.AI_ANALYZER -> "AI分析器";
            default -> serviceName;
        };
    }
}
