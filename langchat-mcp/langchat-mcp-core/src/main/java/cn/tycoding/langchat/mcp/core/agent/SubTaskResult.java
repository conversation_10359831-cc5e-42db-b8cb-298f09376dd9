/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.agent;

import lombok.Builder;
import lombok.Data;

/**
 * 子任务执行结果
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
@Data
@Builder
public class SubTaskResult {
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 任务名称
     */
    private String taskName;
    
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 输出结果
     */
    private Object output;
    
    /**
     * 错误消息
     */
    private String errorMessage;
    
    /**
     * 执行开始时间
     */
    @Builder.Default
    private long startTime = System.currentTimeMillis();
    
    /**
     * 执行结束时间
     */
    private long endTime;
    
    /**
     * 重试次数
     */
    @Builder.Default
    private int retryCount = 0;
    
    /**
     * 创建成功结果
     */
    public static SubTaskResult success(String taskId, String taskName, Object output) {
        return SubTaskResult.builder()
                .taskId(taskId)
                .taskName(taskName)
                .success(true)
                .output(output)
                .endTime(System.currentTimeMillis())
                .build();
    }
    
    /**
     * 创建失败结果
     */
    public static SubTaskResult failure(String taskId, String errorMessage) {
        return SubTaskResult.builder()
                .taskId(taskId)
                .success(false)
                .errorMessage(errorMessage)
                .endTime(System.currentTimeMillis())
                .build();
    }
    
    /**
     * 获取执行时间（毫秒）
     */
    public long getExecutionTime() {
        if (endTime > 0) {
            return endTime - startTime;
        }
        return 0;
    }
}
