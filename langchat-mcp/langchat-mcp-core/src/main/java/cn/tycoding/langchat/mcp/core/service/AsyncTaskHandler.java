/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.mcp.core.service;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 异步任务处理器
 * 用于处理Wanx文生图等异步MCP服务的任务状态轮询
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
@Slf4j
@Service
public class AsyncTaskHandler {

    /**
     * 任务状态枚举
     */
    public enum TaskStatus {
        PENDING("PENDING", "任务排队中"),
        RUNNING("RUNNING", "任务处理中"),
        SUCCEEDED("SUCCEEDED", "任务执行成功"),
        FAILED("FAILED", "任务执行失败"),
        CANCELED("CANCELED", "任务取消成功"),
        UNKNOWN("UNKNOWN", "任务不存在或状态未知");

        private final String code;
        private final String description;

        TaskStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static TaskStatus fromCode(String code) {
            for (TaskStatus status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return UNKNOWN;
        }
    }

    /**
     * 处理Wanx文生图异步任务
     */
    public Map<String, Object> handleWanxImageTask(Map<String, Object> initialResponse, String apiKey) {
        try {
            log.info("开始处理Wanx异步任务，初始响应: {}", initialResponse);

            // 验证API Key
            if (apiKey == null || apiKey.trim().isEmpty()) {
                log.error("API Key为空，无法进行异步任务轮询");
                return createErrorResponse("API Key未配置，无法查询任务状态");
            }

            // 检查初始响应是否包含任务ID
            String taskId = extractTaskId(initialResponse);
            if (taskId == null) {
                log.warn("无法从响应中提取任务ID，响应结构: {}", initialResponse);
                // 降级处理：返回原始响应而不是错误，可能是同步响应
                return initialResponse;
            }

            // 检查初始状态
            TaskStatus status = extractTaskStatus(initialResponse);
            log.info("Wanx文生图任务初始状态: {} ({})，任务ID: {}", status.getCode(), status.getDescription(), taskId);

            // 如果任务已经完成，直接返回
            if (status == TaskStatus.SUCCEEDED) {
                return initialResponse;
            }

            // 如果任务失败或取消，返回错误信息
            if (status == TaskStatus.FAILED || status == TaskStatus.CANCELED) {
                return createErrorResponse(status, initialResponse);
            }

            // 如果任务正在排队或处理中，开始轮询
            if (status == TaskStatus.PENDING || status == TaskStatus.RUNNING) {
                return pollTaskResult(taskId, apiKey);
            }

            // 未知状态，返回原始响应
            return initialResponse;

        } catch (Exception e) {
            log.error("处理Wanx文生图异步任务失败", e);
            return createErrorResponse("处理异步任务失败: " + e.getMessage());
        }
    }

    /**
     * 轮询任务结果
     */
    private Map<String, Object> pollTaskResult(String taskId, String apiKey) {
        int maxAttempts = 30; // 最多轮询30次
        int intervalSeconds = 2; // 每2秒轮询一次
        
        for (int attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                log.info("第{}次轮询任务状态，任务ID: {}", attempt, taskId);
                
                // 调用查询接口
                Map<String, Object> result = queryTaskStatus(taskId, apiKey);
                TaskStatus status = extractTaskStatus(result);
                
                log.info("任务状态: {} ({})", status.getCode(), status.getDescription());
                
                // 检查任务状态
                switch (status) {
                    case SUCCEEDED:
                        log.info("任务执行成功，返回结果");
                        return result;
                        
                    case FAILED:
                    case CANCELED:
                        log.warn("任务执行失败或被取消: {}", status.getDescription());
                        return createErrorResponse(status, result);
                        
                    case PENDING:
                    case RUNNING:
                        // 继续轮询
                        log.info("任务仍在处理中，等待{}秒后继续轮询", intervalSeconds);
                        Thread.sleep(intervalSeconds * 1000);
                        break;
                        
                    case UNKNOWN:
                    default:
                        log.warn("任务状态未知，停止轮询");
                        return createErrorResponse("任务状态未知");
                }
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("轮询被中断", e);
                return createErrorResponse("任务轮询被中断");
            } catch (Exception e) {
                log.error("轮询任务状态失败，尝试次数: {}", attempt, e);
                if (attempt == maxAttempts) {
                    return createErrorResponse("轮询任务状态失败: " + e.getMessage());
                }
                // 继续下一次尝试
                try {
                    Thread.sleep(intervalSeconds * 1000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    return createErrorResponse("任务轮询被中断");
                }
            }
        }
        
        return createErrorResponse("任务轮询超时，请稍后手动查询结果");
    }

    /**
     * 查询任务状态（带重试机制）
     */
    private Map<String, Object> queryTaskStatus(String taskId, String apiKey) {
        String url = "https://dashscope.aliyuncs.com/api/v1/tasks/" + taskId;
        log.debug("查询任务状态，URL: {}", url);

        int maxRetries = 3;
        Exception lastException = null;

        for (int retry = 0; retry < maxRetries; retry++) {
            try {
                if (retry > 0) {
                    log.info("重试查询任务状态，第{}次尝试", retry + 1);
                    Thread.sleep(1000); // 重试前等待1秒
                }

                HttpResponse response = HttpRequest.get(url)
                        .header("Authorization", "Bearer " + apiKey)
                        .header("Content-Type", "application/json")
                        .timeout(10000) // 10秒超时
                        .execute();

                if (!response.isOk()) {
                    String errorBody = response.body();
                    String errorMsg = String.format("查询任务状态失败，HTTP状态码: %d, 响应: %s",
                            response.getStatus(), errorBody);

                    if (retry == maxRetries - 1) {
                        log.error(errorMsg);
                        throw new RuntimeException(errorMsg);
                    } else {
                        log.warn("{}，将重试", errorMsg);
                        continue;
                    }
                }

                String responseBody = response.body();
                log.debug("任务状态查询响应: {}", responseBody);

                JSONObject jsonResponse = JSONUtil.parseObj(responseBody);
                return jsonResponse.toBean(Map.class);

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("查询被中断", e);
            } catch (Exception e) {
                lastException = e;
                if (retry == maxRetries - 1) {
                    log.error("查询任务状态失败，已重试{}次", maxRetries, e);
                    throw new RuntimeException("查询任务状态失败: " + e.getMessage(), e);
                } else {
                    log.warn("查询任务状态失败，第{}次尝试: {}", retry + 1, e.getMessage());
                }
            }
        }

        throw new RuntimeException("查询任务状态失败，已重试" + maxRetries + "次", lastException);
    }

    /**
     * 从响应中提取任务ID
     */
    public String extractTaskId(Map<String, Object> response) {
        // 尝试多种可能的字段名
        String[] possibleFields = {"task_id", "taskId"};
        
        for (String field : possibleFields) {
            Object value = response.get(field);
            if (value != null) {
                return value.toString();
            }
        }
        
        // 尝试从嵌套对象中提取
        Object output = response.get("output");
        if (output instanceof Map) {
            Map<String, Object> outputMap = (Map<String, Object>) output;
            for (String field : possibleFields) {
                Object value = outputMap.get(field);
                if (value != null) {
                    return value.toString();
                }
            }
        }
        
        return null;
    }

    /**
     * 从响应中提取任务状态
     */
    public TaskStatus extractTaskStatus(Map<String, Object> response) {
        // 尝试多种可能的字段名
        String[] possibleFields = {"status", "task_status", "taskStatus", "state"};
        
        for (String field : possibleFields) {
            Object value = response.get(field);
            if (value != null) {
                return TaskStatus.fromCode(value.toString());
            }
        }
        
        // 尝试从嵌套对象中提取
        Object output = response.get("output");
        if (output instanceof Map) {
            Map<String, Object> outputMap = (Map<String, Object>) output;
            for (String field : possibleFields) {
                Object value = outputMap.get(field);
                if (value != null) {
                    return TaskStatus.fromCode(value.toString());
                }
            }
        }
        
        return TaskStatus.UNKNOWN;
    }

    /**
     * 创建错误响应
     */
    private Map<String, Object> createErrorResponse(TaskStatus status, Map<String, Object> originalResponse) {
        return Map.of(
            "success", false,
            "error", status.getDescription(),
            "status", status.getCode(),
            "original_response", originalResponse
        );
    }

    /**
     * 创建错误响应
     */
    private Map<String, Object> createErrorResponse(String errorMessage) {
        return Map.of(
            "success", false,
            "error", errorMessage,
            "status", "ERROR"
        );
    }

    /**
     * 检查响应是否为异步任务
     */
    public boolean isAsyncTask(Map<String, Object> response) {
        TaskStatus status = extractTaskStatus(response);
        boolean isAsync = status == TaskStatus.PENDING || status == TaskStatus.RUNNING;
        log.debug("检查是否为异步任务，状态: {}, 结果: {}", status.getCode(), isAsync);
        return isAsync;
    }

    /**
     * 检查任务是否完成
     */
    public boolean isTaskCompleted(Map<String, Object> response) {
        TaskStatus status = extractTaskStatus(response);
        return status == TaskStatus.SUCCEEDED || status == TaskStatus.FAILED || status == TaskStatus.CANCELED;
    }

    /**
     * 检查任务是否成功
     */
    public boolean isTaskSuccessful(Map<String, Object> response) {
        TaskStatus status = extractTaskStatus(response);
        return status == TaskStatus.SUCCEEDED;
    }
}
