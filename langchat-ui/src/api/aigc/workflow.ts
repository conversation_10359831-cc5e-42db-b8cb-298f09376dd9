/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import {http} from '../../utils/http/axios';

export function getList(params: any) {
  return http.request({
    url: '/aigc/workflow/list',
    method: 'get',
    params,
  });
}

export function detail(params: any) {
  return http.request({
    url: '/aigc/workflow/detail',
    method: 'get',
    params,
  });
}

export function queryModelList() {
  return http.request({
    url: '/aigc/workflow/model/list',
    method: 'get',
  });
}

export function queryKnowledgeList() {
  return http.request({
    url: '/aigc/workflow/knowledge/list',
    method: 'get',
  });
}


export function getRunningParameters(params: any) {
  return http.request({
    url: '/aigc/workflow/getRunningParameters',
    method: 'get',
    params,
  });
}


export function commit(params: any) {
  return http.request({
    url: '/aigc/workflow/commit',
    method: 'post',
    params,
  });
}
export function del(params: any) {
  return http.request({
    url: '/aigc/workflow/del',
    method: 'post',
    params,
  });
}

export function tryRunning(params: any) {
  return http.request({
    url: '/aigc/workflow/tryRunning',
    method: 'post',
    params,
  });
}
