/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

.transition-default() {
  &-enter-active,
  &-leave-active {
    transition: 0.3s cubic-bezier(0.25, 0.8, 0.5, 1) !important;
  }

  &-move {
    transition: transform 0.4s;
  }
}

.expand-transition {
  .transition-default();
}

.expand-x-transition {
  .transition-default();
}

.n-layout .n-layout-scroll-container {
  overflow: hidden;
}

.n-thing-main__description {
  color: rgb(118, 124, 130);
}
