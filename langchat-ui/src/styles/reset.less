/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* --------------------------------------------------------------
   reset.css
   * Resets default browser CSS.
-------------------------------------------------------------- */

/* Reset default CSS styles for all elements */
* {
  margin: 0;
  padding: 0;
  text-decoration: none;
  list-style: none;
  box-sizing: border-box;
}

/* Remove focus outlines on elements */
:focus {
  outline: none;
}

/* Hide scrollbars (compatible with Firefox 64+) */
*::-webkit-scrollbar {
  display: none;
}

/* Ensure that button and select elements inherit the font family */
button,
select {
  font-family: inherit;
}
