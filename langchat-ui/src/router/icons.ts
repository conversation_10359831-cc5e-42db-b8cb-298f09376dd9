/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { renderIcon } from '@/utils/index';
import {
  AccessibilityOutline,
  AddCircleOutline,
  AddOutline,
  AirplaneOutline,
  AlarmOutline,
  AlbumsOutline,
  AlertCircleOutline,
  AlertOutline,
  AmericanFootballOutline,
  AnalyticsOutline,
  ApertureOutline,
  AppsOutline,
  ArchiveOutline,
  ArrowBackCircleOutline,
  ArrowBackOutline,
  ArrowDownCircleOutline,
  ArrowDownOutline,
  ArrowForwardCircleOutline,
  ArrowForwardOutline,
  ArrowRedoCircleOutline,
  ArrowRedoOutline,
  ArrowUndoCircleOutline,
  ArrowUndoOutline,
  ArrowUpCircleOutline,
  ArrowUpOutline,
  AtCircleOutline,
  AtOutline,
  AttachOutline,
  BackspaceOutline,
  BagAddOutline,
  BagCheckOutline,
  BagHandleOutline,
  BagOutline,
  BagRemoveOutline,
  BalloonOutline,
  BandageOutline,
  BanOutline,
  BarbellOutline,
  BarChartOutline,
  BarcodeOutline,
  BaseballOutline,
  BasketballOutline,
  BasketOutline,
  BatteryChargingOutline,
  BatteryDeadOutline,
  BatteryFullOutline,
  BatteryHalfOutline,
  BeakerOutline,
  BedOutline,
  BeerOutline,
  BicycleOutline,
  BluetoothOutline,
  BoatOutline,
  BodyOutline,
  BonfireOutline,
  BookmarkOutline,
  BookmarksOutline,
  BookOutline,
  BowlingBallOutline,
  BriefcaseOutline,
  BrowsersOutline,
  BrushOutline,
  BugOutline,
  BuildOutline,
  BulbOutline,
  BusinessOutline,
  BusOutline,
  CafeOutline,
  CalculatorOutline,
  CalendarClearOutline,
  CalendarNumberOutline,
  CalendarOutline,
  CallOutline,
  CameraOutline,
  CameraReverseOutline,
  CardOutline,
  CaretBackCircleOutline,
  CaretBackOutline,
  CaretDownCircleOutline,
  CaretDownOutline,
  CaretForwardCircleOutline,
  CaretForwardOutline,
  CaretUpCircleOutline,
  CaretUpOutline,
  CarOutline,
  CarSportOutline,
  CartOutline,
  CashOutline,
  CellularOutline,
  ChatboxEllipsesOutline,
  ChatboxOutline,
  ChatbubbleEllipsesOutline,
  ChatbubbleOutline,
  ChatbubblesOutline,
  CheckboxOutline,
  CheckmarkCircleOutline,
  CheckmarkDoneCircleOutline,
  CheckmarkDoneOutline,
  CheckmarkOutline,
  ChevronBackCircleOutline,
  ChevronBackOutline,
  ChevronDownCircleOutline,
  ChevronDownOutline,
  ChevronForwardCircleOutline,
  ChevronForwardOutline,
  ChevronUpCircleOutline,
  ChevronUpOutline,
  ClipboardOutline,
  CloseCircleOutline,
  CloseOutline,
  CloudCircleOutline,
  CloudDoneOutline,
  CloudDownloadOutline,
  CloudOfflineOutline,
  CloudOutline,
  CloudUploadOutline,
  CloudyNightOutline,
  CloudyOutline,
  CodeDownloadOutline,
  CodeOutline,
  CodeSlashOutline,
  CodeWorkingOutline,
  CogOutline,
  ColorFillOutline,
  ColorFilterOutline,
  ColorPaletteOutline,
  ColorWandOutline,
  CompassOutline,
  ConstructOutline,
  ContractOutline,
  ContrastOutline,
  CopyOutline,
  CreateOutline,
  CropOutline,
  CubeOutline,
  CutOutline,
  DesktopOutline,
  DiamondOutline,
  DiceOutline,
  DiscOutline,
  DocumentAttachOutline,
  DocumentLockOutline,
  DocumentOutline,
  DocumentsOutline,
  DocumentTextOutline,
  DownloadOutline,
  DuplicateOutline,
  EarOutline,
  EarthOutline,
  EaselOutline,
  EggOutline,
  EllipseOutline,
  EllipsisHorizontalCircleOutline,
  EllipsisHorizontalOutline,
  EllipsisVerticalCircleOutline,
  EllipsisVerticalOutline,
  EnterOutline,
  ExitOutline,
  ExpandOutline,
  ExtensionPuzzleOutline,
  EyedropOutline,
  EyeOffOutline,
  EyeOutline,
  FastFoodOutline,
  FemaleOutline,
  FileTrayFullOutline,
  FileTrayOutline,
  FileTrayStackedOutline,
  FilmOutline,
  FilterCircleOutline,
  FilterOutline,
  FingerPrintOutline,
  FishOutline,
  FitnessOutline,
  FlagOutline,
  FlameOutline,
  FlashlightOutline,
  FlashOffOutline,
  FlashOutline,
  FlaskOutline,
  FlowerOutline,
  FolderOpenOutline,
  FolderOutline,
  FootballOutline,
  FootstepsOutline,
  FunnelOutline,
  GameControllerOutline,
  GiftOutline,
  GitBranchOutline,
  GitCommitOutline,
  GitCompareOutline,
  GitMergeOutline,
  GitNetworkOutline,
  GitPullRequestOutline,
  GlassesOutline,
  GlobeOutline,
  GolfOutline,
  GridOutline,
  HammerOutline,
  HandLeftOutline,
  HandRightOutline,
  HappyOutline,
  HardwareChipOutline,
  HeadsetOutline,
  HeartCircleOutline,
  HeartDislikeCircleOutline,
  HeartDislikeOutline,
  HeartHalfOutline,
  HeartOutline,
  HelpBuoyOutline,
  HelpCircleOutline,
  HelpOutline,
  HomeOutline,
  HourglassOutline,
  IceCreamOutline,
  IdCardOutline,
  ImageOutline,
  ImagesOutline,
  InfiniteOutline,
  InformationCircleOutline,
  InformationOutline,
  InvertModeOutline,
  JournalOutline,
  KeyOutline,
  KeypadOutline,
  LanguageOutline,
  LaptopOutline,
  LayersOutline,
  LeafOutline,
  LibraryOutline,
  LinkOutline,
  ListCircleOutline,
  ListOutline,
  LocateOutline,
  LocationOutline,
  LockClosedOutline,
  LockOpenOutline,
  LogInOutline,
  LogOutOutline,
  MagnetOutline,
  MailOpenOutline,
  MailOutline,
  MailUnreadOutline,
  MaleFemaleOutline,
  MaleOutline,
  ManOutline,
  MapOutline,
  MedalOutline,
  MedicalOutline,
  MedkitOutline,
  MegaphoneOutline,
  MenuOutline,
  MicCircleOutline,
  MicOffCircleOutline,
  MicOffOutline,
  MicOutline,
  MoonOutline,
  MoveOutline,
  MusicalNoteOutline,
  MusicalNotesOutline,
  NavigateCircleOutline,
  NavigateOutline,
  NewspaperOutline,
  NotificationsCircleOutline,
  NotificationsOffCircleOutline,
  NotificationsOffOutline,
  NotificationsOutline,
  NuclearOutline,
  NutritionOutline,
  OpenOutline,
  OptionsOutline,
  PaperPlaneOutline,
  PartlySunnyOutline,
  PauseCircleOutline,
  PauseOutline,
  PawOutline,
  PencilOutline,
  PeopleCircleOutline,
  PeopleOutline,
  PersonAddOutline,
  PersonCircleOutline,
  PersonOutline,
  PersonRemoveOutline,
  PhoneLandscapeOutline,
  PhonePortraitOutline,
  PieChartOutline,
  PinOutline,
  PintOutline,
  PizzaOutline,
  PlanetOutline,
  PlayBackCircleOutline,
  PlayBackOutline,
  PlayCircleOutline,
  PlayForwardCircleOutline,
  PlayForwardOutline,
  PlayOutline,
  PlaySkipBackCircleOutline,
  PlaySkipBackOutline,
  PlaySkipForwardCircleOutline,
  PlaySkipForwardOutline,
  PodiumOutline,
  PowerOutline,
  PricetagOutline,
  PricetagsOutline,
  PrintOutline,
  PrismOutline,
  PulseOutline,
  PushOutline,
  QrCodeOutline,
  RadioButtonOffOutline,
  RadioButtonOnOutline,
  RadioOutline,
  RainyOutline,
  ReaderOutline,
  ReceiptOutline,
  RecordingOutline,
  RefreshCircleOutline,
  RefreshOutline,
  ReloadCircleOutline,
  ReloadOutline,
  RemoveCircleOutline,
  RemoveOutline,
  ReorderFourOutline,
  ReorderThreeOutline,
  ReorderTwoOutline,
  RepeatOutline,
  ResizeOutline,
  RestaurantOutline,
  ReturnDownBackOutline,
  ReturnDownForwardOutline,
  ReturnUpBackOutline,
  ReturnUpForwardOutline,
  RibbonOutline,
  RocketOutline,
  RoseOutline,
  SadOutline,
  SaveOutline,
  ScaleOutline,
  ScanCircleOutline,
  ScanOutline,
  SchoolOutline,
  SearchCircleOutline,
  SearchOutline,
  SendOutline,
  ServerOutline,
  SettingsOutline,
  ShapesOutline,
  ShareOutline,
  ShareSocialOutline,
  ShieldCheckmarkOutline,
  ShieldHalfOutline,
  ShieldOutline,
  ShirtOutline,
  ShuffleOutline,
  SkullOutline,
  SnowOutline,
  SparklesOutline,
  SpeedometerOutline,
  SquareOutline,
  StarHalfOutline,
  StarOutline,
  StatsChartOutline,
  StopCircleOutline,
  StopOutline,
  StopwatchOutline,
  StorefrontOutline,
  SubwayOutline,
  SunnyOutline,
  SwapHorizontalOutline,
  SwapVerticalOutline,
  SyncCircleOutline,
  SyncOutline,
  TabletLandscapeOutline,
  TabletPortraitOutline,
  TelescopeOutline,
  TennisballOutline,
  TerminalOutline,
  TextOutline,
  ThermometerOutline,
  ThumbsDownOutline,
  ThumbsUpOutline,
  ThunderstormOutline,
  TicketOutline,
  TimeOutline,
  TimerOutline,
  TodayOutline,
  ToggleOutline,
  TrailSignOutline,
  TrainOutline,
  TransgenderOutline,
  TrashBinOutline,
  TrashOutline,
  TrendingDownOutline,
  TrendingUpOutline,
  TriangleOutline,
  TrophyOutline,
  TvOutline,
  UmbrellaOutline,
  UnlinkOutline,
  VideocamOffOutline,
  VideocamOutline,
  VolumeHighOutline,
  VolumeLowOutline,
  VolumeMediumOutline,
  VolumeMuteOutline,
  VolumeOffOutline,
  WalkOutline,
  WalletOutline,
  WarningOutline,
  WatchOutline,
  WaterOutline,
  WifiOutline,
  WineOutline,
  WomanOutline,
} from '@vicons/ionicons5';

//前端路由图标映射表
export const constantRouterIcon = {
  AccessibilityOutline: renderIcon(AccessibilityOutline),
  AddCircleOutline: renderIcon(AddCircleOutline),
  AddOutline: renderIcon(AddOutline),
  AirplaneOutline: renderIcon(AirplaneOutline),
  AlarmOutline: renderIcon(AlarmOutline),
  AlbumsOutline: renderIcon(AlbumsOutline),
  AlertCircleOutline: renderIcon(AlertCircleOutline),
  AlertOutline: renderIcon(AlertOutline),
  AmericanFootballOutline: renderIcon(AmericanFootballOutline),
  AnalyticsOutline: renderIcon(AnalyticsOutline),
  ApertureOutline: renderIcon(ApertureOutline),
  AppsOutline: renderIcon(AppsOutline),
  ArchiveOutline: renderIcon(ArchiveOutline),
  ArrowBackCircleOutline: renderIcon(ArrowBackCircleOutline),
  ArrowBackOutline: renderIcon(ArrowBackOutline),
  ArrowDownCircleOutline: renderIcon(ArrowDownCircleOutline),
  ArrowDownOutline: renderIcon(ArrowDownOutline),
  ArrowForwardCircleOutline: renderIcon(ArrowForwardCircleOutline),
  ArrowForwardOutline: renderIcon(ArrowForwardOutline),
  ArrowRedoCircleOutline: renderIcon(ArrowRedoCircleOutline),
  ArrowRedoOutline: renderIcon(ArrowRedoOutline),
  ArrowUndoCircleOutline: renderIcon(ArrowUndoCircleOutline),
  ArrowUndoOutline: renderIcon(ArrowUndoOutline),
  ArrowUpCircleOutline: renderIcon(ArrowUpCircleOutline),
  ArrowUpOutline: renderIcon(ArrowUpOutline),
  AtCircleOutline: renderIcon(AtCircleOutline),
  AtOutline: renderIcon(AtOutline),
  AttachOutline: renderIcon(AttachOutline),
  BackspaceOutline: renderIcon(BackspaceOutline),
  BagAddOutline: renderIcon(BagAddOutline),
  BagCheckOutline: renderIcon(BagCheckOutline),
  BagHandleOutline: renderIcon(BagHandleOutline),
  BagOutline: renderIcon(BagOutline),
  BagRemoveOutline: renderIcon(BagRemoveOutline),
  BalloonOutline: renderIcon(BalloonOutline),
  BanOutline: renderIcon(BanOutline),
  BandageOutline: renderIcon(BandageOutline),
  BarChartOutline: renderIcon(BarChartOutline),
  BarbellOutline: renderIcon(BarbellOutline),
  BarcodeOutline: renderIcon(BarcodeOutline),
  BaseballOutline: renderIcon(BaseballOutline),
  BasketOutline: renderIcon(BasketOutline),
  BasketballOutline: renderIcon(BasketballOutline),
  BatteryChargingOutline: renderIcon(BatteryChargingOutline),
  BatteryDeadOutline: renderIcon(BatteryDeadOutline),
  BatteryFullOutline: renderIcon(BatteryFullOutline),
  BatteryHalfOutline: renderIcon(BatteryHalfOutline),
  BeakerOutline: renderIcon(BeakerOutline),
  BedOutline: renderIcon(BedOutline),
  BeerOutline: renderIcon(BeerOutline),
  BicycleOutline: renderIcon(BicycleOutline),
  BluetoothOutline: renderIcon(BluetoothOutline),
  BoatOutline: renderIcon(BoatOutline),
  BodyOutline: renderIcon(BodyOutline),
  BonfireOutline: renderIcon(BonfireOutline),
  BookOutline: renderIcon(BookOutline),
  BookmarkOutline: renderIcon(BookmarkOutline),
  BookmarksOutline: renderIcon(BookmarksOutline),
  BowlingBallOutline: renderIcon(BowlingBallOutline),
  BriefcaseOutline: renderIcon(BriefcaseOutline),
  BrowsersOutline: renderIcon(BrowsersOutline),
  BrushOutline: renderIcon(BrushOutline),
  BugOutline: renderIcon(BugOutline),
  BuildOutline: renderIcon(BuildOutline),
  BulbOutline: renderIcon(BulbOutline),
  BusOutline: renderIcon(BusOutline),
  BusinessOutline: renderIcon(BusinessOutline),
  CafeOutline: renderIcon(CafeOutline),
  CalculatorOutline: renderIcon(CalculatorOutline),
  CalendarClearOutline: renderIcon(CalendarClearOutline),
  CalendarNumberOutline: renderIcon(CalendarNumberOutline),
  CalendarOutline: renderIcon(CalendarOutline),
  CallOutline: renderIcon(CallOutline),
  CameraOutline: renderIcon(CameraOutline),
  CameraReverseOutline: renderIcon(CameraReverseOutline),
  CarOutline: renderIcon(CarOutline),
  CarSportOutline: renderIcon(CarSportOutline),
  CardOutline: renderIcon(CardOutline),
  CaretBackCircleOutline: renderIcon(CaretBackCircleOutline),
  CaretBackOutline: renderIcon(CaretBackOutline),
  CaretDownCircleOutline: renderIcon(CaretDownCircleOutline),
  CaretDownOutline: renderIcon(CaretDownOutline),
  CaretForwardCircleOutline: renderIcon(CaretForwardCircleOutline),
  CaretForwardOutline: renderIcon(CaretForwardOutline),
  CaretUpCircleOutline: renderIcon(CaretUpCircleOutline),
  CaretUpOutline: renderIcon(CaretUpOutline),
  CartOutline: renderIcon(CartOutline),
  CashOutline: renderIcon(CashOutline),
  CellularOutline: renderIcon(CellularOutline),
  ChatboxEllipsesOutline: renderIcon(ChatboxEllipsesOutline),
  ChatboxOutline: renderIcon(ChatboxOutline),
  ChatbubbleEllipsesOutline: renderIcon(ChatbubbleEllipsesOutline),
  ChatbubbleOutline: renderIcon(ChatbubbleOutline),
  ChatbubblesOutline: renderIcon(ChatbubblesOutline),
  CheckboxOutline: renderIcon(CheckboxOutline),
  CheckmarkCircleOutline: renderIcon(CheckmarkCircleOutline),
  CheckmarkDoneCircleOutline: renderIcon(CheckmarkDoneCircleOutline),
  CheckmarkDoneOutline: renderIcon(CheckmarkDoneOutline),
  CheckmarkOutline: renderIcon(CheckmarkOutline),
  ChevronBackCircleOutline: renderIcon(ChevronBackCircleOutline),
  ChevronBackOutline: renderIcon(ChevronBackOutline),
  ChevronDownCircleOutline: renderIcon(ChevronDownCircleOutline),
  ChevronDownOutline: renderIcon(ChevronDownOutline),
  ChevronForwardCircleOutline: renderIcon(ChevronForwardCircleOutline),
  ChevronForwardOutline: renderIcon(ChevronForwardOutline),
  ChevronUpCircleOutline: renderIcon(ChevronUpCircleOutline),
  ChevronUpOutline: renderIcon(ChevronUpOutline),
  ClipboardOutline: renderIcon(ClipboardOutline),
  CloseCircleOutline: renderIcon(CloseCircleOutline),
  CloseOutline: renderIcon(CloseOutline),
  CloudCircleOutline: renderIcon(CloudCircleOutline),
  CloudDoneOutline: renderIcon(CloudDoneOutline),
  CloudDownloadOutline: renderIcon(CloudDownloadOutline),
  CloudOfflineOutline: renderIcon(CloudOfflineOutline),
  CloudOutline: renderIcon(CloudOutline),
  CloudUploadOutline: renderIcon(CloudUploadOutline),
  CloudyNightOutline: renderIcon(CloudyNightOutline),
  CloudyOutline: renderIcon(CloudyOutline),
  CodeDownloadOutline: renderIcon(CodeDownloadOutline),
  CodeOutline: renderIcon(CodeOutline),
  CodeSlashOutline: renderIcon(CodeSlashOutline),
  CodeWorkingOutline: renderIcon(CodeWorkingOutline),
  CogOutline: renderIcon(CogOutline),
  ColorFillOutline: renderIcon(ColorFillOutline),
  ColorFilterOutline: renderIcon(ColorFilterOutline),
  ColorPaletteOutline: renderIcon(ColorPaletteOutline),
  ColorWandOutline: renderIcon(ColorWandOutline),
  CompassOutline: renderIcon(CompassOutline),
  ConstructOutline: renderIcon(ConstructOutline),
  ContractOutline: renderIcon(ContractOutline),
  ContrastOutline: renderIcon(ContrastOutline),
  CopyOutline: renderIcon(CopyOutline),
  CreateOutline: renderIcon(CreateOutline),
  CropOutline: renderIcon(CropOutline),
  CubeOutline: renderIcon(CubeOutline),
  CutOutline: renderIcon(CutOutline),
  DesktopOutline: renderIcon(DesktopOutline),
  DiamondOutline: renderIcon(DiamondOutline),
  DiceOutline: renderIcon(DiceOutline),
  DiscOutline: renderIcon(DiscOutline),
  DocumentAttachOutline: renderIcon(DocumentAttachOutline),
  DocumentLockOutline: renderIcon(DocumentLockOutline),
  DocumentOutline: renderIcon(DocumentOutline),
  DocumentTextOutline: renderIcon(DocumentTextOutline),
  DocumentsOutline: renderIcon(DocumentsOutline),
  DownloadOutline: renderIcon(DownloadOutline),
  DuplicateOutline: renderIcon(DuplicateOutline),
  EarOutline: renderIcon(EarOutline),
  EarthOutline: renderIcon(EarthOutline),
  EaselOutline: renderIcon(EaselOutline),
  EggOutline: renderIcon(EggOutline),
  EllipseOutline: renderIcon(EllipseOutline),
  EllipsisHorizontalCircleOutline: renderIcon(EllipsisHorizontalCircleOutline),
  EllipsisHorizontalOutline: renderIcon(EllipsisHorizontalOutline),
  EllipsisVerticalCircleOutline: renderIcon(EllipsisVerticalCircleOutline),
  EllipsisVerticalOutline: renderIcon(EllipsisVerticalOutline),
  EnterOutline: renderIcon(EnterOutline),
  ExitOutline: renderIcon(ExitOutline),
  ExpandOutline: renderIcon(ExpandOutline),
  ExtensionPuzzleOutline: renderIcon(ExtensionPuzzleOutline),
  EyeOffOutline: renderIcon(EyeOffOutline),
  EyeOutline: renderIcon(EyeOutline),
  EyedropOutline: renderIcon(EyedropOutline),
  FastFoodOutline: renderIcon(FastFoodOutline),
  FemaleOutline: renderIcon(FemaleOutline),
  FileTrayFullOutline: renderIcon(FileTrayFullOutline),
  FileTrayOutline: renderIcon(FileTrayOutline),
  FileTrayStackedOutline: renderIcon(FileTrayStackedOutline),
  FilmOutline: renderIcon(FilmOutline),
  FilterCircleOutline: renderIcon(FilterCircleOutline),
  FilterOutline: renderIcon(FilterOutline),
  FingerPrintOutline: renderIcon(FingerPrintOutline),
  FishOutline: renderIcon(FishOutline),
  FitnessOutline: renderIcon(FitnessOutline),
  FlagOutline: renderIcon(FlagOutline),
  FlameOutline: renderIcon(FlameOutline),
  FlashOffOutline: renderIcon(FlashOffOutline),
  FlashOutline: renderIcon(FlashOutline),
  FlashlightOutline: renderIcon(FlashlightOutline),
  FlaskOutline: renderIcon(FlaskOutline),
  FlowerOutline: renderIcon(FlowerOutline),
  FolderOpenOutline: renderIcon(FolderOpenOutline),
  FolderOutline: renderIcon(FolderOutline),
  FootballOutline: renderIcon(FootballOutline),
  FootstepsOutline: renderIcon(FootstepsOutline),
  FunnelOutline: renderIcon(FunnelOutline),
  GameControllerOutline: renderIcon(GameControllerOutline),
  GiftOutline: renderIcon(GiftOutline),
  GitBranchOutline: renderIcon(GitBranchOutline),
  GitCommitOutline: renderIcon(GitCommitOutline),
  GitCompareOutline: renderIcon(GitCompareOutline),
  GitMergeOutline: renderIcon(GitMergeOutline),
  GitNetworkOutline: renderIcon(GitNetworkOutline),
  GitPullRequestOutline: renderIcon(GitPullRequestOutline),
  GlassesOutline: renderIcon(GlassesOutline),
  GlobeOutline: renderIcon(GlobeOutline),
  GolfOutline: renderIcon(GolfOutline),
  GridOutline: renderIcon(GridOutline),
  HammerOutline: renderIcon(HammerOutline),
  HandLeftOutline: renderIcon(HandLeftOutline),
  HandRightOutline: renderIcon(HandRightOutline),
  HappyOutline: renderIcon(HappyOutline),
  HardwareChipOutline: renderIcon(HardwareChipOutline),
  HeadsetOutline: renderIcon(HeadsetOutline),
  HeartCircleOutline: renderIcon(HeartCircleOutline),
  HeartDislikeCircleOutline: renderIcon(HeartDislikeCircleOutline),
  HeartDislikeOutline: renderIcon(HeartDislikeOutline),
  HeartHalfOutline: renderIcon(HeartHalfOutline),
  HeartOutline: renderIcon(HeartOutline),
  HelpBuoyOutline: renderIcon(HelpBuoyOutline),
  HelpCircleOutline: renderIcon(HelpCircleOutline),
  HelpOutline: renderIcon(HelpOutline),
  HomeOutline: renderIcon(HomeOutline),
  HourglassOutline: renderIcon(HourglassOutline),
  IceCreamOutline: renderIcon(IceCreamOutline),
  IdCardOutline: renderIcon(IdCardOutline),
  ImageOutline: renderIcon(ImageOutline),
  ImagesOutline: renderIcon(ImagesOutline),
  InfiniteOutline: renderIcon(InfiniteOutline),
  InformationCircleOutline: renderIcon(InformationCircleOutline),
  InformationOutline: renderIcon(InformationOutline),
  InvertModeOutline: renderIcon(InvertModeOutline),
  JournalOutline: renderIcon(JournalOutline),
  KeyOutline: renderIcon(KeyOutline),
  KeypadOutline: renderIcon(KeypadOutline),
  LanguageOutline: renderIcon(LanguageOutline),
  LaptopOutline: renderIcon(LaptopOutline),
  LayersOutline: renderIcon(LayersOutline),
  LeafOutline: renderIcon(LeafOutline),
  LibraryOutline: renderIcon(LibraryOutline),
  LinkOutline: renderIcon(LinkOutline),
  ListCircleOutline: renderIcon(ListCircleOutline),
  ListOutline: renderIcon(ListOutline),
  LocateOutline: renderIcon(LocateOutline),
  LocationOutline: renderIcon(LocationOutline),
  LockClosedOutline: renderIcon(LockClosedOutline),
  LockOpenOutline: renderIcon(LockOpenOutline),
  LogInOutline: renderIcon(LogInOutline),
  LogOutOutline: renderIcon(LogOutOutline),
  MagnetOutline: renderIcon(MagnetOutline),
  MailOpenOutline: renderIcon(MailOpenOutline),
  MailOutline: renderIcon(MailOutline),
  MailUnreadOutline: renderIcon(MailUnreadOutline),
  MaleFemaleOutline: renderIcon(MaleFemaleOutline),
  MaleOutline: renderIcon(MaleOutline),
  ManOutline: renderIcon(ManOutline),
  MapOutline: renderIcon(MapOutline),
  MedalOutline: renderIcon(MedalOutline),
  MedicalOutline: renderIcon(MedicalOutline),
  MedkitOutline: renderIcon(MedkitOutline),
  MegaphoneOutline: renderIcon(MegaphoneOutline),
  MenuOutline: renderIcon(MenuOutline),
  MicCircleOutline: renderIcon(MicCircleOutline),
  MicOffCircleOutline: renderIcon(MicOffCircleOutline),
  MicOffOutline: renderIcon(MicOffOutline),
  MicOutline: renderIcon(MicOutline),
  MoonOutline: renderIcon(MoonOutline),
  MoveOutline: renderIcon(MoveOutline),
  MusicalNoteOutline: renderIcon(MusicalNoteOutline),
  MusicalNotesOutline: renderIcon(MusicalNotesOutline),
  NavigateCircleOutline: renderIcon(NavigateCircleOutline),
  NavigateOutline: renderIcon(NavigateOutline),
  NewspaperOutline: renderIcon(NewspaperOutline),
  NotificationsCircleOutline: renderIcon(NotificationsCircleOutline),
  NotificationsOffCircleOutline: renderIcon(NotificationsOffCircleOutline),
  NotificationsOffOutline: renderIcon(NotificationsOffOutline),
  NotificationsOutline: renderIcon(NotificationsOutline),
  NuclearOutline: renderIcon(NuclearOutline),
  NutritionOutline: renderIcon(NutritionOutline),
  OpenOutline: renderIcon(OpenOutline),
  OptionsOutline: renderIcon(OptionsOutline),
  PaperPlaneOutline: renderIcon(PaperPlaneOutline),
  PartlySunnyOutline: renderIcon(PartlySunnyOutline),
  PauseCircleOutline: renderIcon(PauseCircleOutline),
  PauseOutline: renderIcon(PauseOutline),
  PawOutline: renderIcon(PawOutline),
  PencilOutline: renderIcon(PencilOutline),
  PeopleCircleOutline: renderIcon(PeopleCircleOutline),
  PeopleOutline: renderIcon(PeopleOutline),
  PersonAddOutline: renderIcon(PersonAddOutline),
  PersonCircleOutline: renderIcon(PersonCircleOutline),
  PersonOutline: renderIcon(PersonOutline),
  PersonRemoveOutline: renderIcon(PersonRemoveOutline),
  PhoneLandscapeOutline: renderIcon(PhoneLandscapeOutline),
  PhonePortraitOutline: renderIcon(PhonePortraitOutline),
  PieChartOutline: renderIcon(PieChartOutline),
  PinOutline: renderIcon(PinOutline),
  PintOutline: renderIcon(PintOutline),
  PizzaOutline: renderIcon(PizzaOutline),
  PlanetOutline: renderIcon(PlanetOutline),
  PlayBackCircleOutline: renderIcon(PlayBackCircleOutline),
  PlayBackOutline: renderIcon(PlayBackOutline),
  PlayCircleOutline: renderIcon(PlayCircleOutline),
  PlayForwardCircleOutline: renderIcon(PlayForwardCircleOutline),
  PlayForwardOutline: renderIcon(PlayForwardOutline),
  PlayOutline: renderIcon(PlayOutline),
  PlaySkipBackCircleOutline: renderIcon(PlaySkipBackCircleOutline),
  PlaySkipBackOutline: renderIcon(PlaySkipBackOutline),
  PlaySkipForwardCircleOutline: renderIcon(PlaySkipForwardCircleOutline),
  PlaySkipForwardOutline: renderIcon(PlaySkipForwardOutline),
  PodiumOutline: renderIcon(PodiumOutline),
  PowerOutline: renderIcon(PowerOutline),
  PricetagOutline: renderIcon(PricetagOutline),
  PricetagsOutline: renderIcon(PricetagsOutline),
  PrintOutline: renderIcon(PrintOutline),
  PrismOutline: renderIcon(PrismOutline),
  PulseOutline: renderIcon(PulseOutline),
  PushOutline: renderIcon(PushOutline),
  QrCodeOutline: renderIcon(QrCodeOutline),
  RadioButtonOffOutline: renderIcon(RadioButtonOffOutline),
  RadioButtonOnOutline: renderIcon(RadioButtonOnOutline),
  RadioOutline: renderIcon(RadioOutline),
  RainyOutline: renderIcon(RainyOutline),
  ReaderOutline: renderIcon(ReaderOutline),
  ReceiptOutline: renderIcon(ReceiptOutline),
  RecordingOutline: renderIcon(RecordingOutline),
  RefreshCircleOutline: renderIcon(RefreshCircleOutline),
  RefreshOutline: renderIcon(RefreshOutline),
  ReloadCircleOutline: renderIcon(ReloadCircleOutline),
  ReloadOutline: renderIcon(ReloadOutline),
  RemoveCircleOutline: renderIcon(RemoveCircleOutline),
  RemoveOutline: renderIcon(RemoveOutline),
  ReorderFourOutline: renderIcon(ReorderFourOutline),
  ReorderThreeOutline: renderIcon(ReorderThreeOutline),
  ReorderTwoOutline: renderIcon(ReorderTwoOutline),
  RepeatOutline: renderIcon(RepeatOutline),
  ResizeOutline: renderIcon(ResizeOutline),
  RestaurantOutline: renderIcon(RestaurantOutline),
  ReturnDownBackOutline: renderIcon(ReturnDownBackOutline),
  ReturnDownForwardOutline: renderIcon(ReturnDownForwardOutline),
  ReturnUpBackOutline: renderIcon(ReturnUpBackOutline),
  ReturnUpForwardOutline: renderIcon(ReturnUpForwardOutline),
  RibbonOutline: renderIcon(RibbonOutline),
  RocketOutline: renderIcon(RocketOutline),
  RoseOutline: renderIcon(RoseOutline),
  SadOutline: renderIcon(SadOutline),
  SaveOutline: renderIcon(SaveOutline),
  ScaleOutline: renderIcon(ScaleOutline),
  ScanCircleOutline: renderIcon(ScanCircleOutline),
  ScanOutline: renderIcon(ScanOutline),
  SchoolOutline: renderIcon(SchoolOutline),
  SearchCircleOutline: renderIcon(SearchCircleOutline),
  SearchOutline: renderIcon(SearchOutline),
  SendOutline: renderIcon(SendOutline),
  ServerOutline: renderIcon(ServerOutline),
  SettingsOutline: renderIcon(SettingsOutline),
  ShapesOutline: renderIcon(ShapesOutline),
  ShareOutline: renderIcon(ShareOutline),
  ShareSocialOutline: renderIcon(ShareSocialOutline),
  ShieldCheckmarkOutline: renderIcon(ShieldCheckmarkOutline),
  ShieldHalfOutline: renderIcon(ShieldHalfOutline),
  ShieldOutline: renderIcon(ShieldOutline),
  ShirtOutline: renderIcon(ShirtOutline),
  ShuffleOutline: renderIcon(ShuffleOutline),
  SkullOutline: renderIcon(SkullOutline),
  SnowOutline: renderIcon(SnowOutline),
  SparklesOutline: renderIcon(SparklesOutline),
  SpeedometerOutline: renderIcon(SpeedometerOutline),
  SquareOutline: renderIcon(SquareOutline),
  StarHalfOutline: renderIcon(StarHalfOutline),
  StarOutline: renderIcon(StarOutline),
  StatsChartOutline: renderIcon(StatsChartOutline),
  StopCircleOutline: renderIcon(StopCircleOutline),
  StopOutline: renderIcon(StopOutline),
  StopwatchOutline: renderIcon(StopwatchOutline),
  StorefrontOutline: renderIcon(StorefrontOutline),
  SubwayOutline: renderIcon(SubwayOutline),
  SunnyOutline: renderIcon(SunnyOutline),
  SwapHorizontalOutline: renderIcon(SwapHorizontalOutline),
  SwapVerticalOutline: renderIcon(SwapVerticalOutline),
  SyncCircleOutline: renderIcon(SyncCircleOutline),
  SyncOutline: renderIcon(SyncOutline),
  TabletLandscapeOutline: renderIcon(TabletLandscapeOutline),
  TabletPortraitOutline: renderIcon(TabletPortraitOutline),
  TelescopeOutline: renderIcon(TelescopeOutline),
  TennisballOutline: renderIcon(TennisballOutline),
  TerminalOutline: renderIcon(TerminalOutline),
  TextOutline: renderIcon(TextOutline),
  ThermometerOutline: renderIcon(ThermometerOutline),
  ThumbsDownOutline: renderIcon(ThumbsDownOutline),
  ThumbsUpOutline: renderIcon(ThumbsUpOutline),
  ThunderstormOutline: renderIcon(ThunderstormOutline),
  TicketOutline: renderIcon(TicketOutline),
  TimeOutline: renderIcon(TimeOutline),
  TimerOutline: renderIcon(TimerOutline),
  TodayOutline: renderIcon(TodayOutline),
  ToggleOutline: renderIcon(ToggleOutline),
  TrailSignOutline: renderIcon(TrailSignOutline),
  TrainOutline: renderIcon(TrainOutline),
  TransgenderOutline: renderIcon(TransgenderOutline),
  TrashBinOutline: renderIcon(TrashBinOutline),
  TrashOutline: renderIcon(TrashOutline),
  TrendingDownOutline: renderIcon(TrendingDownOutline),
  TrendingUpOutline: renderIcon(TrendingUpOutline),
  TriangleOutline: renderIcon(TriangleOutline),
  TrophyOutline: renderIcon(TrophyOutline),
  TvOutline: renderIcon(TvOutline),
  UmbrellaOutline: renderIcon(UmbrellaOutline),
  UnlinkOutline: renderIcon(UnlinkOutline),
  VideocamOffOutline: renderIcon(VideocamOffOutline),
  VideocamOutline: renderIcon(VideocamOutline),
  VolumeHighOutline: renderIcon(VolumeHighOutline),
  VolumeLowOutline: renderIcon(VolumeLowOutline),
  VolumeMediumOutline: renderIcon(VolumeMediumOutline),
  VolumeMuteOutline: renderIcon(VolumeMuteOutline),
  VolumeOffOutline: renderIcon(VolumeOffOutline),
  WalkOutline: renderIcon(WalkOutline),
  WalletOutline: renderIcon(WalletOutline),
  WarningOutline: renderIcon(WarningOutline),
  WatchOutline: renderIcon(WatchOutline),
  WaterOutline: renderIcon(WaterOutline),
  WifiOutline: renderIcon(WifiOutline),
  WineOutline: renderIcon(WineOutline),
  WomanOutline: renderIcon(WomanOutline),
};
