<template>
  <div style="width: 100%; height: calc(100vh - 16px); position: relative; overflow: hidden;">
    <!-- 按钮容器，置于右上角 -->
    <div
      style="position: absolute; top: 10px; right: 10px; display: flex; gap: 10px; z-index: 1000;">
      <button @click="save"
              style="padding: 5px 10px; background-color: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">
        保存
      </button>
      <button @click="tryRunning"
              style="padding: 5px 10px; background-color: #008CBA; color: white; border: none; border-radius: 4px; cursor: pointer;">
        试运行
      </button>
    </div>
    <Tinyflow
      v-if="dataLoaded"
      :className="'custom-class'"
      :style="{ width: '100%', height: '100%' }"
      :data="initialData"
      :provider="provider"
      ref="tinyflowRef"
    />

    <el-dialog
      v-model="dialogVisible"
      title="试运行参数"
      width="30%"
    >
      <form v-if="parameters">
        <div v-for="(param, index) in parameters" :key="index" class="mb-4">
          <label :for="param.name" class="block mb-1 font-medium">
            {{ param.name }}
            <span v-if="param.required" class="text-red-500">*</span>
          </label>
          <input
            :id="param.name"
            type="text"
            v-model="formData[param.name]"
            :placeholder="param.description"
            class="w-full p-2 border border-gray-300 rounded"
          />
        </div>
      </form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {Tinyflow} from '@tinyflow-ai/vue';
import '@tinyflow-ai/vue/dist/index.css';
import {onMounted, reactive, ref} from 'vue';
import {commit, detail, getRunningParameters,queryModelList,queryKnowledgeList,tryRunning as tryRunningCommit} from '@/api/aigc/workflow';
import {useMessage,useNotification} from "naive-ui";
import {ElButton, ElDialog} from 'element-plus';
import 'element-plus/dist/index.css';
import {useRoute} from 'vue-router';

const message = useMessage();
const tinyflowRef = ref<InstanceType<typeof Tinyflow> | null>(null);

const route = useRoute();
const id = route.query.id;

const provider = {
  llm: () => [
    {
      value: 'llm',
      label: 'llm',
    }
  ],
  knowledge: () => [],
}
const initialData = ref({
  nodes: [],
  edges: [],
});
const parameters = ref<any[] | undefined>(
  [
    {
      "id": "g3qfdWpJAsAiHoZx",
      "name": "test",
      "description": null,
      "dataType": null,
      "ref": null,
      "refType": null,
      "value": null,
      "required": true,
      "defaultValue": null,
      "children": null
    },
    {
      "id": "4yGuWCCRLSR7IU9o",
      "name": "a",
      "description": null,
      "dataType": null,
      "ref": null,
      "refType": null,
      "value": null,
      "required": true,
      "defaultValue": null,
      "children": null
    }
  ]
);
// 用于存储表单数据
const formData = reactive({});

const workflowData = ref({});

// 控制对话框显示与隐藏
const dialogVisible = ref(false);
// 提交表单的方法
const notification = useNotification();

// 表单提交事件
const submitForm = async () => {
  dialogVisible.value = false;
  const data = await tryRunningCommit({
    id:id,
    variables:formData
  });
  // 尝试获取 message 下的 content

  if (data && data.output ) {
    const content = data.output;
    // 使用 naive-ui 的 message 组件展示内容
    notification.info({
      title: '执行结果',
      content: content,
      duration: 0, // 不自动关闭
      closable: true, // 允许手动关闭
      dangerouslyUseHTMLString: true
    });
  } else {
    message.warning('未找到有效的财务分析报告内容');
  }
};
const dataLoaded = ref(false);
async function save() {


  await commit({id:id,name: id?null:'流程', graph: JSON.stringify(tinyflowRef.value?.getData())});
  message.success('保存成功');
}

onMounted(async () => {
  //初始化知识库及模型
  await queryModel();
  await queryKnowledge();

  if (id) {
    try {
      const data = await detail({ id });
      workflowData.value = data;
      initialData.value = JSON.parse(data.graph);
    } catch (error) {
      console.error('解析 JSON 数据出错:', error);
    } finally {
      dataLoaded.value = true;
    }
  } else {
    dataLoaded.value = true;
  }
})


// 试运行按钮点击事件
const tryRunning = async() => {
  if(id){
    const data = await getRunningParameters({id});
    parameters.value = data;
    dialogVisible.value = true;
  }else {
    message.warning('请先保存流程再尝试试运行！');
  }
};

const queryModel = async() => {
  const data = await queryModelList();
  provider.llm=()=>data;
}

const queryKnowledge = async() => {
  const data =  await queryKnowledgeList();
  provider.knowledge=()=>data;
}
</script>
