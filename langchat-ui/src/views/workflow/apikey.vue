<script lang="ts" setup>
  import ApiChannel from './workflow-api/index.vue';
  import SvgIcon from '@/components/SvgIcon/index.vue';

  import { onMounted, ref } from 'vue';
  import { detail } from '@/api/aigc/workflow';
  import router from "@/router";

  const form = ref<any>({});
  const loading = ref(false);

  onMounted(async () => {
    await fetchData();
  });


  async function fetchData() {
    loading.value = true;
    const id = router.currentRoute.value.params.id;
    const data = await detail({
      id: id,
    });
    form.value = data;
    loading.value = false;
  }
</script>

<template>
  <div v-if="form.name !== undefined" class="rounded bg-[#f9f9f9] w-full h-full pb-10">
    <div class="p-4 flex justify-between items-center bg-white rounded">
      <div class="flex gap-5 items-center min-w-20">
        <n-button text type="primary" @click="router.push('/app/list')">
          <SvgIcon class="text-xl" icon="icon-park-outline:back" />
        </n-button>
        <div class="flex gap-2 items-center pr-4">
          <div class="mr-3">
            <div class="relative bg-orange-100 p-4 rounded-lg">
              <SvgIcon class="text-3xl" icon="prime:microchip-ai" />
              <div
                class="absolute bottom-[-6px] p-1 right-[-5px] shadow bg-white mx-auto rounded-lg"
              >
                <SvgIcon class="text-sm text-orange-500" icon="lucide:bot" />
              </div>
            </div>
          </div>

          <div class="flex flex-col justify-between gap-2">
            <div class="font-bold text-lg">{{ form.name }}</div>
            <div v-if="!loading" class="text-gray-400 text-xs">{{ form.craterTime }}</div>
          </div>
        </div>
      </div>
    </div>
    <ApiChannel />
  </div>
</template>

<style lang="less" scoped></style>
