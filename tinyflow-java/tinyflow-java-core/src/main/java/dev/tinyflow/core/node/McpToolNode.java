/*
 * Copyright (c) 2025-2026, <PERSON> 杨福海 (<EMAIL>).
 * <p>
 * Licensed under the GNU Lesser General Public License (LGPL) ,Version 3.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl-3.0.txt
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package dev.tinyflow.core.node;

import com.agentsflex.core.chain.Chain;
import com.agentsflex.core.chain.node.BaseNode;
import com.agentsflex.core.prompt.template.TextPromptTemplate;
import com.agentsflex.core.util.Maps;
import com.agentsflex.core.util.StringUtil;
import dev.tinyflow.core.provider.McpProvider;
import lombok.Getter;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * MCP工具调用节点
 * 在工作流中调用MCP服务的工具
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
@Getter
@Setter
public class McpToolNode extends BaseNode {

    private static final Logger logger = LoggerFactory.getLogger(McpToolNode.class);

    /**
     * MCP服务名称
     */
    private String serviceName;

    /**
     * 工具名称
     */
    private String toolName;

    /**
     * 工具参数（支持模板变量）
     */
    private Map<String, String> toolParameters = new HashMap<>();

    /**
     * 输出变量名
     */
    private String outputVariable = "mcpResult";

    /**
     * 是否需要确认
     */
    private boolean requiresConfirmation = false;

    /**
     * 超时时间（秒）
     */
    private int timeout = 30;

    /**
     * MCP提供者
     */
    private transient McpProvider mcpProvider;

    @Override
    protected Map<String, Object> execute(Chain chain) {
        if (mcpProvider == null) {
            logger.error("MCP提供者未设置");
            return Collections.emptyMap();
        }

        if (StringUtil.noText(serviceName) || StringUtil.noText(toolName)) {
            logger.error("服务名称或工具名称未设置");
            return Collections.emptyMap();
        }

        try {
            // 获取当前链的参数值
            Map<String, Object> chainParams = chain.getParameterValues(this);
            
            // 处理工具参数模板
            Map<String, Object> processedParams = processParameters(chainParams);
            
            logger.info("调用MCP工具: 服务={}, 工具={}, 参数={}", serviceName, toolName, processedParams);
            
            // 调用MCP工具
            Object result = mcpProvider.callTool(serviceName, toolName, processedParams, timeout);
            
            if (result != null) {
                logger.info("MCP工具调用成功，结果: {}", result);
                return Maps.of(outputVariable, result);
            } else {
                logger.warn("MCP工具调用返回空结果");
                return Maps.of(outputVariable, "");
            }
            
        } catch (Exception e) {
            logger.error("MCP工具调用失败", e);
            return Maps.of(outputVariable, "错误: " + e.getMessage());
        }
    }

    /**
     * 处理参数模板
     */
    private Map<String, Object> processParameters(Map<String, Object> chainParams) {
        Map<String, Object> processedParams = new HashMap<>();
        
        for (Map.Entry<String, String> entry : toolParameters.entrySet()) {
            String key = entry.getKey();
            String valueTemplate = entry.getValue();
            
            if (StringUtil.hasText(valueTemplate)) {
                try {
                    // 使用模板引擎处理参数值
                    String processedValue = TextPromptTemplate.create(valueTemplate).formatToString(chainParams);
                    processedParams.put(key, processedValue);
                } catch (Exception e) {
                    logger.warn("处理参数模板失败: {} = {}", key, valueTemplate, e);
                    processedParams.put(key, valueTemplate);
                }
            }
        }
        
        return processedParams;
    }

    /**
     * 设置MCP提供者
     */
    public void setMcpProvider(McpProvider mcpProvider) {
        this.mcpProvider = mcpProvider;
    }

    /**
     * 添加工具参数
     */
    public void addParameter(String name, String value) {
        this.toolParameters.put(name, value);
    }

    /**
     * 获取节点描述
     */
    @Override
    public String getDescription() {
        return String.format("MCP工具调用节点 - 服务: %s, 工具: %s", serviceName, toolName);
    }
}
