/**
 * Copyright (c) 2025-2026, <PERSON> 杨福海 (<EMAIL>).
 * <p>
 * Licensed under the GNU Lesser General Public License (LGPL) ,Version 3.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl-3.0.txt
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package dev.tinyflow.core.knowledge;

import com.agentsflex.core.chain.Chain;
import com.agentsflex.core.document.Document;
import dev.tinyflow.core.node.KnowledgeNode;

import java.util.List;

public interface Knowledge {

    List<Document> search(String keyword, int limit, KnowledgeNode knowledgeNode, Chain chain);

}
