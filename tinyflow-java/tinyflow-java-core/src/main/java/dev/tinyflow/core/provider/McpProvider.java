/*
 * Copyright (c) 2025-2026, <PERSON> 杨福海 (<EMAIL>).
 * <p>
 * Licensed under the GNU Lesser General Public License (LGPL) ,Version 3.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl-3.0.txt
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package dev.tinyflow.core.provider;

import java.util.Map;

/**
 * MCP提供者接口
 * 为工作流提供MCP服务调用能力
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
public interface McpProvider {

    /**
     * 调用MCP工具
     * 
     * @param serviceName 服务名称
     * @param toolName 工具名称
     * @param parameters 参数
     * @param timeout 超时时间（秒）
     * @return 调用结果
     */
    Object callTool(String serviceName, String toolName, Map<String, Object> parameters, int timeout);

    /**
     * 检查服务是否可用
     * 
     * @param serviceName 服务名称
     * @return 是否可用
     */
    boolean isServiceAvailable(String serviceName);

    /**
     * 获取服务的工具列表
     * 
     * @param serviceName 服务名称
     * @return 工具列表
     */
    Object getServiceTools(String serviceName);
}
