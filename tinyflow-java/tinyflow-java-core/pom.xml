<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>dev.tinyflow</groupId>
        <artifactId>tinyflow-java</artifactId>
        <version>1.1.0</version>
    </parent>

    <name>tinyflow-java-core</name>
    <artifactId>tinyflow-java-core</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.agentsflex</groupId>
            <artifactId>agents-flex-bom</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jfinal</groupId>
            <artifactId>enjoy</artifactId>
        </dependency>

    </dependencies>

</project>