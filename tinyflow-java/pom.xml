<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>dev.tinyflow</groupId>
    <artifactId>tinyflow-java</artifactId>
    <version>1.1.0</version>
    <packaging>pom</packaging>

    <url>https://tinyflow.dev</url>
    <description>Tinyflow-java is an intelligent agent orchestration solution developed using Java. It is not a product,
        but a development component. By integrating Tinyflow-java, you can make any traditional Java Web application
        have the ability to orchestrate AI intelligent agents.
    </description>



    <modules>
        <module>tinyflow-java-core</module>
    </modules>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.agentsflex</groupId>
                <artifactId>agents-flex-bom</artifactId>
                <version>1.1.2-snapshot</version>
            </dependency>

            <!--使用 enjoy 模板引擎-->
            <dependency>
                <groupId>com.jfinal</groupId>
                <artifactId>enjoy</artifactId>
                <version>5.1.3</version>
            </dependency>
        </dependencies>
    </dependencyManagement>


    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.10.0</version>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                </configuration>
            </plugin>

            <!-- Source -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.2.1</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

        </plugins>
    </build>


    <!-- 跳过测试：mvn package -Dmaven.test.skip=true   -->
    <!-- 检测依赖最新版本：mvn versions:display-dependency-updates  -->
    <!-- 统一修改版本号：mvn versions:set -DnewVersion=3.0  -->
    <!-- mvn -N versions:update-child-modules  -->
    <!-- mvn versions:set -DnewVersion=2.0 -DprocessAllModules=true -DallowSnapshots=true -->

    <!-- mvn clean source:jar install -->
    <!-- mvn deploy -Dmaven.test.skip=true -e -P release -->
    <!-- mvn deploy -e -->
    <distributionManagement>
        <repository>
            <id>release</id>
            <name>jrzh-release</name>
            <url>https://test.jingruiit.com:18481/nexus/content/repositories/releases/</url>
        </repository>

        <snapshotRepository>
            <id>snapshot</id>
            <name>jrzh-snapshot</name>
            <url>https://test.jingruiit.com:18481/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>


    <repositories>
        <repository>
            <id>snapshot</id>
            <name>snapshot maven</name>
            <url>https://ceshi.jingruiit.com:18481/nexus/content/repositories/snapshots/</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>

        <repository>
            <id>release</id>
            <name>release maven</name>
            <url>https://ceshi.jingruiit.com:18481/nexus/content/groups/public/</url>
        </repository>
    </repositories>
</project>