/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.common.core.constant;

/**
 * <AUTHOR>
 * @since 2024/1/15
 */
public interface CommonConst {

    /**
     * UTF-8 编码
     */
    String UTF_8 = "utf-8";

    /**
     * 菜单类型：menu
     */
    String MENU_TYPE_MENU = "menu";

    /**
     * 菜单类型：button
     */
    String MENU_TYPE_BUTTON = "button";

    /**
     * 菜单：默认Icon图标
     */
    String MENU_ICON = "alert";

    String LAYOUT = "Layout";
    /**
     * 生效
     */
    Integer EFFECTIVE = 1;
    /**
     * 失效
     */
    Integer NOT_EFFECTIVE = 0;
}
