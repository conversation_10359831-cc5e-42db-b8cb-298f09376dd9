<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
  ~
  ~ Licensed under the GNU Affero General Public License, Version 3 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     https://www.gnu.org/licenses/agpl-3.0.html
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>cn.tycoding</groupId>
    <artifactId>langchat-common-bom</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <properties>
        <revision>1.0.0</revision>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- AI模块 -->
            <dependency>
                <groupId>cn.tycoding</groupId>
                <artifactId>langchat-ai</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.tycoding</groupId>
                <artifactId>langchat-ai-core</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.tycoding</groupId>
                <artifactId>langchat-ai-biz</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- Upms模块 -->
            <dependency>
                <groupId>cn.tycoding</groupId>
                <artifactId>langchat-upms-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.tycoding</groupId>
                <artifactId>langchat-upms-biz</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 公共模块 -->
            <dependency>
                <groupId>cn.langchat</groupId>
                <artifactId>langchat-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.langchat</groupId>
                <artifactId>langchat-common-bom</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.tycoding</groupId>
                <artifactId>langchat-common-core</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.tycoding</groupId>
                <artifactId>langchat-common-auth</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.tycoding</groupId>
                <artifactId>langchat-common-ai</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.tycoding</groupId>
                <artifactId>langchat-common-oss</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 认证模块 -->
            <dependency>
                <groupId>cn.tycoding</groupId>
                <artifactId>langchat-auth</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>${snakeyaml.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql-connector.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>
