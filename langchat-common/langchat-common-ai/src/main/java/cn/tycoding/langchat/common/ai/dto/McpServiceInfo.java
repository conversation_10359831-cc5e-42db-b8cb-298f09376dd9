/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.common.ai.dto;

import lombok.Data;

/**
 * MCP服务信息DTO
 * 用于在不同模块间传递MCP服务信息，避免循环依赖
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
@Data
public class McpServiceInfo {

    /**
     * 服务ID
     */
    private String id;

    /**
     * 服务名称
     */
    private String name;

    /**
     * 显示名称
     */
    private String displayName;

    /**
     * 服务描述
     */
    private String description;

    /**
     * 服务端点
     */
    private String endpoint;

    /**
     * 服务类型
     */
    private String type;

    /**
     * 服务分类
     */
    private String category;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 工具列表JSON
     */
    private String tools;
}
