<assembly>
    <!--
        必须写，否则打包时会有 assembly ID must be present and non-empty 错误
        这个名字最终会追加到打包的名字的末尾，如项目的名字为 hangge-test-0.0.1-SNAPSHOT,
        则最终生成的包名为 hangge-test-0.0.1-SNAPSHOT-bin.tar.gz
     -->
    <id>bin</id>
    <!-- 打包的类型，如果有N个，将会打N个类型的包 -->
    <formats>
        <format>tar.gz</format>
        <!--<format>zip</format>-->
    </formats>
    <includeBaseDirectory>true</includeBaseDirectory>
    <!--文件设置-->
    <fileSets>
        <!--
            0755->即用户具有读/写/执行权限，组用户和其它用户具有读写权限；
            0644->即用户具有读写权限，组用户和其它用户具有只读权限；
        -->
        <!-- 将script/assembly/bin目录下的所有文件输出到打包后的bin目录中 -->
        <fileSet>
            <directory>script/assembly/bin</directory>
            <outputDirectory>bin</outputDirectory>
            <fileMode>0755</fileMode>
            <!--如果是脚本，一定要改为unix.如果是在windows上面编码，会出现dos编写问题-->
            <lineEnding>unix</lineEnding>
            <filtered>true</filtered><!-- 是否进行属性替换 -->
        </fileSet>
        <!-- 将script/assembly/config目录下的所有文件输出到打包后的config目录中 -->
        <fileSet>
            <directory>script/assembly/config</directory>
            <outputDirectory>config</outputDirectory>
            <fileMode>0644</fileMode>
        </fileSet>
        <!-- 将src/main/resources下配置文件打包到config目录 -->
        <fileSet>
            <directory>src/main/resources</directory>
            <outputDirectory>/config</outputDirectory>
            <includes>
                <include>**/*.properties</include>
                <include>**/*.yml</include>
                <include>banner.txt</include>
            </includes>
            <filtered>true</filtered><!-- 是否进行属性替换 -->
        </fileSet>
        <!-- 将项目启动jar打包到lib目录中 -->
        <fileSet>
            <directory>target</directory>
            <outputDirectory>lib</outputDirectory>
            <includes>
                <include>*.jar</include>
            </includes>
        </fileSet>
    </fileSets>
</assembly>