server:
  port: 8200

spring:
  # 数据库配置
  datasource:
    username: aigc
    password: <PERSON><PERSON><PERSON>@2025_aigc
    url: ****************************************************************************************************************************************************************************************************************************************
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    druid:
      keep-alive: true
  # Redis配置
  data:
    redis:
      port: 9010
      host: ************
      database: 3
      password: Jrzh@2022
      jedis:
        pool:
          max-wait: 2000
          time-between-eviction-runs: 1000

aigc:
  oss:
    default-platform: aliyun-oss
    default-path: /home/<USER>/project/aigc/upload/ # 默认路径
    aliyun-oss:
      - platform: aliyun-oss # 存储平台标识，七牛：qiniu、阿里OSS：aliyun-oss、腾讯OSS：tencent-cos
        enable-storage: true  # 启用存储
        access-key: LTAI5tPeGREtYmqiq6jWQn6W
        secret-key: ******************************
        bucket-name: jrzh-aigc
        end-point: oss-cn-hangzhou.aliyuncs.com
        domain: https://jrzh-aigc.oss-cn-hangzhou.aliyuncs.com/
        base-path: aigc/ # 基础路径
    local-plus:
      - platform: local # 存储平台标识
        enable-storage: true  #启用存储
        enable-access: true #启用访问（线上请使用 Nginx 配置，效率更高）
        domain: http://127.0.0.1:8100/ # 访问域名，例如：“http://127.0.0.1:8030/file/”，注意后面要和 path-patterns 保持一致，“/”结尾，本地存储建议使用相对路径，方便后期更换域名
        base-path: /aigc/ # 基础路径
        path-patterns: /static/** # 访问路径（默认本地target目录），注意：如果使用本地nginx容器，此路径要修改为nginx存储路径
        storage-path: ./static/ # 存储路径（默认本地target目录），注意：如果使用本地nginx容器，此路径要修改为nginx存储路径
  ocr:
    keywords:
      contract-fields: ["甲方", "地址", "乙方", "联系方式", "户名", "账号", "开户行"]


