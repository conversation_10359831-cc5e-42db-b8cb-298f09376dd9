/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.server.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.tycoding.langchat.ai.biz.entity.AigcModelProvider;
import cn.tycoding.langchat.ai.biz.service.AigcModelProviderService;
import cn.tycoding.langchat.common.core.annotation.ApiLog;
import cn.tycoding.langchat.common.core.utils.MybatisUtil;
import cn.tycoding.langchat.common.core.utils.QueryPage;
import cn.tycoding.langchat.common.core.utils.R;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 模型供应商控制器
 * 
 * <AUTHOR>
 * @since 2024/12/17
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/aigc/model/provider")
public class AigcModelProviderController {

    private final AigcModelProviderService providerService;

    @GetMapping("/list")
    public R<List<AigcModelProvider>> list(AigcModelProvider data) {
        return R.ok(providerService.list(data));
    }

    @GetMapping("/page")
    public R page(AigcModelProvider data, QueryPage queryPage) {
        return R.ok(MybatisUtil.getData(providerService.page(data, queryPage)));
    }

    @GetMapping("/{id}")
    public R<AigcModelProvider> findById(@PathVariable String id) {
        return R.ok(providerService.getById(id));
    }

    @GetMapping("/enabled")
    public R<List<AigcModelProvider>> getEnabledProviders() {
        return R.ok(providerService.getEnabledProviders());
    }

    @GetMapping("/by-model-type/{modelType}")
    public R<List<AigcModelProvider>> getProvidersByModelType(@PathVariable String modelType) {
        return R.ok(providerService.getProvidersByModelType(modelType));
    }

    @PostMapping
    @ApiLog("新增模型供应商")
    @SaCheckPermission("aigc:model:provider:add")
    public R add(@RequestBody AigcModelProvider data) {
        providerService.save(data);
        return R.ok();
    }

    @PutMapping
    @ApiLog("修改模型供应商")
    @SaCheckPermission("aigc:model:provider:update")
    public R update(@RequestBody AigcModelProvider data) {
        providerService.updateById(data);
        return R.ok();
    }

    @DeleteMapping("/{id}")
    @ApiLog("删除模型供应商")
    @SaCheckPermission("aigc:model:provider:delete")
    public R delete(@PathVariable String id) {
        providerService.removeById(id);
        return R.ok();
    }
}
