package cn.tycoding.langchat.server.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.tycoding.service.DocumentRecognitionService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 控制层。
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
@Slf4j
@RestController
@RequestMapping("test")
@AllArgsConstructor
public class AiTestController {

    private final DocumentRecognitionService documentRecognitionService;



    @SaIgnore
    @GetMapping("/company")
    public String testCompanyData(String companyName){
        Map<String, String> variables = new HashMap<>();
        variables.put("潍坊盛清包装制品有限公司", "流动资产: 150,32800.1; 流动负债: 407,858.27; 存货: 12,629; 营业收入: 300,450.00; 营业成本: 100,450; 利润总额: 2,924.69; 所得税费用: 1000; 负债合计: 858.27; 资产总计: 420,432.1");
        variables.put("潍坊博泰机电配套厂", "流动资产:1000000.00, 流动负债:100000.00, 存货:100000.00, 现金及现金等价物净增加额:10000000.00, 负债合计:100000.00, 资产总计:30000000.00, 净利润:1000000.00, 利息费用:10000.00, 所得税费用:100000.00, 营业收入:20000000.00, 营业成本:8000000.00, 利润总额:12000000.00, 应收账款:1000000.00, 存货期末余额:1000000.00, 存货期初余额:100000.00, 购建固定资产、无形资产和其他长期资产所支付的现金:10000000.00, 处置固定资产、无形资产和其他长期资产所收回的现金净额:10000000.00, 当期营业收入:10000000.00, 当期净利润:5000000.00");
        variables.put("潍坊中万润机械有限公司", "流动资产: 550,328.1; 流动负债: 207,8580.27; 存货: 12,629; 营业收入: 0; 营业成本: 0; 利润总额: -1,924.69; 所得税费用: 0; 负债合计: 207,858.27; 资产总计: 270,432.1");
        return variables.get(companyName);
    }
    @SaIgnore
    @GetMapping("/image")
    public String testImageData(String templateType,String imageUrl){
        try {
            URL url = new URL(imageUrl);
            List<Object> inputs = new ArrayList<>();
            inputs.add(url);
            String ocrResult = documentRecognitionService.recognizeDocument(templateType,inputs);
            log.info("OCR识别结果文本：{}",ocrResult);
            return ocrResult;
        }catch (Exception e){
            e.printStackTrace();
        }
        return "";
    }

}
