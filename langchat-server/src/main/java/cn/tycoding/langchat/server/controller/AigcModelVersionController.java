/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.server.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.tycoding.langchat.ai.biz.entity.AigcModelVersion;
import cn.tycoding.langchat.ai.biz.service.AigcModelVersionService;
import cn.tycoding.langchat.common.core.annotation.ApiLog;
import cn.tycoding.langchat.common.core.utils.MybatisUtil;
import cn.tycoding.langchat.common.core.utils.QueryPage;
import cn.tycoding.langchat.common.core.utils.R;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 模型版本控制器
 * 
 * <AUTHOR>
 * @since 2024/12/17
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/aigc/model/version")
public class AigcModelVersionController {

    private final AigcModelVersionService versionService;

    @GetMapping("/list")
    public R<List<AigcModelVersion>> list(AigcModelVersion data) {
        return R.ok(versionService.list(data));
    }

    @GetMapping("/page")
    public R page(AigcModelVersion data, QueryPage queryPage) {
        return R.ok(MybatisUtil.getData(versionService.page(data, queryPage)));
    }

    @GetMapping("/{id}")
    public R<AigcModelVersion> findById(@PathVariable String id) {
        return R.ok(versionService.getById(id));
    }

    @GetMapping("/category/{categoryCode}")
    public R<List<AigcModelVersion>> getByCategoryCode(@PathVariable String categoryCode) {
        return R.ok(versionService.getByCategoryCode(categoryCode));
    }

    @GetMapping("/provider/{providerId}/category/{categoryId}")
    public R<List<AigcModelVersion>> getByProviderAndCategory(@PathVariable String providerId, @PathVariable String categoryId) {
        return R.ok(versionService.getByProviderAndCategory(providerId, categoryId));
    }

    @GetMapping("/all")
    public R<List<AigcModelVersion>> getAllWithRelations() {
        return R.ok(versionService.getAllWithRelations());
    }

    @PostMapping
    @ApiLog("新增模型版本")
    @SaCheckPermission("aigc:model:version:add")
    public R add(@RequestBody AigcModelVersion data) {
        versionService.save(data);
        return R.ok();
    }

    @PutMapping
    @ApiLog("修改模型版本")
    @SaCheckPermission("aigc:model:version:update")
    public R update(@RequestBody AigcModelVersion data) {
        versionService.updateById(data);
        return R.ok();
    }

    @DeleteMapping("/{id}")
    @ApiLog("删除模型版本")
    @SaCheckPermission("aigc:model:version:delete")
    public R delete(@PathVariable String id) {
        versionService.removeById(id);
        return R.ok();
    }
}
