/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.server.controller.mcp;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.tycoding.langchat.common.core.utils.QueryPage;
import cn.tycoding.langchat.common.core.utils.R;
import cn.tycoding.langchat.mcp.core.entity.AigcMcpService;
import cn.tycoding.langchat.mcp.core.service.AigcMcpServiceService;
import cn.tycoding.langchat.mcp.core.service.McpServiceManager;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * MCP服务管理控制器
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/aigc/mcp/service")
public class AigcMcpServiceController {

    private final AigcMcpServiceService aigcMcpServiceService;
    private final McpServiceManager mcpServiceManager;

    /**
     * 分页查询
     */
    @GetMapping("/list")
    @SaCheckPermission("aigc:mcp:service:list")
    public R<IPage<AigcMcpService>> list(AigcMcpService aigcMcpService, QueryPage queryPage) {
        return R.ok(aigcMcpServiceService.list(aigcMcpService, queryPage));
    }

    /**
     * 根据ID查询
     */
    @GetMapping("/{id}")
    @SaCheckPermission("aigc:mcp:service:info")
    public R<AigcMcpService> findById(@PathVariable String id) {
        return R.ok(aigcMcpServiceService.findById(id));
    }



    /**
     * 根据ID查询
     */
    @GetMapping("/findByIds")
    public R<List<AigcMcpService>> findByIds(@RequestParam String ids) {
        List<AigcMcpService> aigcMcpServiceList = aigcMcpServiceService.list(Wrappers.<AigcMcpService>lambdaQuery().in(AigcMcpService::getId, Arrays.asList(ids.split(","))));

        return R.ok(aigcMcpServiceList);
    }

    /**
     * 新增
     */
    @PostMapping
    //@SaCheckPermission("aigc:mcp:service:add")
    public R<Void> add(@RequestBody AigcMcpService aigcMcpService) {
        aigcMcpServiceService.add(aigcMcpService);
        return R.ok();
    }

    /**
     * 修改
     */
    @PutMapping
    @SaCheckPermission("aigc:mcp:service:update")
    public R<Void> update(@RequestBody AigcMcpService aigcMcpService) {
        aigcMcpServiceService.update(aigcMcpService);
        return R.ok();
    }

    /**
     * 删除
     */
    @DeleteMapping
    @SaCheckPermission("aigc:mcp:service:delete")
    public R<Void> delete(@RequestBody List<String> ids) {
        aigcMcpServiceService.delete(ids);
        return R.ok();
    }

    /**
     * 启用/禁用服务
     */
    @PutMapping("/{id}/toggle")
    @SaCheckPermission("aigc:mcp:service:toggle")
    public R<Void> toggleEnabled(@PathVariable String id, @RequestBody Map<String, Boolean> request) {
        Boolean enabled = request.get("enabled");
        aigcMcpServiceService.toggleEnabled(id, enabled);
        return R.ok();
    }

    /**
     * 测试服务连接
     */
    @PostMapping("/{id}/test")
    @SaCheckPermission("aigc:mcp:service:test")
    public R<Boolean> testConnection(@PathVariable String id) {
        boolean result = aigcMcpServiceService.testConnection(id);
        return R.ok(result);
    }

    /**
     * 同步服务到MCP客户端
     */
    @PostMapping("/{id}/sync")
    @SaCheckPermission("aigc:mcp:service:sync")
    public R<Void> syncToMcpClient(@PathVariable String id) {
        aigcMcpServiceService.syncToMcpClient(id);
        return R.ok();
    }

    /**
     * 批量同步所有启用的服务
     */
    @PostMapping("/sync/all")
    @SaCheckPermission("aigc:mcp:service:sync")
    public R<Void> syncAllToMcpClient() {
        aigcMcpServiceService.syncAllToMcpClient();
        return R.ok();
    }

    /**
     * 获取服务健康状态
     */
    @GetMapping("/{id}/health")
    @SaCheckPermission("aigc:mcp:service:health")
    public R<String> getHealthStatus(@PathVariable String id) {
        String status = aigcMcpServiceService.getHealthStatus(id);
        return R.ok(status);
    }

    /**
     * 刷新服务工具列表
     */
    @PostMapping("/{id}/refresh-tools")
    @SaCheckPermission("aigc:mcp:service:refresh")
    public R<Void> refreshTools(@PathVariable String id) {
        aigcMcpServiceService.refreshTools(id);
        return R.ok();
    }

    /**
     * 获取启用的服务列表
     */
    @GetMapping("/enabled")
    @SaCheckPermission("aigc:mcp:service:list")
    public R<List<AigcMcpService>> getEnabledServices() {
        return R.ok(aigcMcpServiceService.getEnabledServices());
    }

    /**
     * 根据分类获取服务列表
     */
    @GetMapping("/category/{category}")
    @SaCheckPermission("aigc:mcp:service:list")
    public R<List<AigcMcpService>> getServicesByCategory(@PathVariable String category) {
        return R.ok(aigcMcpServiceService.getServicesByCategory(category));
    }

    /**
     * 获取服务统计信息
     */
    @GetMapping("/stats")
    public R<Map<String, Object>> getStats() {
        List<AigcMcpService> allServices = aigcMcpServiceService.list();
        
        long totalCount = allServices.size();
        long enabledCount = allServices.stream().mapToLong(s -> s.getEnabled() ? 1 : 0).sum();
        long builtinCount = allServices.stream().mapToLong(s -> "builtin".equals(s.getCategory()) ? 1 : 0).sum();
        long externalCount = allServices.stream().mapToLong(s -> "external".equals(s.getCategory()) ? 1 : 0).sum();
        long activeCount = allServices.stream().mapToLong(s -> "ACTIVE".equals(s.getStatus()) ? 1 : 0).sum();
        
        Map<String, Object> stats = Map.of(
                "totalCount", totalCount,
                "enabledCount", enabledCount,
                "builtinCount", builtinCount,
                "externalCount", externalCount,
                "activeCount", activeCount
        );
        
        return R.ok(stats);
    }

    /**
     * 获取服务类型选项
     */
    @GetMapping("/types")
    public R<List<Map<String, String>>> getServiceTypes() {
        List<Map<String, String>> types = List.of(
                Map.of("value", "HTTP", "label", "HTTP"),
                Map.of("value", "WEBSOCKET", "label", "WebSocket"),
                Map.of("value", "GRPC", "label", "gRPC")
        );
        return R.ok(types);
    }

    /**
     * 获取服务分类选项
     */
    @GetMapping("/categories")
    public R<List<Map<String, String>>> getServiceCategories() {
        List<Map<String, String>> categories = List.of(
                Map.of("value", "builtin", "label", "内置服务"),
                Map.of("value", "external", "label", "外部服务")
        );
        return R.ok(categories);
    }

    /**
     * 获取认证类型选项
     */
    @GetMapping("/auth-types")
    public R<List<Map<String, String>>> getAuthTypes() {
        List<Map<String, String>> authTypes = List.of(
                Map.of("value", "none", "label", "无认证"),
                Map.of("value", "bearer", "label", "Bearer Token"),
                Map.of("value", "basic", "label", "Basic Auth"),
                Map.of("value", "api_key", "label", "API Key")
        );
        return R.ok(authTypes);
    }

    /**
     * 获取服务状态选项
     */
    @GetMapping("/statuses")
    public R<List<Map<String, String>>> getServiceStatuses() {
        List<Map<String, String>> statuses = List.of(
                Map.of("value", "ACTIVE", "label", "活跃"),
                Map.of("value", "INACTIVE", "label", "非活跃"),
                Map.of("value", "ERROR", "label", "错误"),
                Map.of("value", "UNKNOWN", "label", "未知")
        );
        return R.ok(statuses);
    }

    // ==================== MCP服务管理接口 ====================




    /**
     * 移除服务注册记录
     */
    @DeleteMapping("/management/registered/{serviceName}")
    @SaCheckPermission("aigc:mcp:service:manage")
    public R<Void> unregisterService(@PathVariable String serviceName) {
        mcpServiceManager.unregisterService(serviceName);
        return R.ok();
    }


}
