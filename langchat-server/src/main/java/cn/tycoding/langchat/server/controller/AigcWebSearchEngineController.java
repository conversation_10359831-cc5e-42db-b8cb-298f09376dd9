/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.server.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.tycoding.langchat.ai.biz.entity.AigcWebSearchEngine;
import cn.tycoding.langchat.ai.biz.service.AigcWebSearchEngineService;
import cn.tycoding.langchat.ai.biz.searchEngine.SearchEngineStrategyManager;
import cn.tycoding.langchat.common.core.annotation.ApiLog;
import cn.tycoding.langchat.common.core.exception.ServiceException;
import cn.tycoding.langchat.common.core.utils.MybatisUtil;
import cn.tycoding.langchat.common.core.utils.QueryPage;
import cn.tycoding.langchat.common.core.utils.R;
import com.agentsflex.core.document.Document;
import dev.tinyflow.core.searchengine.SearchEngine;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 搜索引擎管理控制器
 *
 * <AUTHOR>
 * @since 2024/6/16
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/web-search")
public class AigcWebSearchEngineController {

    private final AigcWebSearchEngineService webSearchEngineService;
    private final SearchEngineStrategyManager strategyManager;

    @GetMapping("/list")
    public R<List<AigcWebSearchEngine>> list(AigcWebSearchEngine data) {
        return R.ok(webSearchEngineService.list(data));
    }

    @GetMapping("/page")
    public R page(AigcWebSearchEngine data, QueryPage queryPage) {
        return R.ok(MybatisUtil.getData(webSearchEngineService.page(data, queryPage)));
    }

    @GetMapping("/{id}")
    public R<AigcWebSearchEngine> findById(@PathVariable String id) {
        return R.ok(webSearchEngineService.getById(id));
    }

    @GetMapping("/enabled")
    public R<List<AigcWebSearchEngine>> getEnabledSearchEngines() {
        return R.ok(webSearchEngineService.getEnabledSearchEngines());
    }

    @PostMapping
    @ApiLog("新增搜索引擎")
    @SaCheckPermission("aigc:search-engine:add")
    public R add(@RequestBody AigcWebSearchEngine data) {
        webSearchEngineService.save(data);
        return R.ok();
    }

    @PutMapping
    @ApiLog("修改搜索引擎")
    @SaCheckPermission("aigc:search-engine:update")
    public R update(@RequestBody AigcWebSearchEngine data) {
        webSearchEngineService.updateById(data);
        return R.ok();
    }

    @DeleteMapping("/{id}")
    @ApiLog("删除搜索引擎")
    @SaCheckPermission("aigc:search-engine:delete")
    public R delete(@PathVariable String id) {
        webSearchEngineService.removeById(id);
        return R.ok();
    }

    /**
     * 测试搜索引擎
     */
    @PostMapping("/{id}/test")
    @ApiLog("测试搜索引擎")
    @SaCheckPermission("aigc:search-engine:test")
    public R<List<Map<String, Object>>> testSearchEngine(@PathVariable String id, @RequestBody Map<String, Object> request) {
        try {
            String keyword = (String) request.get("keyword");
            Integer maxResults = (Integer) request.getOrDefault("maxResults", 10);

            if (StrUtil.isBlank(keyword)) {
                throw new ServiceException("搜索关键词不能为空");
            }

            // 获取搜索引擎配置
            AigcWebSearchEngine searchEngine = webSearchEngineService.getById(id);
            if (searchEngine == null) {
                throw new ServiceException("搜索引擎不存在");
            }

            if (searchEngine.getStatus() != 1) {
                throw new ServiceException("搜索引擎已禁用");
            }

            // 构建搜索引擎参数
            Map<String, Object> params = new HashMap<>();
            params.put("apiKey", searchEngine.getApiKey());
            params.put("baseUrl", searchEngine.getBaseUrl());
            params.put("searchEndpoint", searchEngine.getSearchEndpoint());
            params.put("timeoutSeconds", searchEngine.getTimeoutSeconds());

            // 解析额外参数
            if (StrUtil.isNotBlank(searchEngine.getAdditionalParams())) {
                try {
                    // 使用JSON解析额外参数
                    Map<String, Object> additionalParams = BeanUtil.toBean(searchEngine.getAdditionalParams(), Map.class);
                    params.putAll(additionalParams);
                } catch (Exception e) {
                    log.warn("解析额外参数失败: {}", e.getMessage());
                }
            }

            // 构建搜索引擎实例
            SearchEngine engine = strategyManager.buildSearchEngine(searchEngine.getProvider(), params);
            if (engine == null) {
                throw new ServiceException("构建搜索引擎实例失败");
            }

            // 执行搜索
            List<Document> documents = engine.search(keyword, maxResults, null, null);

            // 转换结果格式
            List<Map<String, Object>> results = documents.stream().map(doc -> {
                Map<String, Object> result = new HashMap<>();
                result.put("title", doc.getMetadata("title"));
                result.put("url", doc.getMetadata("url"));
                result.put("snippet", doc.getContent());
                return result;
            }).collect(Collectors.toList());

            log.info("搜索引擎测试成功: {}, 关键词: {}, 结果数: {}", searchEngine.getName(), keyword, results.size());
            return R.ok(results);

        } catch (ServiceException e) {
            log.error("搜索引擎测试失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("搜索引擎测试异常: {}", e.getMessage(), e);
            throw new ServiceException("测试失败: " + e.getMessage());
        }
    }

}