package cn.tycoding.langchat.server.api;

import cn.tycoding.langchat.ai.biz.dto.WorkflowRunningDto;
import cn.tycoding.langchat.ai.biz.service.WorkflowService;
import cn.tycoding.langchat.ai.core.service.WorkflowRunningService;
import cn.tycoding.langchat.common.core.utils.R;
import cn.tycoding.langchat.server.api.auth.OpenapiAuth;
import cn.tycoding.langchat.server.consts.AigcConst;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description:
 * @date 2025年05月15日 10:46
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/v1")
public class WorkflowEndpoint {

    private final WorkflowRunningService workflowRunningService;

    /**
     * 执行流程操作
     * <AUTHOR>
     * @date 2025/5/15 11:14
     * @param runningDto
     * @return cn.tycoding.langchat.common.core.utils.R
     */
    @OpenapiAuth(AigcConst.WORKFLOW_API)
    @RequestMapping("/workflow/running")
    public R run(@RequestBody WorkflowRunningDto runningDto) {
        return R.ok(workflowRunningService.workflowRunning(runningDto));
    }
}
