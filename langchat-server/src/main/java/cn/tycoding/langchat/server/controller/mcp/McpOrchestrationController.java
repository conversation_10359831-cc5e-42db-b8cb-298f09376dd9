/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.server.controller.mcp;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.tycoding.langchat.common.core.utils.R;
import cn.tycoding.langchat.mcp.core.service.McpServiceOrchestrator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * MCP服务编排控制器
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
@Slf4j
@RestController
@RequestMapping("/aigc/mcp/orchestration")
@RequiredArgsConstructor
public class McpOrchestrationController {

    private final McpServiceOrchestrator mcpOrchestrator;

    /**
     * 生成执行计划
     */
    @PostMapping("/plan")
    @SaCheckPermission("aigc:mcp:orchestration:plan")
    public R<McpServiceOrchestrator.ExecutionPlan> generatePlan(@RequestBody Map<String, Object> request) {
        try {
            String userPrompt = (String) request.get("userPrompt");
            List<String> serviceIds = (List<String>) request.get("serviceIds");
            
            log.info("生成执行计划请求: {}", userPrompt);
            
            McpServiceOrchestrator.ExecutionPlan plan = mcpOrchestrator.generateExecutionPlan(userPrompt, serviceIds);
            
            return R.ok(plan);
        } catch (Exception e) {
            log.error("生成执行计划失败", e);
            return R.fail("生成执行计划失败: " + e.getMessage());
        }
    }

    /**
     * 执行计划
     */
    @PostMapping("/execute")
    @SaCheckPermission("aigc:mcp:orchestration:execute")
    public R<Map<String, Object>> executePlan(@RequestBody Map<String, Object> request) {
        try {
            String userPrompt = (String) request.get("userPrompt");
            List<String> serviceIds = (List<String>) request.get("serviceIds");
            String userId = (String) request.get("userId");
            String conversationId = (String) request.get("conversationId");
            
            log.info("执行编排请求: {}", userPrompt);
            
            // 1. 生成执行计划
            McpServiceOrchestrator.ExecutionPlan plan = mcpOrchestrator.generateExecutionPlan(userPrompt, serviceIds);
            
            // 2. 执行计划
            Map<String, Object> result = mcpOrchestrator.executePlan(plan, userId, conversationId);
            
            return R.ok(result);
        } catch (Exception e) {
            log.error("执行编排失败", e);
            return R.fail("执行编排失败: " + e.getMessage());
        }
    }

    /**
     * 预览执行计划（不实际执行）
     */
    @PostMapping("/preview")
    @SaCheckPermission("aigc:mcp:orchestration:plan")
    public R<Map<String, Object>> previewPlan(@RequestBody Map<String, Object> request) {
        try {
            String userPrompt = (String) request.get("userPrompt");
            List<String> serviceIds = (List<String>) request.get("serviceIds");
            
            log.info("预览执行计划: {}", userPrompt);
            
            McpServiceOrchestrator.ExecutionPlan plan = mcpOrchestrator.generateExecutionPlan(userPrompt, serviceIds);
            
            // 构建预览信息
            Map<String, Object> preview = Map.of(
                "planDescription", plan.getPlanDescription(),
                "stepCount", plan.getSteps().size(),
                "steps", plan.getSteps().stream().map(step -> Map.of(
                    "stepId", step.getStepId(),
                    "serviceName", step.getServiceName(),
                    "toolName", step.getToolName(),
                    "description", step.getDescription(),
                    "dependencies", step.getDependencies(),
                    "parameters", step.getParameters()
                )).collect(Collectors.toList()),
                "estimatedDuration", estimateDuration(plan)
            );
            
            return R.ok(preview);
        } catch (Exception e) {
            log.error("预览执行计划失败", e);
            return R.fail("预览执行计划失败: " + e.getMessage());
        }
    }

    /**
     * 获取编排示例
     */
    @GetMapping("/examples")
    @SaCheckPermission("aigc:mcp:orchestration:list")
    public R<List<Map<String, Object>>> getOrchestrationExamples() {
        List<Map<String, Object>> examples = List.of(
            Map.of(
                "title", "文生图并发布",
                "description", "生成图片并发布到EdgeOne Pages",
                "prompt", "帮我生成一张可爱小猫的图片，然后发布到我的网站上",
                "services", List.of("wanx-image-generation", "edgeone-pages"),
                "steps", List.of("生成图片", "上传到CDN", "发布到网站")
            ),
            Map.of(
                "title", "内容创作流程",
                "description", "搜索资料、生成文章、配图、发布",
                "prompt", "帮我搜索人工智能的最新发展，写一篇文章，配上相关图片，然后发布到我的博客",
                "services", List.of("brave-search", "text-generation", "wanx-image-generation", "blog-publisher"),
                "steps", List.of("搜索资料", "生成文章", "生成配图", "发布博客")
            ),
            Map.of(
                "title", "数据分析报告",
                "description", "获取数据、分析、生成图表、发布报告",
                "prompt", "帮我分析最近的销售数据，生成图表，写一份分析报告并发布",
                "services", List.of("data-fetcher", "data-analyzer", "chart-generator", "report-publisher"),
                "steps", List.of("获取数据", "数据分析", "生成图表", "生成报告", "发布报告")
            )
        );
        
        return R.ok(examples);
    }

    /**
     * 获取编排模板
     */
    @GetMapping("/templates")
    @SaCheckPermission("aigc:mcp:orchestration:list")
    public R<List<Map<String, Object>>> getOrchestrationTemplates() {
        List<Map<String, Object>> templates = List.of(
            Map.of(
                "id", "image-publish-template",
                "name", "图片生成发布模板",
                "description", "生成图片并发布到指定平台",
                "steps", List.of(
                    Map.of(
                        "stepId", "generate_image",
                        "serviceName", "wanx-image-generation",
                        "toolName", "text_to_image",
                        "description", "根据描述生成图片",
                        "parameters", Map.of("prompt", "${user_prompt}")
                    ),
                    Map.of(
                        "stepId", "publish_image",
                        "serviceName", "edgeone-pages",
                        "toolName", "upload_file",
                        "description", "发布图片到EdgeOne",
                        "parameters", Map.of("file_url", "${generate_image.result}"),
                        "dependencies", List.of("generate_image")
                    )
                )
            ),
            Map.of(
                "id", "content-creation-template",
                "name", "内容创作模板",
                "description", "搜索、创作、配图、发布的完整流程",
                "steps", List.of(
                    Map.of(
                        "stepId", "search_info",
                        "serviceName", "brave-search",
                        "toolName", "web_search",
                        "description", "搜索相关信息",
                        "parameters", Map.of("q", "${search_query}")
                    ),
                    Map.of(
                        "stepId", "generate_content",
                        "serviceName", "text-generation",
                        "toolName", "generate_article",
                        "description", "基于搜索结果生成文章",
                        "parameters", Map.of("reference", "${search_info.result}"),
                        "dependencies", List.of("search_info")
                    ),
                    Map.of(
                        "stepId", "generate_image",
                        "serviceName", "wanx-image-generation",
                        "toolName", "text_to_image",
                        "description", "生成配图",
                        "parameters", Map.of("prompt", "${image_prompt}"),
                        "dependencies", List.of("generate_content")
                    ),
                    Map.of(
                        "stepId", "publish_content",
                        "serviceName", "blog-publisher",
                        "toolName", "publish_post",
                        "description", "发布文章",
                        "parameters", Map.of(
                            "content", "${generate_content.result}",
                            "image", "${generate_image.result}"
                        ),
                        "dependencies", List.of("generate_content", "generate_image")
                    )
                )
            )
        );
        
        return R.ok(templates);
    }

    /**
     * 估算执行时间
     */
    private String estimateDuration(McpServiceOrchestrator.ExecutionPlan plan) {
        int stepCount = plan.getSteps().size();
        
        // 简单估算：每个步骤平均30秒，并行步骤可以减少时间
        int estimatedSeconds = stepCount * 30;
        
        if (estimatedSeconds < 60) {
            return estimatedSeconds + "秒";
        } else if (estimatedSeconds < 3600) {
            return (estimatedSeconds / 60) + "分钟";
        } else {
            return (estimatedSeconds / 3600) + "小时";
        }
    }
}
