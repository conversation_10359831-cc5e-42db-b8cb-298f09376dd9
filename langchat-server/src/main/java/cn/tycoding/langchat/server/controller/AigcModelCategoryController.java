/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.server.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.tycoding.langchat.ai.biz.entity.AigcModelCategory;
import cn.tycoding.langchat.ai.biz.service.AigcModelCategoryService;
import cn.tycoding.langchat.common.core.annotation.ApiLog;
import cn.tycoding.langchat.common.core.utils.MybatisUtil;
import cn.tycoding.langchat.common.core.utils.QueryPage;
import cn.tycoding.langchat.common.core.utils.R;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 模型分类控制器
 * 
 * <AUTHOR>
 * @since 2024/12/17
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/aigc/model/category")
public class AigcModelCategoryController {

    private final AigcModelCategoryService categoryService;

    @GetMapping("/list")
    public R<List<AigcModelCategory>> list(AigcModelCategory data) {
        return R.ok(categoryService.list(data));
    }

    @GetMapping("/page")
    public R page(AigcModelCategory data, QueryPage queryPage) {
        return R.ok(MybatisUtil.getData(categoryService.page(data, queryPage)));
    }

    @GetMapping("/{id}")
    public R<AigcModelCategory> findById(@PathVariable String id) {
        return R.ok(categoryService.getById(id));
    }

    @GetMapping("/enabled")
    public R<List<AigcModelCategory>> getEnabledCategories() {
        return R.ok(categoryService.getEnabledCategories());
    }

    @PostMapping
    @ApiLog("新增模型分类")
    @SaCheckPermission("aigc:model:category:add")
    public R add(@RequestBody AigcModelCategory data) {
        categoryService.save(data);
        return R.ok();
    }

    @PutMapping
    @ApiLog("修改模型分类")
    @SaCheckPermission("aigc:model:category:update")
    public R update(@RequestBody AigcModelCategory data) {
        categoryService.updateById(data);
        return R.ok();
    }

    @DeleteMapping("/{id}")
    @ApiLog("删除模型分类")
    @SaCheckPermission("aigc:model:category:delete")
    public R delete(@PathVariable String id) {
        categoryService.removeById(id);
        return R.ok();
    }
}
