/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.server.controller.mcp;

import cn.tycoding.langchat.common.core.annotation.ApiLog;
import cn.tycoding.langchat.common.core.utils.R;
import cn.tycoding.langchat.mcp.core.entity.AigcMcpService;
import cn.tycoding.langchat.mcp.core.protocol.McpResponse;
import cn.tycoding.langchat.mcp.core.protocol.McpTool;
import cn.tycoding.langchat.mcp.core.service.McpServiceManager;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * MCP服务控制器
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
@Slf4j
@RestController
@RequestMapping("/mcp")
@RequiredArgsConstructor
public class McpController {

    private final McpServiceManager mcpServiceManager;

    @GetMapping("/services")
    public R<List<AigcMcpService>> getEnabledServices() {
        try {
            List<AigcMcpService> services = mcpServiceManager.getEnabledServices();
            return R.ok(services);
        } catch (Exception e) {
            log.error("获取MCP服务列表失败", e);
            return R.fail("获取服务列表失败: " + e.getMessage());
        }
    }

    @GetMapping("/services/category/{category}")
    public R<List<AigcMcpService>> getServicesByCategory(
           @PathVariable String category) {
        try {
            List<AigcMcpService> services = mcpServiceManager.getServicesByCategory(category);
            return R.ok(services);
        } catch (Exception e) {
            log.error("获取分类服务列表失败: {}", category, e);
            return R.fail("获取分类服务列表失败: " + e.getMessage());
        }
    }


    @GetMapping("/services/{serviceName}")
    public R<AigcMcpService> getService(
            @PathVariable String serviceName) {
        try {
            AigcMcpService service = mcpServiceManager.getService(serviceName);
            if (service == null) {
                return R.fail("服务不存在: " + serviceName);
            }
            return R.ok(service);
        } catch (Exception e) {
            log.error("获取服务详情失败: {}", serviceName, e);
            return R.fail("获取服务详情失败: " + e.getMessage());
        }
    }

    @ApiLog("获取服务的工具列表")
    @GetMapping("/services/{serviceName}/tools")
    public R<List<McpTool>> getServiceTools(
            @PathVariable String serviceName) {
        try {
            List<McpTool> tools = mcpServiceManager.getServiceTools(serviceName);
            return R.ok(tools);
        } catch (Exception e) {
            log.error("获取服务工具列表失败: {}", serviceName, e);
            return R.fail("获取工具列表失败: " + e.getMessage());
        }
    }
    @ApiLog("调用MCP工具")
    @PostMapping("/tools/call")
    public R<McpResponse> callTool(@RequestBody Map<String, Object> request) {
        try {
            String serviceName = (String) request.get("serviceName");
            String toolName = (String) request.get("toolName");
            @SuppressWarnings("unchecked")
            Map<String, Object> parameters = (Map<String, Object>) request.get("parameters");
            String userId = (String) request.get("userId");
            String conversationId = (String) request.get("conversationId");

            log.info("调用MCP工具: 服务={}, 工具={}, 用户={}", serviceName, toolName, userId);

            McpResponse response = mcpServiceManager.callTool(serviceName, toolName, parameters, userId, conversationId);
            return R.ok(response);
        } catch (Exception e) {
            log.error("调用MCP工具失败", e);
            return R.fail("工具调用失败: " + e.getMessage());
        }
    }
    @ApiLog("批量调用MCP工具")
    @PostMapping("/tools/call/batch")
    public R<List<McpResponse>> callToolsBatch(@RequestBody List<Map<String, Object>> requests) {
        try {
            List<McpResponse> responses = requests.stream()
                    .map(request -> {
                        String serviceName = (String) request.get("serviceName");
                        String toolName = (String) request.get("toolName");
                        @SuppressWarnings("unchecked")
                        Map<String, Object> parameters = (Map<String, Object>) request.get("parameters");
                        String userId = (String) request.get("userId");
                        String conversationId = (String) request.get("conversationId");

                        return mcpServiceManager.callTool(serviceName, toolName, parameters, userId, conversationId);
                    })
                    .toList();

            return R.ok(responses);
        } catch (Exception e) {
            log.error("批量调用MCP工具失败", e);
            return R.fail("批量工具调用失败: " + e.getMessage());
        }
    }

    @ApiLog("服务健康检查")
    @GetMapping("/services/{serviceName}/health")
    public R<Boolean> healthCheck(
            @PathVariable String serviceName) {
        try {
            boolean healthy = mcpServiceManager.healthCheck(serviceName);
            return R.ok(healthy);
        } catch (Exception e) {
            log.error("健康检查失败: {}", serviceName, e);
            return R.fail("健康检查失败: " + e.getMessage());
        }
    }
    @ApiLog("批量健康检查")
    @GetMapping("/services/health")
    public R<Map<String, Boolean>> batchHealthCheck() {
        try {
            Map<String, Boolean> healthStatus = mcpServiceManager.batchHealthCheck();
            return R.ok(healthStatus);
        } catch (Exception e) {
            log.error("批量健康检查失败", e);
            return R.fail("批量健康检查失败: " + e.getMessage());
        }
    }
    @ApiLog("启用服务")
    @PostMapping("/services/{serviceName}/enable")
    public R<Void> enableService(
            @PathVariable String serviceName) {
        try {
            mcpServiceManager.enableService(serviceName);
            return R.ok();
        } catch (Exception e) {
            log.error("启用服务失败: {}", serviceName, e);
            return R.fail("启用服务失败: " + e.getMessage());
        }
    }
    @ApiLog("禁用服务")
    @PostMapping("/services/{serviceName}/disable")
    public R<Void> disableService(
             @PathVariable String serviceName) {
        try {
            mcpServiceManager.disableService(serviceName);
            return R.ok();
        } catch (Exception e) {
            log.error("禁用服务失败: {}", serviceName, e);
            return R.fail("禁用服务失败: " + e.getMessage());
        }
    }
    @ApiLog("刷新服务配置")
    @PostMapping("/services/{serviceName}/refresh")
    public R<Void> refreshService(
             @PathVariable String serviceName) {
        try {
            mcpServiceManager.refreshService(serviceName);
            return R.ok();
        } catch (Exception e) {
            log.error("刷新服务配置失败: {}", serviceName, e);
            return R.fail("刷新服务配置失败: " + e.getMessage());
        }
    }
    @ApiLog("获取服务统计信息")
    @GetMapping("/services/{serviceName}/stats")
    public R<Map<String, Object>> getServiceStats(
             @PathVariable String serviceName) {
        try {
            Map<String, Object> stats = mcpServiceManager.getServiceStats(serviceName);
            return R.ok(stats);
        } catch (Exception e) {
            log.error("获取服务统计失败: {}", serviceName, e);
            return R.fail("获取服务统计失败: " + e.getMessage());
        }
    }
    @ApiLog("获取所有服务统计信息")
    @GetMapping("/stats")
    public R<Map<String, Object>> getAllServicesStats() {
        try {
            Map<String, Object> stats = mcpServiceManager.getAllServicesStats();
            return R.ok(stats);
        } catch (Exception e) {
            log.error("获取所有服务统计失败", e);
            return R.fail("获取统计信息失败: " + e.getMessage());
        }
    }

}
