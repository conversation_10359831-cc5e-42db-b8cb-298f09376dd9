/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.server.controller.mcp;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.tycoding.langchat.common.core.utils.R;
import cn.tycoding.langchat.mcp.core.client.McpToolRouter;
import cn.tycoding.langchat.mcp.core.dto.McpToolCallRequest;
import cn.tycoding.langchat.mcp.core.entity.AigcMcpService;
import cn.tycoding.langchat.mcp.core.entity.AigcMcpServiceLog;
import cn.tycoding.langchat.mcp.core.protocol.McpResponse;
import cn.tycoding.langchat.mcp.core.service.AigcMcpServiceLogService;
import cn.tycoding.langchat.mcp.core.service.AigcMcpServiceService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * MCP工具调用控制器
 * 
 * <AUTHOR>
 * @since 2024/12/18
 */
@Slf4j
@RestController
@RequestMapping("/aigc/mcp/tools")
@RequiredArgsConstructor
public class McpToolController {

    private final McpToolRouter mcpToolRouter;
    private final AigcMcpServiceService mcpServiceService;
    private final AigcMcpServiceLogService mcpServiceLogService;

    /**
     * 调用MCP工具
     */
    @PostMapping("/call")
    @SaCheckPermission("aigc:mcp:tools:call")
    public R<McpResponse> callTool(@RequestBody McpToolCallRequest request) {
        try {
            
            log.info("接收到MCP工具调用请求: 服务={}, 工具={}, 用户={}", 
                    request.getServiceName(), request.getToolName(), request.getUserId());
            
            // 调用工具（通过路由器）
            McpResponse response = mcpToolRouter.routeToolCall(
                    request.getServiceName(),
                    request.getToolName(),
                    request.getParameters(),
                    request.getUserId(),
                    request.getConversationId(),
                    request.getUserInput()
            );
            
            return R.ok(response);
        } catch (Exception e) {
            log.error("MCP工具调用失败", e);
            return R.fail("工具调用失败: " + e.getMessage());
        }
    }

    /**
     * 批量调用MCP工具
     */
    @PostMapping("/call/batch")
    @SaCheckPermission("aigc:mcp:tools:call")
    public R<List<McpResponse>> callToolsBatch(@RequestBody List<McpToolCallRequest> requests) {
        try {
            List<McpResponse> responses = requests.stream()
                    .map(request -> {
                        
                        return mcpToolRouter.routeToolCall(
                                request.getServiceName(),
                                request.getToolName(),
                                request.getParameters(),
                                request.getUserId(),
                                request.getConversationId(),
                                request.getUserInput()
                        );
                    })
                    .collect(Collectors.toList());
            
            return R.ok(responses);
        } catch (Exception e) {
            log.error("批量MCP工具调用失败", e);
            return R.fail("批量工具调用失败: " + e.getMessage());
        }
    }

    /**
     * 获取可用的MCP工具列表
     */
    @GetMapping("/available")
    @SaCheckPermission("aigc:mcp:tools:list")
    public R<List<Map<String, Object>>> getAvailableTools() {
        try {
            // 获取启用的服务及其工具
            List<AigcMcpService> enabledServices = mcpServiceService.getEnabledServices();
            
            List<Map<String, Object>> tools = enabledServices.stream()
                    .flatMap(service -> {
                        try {
                            // 解析工具列表
                            if (service.getTools() != null && !service.getTools().isEmpty()) {
                                var toolsArray = cn.hutool.json.JSONUtil.parseArray(service.getTools());
                                return toolsArray.stream()
                                        .map(tool -> {
                                            var toolObj = (cn.hutool.json.JSONObject) tool;
                                            Map<String, Object> toolMap = new HashMap<>();
                                            toolMap.put("serviceName", service.getName());
                                            toolMap.put("serviceDisplayName", service.getDisplayName());
                                            toolMap.put("toolName", toolObj.getStr("name"));
                                            toolMap.put("toolDescription", toolObj.getStr("description"));
                                            toolMap.put("toolCategory", toolObj.getStr("category"));
                                            toolMap.put("serviceCategory", service.getCategory());
                                            toolMap.put("priority", service.getPriority());
                                            return toolMap;
                                        });
                            }
                            return java.util.stream.Stream.empty();
                        } catch (Exception e) {
                            log.warn("解析服务工具列表失败: {}", service.getName(), e);
                            return java.util.stream.Stream.empty();
                        }
                    })
                    .collect(Collectors.toList());

            
            return R.ok(tools);
        } catch (Exception e) {
            log.error("获取可用工具列表失败", e);
            return R.fail("获取工具列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取服务的工具列表
     */
    @GetMapping("/service/{serviceName}")
    @SaCheckPermission("aigc:mcp:tools:list")
    public R<List<Map<String, Object>>> getServiceTools(@PathVariable String serviceName) {
        try {
            var service = mcpServiceService.findByName(serviceName);
            if (service == null) {
                return R.fail("服务不存在: " + serviceName);
            }
            
            if (!service.getEnabled()) {
                return R.fail("服务已禁用: " + serviceName);
            }
            
            List<Map<String, Object>> tools = new ArrayList<>();
            if (service.getTools() != null && !service.getTools().isEmpty()) {
                try {
                    var toolsArray = cn.hutool.json.JSONUtil.parseArray(service.getTools());
                    tools = toolsArray.stream()
                            .map(tool -> {
                                var toolObj = (cn.hutool.json.JSONObject) tool;
                                Map<String, Object> toolMap = new HashMap<>();
                                toolMap.put("name", toolObj.getStr("name"));
                                toolMap.put("description", toolObj.getStr("description"));
                                toolMap.put("category", toolObj.getStr("category"));
                                Object inputSchema = toolObj.get("inputSchema");
                                toolMap.put("inputSchema", inputSchema != null ? inputSchema : new HashMap<>());
                                return toolMap;
                            })
                            .collect(Collectors.toList());
                } catch (Exception e) {
                    log.warn("解析服务工具列表失败: {}", serviceName, e);
                }
            }
            
            return R.ok(tools);
        } catch (Exception e) {
            log.error("获取服务工具列表失败: {}", serviceName, e);
            return R.fail("获取服务工具列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取调用日志列表
     */
    @GetMapping("/logs")
    @SaCheckPermission("aigc:mcp:logs:list")
    public R<IPage<AigcMcpServiceLog>> getLogs(AigcMcpServiceLog log, @RequestParam Map<String, Object> queryPage) {
        return R.ok(mcpServiceLogService.list(log, queryPage));
    }

    /**
     * 获取调用统计信息
     */
    @GetMapping("/stats")
    @SaCheckPermission("aigc:mcp:logs:list")
    public R<Map<String, Object>> getCallStatistics() {
        return R.ok(mcpServiceLogService.getCallStatistics());
    }

    /**
     * 获取服务调用统计
     */
    @GetMapping("/stats/{serviceName}")
    @SaCheckPermission("aigc:mcp:logs:list")
    public R<Map<String, Object>> getServiceCallStatistics(@PathVariable String serviceName) {
        return R.ok(mcpServiceLogService.getServiceCallStatistics(serviceName));
    }

    /**
     * 清理过期日志
     */
    @DeleteMapping("/logs/cleanup/{days}")
    @SaCheckPermission("aigc:mcp:logs:delete")
    public R<Void> cleanupExpiredLogs(@PathVariable int days) {
        mcpServiceLogService.cleanExpiredLogs(days);
        return R.ok();
    }


}
