package cn.tycoding.langchat.server.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.util.IdUtil;
import cn.tycoding.langchat.ai.biz.dto.WorkflowRunningDto;
import cn.tycoding.langchat.ai.biz.entity.AigcKnowledge;
import cn.tycoding.langchat.ai.biz.entity.AigcModel;
import cn.tycoding.langchat.ai.biz.entity.AigcWebSearchEngine;
import cn.tycoding.langchat.ai.biz.entity.Workflow;
import cn.tycoding.langchat.ai.biz.service.AigcKnowledgeService;
import cn.tycoding.langchat.ai.biz.service.AigcModelService;
import cn.tycoding.langchat.ai.biz.service.AigcWebSearchEngineService;
import cn.tycoding.langchat.ai.biz.service.WorkflowService;
import cn.tycoding.langchat.ai.core.service.WorkflowRunningService;
import cn.tycoding.langchat.common.core.constant.CommonConst;
import cn.tycoding.langchat.common.core.utils.R;
import cn.tycoding.langchat.server.consts.AigcConst;
import cn.tycoding.service.DocumentRecognitionService;
import com.agentsflex.core.chain.*;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import dev.tinyflow.core.Tinyflow;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.net.URL;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 控制层。
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
@Slf4j
@RestController
@RequestMapping("/aigc/workflow")
@AllArgsConstructor
public class AiWorkflowController {
    private final AigcModelService aigcModelService;


    private final AigcKnowledgeService aigcKnowledgeService;
    private final AigcWebSearchEngineService aigcWebSearchEngineService;


    private final WorkflowService workflowService;

    private final DocumentRecognitionService documentRecognitionService;

    private final WorkflowRunningService workflowRunningService;

    @PostMapping("/commit")
    public R save(@RequestBody Workflow wf) {
        if(wf.getId()==null){
            wf.setCreateTime(LocalDateTime.now());
            wf.setStatus(CommonConst.EFFECTIVE);
            wf.setAppId(AigcConst.WORKFLOW_PREFIX + IdUtil.simpleUUID());
        }
        workflowService.saveOrUpdate(wf);
        return R.ok();
    }

    @GetMapping("/knowledge/list")
    public R<List<Map<String,Object>>> queryKnowledgeList() {
        List<AigcKnowledge> list = aigcKnowledgeService.list();
        List<Map<String,Object>> result = list.stream().map(aigcKnowledge -> {
            Map<String, Object> map = new HashMap<>();
            map.put("value", aigcKnowledge.getId());
            map.put("label", aigcKnowledge.getName());
            return map;
        }).collect(Collectors.toList());
        return R.ok(result);
    }

    @GetMapping("/model/list")
    public R<List<Map<String,Object>>> queryModelList() {
        List<AigcModel> list = aigcModelService.list();
        List<Map<String,Object>> result = list.stream().map(model -> {
            Map<String, Object> map = new HashMap<>();
            map.put("value", model.getId());
            map.put("label", model.getName());
            return map;
        }).collect(Collectors.toList());
        return R.ok(result);
    }



    @PostMapping("/del")
    public R delete(@RequestBody Workflow wf) {
        wf.setStatus(CommonConst.NOT_EFFECTIVE);
        workflowService.updateById(wf);
        return R.ok();
    }

    @GetMapping("/detail")
    public R<Workflow> get(@RequestParam Integer id) {
        return R.ok(workflowService.getById(id));
    }

    @GetMapping("/list")
    public R<List<Workflow>> getAll() {
        return R.ok(workflowService.list(Wrappers.<Workflow>lambdaQuery()
                .eq(Workflow::getStatus, CommonConst.EFFECTIVE)));
    }

    @GetMapping("/getRunningParameters")
    public R getRunningParameters(@RequestParam Integer id) {
        Workflow workflow = workflowService.getById(id);

        if (workflow == null) {
            return R.fail(1, "can not find the workflow by id: " + id);
        }

        Tinyflow tinyflow = workflow.toTinyflow();
        if (tinyflow == null) {
            return R.fail(2, "workflow content is empty! ");
        }

        Chain chain = tinyflow.toChain();
        if (chain == null) {
            return R.ok(new ArrayList<>());
        }
        List<Parameter> chainParameters = chain.getParameters();
        return R.ok(chainParameters);
    }

    @PostMapping("tryRunning")
    public R tryRunning(@RequestBody WorkflowRunningDto runningDto) {
        return R.ok(workflowRunningService.workflowRunning(runningDto));
    }


    @SaIgnore
    @GetMapping("/test/company")
    public String testCompanyData(String companyName){
        Map<String, String> variables = new HashMap<>();
        variables.put("潍坊盛清包装制品有限公司", "流动资产: 150,32800.1; 流动负债: 407,858.27; 存货: 12,629; 营业收入: 300,450.00; 营业成本: 100,450; 利润总额: 2,924.69; 所得税费用: 1000; 负债合计: 858.27; 资产总计: 420,432.1");
        variables.put("潍坊博泰机电配套厂", "流动资产: 250,328.1; 流动负债: 208,858.27; 存货: 12,629; 营业收入: 0; 营业成本: 0; 利润总额: -1,924.69; 所得税费用: 0; 负债合计: 207,858.27; 资产总计: 270,432.1");
        variables.put("潍坊中万润机械有限公司", "流动资产: 550,328.1; 流动负债: 207,8580.27; 存货: 12,629; 营业收入: 0; 营业成本: 0; 利润总额: -1,924.69; 所得税费用: 0; 负债合计: 207,858.27; 资产总计: 270,432.1");
        return variables.get(companyName);
    }
    @SaIgnore
    @GetMapping("/test/image")
    public String testImageData(String templateType,String imageUrl){
        try {
            URL url = new URL(imageUrl);
            List<Object> inputs = new ArrayList<>();
            inputs.add(url);
            String ocrResult = documentRecognitionService.recognizeDocument(templateType,inputs);
            log.info("OCR识别结果文本：{}",ocrResult);
            return ocrResult;
        }catch (Exception e){
            e.printStackTrace();
        }
        return "";
    }

    @GetMapping("/searchEngine/list")
    public R<List<Map<String,Object>>> querySearchEngineList() {
        List<AigcWebSearchEngine> list = aigcWebSearchEngineService.list();
        List<Map<String,Object>> result = list.stream().map(AigcWebSearchEngine -> {
            Map<String, Object> map = new HashMap<>();
            map.put("value", AigcWebSearchEngine.getId());
            map.put("label", AigcWebSearchEngine.getName());
            return map;
        }).collect(Collectors.toList());
        return R.ok(result);
    }

    @PostMapping("/searchEngine/httpParamTest")
    public R httpParamTest(@RequestParam String httpParam) {
        log.info("httpParamTest:{}",httpParam);
        return R.ok();
    }

}