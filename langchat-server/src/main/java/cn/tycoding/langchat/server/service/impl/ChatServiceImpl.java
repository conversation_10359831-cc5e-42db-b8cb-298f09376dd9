/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.server.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.tycoding.langchat.ai.biz.entity.AigcApp;
import cn.tycoding.langchat.ai.biz.entity.AigcConversation;
import cn.tycoding.langchat.ai.biz.entity.AigcMessage;
import cn.tycoding.langchat.ai.biz.entity.AigcOss;
import cn.tycoding.langchat.ai.biz.service.AigcMessageService;
import cn.tycoding.langchat.ai.core.service.LangChatService;
import cn.tycoding.langchat.common.ai.dto.ChatReq;
import cn.tycoding.langchat.common.ai.dto.ChatRes;
import cn.tycoding.langchat.common.ai.dto.ImageR;
import cn.tycoding.langchat.common.ai.utils.StreamEmitter;
import cn.tycoding.langchat.common.core.constant.RoleEnum;
import cn.tycoding.langchat.common.core.utils.ServletUtil;
import cn.tycoding.langchat.server.service.ChatService;
import cn.tycoding.langchat.server.store.AppStore;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import dev.langchain4j.data.image.Image;
import dev.langchain4j.model.output.Response;
import dev.langchain4j.model.output.TokenUsage;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/1/4
 */
@Slf4j
@Service
public class ChatServiceImpl implements ChatService {

    private final LangChatService langChatService;
    private final AigcMessageService aigcMessageService;
    private final AppStore appStore;
    private final ApplicationContext applicationContext;

    public ChatServiceImpl(LangChatService langChatService,
                          AigcMessageService aigcMessageService,
                          AppStore appStore,
                          ApplicationContext applicationContext) {
        this.langChatService = langChatService;
        this.aigcMessageService = aigcMessageService;
        this.appStore = appStore;
        this.applicationContext = applicationContext;
    }

    @Override
    public void chat(ChatReq req) {
        StreamEmitter emitter = req.getEmitter();
        long startTime = System.currentTimeMillis();
        StringBuilder text = new StringBuilder();

        if (StrUtil.isNotBlank(req.getAppId())) {
            AigcApp app = appStore.get(req.getAppId());
            if (app != null) {
                req.setModelId(app.getModelId());
                req.setPromptText(app.getPrompt());
                req.setKnowledgeIds(app.getKnowledgeIds());
                req.setMcpServiceIds(app.getMcpServiceIds());
            }
        }

        // save user message
        req.setRole(RoleEnum.USER.getName());
        saveMessage(req, 0, 0);

        try {
            // 根据是否有MCP服务选择不同的服务实现
            LangChatService chatService = selectChatService(req);

            chatService
                    .chat(req)
                    .onNext(e -> {
                        text.append(e);
                        emitter.send(new ChatRes(e));
                    })
                    .onComplete((e) -> {
                        TokenUsage tokenUsage = e.tokenUsage();
                        ChatRes res = new ChatRes(tokenUsage.totalTokenCount(), startTime);
                        emitter.send(res);
                        emitter.complete();

                        // save assistant message
                        req.setMessage(text.toString());
                        req.setRole(RoleEnum.ASSISTANT.getName());
                        saveMessage(req, tokenUsage.inputTokenCount(), tokenUsage.outputTokenCount());
                    })
                    .onError((e) -> {
                        emitter.error(e.getMessage());
                        throw new RuntimeException(e.getMessage());
                    })
                    .start();
        } catch (Exception e) {
            e.printStackTrace();
            emitter.error(e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 根据请求选择合适的聊天服务
     */
    private LangChatService selectChatService(ChatReq req) {
        // 检查是否有MCP服务配置
        if (req.getMcpServiceIds() != null && !req.getMcpServiceIds().isEmpty()) {
            try {
                // 尝试获取MCP增强的聊天服务
                LangChatService mcpEnhancedService = applicationContext.getBean("mcpEnhancedLangChatService", LangChatService.class);
                log.info("使用MCP增强聊天服务，配置的MCP服务数: {}", req.getMcpServiceIds().size());
                return mcpEnhancedService;
            } catch (Exception e) {
                log.warn("MCP增强聊天服务不可用，使用普通聊天服务: {}", e.getMessage());
                return langChatService;
            }
        } else {
            log.info("使用普通聊天服务");
            return langChatService;
        }
    }

    private void saveMessage(ChatReq req, Integer inputToken, Integer outputToken) {
        if (req.getConversationId() != null) {
            log.debug("保存消息: conversationId={}, appId={}, userId={}",
                req.getConversationId(), req.getAppId(), req.getUserId());

            // 如果是应用级别的conversationId（格式为app_{appId}），需要确保对话记录存在
            if (req.getConversationId().startsWith("app_") && StrUtil.isNotBlank(req.getAppId())) {
                log.info("检测到应用级别对话，确保对话记录存在: conversationId={}, appId={}",
                    req.getConversationId(), req.getAppId());
                ensureAppConversationExists(req);
            }

            AigcMessage message = new AigcMessage();
            BeanUtils.copyProperties(req, message);
            message.setIp(ServletUtil.getIpAddr());
            message.setPromptTokens(inputToken);
            message.setTokens(outputToken);
            aigcMessageService.addMessage(message);
        }
    }

    /**
     * 确保应用对话记录存在
     */
    private void ensureAppConversationExists(ChatReq req) {
        String conversationId = req.getConversationId();
        String userId = String.valueOf(req.getUserId());
        String appId = req.getAppId();

        // 直接检查这个conversationId是否已存在
        AigcConversation existingConversation = aigcMessageService.getConversation(conversationId);

        if (existingConversation == null) {
            // 创建新的对话记录
            AigcConversation conversation = new AigcConversation()
                    .setId(conversationId)
                    .setUserId(userId)
                    .setAppId(appId)
                    .setTitle("应用对话")
                    .setCreateTime(new Date());

            log.info("创建应用对话记录: conversationId={}, userId={}, appId={}", conversationId, userId, appId);
            aigcMessageService.addConversation(conversation);
        }
    }

    @Override
    public String text(ChatReq req) {
        String text;
        try {
            // 根据是否有MCP服务选择不同的服务实现
            LangChatService chatService = selectChatService(req);
            text = chatService.text(req);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e.getMessage());
        }
        return text;
    }

    @Override
    public AigcOss image(ImageR req) {
        Response<Image> res = langChatService.image(req);

        String path = res.content().url().toString();
        AigcOss oss = new AigcOss();
        oss.setUrl(path);
        return oss;
    }
}
