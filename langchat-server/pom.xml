<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
  ~
  ~ Licensed under the GNU Affero General Public License, Version 3 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     https://www.gnu.org/licenses/agpl-3.0.html
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.tycoding</groupId>
        <artifactId>langchat</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>langchat-server</artifactId>
    <version>${revision}</version>
    <packaging>jar</packaging>

    <dependencies>
        <!-- Modules -->
        <dependency>
            <groupId>cn.tycoding</groupId>
            <artifactId>langchat-auth</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.tycoding</groupId>
            <artifactId>langchat-ai-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.tycoding</groupId>
            <artifactId>langchat-mcp-core</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>cn.tycoding</groupId>
            <artifactId>langchat-upms-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>image-server</artifactId>
            <version>1.1.0</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.name}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <finalName>${project.build.finalName}</finalName>
                    <mainClass>cn.tycoding.langchat.LangChatApp</mainClass>
                    <layers>
                        <enabled>true</enabled>
                    </layers>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>3.3.0</version>
                <configuration>
                    <archive>
                        <manifest>
                            <mainClass>cn.tycoding.langchat.LangChatApp</mainClass>
                        </manifest>
                    </archive>
                    <descriptors>
                        <descriptor>/script/assembly/assembly.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
