<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
  ~
  ~ Licensed under the GNU Affero General Public License, Version 3 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     https://www.gnu.org/licenses/agpl-3.0.html
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.tycoding.langchat.upms.mapper.SysUserRoleMapper">

    <select id="getUserListByRoleId" resultType="cn.tycoding.langchat.upms.entity.SysUser">
        SELECT `user`.*
        FROM sys_user `user`,
             sys_user_role sur,
             sys_role `role`
        WHERE `user`.id = sur.user_id
          AND sur.role_id = `role`.id
          AND `role`.id = #{roleId}
    </select>

    <select id="getRoleListByUserId" resultType="cn.tycoding.langchat.upms.entity.SysRole">
        SELECT `role`.*
        FROM sys_user `user`,
             sys_user_role sur,
             sys_role `role`
        WHERE `user`.id = sur.user_id
          AND sur.role_id = `role`.id
          AND `user`.id = #{userId}
    </select>
</mapper>
