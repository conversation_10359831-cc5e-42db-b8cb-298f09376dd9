package cn.tycoding.utils;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.interactive.documentnavigation.outline.PDDocumentOutline;
import org.apache.pdfbox.pdmodel.interactive.documentnavigation.outline.PDOutlineItem;
import org.apache.pdfbox.text.PDFTextStripper;

import java.io.IOException;
import java.util.*;
import java.util.regex.Pattern;

/**
 * PDF内容过滤器工具类
 * 
 * 该类提供了从PDF文件中提取包含特定标题和关键字内容的功能，
 * 支持根据目录结构智能提取相关章节页面，并可作为备选方案通过
 * 内容关键词匹配来提取所需页面，最终生成一个新的PDF文档。
 * 
 * 主要功能包括：
 * - 读取PDF目录结构并按层级打印
 * - 根据标题关键词智能提取相关页面
 * - 在无目录或目录不匹配时，通过内容关键词进行页面提取
 * - 提取结果保存为新的PDF文件
 * 
 * <AUTHOR>
 * @date 2025/8/1 12:04
 */
public class PDFContentFilter {

    // 关键词配置（匹配标题用 模糊匹配）
    private static final Set<String> TITLE_KEYWORDS = new HashSet<>(Arrays.asList(
            "负债表", "利润表", "现金流量表", "会计数据", "财务指标", "财务报表",
            "财务数据", "成本分析", "收入分析", "成本分析表", "资产及负债", "资产和负债", "合并资产负债表"
    ));

    // 关键词配置（无标题匹配时用 精确匹配）
    private static final Set<String> KEYWORDS = new HashSet<>(Arrays.asList(
            "现金流量表","现金流量表(续)","现金流量表（续）",
            "合并资产负债表","合并资产负债表(续)","合并资产负债表（续）",
            "资产负债表","资产负债表(续)","资产负债表（续）",
            "合并利润表","合并利润表(续)","合并利润表（续）",
            "利润表","利润表(续)","利润表",
            "合并现金流量表","合并现金流量表(续)","合并现金流量表（续）"
    ));

    public static void main(String[] args) throws IOException {
        String inputPath = "C:\\Users\\<USER>\\Desktop\\图片\\财报\\海航24年报告.pdf";
        String outputPath = "C:\\Users\\<USER>\\Desktop\\图片\\财报\\海航24年报告-已过滤.pdf";
//        readFilePdfTitle();
//        extractPagesSmart(inputPath,outputPath);
    }

    /**
     * 读取并打印PDF文件的目录标题结构
     * 支持最多三级标题的递归遍历，并按层级缩进显示
     * 
     * @throws IOException 当文件读取或PDF解析失败时抛出
     */
    public static void readFilePdfTitle(PDDocument document) throws IOException {
        try (document; document) {
            // 获取PDF文档的大纲（目录）结构
            PDDocumentOutline outline = document.getDocumentCatalog().getDocumentOutline();

            // 如果存在目录结构，则遍历并打印标题
            if (outline != null) {
                PDOutlineItem current = outline.getFirstChild();

                // 遍历所有一级标题
                while (current != null) {
                    System.out.println("一级标题：" + current.getTitle());

                    // 遍历二级标题
                    PDOutlineItem child = current.getFirstChild();
                    while (child != null) {
                        System.out.println("  二级标题：" + child.getTitle());

                        // 遍历三级标题
                        PDOutlineItem grandChild = child.getFirstChild();
                        while (grandChild != null) {
                            System.out.println("    三级标题：" + grandChild.getTitle());
                            grandChild = grandChild.getNextSibling();
                        }
                        child = child.getNextSibling();
                    }
                    current = current.getNextSibling();
                }
            }
        } catch (IOException e) {
            System.err.println("读取PDF文本时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
        // 关闭文档资源
        document.close();
    }

    /**
     * 根据传入的已加载PDF文档，智能提取包含特定标题关键词和内容关键词的页面，
     * 并返回包含提取页面的新PDF文档对象。
     *
     * @param document 已加载的PDF文档对象
     * @return 包含提取页面的新PDDocument对象，若无匹配返回空的PDDocument，异常返回null
     */
    public static PDDocument extractPagesSmartToDocument(PDDocument document) {
        if (document == null) {
            System.err.println("❌ 错误：传入的PDF文档为空！");
            return null;
        }

        PDDocument outputDoc = new PDDocument();
        Set<Integer> extractedPages = new LinkedHashSet<>(); // 保证顺序并去重

        try {
            // 尝试读取PDF目录结构（大纲）
            PDDocumentOutline outline = document.getDocumentCatalog().getDocumentOutline();
            if (outline != null) {
                List<PDOutlineItem> items = flattenOutline(outline);
                for (PDOutlineItem item : items) {
                    String title = item.getTitle().trim();
                    boolean contains = TITLE_KEYWORDS.stream().anyMatch(title::contains);
                    if (!contains) continue;

                    int startPage = getPageNumber(document, item);
                    int endPage = findNextOutlinePage(items, item, document) - 1;
                    if (startPage < 0 || endPage < 0) continue; // 安全保护
                    if (endPage < startPage) endPage = startPage;

                    System.out.printf("✅ 目录匹配：%s，页码：%d-%d\n", title, startPage + 1, endPage + 1);
                    for (int i = startPage; i <= endPage; i++) {
                        extractedPages.add(i);
                    }
                }
            } else {
                System.out.println("📂 PDF没有目录结构。");
            }

            // 无论是否匹配标题，始终进行关键词匹配
            System.out.println("🔍 开始内容关键字匹配...");
            fallbackByContent(document, extractedPages);

            // 按顺序写入最终提取页
            List<Integer> sortedPages = new ArrayList<>(extractedPages);
            Collections.sort(sortedPages);
            for (int pageNum : sortedPages) {
                try {
                    outputDoc.addPage(document.getPage(pageNum));
                } catch (Exception e) {
                    System.err.println("⚠️ 警告：无法添加页 " + (pageNum + 1));
                }
            }

            return outputDoc;

        } catch (IOException e) {
            System.err.println("❌ IO异常：" + e.getMessage());
            try {
                outputDoc.close();
            } catch (IOException ex) {
                System.err.println("⚠️ 关闭输出文档时出错：" + ex.getMessage());
            }
            return null;
        } catch (Exception e) {
            System.err.println("❌ 未知异常：" + e.getMessage());
            try {
                outputDoc.close();
            } catch (IOException ex) {
                System.err.println("⚠️ 关闭输出文档时出错：" + ex.getMessage());
            }
            return null;
        }
    }

    /**
     * 展平 PDF 目录项，提取所有 1~3 级标题为一维列表
     *
     * @param outline PDF 文档的目录对象
     * @return 包含所有层级标题的列表（最多到三级）
     */
    private static List<PDOutlineItem> flattenOutline(PDDocumentOutline outline) {
        List<PDOutlineItem> result = new ArrayList<>();
        PDOutlineItem first = outline.getFirstChild();
        while (first != null) {
            result.add(first);
            PDOutlineItem child = first.getFirstChild();
            while (child != null) {
                result.add(child);
                PDOutlineItem grand = child.getFirstChild();
                while (grand != null) {
                    result.add(grand);
                    grand = grand.getNextSibling();
                }
                child = child.getNextSibling();
            }
            first = first.getNextSibling();
        }
        return result;
    }

    /**
     * 获取目录项对应的页码
     *
     * @param doc PDF文档对象
     * @param item 目录项
     * @return 页码索引（从0开始），如果获取失败则返回-1
     */
    private static int getPageNumber(PDDocument doc, PDOutlineItem item) {
        try {
            PDPage page = item.findDestinationPage(doc);
            return doc.getPages().indexOf(page);
        } catch (Exception e) {
            System.err.println("⚠️ 无法获取目录项页码：" + e.getMessage());
            return -1;
        }
    }

    /**
     * 查找当前目录项的下一个有效页码
     * 用于确定当前章节的结束页码，从而正确提取PDF页面范围
     *
     * @param all 所有目录项的扁平化列表
     * @param current 当前目录项
     * @param doc PDF文档对象
     * @return 下一个有效页码，如果没有找到则返回文档总页数
     */
    private static int findNextOutlinePage(List<PDOutlineItem> all, PDOutlineItem current, PDDocument doc) {
        int currIndex = all.indexOf(current);
        // 从当前目录项的下一个位置开始查找
        for (int i = currIndex + 1; i < all.size(); i++) {
            int page = getPageNumber(doc, all.get(i));
            // 找到下一个有效的页码就返回
            if (page != -1) return page;
        }
        // 没有找到下一个有效页码，默认返回文档总页数
        return doc.getNumberOfPages();
    }

    /**
     * 通过内容关键词匹配来提取PDF页面
     * 当无法通过目录结构提取时，作为备选方案使用内容匹配
     *
     * @param doc 已加载的PDF文档对象
     * @param extractedPages 已提取的页面集合，方法会向其中添加匹配的页面
     * @throws IOException 当读取PDF文本时发生IO异常
     */
    private static void fallbackByContent(PDDocument doc, Set<Integer> extractedPages) throws IOException {
        PDFTextStripper stripper = new PDFTextStripper();
        int total = doc.getNumberOfPages();

        // 遍历每一页进行关键词匹配
        for (int i = 1; i <= total; i++) {
            // 设置提取单页文本
            stripper.setStartPage(i);
            stripper.setEndPage(i);
            String text = stripper.getText(doc);

            // 使用正则表达式进行关键词匹配，确保关键词是独立的词
            boolean matched = KEYWORDS.stream().anyMatch(keyword -> {
                String regex = "(?<!\\p{L}|\\p{N})" + Pattern.quote(keyword) + "(?!\\p{L}|\\p{N})";
                return Pattern.compile(regex).matcher(text).find();
            });

            // 如果当前页匹配成功，添加当前页和下一页到提取集合中
            if (matched) {
                int currentPage = i - 1; // PDF页码从1开始，转换为从0开始的索引
                extractedPages.add(currentPage);
                // 避免越界，确保下一页存在
                if (currentPage + 1 < total) {
                    extractedPages.add(currentPage + 1); // 追加下一页
                }
                System.out.printf("📄 关键词命中页：%d，追加页：%d\n", currentPage + 1, currentPage + 2);
            }
        }
    }

    /**
     * 读取并打印PDF文档中的所有文本内容
     * 该方法会按页面顺序提取文本，并保持文本在页面中的位置关系
     *
     * @param document 已加载的PDF文档对象，不可为null
     */
    private static void readPDFText(PDDocument document) {
        try (document) {
            PDFTextStripper stripper = new PDFTextStripper();
            stripper.setSortByPosition(true);  // 关键设置：按位置排序，保持文本顺序
            stripper.setStartPage(1);          // 设置起始页为第1页
            stripper.setEndPage(document.getNumberOfPages()); // 设置结束页为最后一页

            String text = stripper.getText(document);
            System.out.println(text);
        } catch (IOException e) {
            System.err.println("读取PDF文本时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

}
