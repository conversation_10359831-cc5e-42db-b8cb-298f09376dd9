package cn.tycoding.engine;

import com.benjaminwan.ocrlibrary.OcrResult;
import io.github.mymonstercat.Model;
import io.github.mymonstercat.ocr.InferenceEngine;
import io.github.mymonstercat.ocr.config.ParamConfig;
import lombok.extern.slf4j.Slf4j;

/**
 * OCR引擎管理器
 * <AUTHOR>
 * @date 2025/5/13 11:04
 */
@Slf4j
public class OcrEngineManager {
	private static volatile InferenceEngine instance;
	private static final Object LOCK = new Object();

	private OcrEngineManager() {
	}

	public static InferenceEngine getInstance() {
		if (instance == null) {
			synchronized (LOCK) {
				if (instance == null) {
					instance = InferenceEngine.getInstance(Model.ONNX_PPOCR_V4);
				}
			}
		}
		return instance;
	}

	public static OcrResult performOcr(String imagePath) {
		try {
			ParamConfig config = ParamConfig.getDefaultConfig();
			config.setDoAngle(true);
			config.setMostAngle(true);
			return getInstance().runOcr(imagePath, config);
		} catch (Exception e) {
			log.error("OCR识别失败", e);
			throw new RuntimeException("OCR识别失败");
		}
	}
}