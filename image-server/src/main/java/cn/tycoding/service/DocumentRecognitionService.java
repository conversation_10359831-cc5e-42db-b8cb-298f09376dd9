package cn.tycoding.service;

import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface DocumentRecognitionService {

	/**
	 * 通用文档识别方法，支持多种输入类型
	 *
	 * @param templateType 模板类型
	 * @param inputs       输入对象列表（支持MultipartFile、URL、File、InputStream等）
	 * @return 识别结果JSON字符串
	 */
	String recognizeDocument(String templateType, List<?> inputs);

	/**
	 * 识别合同文档
	 * 
	 * @param pdfInputStream PDF文件输入流
	 * @return 合同识别结果JSON字符串
	 */
	String recognizeContract(InputStream pdfInputStream);

	/**
	 * 识别身份证
	 * 
	 * @param imageInputStreams 图片输入流列表
	 * @return 身份证识别结果JSON字符串
	 */
	String recognizeIdCard(List<InputStream> imageInputStreams);
	/**
	 * 识别PDF
	 *
	 * @param pdfInputStream PDF文件输入流
	 * @return 文本
	 */
    String recognizeEarningsPDF(InputStream pdfInputStream);
}