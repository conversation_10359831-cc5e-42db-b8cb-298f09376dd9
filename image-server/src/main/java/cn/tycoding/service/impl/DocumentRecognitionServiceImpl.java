package cn.tycoding.service.impl;


import cn.tycoding.consts.OcrAiProperties;
import cn.tycoding.consts.OrcTemplateType;
import cn.tycoding.engine.OcrEngineManager;
import cn.tycoding.service.DocumentRecognitionService;
import cn.tycoding.utils.PDFContentFilter;
import com.benjaminwan.ocrlibrary.OcrResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.io.IOUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.ImageType;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.apache.pdfbox.text.PDFTextStripper;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DocumentRecognitionServiceImpl implements DocumentRecognitionService {

	private final OcrAiProperties ocrAiProperties;


	@Override
	public String recognizeDocument(String templateType, List<?> inputs) {
		try {
			List<InputStream> inputStreams = convertInputs(inputs);
			OrcTemplateType type = OrcTemplateType.valueOf(templateType);
            return switch (type) {
				case DOCUMENT -> recognizeContract(inputStreams.get(0));
                case ID_CARD -> recognizeIdCard(inputStreams);
                case PDF -> recognizeEarningsPDF(inputStreams.get(0));
				case SCANNED_PDF -> recognizeContractWithScanned(inputStreams.get(0));
                default -> throw new IllegalArgumentException("不支持的模板类型: " + templateType);
            };
		} catch (IllegalArgumentException e) {
			throw new IllegalArgumentException("无效的模板类型: " + templateType, e);
		} catch (IOException e) {
			throw new RuntimeException(e.getMessage());
		}
	}

	@Override
	public String recognizeContract(InputStream pdfInputStream) {
		try (PDDocument document = Loader.loadPDF(IOUtils.toByteArray(pdfInputStream))) {
			// 1. 提取PDF文本内容
			PDFTextStripper stripper = new PDFTextStripper();
			// 按位置排序提取文本
			stripper.setSortByPosition(true);
			// 处理表格
			stripper.setShouldSeparateByBeads(true);
			String text = stripper.getText(document);
			return text;

			// 2. 清洗文本内容
			//String cleanedText = cleanExtractedText(rawText);

			// 3. 提取关键信息生成JSON
			//return extractKeyInformation(rawText);
		} catch (Exception e) {
			log.error("合同处理流程异常", e);
			throw new RuntimeException("合同处理失败", e);
		}
	}

	// 新增文本清洗方法
	private String cleanExtractedText(String rawText) {
		// 移除多余空格、换行符和特殊字符
		return rawText.replaceAll("[\\u0000-\\u001F]", "")
				.replaceAll("\\s+", " ")
				.replaceAll("(?m)^\\s+$", "")
				.trim();
	}

	// 新增关键信息提取方法
	private String extractKeyInformation(String text) {
		return text.lines()
						.filter(line -> ocrAiProperties.getKeywords().getContractFields()
								.stream().anyMatch(line::contains))
						.map(line -> String.format("\"%s\"", line.trim()))
						.collect(Collectors.joining(","));
	}


	@Override
	public String recognizeIdCard(List<InputStream> imageInputStreams) {
		try {
			// 1. 并行处理所有图片
			List<CompletableFuture<String>> futures = imageInputStreams.stream()
					.map(this::processImageAsync)
					.collect(Collectors.toList());

			// 2. 等待所有图片处理完成
			List<String> results = futures.stream()
					.map(CompletableFuture::join)
					.collect(Collectors.toList());

			// 3. 合并结果
			return String.join("\n", results);
		} catch (Exception e) {
			log.error("身份证识别失败", e);
			throw new RuntimeException("身份证识别失败", e);
		}
	}

	public CompletableFuture<String> processImageAsync(InputStream imageInputStream) {
		try {
			BufferedImage image = ImageIO.read(imageInputStream);
			return CompletableFuture.completedFuture(processImage(image));
		} catch (Exception e) {
			log.error("图片处理失败", e);
			return CompletableFuture.failedFuture(e);
		}
	}

	private String processImage(BufferedImage image) {
		try {
			if (image == null) {
				throw new IllegalArgumentException("无法读取图片数据");
			}
			// 1. 将BufferedImage保存为临时文件
			File tempFile = File.createTempFile("ocr_", ".png");
			tempFile.deleteOnExit();
			ImageIO.write(image, "PNG", tempFile);

			// 2. 使用OCR引擎管理器执行识别
			OcrResult result = OcrEngineManager.performOcr(tempFile.getAbsolutePath());
			return result.getStrRes();
		} catch (Exception e) {
			log.error("OCR识别失败", e);
			throw new RuntimeException("OCR识别失败", e);
		}
	}

	private InputStream convertToInputStream(Object input) throws IOException {
		if (input instanceof MultipartFile) {
			return ((MultipartFile) input).getInputStream();
		} else if (input instanceof URL) {
			return ((URL) input).openStream();
		} else if (input instanceof File) {
			return new FileInputStream((File) input);
		} else if (input instanceof InputStream) {
			return (InputStream) input;
		} else {
			throw new IllegalArgumentException("不支持的输入类型: " + input.getClass().getName());
		}
	}

	private List<InputStream> convertInputs(List<?> inputs) throws IOException {
		List<InputStream> streams = new ArrayList<>();
		try {
			for (Object input : inputs) {
				streams.add(convertToInputStream(input));
			}
			return streams;
		} catch (Exception e) {
			// 确保已打开的流被关闭
			for (InputStream stream : streams) {
				stream.close();
			}
			throw e;
		}
	}


	//识别PDF
	@Override
	public String recognizeEarningsPDF(InputStream pdfInputStream) {
		try (PDDocument document = Loader.loadPDF(IOUtils.toByteArray(pdfInputStream))) {
			// 1. 提取PDF文本内容
			PDFTextStripper stripper = new PDFTextStripper();
			// 按位置排序提取文本
			stripper.setSortByPosition(true);
			// 处理表格
			stripper.setShouldSeparateByBeads(true);
			//关键字匹配提纯PDF
			PDDocument extracted = PDFContentFilter.extractPagesSmartToDocument(document);
			return stripper.getText(extracted);
		} catch (Exception e) {
			log.error("合同处理流程异常", e);
			throw new RuntimeException("合同处理失败", e);
		}
	}

	//识别合同，包含扫描件
	private String recognizeContractWithScanned(InputStream inputStream) {
		try (PDDocument document = Loader.loadPDF(IOUtils.toByteArray(inputStream))) {
			log.info("开始处理扫描版PDF，总页数: {}", document.getNumberOfPages());

			// 1. 先尝试提取文本，判断是否为扫描版
			PDFTextStripper stripper = new PDFTextStripper();
			stripper.setSortByPosition(true);
			String extractedText = stripper.getText(document);

			// 2. 如果文本内容充足，直接返回（可能不是纯扫描版）
			if (extractedText != null && extractedText.trim().length() > 100) {
				log.info("PDF包含可提取文本，直接返回文本内容");
				return extractedText;
			}

			// 3. 文本内容不足，进行OCR处理
			log.info("PDF文本内容不足，开始OCR识别");
			return performPdfOcr(document);

		} catch (Exception e) {
			log.error("扫描版PDF处理异常", e);
			throw new RuntimeException("扫描版PDF处理失败", e);
		}
	}

	/**
	 * 对PDF进行OCR识别
	 */
	private String performPdfOcr(PDDocument document) throws IOException {
		StringBuilder result = new StringBuilder();
		PDFRenderer pdfRenderer = new PDFRenderer(document);

		int totalPages = document.getNumberOfPages();
		int maxPages = Math.min(totalPages, ocrAiProperties.getMaxPdfPages()); // 限制最大页数

		for (int pageIndex = 0; pageIndex < maxPages; pageIndex++) {
			try {
				log.info("正在处理第 {} 页，共 {} 页", pageIndex + 1, totalPages);

				// 将PDF页面渲染为高分辨率图片
				BufferedImage pageImage = pdfRenderer.renderImageWithDPI(
						pageIndex,
						300,  // 300 DPI，确保OCR质量
						ImageType.RGB
				);

				// 对页面图片进行OCR识别
				String pageText = processPageImage(pageImage, pageIndex + 1);

				if (pageText != null && !pageText.trim().isEmpty()) {
					result.append("=== 第").append(pageIndex + 1).append("页 ===\n");
					result.append(pageText.trim()).append("\n\n");
				}

			} catch (Exception e) {
				log.warn("处理第 {} 页时出错: {}", pageIndex + 1, e.getMessage());
				result.append("=== 第").append(pageIndex + 1).append("页处理失败 ===\n\n");
			}
		}

		if (totalPages > maxPages) {
			result.append("注意：PDF共").append(totalPages).append("页，")
					.append("受限制只处理了前").append(maxPages).append("页\n");
		}

		return result.toString();
	}

	/**
	 * 处理单个页面图片
	 */
	private String processPageImage(BufferedImage pageImage, int pageNumber) {
		try {
			if (pageImage == null) {
				log.warn("第 {} 页图片为空", pageNumber);
				return "";
			}

			// 1. 图片预处理（可选）
//			BufferedImage processedImage = preprocessImage(pageImage);

			// 2. 保存为临时文件
			File tempFile = File.createTempFile("pdf_page_" + pageNumber + "_", ".png");
			tempFile.deleteOnExit();
			ImageIO.write(pageImage, "PNG", tempFile);
//			ImageIO.write(processedImage, "PNG", tempFile);

			// 3. 使用OCR引擎识别
			OcrResult ocrResult = OcrEngineManager.performOcr(tempFile.getAbsolutePath());

			// 4. 清理临时文件
			tempFile.delete();

			return ocrResult.getStrRes();

		} catch (Exception e) {
			log.error("处理第 {} 页图片时出错", pageNumber, e);
			return "";
		}
	}
}