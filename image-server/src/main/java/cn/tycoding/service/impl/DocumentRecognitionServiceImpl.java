package cn.tycoding.service.impl;


import cn.tycoding.consts.OcrAiProperties;
import cn.tycoding.consts.OrcTemplateType;
import cn.tycoding.engine.OcrEngineManager;
import cn.tycoding.service.DocumentRecognitionService;
import cn.tycoding.utils.PDFContentFilter;
import com.benjaminwan.ocrlibrary.OcrResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.io.IOUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.ImageType;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.apache.pdfbox.text.PDFTextStripper;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DocumentRecognitionServiceImpl implements DocumentRecognitionService {

    private final OcrAiProperties ocrAiProperties;


    @Override
    public String recognizeDocument(String templateType, List<?> inputs) {
        try {
            List<InputStream> inputStreams = convertInputs(inputs);
            OrcTemplateType type = OrcTemplateType.valueOf(templateType);
            return switch (type) {
                case DOCUMENT -> recognizeContract(inputStreams.get(0));
                case ID_CARD -> recognizeIdCard(inputStreams);
                case PDF -> recognizeEarningsPDF(inputStreams.get(0));
                case SCANNED_PDF -> recognizeContractWithScanned(inputStreams.get(0));
                default -> throw new IllegalArgumentException("不支持的模板类型: " + templateType);
            };
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("无效的模板类型: " + templateType, e);
        } catch (IOException e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public String recognizeContract(InputStream pdfInputStream) {
        try (PDDocument document = Loader.loadPDF(IOUtils.toByteArray(pdfInputStream))) {
            // 1. 提取PDF文本内容
            PDFTextStripper stripper = new PDFTextStripper();
            // 按位置排序提取文本
            stripper.setSortByPosition(true);
            // 处理表格
            stripper.setShouldSeparateByBeads(true);
            String text = stripper.getText(document);
            return text;

            // 2. 清洗文本内容
            //String cleanedText = cleanExtractedText(rawText);

            // 3. 提取关键信息生成JSON
            //return extractKeyInformation(rawText);
        } catch (Exception e) {
            log.error("合同处理流程异常", e);
            throw new RuntimeException("合同处理失败", e);
        }
    }

    // 新增文本清洗方法
    private String cleanExtractedText(String rawText) {
        // 移除多余空格、换行符和特殊字符
        return rawText.replaceAll("[\\u0000-\\u001F]", "")
                .replaceAll("\\s+", " ")
                .replaceAll("(?m)^\\s+$", "")
                .trim();
    }

    // 新增关键信息提取方法
    private String extractKeyInformation(String text) {
        return text.lines()
                .filter(line -> ocrAiProperties.getKeywords().getContractFields()
                        .stream().anyMatch(line::contains))
                .map(line -> String.format("\"%s\"", line.trim()))
                .collect(Collectors.joining(","));
    }


    @Override
    public String recognizeIdCard(List<InputStream> imageInputStreams) {
        try {
            // 1. 并行处理所有图片
            List<CompletableFuture<String>> futures = imageInputStreams.stream()
                    .map(this::processImageAsync)
                    .collect(Collectors.toList());

            // 2. 等待所有图片处理完成
            List<String> results = futures.stream()
                    .map(CompletableFuture::join)
                    .collect(Collectors.toList());

            // 3. 合并结果
            return String.join("\n", results);
        } catch (Exception e) {
            log.error("身份证识别失败", e);
            throw new RuntimeException("身份证识别失败", e);
        }
    }

    public CompletableFuture<String> processImageAsync(InputStream imageInputStream) {
        try {
            BufferedImage image = ImageIO.read(imageInputStream);
            return CompletableFuture.completedFuture(processImage(image));
        } catch (Exception e) {
            log.error("图片处理失败", e);
            return CompletableFuture.failedFuture(e);
        }
    }

    private String processImage(BufferedImage image) {
        try {
            if (image == null) {
                throw new IllegalArgumentException("无法读取图片数据");
            }
            // 1. 将BufferedImage保存为临时文件
            File tempFile = File.createTempFile("ocr_", ".png");
            tempFile.deleteOnExit();
            ImageIO.write(image, "PNG", tempFile);

            // 2. 使用OCR引擎管理器执行识别
            OcrResult result = OcrEngineManager.performOcr(tempFile.getAbsolutePath());
            return result.getStrRes();
        } catch (Exception e) {
            log.error("OCR识别失败", e);
            throw new RuntimeException("OCR识别失败", e);
        }
    }

    private InputStream convertToInputStream(Object input) throws IOException {
        if (input instanceof MultipartFile) {
            return ((MultipartFile) input).getInputStream();
        } else if (input instanceof URL) {
            return ((URL) input).openStream();
        } else if (input instanceof File) {
            return new FileInputStream((File) input);
        } else if (input instanceof InputStream) {
            return (InputStream) input;
        } else {
            throw new IllegalArgumentException("不支持的输入类型: " + input.getClass().getName());
        }
    }

    private List<InputStream> convertInputs(List<?> inputs) throws IOException {
        List<InputStream> streams = new ArrayList<>();
        try {
            for (Object input : inputs) {
                streams.add(convertToInputStream(input));
            }
            return streams;
        } catch (Exception e) {
            // 确保已打开的流被关闭
            for (InputStream stream : streams) {
                stream.close();
            }
            throw e;
        }
    }


    //识别PDF
    @Override
    public String recognizeEarningsPDF(InputStream pdfInputStream) {
        try (PDDocument document = Loader.loadPDF(IOUtils.toByteArray(pdfInputStream))) {
            // 1. 提取PDF文本内容
            PDFTextStripper stripper = new PDFTextStripper();
            // 按位置排序提取文本
            stripper.setSortByPosition(true);
            // 处理表格
            stripper.setShouldSeparateByBeads(true);
            //关键字匹配提纯PDF
            PDDocument extracted = PDFContentFilter.extractPagesSmartToDocument(document);
            return stripper.getText(extracted);
        } catch (Exception e) {
            log.error("合同处理流程异常", e);
            throw new RuntimeException("合同处理失败", e);
        }
    }

    //识别合同，包含扫描件
    private String recognizeContractWithScanned(InputStream inputStream) {
        try (PDDocument document = Loader.loadPDF(IOUtils.toByteArray(inputStream))) {
            log.info("开始处理扫描版合同PDF，总页数: {}", document.getNumberOfPages());

            // 1. 先尝试提取文本，判断是否为扫描版
            PDFTextStripper stripper = new PDFTextStripper();
            stripper.setSortByPosition(true);
            // 处理表格
            stripper.setShouldSeparateByBeads(true);
            String extractedText = stripper.getText(document);

            // 2. 如果文本内容充足，进行表格结构化处理后返回
            if (extractedText != null && extractedText.trim().length() > 100) {
                return extractedText;
            }

            // 3. 文本内容不足，进行OCR处理（增强表格识别）
            log.info("PDF文本内容不足，开始OCR表格识别");
            return performPdfOcrWithTableRecognition(document);

        } catch (Exception e) {
            log.error("扫描版合同PDF处理异常", e);
            throw new RuntimeException("扫描版合同PDF处理失败", e);
        }
    }

    /**
     * 对PDF进行OCR识别（增强表格识别）
     */
    private String performPdfOcrWithTableRecognition(PDDocument document) throws IOException {
        StringBuilder result = new StringBuilder();
        PDFRenderer pdfRenderer = new PDFRenderer(document);

        int totalPages = document.getNumberOfPages();
        int maxPages = Math.min(totalPages, ocrAiProperties.getMaxPdfPages());

        for (int pageIndex = 0; pageIndex < maxPages; pageIndex++) {
            try {
                log.info("正在OCR处理第 {} 页，共 {} 页", pageIndex + 1, totalPages);

                BufferedImage pageImage = pdfRenderer.renderImageWithDPI(
                        pageIndex, 300, ImageType.RGB);

                // 增强的表格识别处理
                String pageText = processPageImageWithTableRecognition(pageImage, pageIndex + 1);

                if (pageText != null && !pageText.trim().isEmpty()) {
                    result.append("=== 第").append(pageIndex + 1).append("页 ===\n");
                    result.append(pageText).append("\n\n");
                }

            } catch (Exception e) {
                log.warn("OCR处理第 {} 页时出错: {}", pageIndex + 1, e.getMessage());
                result.append("=== 第").append(pageIndex + 1).append("页处理失败 ===\n\n");
            }
        }

        if (totalPages > maxPages) {
            result.append("注意：PDF共").append(totalPages).append("页，")
                    .append("受限制只处理了前").append(maxPages).append("页\n");
        }

        return result.toString();
    }

    /**
     * 增强的页面图片处理，支持表格识别
     */
    private String processPageImageWithTableRecognition(BufferedImage pageImage, int pageNumber) {
        try {
            if (pageImage == null) {
                log.warn("第 {} 页图片为空", pageNumber);
                return "";
            }

            // 1. 保存为临时文件
            File tempFile = File.createTempFile("contract_page_" + pageNumber + "_", ".png");
            tempFile.deleteOnExit();
            ImageIO.write(pageImage, "PNG", tempFile);

            // 2. 使用OCR引擎识别
            OcrResult ocrResult = OcrEngineManager.performOcr(tempFile.getAbsolutePath());

            // 3. 清理临时文件
            tempFile.delete();

            // 4. 获取原始OCR文本
            String rawText = ocrResult.getStrRes();
            if (rawText == null || rawText.trim().isEmpty()) {
                return "";
            }

            // 5. 尝试重构表格结构
            String structuredText = reconstructTableStructureFromOcr(rawText, pageNumber);

            return structuredText;

        } catch (Exception e) {
            log.error("处理第 {} 页图片时出错", pageNumber, e);
            return "";
        }
    }


    /**
     * 重构OCR文本的表格结构
     */
    private String reconstructTableStructureFromOcr(String rawText, int pageNumber) {
        try {
            log.debug("开始重构第{}页的表格结构", pageNumber);

            // 1. 文本预处理
            String cleanedText = cleanOcrText(rawText);

            // 2. 特殊处理：识别采购信息表格
            String processedText = processPurchaseTable(cleanedText);
            if (!processedText.equals(cleanedText)) {
                return processedText; // 如果成功处理了采购表格，直接返回
            }

            // 3. 通用表格处理逻辑
            String[] lines = cleanedText.split("\n");
            List<TableRow> tableRows = new ArrayList<>();
            List<String> nonTableLines = new ArrayList<>();

            for (String line : lines) {
                String trimmedLine = line.trim();
                if (trimmedLine.isEmpty()) {
                    continue;
                }

                // 尝试解析为表格行
                TableRow tableRow = parseLineAsTableRow(trimmedLine);
                if (tableRow != null && tableRow.getCells().size() > 1) {
                    tableRows.add(tableRow);
                } else {
                    nonTableLines.add(trimmedLine);
                }
            }

            // 4. 格式化输出
            return formatOcrResults(tableRows, nonTableLines, pageNumber);

        } catch (Exception e) {
            log.warn("第{}页表格结构重构失败，返回原始文本", pageNumber, e);
            return rawText;
        }
    }

    /**
     * 检测是否为表格行
     */
    private boolean isTableLine(String line) {
        if (line == null || line.trim().isEmpty()) {
            return false;
        }

        // 检测条件：
        // 1. 包含多个由空格或制表符分隔的字段
        // 2. 包含数字和文字的组合
        // 3. 字段数量大于等于2

        String[] fields = line.trim().split("\\s{2,}|\\t+"); // 多个空格或制表符分隔
        if (fields.length < 2) {
            // 尝试其他分隔符
            fields = line.trim().split("\\s+");
        }

        return fields.length >= 2 &&
                line.length() > 10 &&
                (line.matches(".*\\d.*") || containsCommonTableKeywords(line));
    }

    /**
     * 检查是否包含常见的表格关键词
     */
    private boolean containsCommonTableKeywords(String line) {
        String[] keywords = {
                "合同", "编号", "日期", "金额", "数量", "单价", "总价", "甲方", "乙方",
                "名称", "规格", "型号", "单位", "备注", "签署", "生效", "到期"
        };

        String lowerLine = line.toLowerCase();
        for (String keyword : keywords) {
            if (lowerLine.contains(keyword)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 解析表格字段
     */
    private String[] parseTableFields(String line) {
        // 尝试多种分隔方式
        String[] fields;

        // 1. 尝试多个空格分隔
        fields = line.trim().split("\\s{2,}");
        if (fields.length > 1) {
            return fields;
        }

        // 2. 尝试制表符分隔
        fields = line.trim().split("\\t+");
        if (fields.length > 1) {
            return fields;
        }

        // 3. 尝试单个空格分隔（可能不准确，但作为备选）
        fields = line.trim().split("\\s+");

        return fields;
    }

    /**
     * 清理OCR文本
     */
    private String cleanOcrText(String text) {
        if (text == null) {
            return "";
        }

        return text
                .replaceAll("[\\u0000-\\u001F]", "") // 移除控制字符
                .replaceAll("\\s+", " ")             // 多个空格合并为一个
                .replaceAll("(?m)^\\s+$", "")        // 移除只包含空格的行
                .trim();
    }

    /**
     * 解析行为表格行
     */
    private TableRow parseLineAsTableRow(String line) {
        String[] fields = parseTableFields(line);

        // 至少需要2个字段才认为是表格行
        if (fields.length < 2) {
            return null;
        }

        TableRow row = new TableRow();
        for (String field : fields) {
            String cleanField = field.trim();
            if (!cleanField.isEmpty()) {
                row.addCell(cleanField);
            }
        }

        return row.getCells().size() >= 2 ? row : null;
    }

    /**
     * 格式化OCR结果
     */
    private String formatOcrResults(List<TableRow> tableRows, List<String> nonTableLines, int pageNumber) {
        StringBuilder result = new StringBuilder();

        // 1. 添加非表格文本
        if (!nonTableLines.isEmpty()) {
            result.append("【文本内容】\n");
            for (String line : nonTableLines) {
                result.append(line).append("\n");
            }
            result.append("\n");
        }

        // 2. 处理表格数据
        if (!tableRows.isEmpty()) {
            result.append("【表格数据】\n");

            // 尝试识别表头
            boolean hasHeader = tableRows.size() > 1 &&
                    containsHeaderKeywords(tableRows.get(0));

            for (int i = 0; i < tableRows.size(); i++) {
                TableRow row = tableRows.get(i);

                if (i == 0 && hasHeader) {
                    result.append("表头: ");
                } else {
                    result.append("第").append(hasHeader ? i : i + 1).append("行: ");
                }

                result.append(String.join(" | ", row.getCells())).append("\n");
            }

            // 3. 生成键值对格式
            if (hasHeader && tableRows.size() > 1) {
                result.append("\n【结构化数据】\n");
                TableRow headerRow = tableRows.get(0);

                for (int i = 1; i < tableRows.size(); i++) {
                    TableRow dataRow = tableRows.get(i);
                    result.append("记录").append(i).append(": ");

                    List<String> headers = headerRow.getCells();
                    List<String> values = dataRow.getCells();

                    for (int j = 0; j < Math.min(headers.size(), values.size()); j++) {
                        if (j > 0) {
                            result.append(", ");
                        }
                        result.append(headers.get(j)).append("=").append(values.get(j));
                    }
                    result.append("\n");
                }
            }
        }

        return result.toString();
    }

    /**
     * 检查是否包含表头关键词
     */
    private boolean containsHeaderKeywords(TableRow row) {
        String[] headerKeywords = {
                "编号", "名称", "日期", "金额", "数量", "单价", "规格", "型号",
                "甲方", "乙方", "合同", "订单", "商品", "服务", "备注"
        };

        String rowText = String.join(" ", row.getCells()).toLowerCase();

        for (String keyword : headerKeywords) {
            if (rowText.contains(keyword)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 专门处理采购信息表格
     */
    private String processPurchaseTable(String text) {
        try {
            // 查找采购信息表格部分
            String purchasePattern = "一、采购信息：.*?(?=二、|三、|四、|五、|$)";
            Pattern pattern = Pattern.compile(purchasePattern, Pattern.DOTALL);
            Matcher matcher = pattern.matcher(text);

            if (!matcher.find()) {
                return text; // 没有找到采购信息，返回原文
            }

            String purchaseSection = matcher.group();
            log.debug("找到采购信息部分: {}", purchaseSection);

            // 解析采购表格
            String reconstructedTable = reconstructPurchaseTableStructure(purchaseSection);

            // 替换原文中的采购信息部分
            String result = text.replace(purchaseSection, reconstructedTable);

            return result;

        } catch (Exception e) {
            log.warn("处理采购表格失败", e);
            return text;
        }
    }

    /**
     * 重构采购表格结构
     */
    private String reconstructPurchaseTableStructure(String purchaseSection) {
        StringBuilder result = new StringBuilder();
        result.append("一、采购信息：\n");

        // 提取表格相关文本
        String tableText = purchaseSection.replace("一、采购信息：", "").trim();

        // 识别表格字段和数据
        PurchaseTableData tableData = parsePurchaseTableData(tableText);

        if (tableData.isValid()) {
            // 格式化输出表格
            result.append("\n【采购明细表格】\n");
            result.append("表头: ").append(String.join(" | ", tableData.getHeaders())).append("\n");

            for (int i = 0; i < tableData.getDataRows().size(); i++) {
                List<String> row = tableData.getDataRows().get(i);
                result.append("第").append(i + 1).append("行: ").append(String.join(" | ", row)).append("\n");
            }

            // 键值对格式
            result.append("\n【结构化数据】\n");
            for (int i = 0; i < tableData.getDataRows().size(); i++) {
                List<String> row = tableData.getDataRows().get(i);
                result.append("商品").append(i + 1).append(": ");

                List<String> headers = tableData.getHeaders();
                for (int j = 0; j < Math.min(headers.size(), row.size()); j++) {
                    if (j > 0) result.append(", ");
                    result.append(headers.get(j)).append("=").append(row.get(j));
                }
                result.append("\n");
            }

            // 添加费用信息
            if (tableData.hasAdditionalInfo()) {
                result.append("\n【费用信息】\n");
                result.append(tableData.getAdditionalInfo());
            }

        } else {
            // 如果解析失败，返回原始文本
            result.append(tableText);
        }

        return result.toString();
    }

    /**
     * 解析采购表格数据
     */
    private PurchaseTableData parsePurchaseTableData(String tableText) {
        PurchaseTableData data = new PurchaseTableData();

        try {
            // 定义标准的表头顺序
            List<String> standardHeaders = Arrays.asList("序号", "商品名称", "数量", "单位", "单价", "金额（元）");
            data.setHeaders(standardHeaders);

            // 使用正则表达式提取关键信息
            // 查找商品信息：数字 + 商品名称 + 数量 + 单位 + 单价 + 金额
            Pattern itemPattern = Pattern.compile("(\\d+)\\s*([^\\d￥]+?)\\s*(\\d+)\\s*([^\\d￥]+?)\\s*(￥[\\d,]+\\.\\d{2})\\s*(￥[\\d,]+\\.\\d{2})");
            Matcher itemMatcher = itemPattern.matcher(tableText);

            while (itemMatcher.find()) {
                List<String> row = new ArrayList<>();
                row.add(itemMatcher.group(1).trim()); // 序号
                row.add(itemMatcher.group(2).trim()); // 商品名称
                row.add(itemMatcher.group(3).trim()); // 数量
                row.add(itemMatcher.group(4).trim()); // 单位
                row.add(itemMatcher.group(5).trim()); // 单价
                row.add(itemMatcher.group(6).trim()); // 金额

                data.addDataRow(row);
            }

            // 如果正则匹配失败，尝试手动解析
            if (data.getDataRows().isEmpty()) {
                data = parseManually(tableText);
            }

            // 提取费用信息
            StringBuilder additionalInfo = new StringBuilder();
            if (tableText.contains("运费：")) {
                Pattern feePattern = Pattern.compile("运费：(￥[\\d,]+\\.\\d{2})");
                Matcher feeMatcher = feePattern.matcher(tableText);
                if (feeMatcher.find()) {
                    additionalInfo.append("运费: ").append(feeMatcher.group(1)).append("\n");
                }
            }

            if (tableText.contains("总额：")) {
                Pattern totalPattern = Pattern.compile("总额：(￥[\\d,]+\\.\\d{2})");
                Matcher totalMatcher = totalPattern.matcher(tableText);
                if (totalMatcher.find()) {
                    additionalInfo.append("总额: ").append(totalMatcher.group(1)).append("\n");
                }
            }

            if (tableText.contains("合计人民币")) {
                Pattern chinesePattern = Pattern.compile("合计人民币（大写）：([^\\n]+)");
                Matcher chineseMatcher = chinesePattern.matcher(tableText);
                if (chineseMatcher.find()) {
                    additionalInfo.append("合计人民币（大写）: ").append(chineseMatcher.group(1).trim()).append("\n");
                }
            }

            data.setAdditionalInfo(additionalInfo.toString());

        } catch (Exception e) {
            log.warn("解析采购表格数据失败", e);
        }

        return data;
    }

    /**
     * 手动解析表格数据（当正则匹配失败时）
     */
    private PurchaseTableData parseManually(String tableText) {
        PurchaseTableData data = new PurchaseTableData();

        // 根据您提供的示例进行手动解析
        // "单位单价数量序号金额（元）商品名称吨40白砂糖粗砂糖￥1,000.00￥40,000.00"

        // 查找关键信息
        if (tableText.contains("白砂糖") && tableText.contains("￥1,000.00") && tableText.contains("￥40,000.00")) {
            List<String> headers = Arrays.asList("序号", "商品名称", "数量", "单位", "单价", "金额（元）");
            data.setHeaders(headers);

            List<String> row = new ArrayList<>();
            row.add("1"); // 序号
            row.add("白砂糖 粗砂糖"); // 商品名称
            row.add("40"); // 数量
            row.add("吨"); // 单位
            row.add("￥1,000.00"); // 单价
            row.add("￥40,000.00"); // 金额

            data.addDataRow(row);
        }

        return data;
    }

    /**
     * 采购表格数据结构
     */
    private static class PurchaseTableData {
        private List<String> headers = new ArrayList<>();
        private List<List<String>> dataRows = new ArrayList<>();
        private String additionalInfo = "";

        public boolean isValid() {
            return !headers.isEmpty() && !dataRows.isEmpty();
        }

        public boolean hasAdditionalInfo() {
            return additionalInfo != null && !additionalInfo.trim().isEmpty();
        }

        // Getters and setters
        public List<String> getHeaders() { return headers; }
        public void setHeaders(List<String> headers) { this.headers = headers; }
        public List<List<String>> getDataRows() { return dataRows; }
        public void addDataRow(List<String> row) { this.dataRows.add(row); }
        public String getAdditionalInfo() { return additionalInfo; }
        public void setAdditionalInfo(String additionalInfo) { this.additionalInfo = additionalInfo; }
    }

    /**
     * 表格行数据结构
     */
    private static class TableRow {
        private List<String> cells = new ArrayList<>();

        public void addCell(String cell) {
            cells.add(cell);
        }

        public List<String> getCells() {
            return cells;
        }
    }
}