package cn.tycoding.consts;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "aigc.ocr")
public class OcrAiProperties {
	/**
	 * RapidOCR模型路径
	 */
	private String modelPath = "models";

	/**
	 * PDF文档最大处理页数
	 */
	private int maxPdfPages = 10;

	/**
	 * OCR配置
	 */
	private OcrConfig ocr = new OcrConfig();


	@Data
	public static class OcrConfig {
		/**
		 * 识别阈值
		 */
		private float threshold = 0.5f;

		/**
		 * 是否启用GPU
		 */
		private boolean useGpu = false;

		/**
		 * 线程数
		 */
		private int numThread = 4;
	}

	
	/**
	 * 关键信息提取配置
	 */
	private KeywordConfig keywords = new KeywordConfig();

	@Data
	public static class KeywordConfig {
		/**
		 * 需要提取的关键字段列表
		 * 示例：["甲方", "乙方", "合同金额", "签订日期"]
		 */
		private List<String> contractFields = new ArrayList<>();
	}

	// 新增扫描PDF配置
	private ScannedPdfConfig scannedPdf = new ScannedPdfConfig();

	@Data
	public static class ScannedPdfConfig {
		/**
		 * OCR渲染DPI（影响识别质量和处理速度）
		 */
		private int renderDpi = 300;

		/**
		 * 是否启用图片预处理
		 */
		private boolean enablePreprocess = false;

		/**
		 * 并发处理页面数
		 */
		private int concurrentPages = 2;

		/**
		 * 单页OCR超时时间（秒）
		 */
		private int pageTimeout = 30;
	}
}