<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
  ~
  ~ Licensed under the GNU Affero General Public License, Version 3 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     https://www.gnu.org/licenses/agpl-3.0.html
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.tycoding</groupId>
        <artifactId>langchat-ai</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>langchat-ai-core</artifactId>
    <version>${revision}</version>

    <dependencies>
        <dependency>
            <groupId>cn.tycoding</groupId>
            <artifactId>langchat-common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.tycoding</groupId>
            <artifactId>langchat-common-ai</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.tycoding</groupId>
            <artifactId>langchat-ai-biz</artifactId>
        </dependency>

        <dependency>
            <groupId>dev.langchain4j</groupId>
            <artifactId>langchain4j</artifactId>
            <version>${langchain4j.version}</version>
        </dependency>
        <dependency>
            <groupId>dev.langchain4j</groupId>
            <artifactId>langchain4j-core</artifactId>
            <version>${langchain4j.version}</version>
        </dependency>
        <dependency>
            <groupId>dev.langchain4j</groupId>
            <artifactId>langchain4j-open-ai</artifactId>
            <version>${langchain4j.version}</version>
        </dependency>
        <dependency>
            <groupId>dev.langchain4j</groupId>
            <artifactId>langchain4j-ollama</artifactId>
            <version>${langchain4j.version}</version>
        </dependency>
        <dependency>
            <groupId>dev.langchain4j</groupId>
            <artifactId>langchain4j-community-qianfan</artifactId>
            <version>${langchain4j.version}</version>
        </dependency>
        <dependency>
            <groupId>dev.langchain4j</groupId>
            <artifactId>langchain4j-community-dashscope</artifactId>
            <version>${langchain4j.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-simple</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>dev.langchain4j</groupId>
            <artifactId>langchain4j-community-zhipu-ai</artifactId>
            <version>${langchain4j.version}</version>
        </dependency>
        <dependency>
            <groupId>dev.langchain4j</groupId>
            <artifactId>langchain4j-embeddings</artifactId>
            <version>${langchain4j.version}</version>
        </dependency>
        <dependency>
            <groupId>dev.langchain4j</groupId>
            <artifactId>langchain4j-community-redis</artifactId>
            <version>${langchain4j.version}</version>
        </dependency>
        <dependency>
            <groupId>dev.langchain4j</groupId>
            <artifactId>langchain4j-pgvector</artifactId>
            <version>${langchain4j.version}</version>
        </dependency>
        <dependency>
            <groupId>dev.langchain4j</groupId>
            <artifactId>langchain4j-milvus</artifactId>
            <version>${langchain4j.version}</version>
        </dependency>
        <dependency>
            <groupId>dev.langchain4j</groupId>
            <artifactId>langchain4j-embedding-store-filter-parser-sql</artifactId>
            <version>${langchain4j.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.jsqlparser</groupId>
                    <artifactId>jsqlparser</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>dev.langchain4j</groupId>
            <artifactId>langchain4j-document-parser-apache-tika</artifactId>
            <version>${langchain4j.version}</version>
        </dependency>
        <dependency>
            <groupId>com.agentsflex</groupId>
            <artifactId>agents-flex-core</artifactId>
            <version>1.1.2-snapshot</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.agentsflex</groupId>
            <artifactId>agents-flex-llm-ollama</artifactId>
            <version>1.1.2-snapshot</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.agentsflex</groupId>
            <artifactId>agents-flex-llm-siliconflow</artifactId>
            <version>1.1.2-snapshot</version>
        </dependency>

        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext7-core</artifactId>
            <version>7.2.5</version>
            <type>pom</type>
        </dependency>
        <dependency>
            <groupId>cn.tycoding</groupId>
            <artifactId>langchat-ai-biz</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>image-server</artifactId>
            <version>1.1.0</version>
        </dependency>
    </dependencies>
</project>
