/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.ai.core.provider.build;

import cn.tycoding.langchat.ai.biz.entity.AigcModel;
import com.agentsflex.core.llm.Llm;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.chat.StreamingChatLanguageModel;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.model.image.ImageModel;

/**
 * <AUTHOR>
 * @since 2024-08-18 09:57
 */
public interface ModelBuildHandler {

    /**
     * 判断是不是当前模型
     */
    boolean whetherCurrentModel(AigcModel model);

    /**
     * basic check
     */
    boolean basicCheck(AigcModel model);

    /**
     * streaming chat build
     */
    StreamingChatLanguageModel buildStreamingChat(AigcModel model);

    /**
     * chat build
     */
    ChatLanguageModel buildChatLanguageModel(AigcModel model);

    /**
     * embedding config
     */
    EmbeddingModel buildEmbedding(AigcModel model);

    /**
     * image config
     */
    ImageModel buildImage(AigcModel model);
    /**
     * 构建tinyflowLLm对象
     *
     * @param model 模型
     * @return com.agentsflex.core.llm.Llm
     * <AUTHOR>
     * @date 2025/5/6 14:49
     */
    Llm buildLlm(AigcModel model);
}
