package cn.tycoding.langchat.ai.core.service;

import cn.tycoding.langchat.ai.biz.dto.WorkflowRunningDto;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

/**
 * <AUTHOR>
 * @Description:
 * @date 2025年05月15日 11:00
 */
public interface WorkflowRunningService {
    /**
     * @Description:  流程运行
     * <AUTHOR>
     * @date 2025/5/15 11:10
     * @param runningDto
     * @return java.util.Map<java.lang.String,java.lang.Object>
     */
    Map<String,Object> workflowRunning(WorkflowRunningDto runningDto);
}
