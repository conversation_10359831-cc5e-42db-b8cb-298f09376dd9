/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.ai.core.consts;

/**
 * <AUTHOR>
 * @since 2024/6/6
 */
public interface EmbedConst {

    String ORIGIN_TYPE_INPUT = "INPUT";
    String ORIGIN_TYPE_UPLOAD = "UPLOAD";

    String KNOWLEDGE = "knowledgeId";
    String FILENAME = "docsName";

    String CLAZZ_NAME_OPENAI = "OpenAiEmbeddingModel";
    String CLAZZ_NAME_QIANFAN = "QianfanEmbeddingModel";
    String CLAZZ_NAME_QIANWEN = "QwenEmbeddingModel";
    String CLAZZ_NAME_ZHIPU = "ZhipuAiEmbeddingModel";
    String CLAZZ_NAME_OLLAMA = "OllamaEmbeddingModel";
}
