package cn.tycoding.langchat.ai.core.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.tycoding.langchat.ai.biz.dto.WorkflowRunningDto;
import cn.tycoding.langchat.ai.biz.entity.*;
import cn.tycoding.langchat.ai.biz.service.AigcModelService;
import cn.tycoding.langchat.ai.biz.service.AigcWebSearchEngineService;
import cn.tycoding.langchat.ai.biz.service.WorkflowService;
import cn.tycoding.langchat.ai.core.consts.AigcModelServiceEnum;
import cn.tycoding.langchat.ai.core.consts.WorkflowResultConst;
import cn.tycoding.langchat.ai.core.provider.EmbeddingProvider;
import cn.tycoding.langchat.ai.core.provider.WebSearchEngineFactory;
import cn.tycoding.langchat.ai.core.provider.build.ModelBuildHandler;
import cn.tycoding.langchat.ai.core.service.WorkflowRunningService;
import cn.tycoding.langchat.common.core.exception.ServiceException;
import cn.tycoding.langchat.common.oss.config.SpringFileStorageProperties;
import com.agentsflex.core.chain.Chain;
import com.agentsflex.core.chain.ChainException;
import com.agentsflex.core.document.Document;
import com.agentsflex.core.llm.Llm;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.element.Paragraph;
import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.store.embedding.EmbeddingSearchRequest;
import dev.langchain4j.store.embedding.EmbeddingSearchResult;
import dev.langchain4j.store.embedding.EmbeddingStore;
import dev.langchain4j.store.embedding.filter.Filter;
import dev.tinyflow.core.Tinyflow;
import dev.tinyflow.core.knowledge.Knowledge;
import dev.tinyflow.core.node.KnowledgeNode;
import dev.tinyflow.core.provider.KnowledgeProvider;
import dev.tinyflow.core.provider.LlmProvider;
import dev.tinyflow.core.provider.SearchEngineProvider;
import dev.tinyflow.core.searchengine.SearchEngine;
import lombok.extern.slf4j.Slf4j;
import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.*;

import static cn.tycoding.langchat.ai.core.consts.EmbedConst.KNOWLEDGE;
import static dev.langchain4j.store.embedding.filter.MetadataFilterBuilder.metadataKey;


/**
 * <AUTHOR>
 * @Description:
 * @date 2025年05月15日 11:00
 */
@Slf4j
@Service
public class WorkflowRunningServiceImpl implements WorkflowRunningService {

    private final AigcModelService aigcModelService;
    private final AigcWebSearchEngineService aigcWebSearchEngineService;
    private final WorkflowService workflowService;

    private final Map<String, ModelBuildHandler> modelBuildHandlers;

    private final EmbeddingProvider embeddingProvider;

    private final FileStorageService fileStorageService;

    private final SpringFileStorageProperties springFileStorageProperties;
    private final WebSearchEngineFactory webSearchEngineFactory;

    public WorkflowRunningServiceImpl(AigcModelService aigcModelService, AigcWebSearchEngineService aigcWebSearchEngineService, WorkflowService workflowService, Map<String, ModelBuildHandler> modelBuildHandlers, EmbeddingProvider embeddingProvider, FileStorageService fileStorageService, SpringFileStorageProperties springFileStorageProperties, WebSearchEngineFactory webSearchEngineFactory) {
        this.aigcModelService = aigcModelService;
        this.aigcWebSearchEngineService = aigcWebSearchEngineService;
        this.workflowService = workflowService;
        this.modelBuildHandlers = modelBuildHandlers;
        this.embeddingProvider = embeddingProvider;
        this.fileStorageService = fileStorageService;
        this.springFileStorageProperties = springFileStorageProperties;
        this.webSearchEngineFactory = webSearchEngineFactory;
    }

    @Override
    public Map<String,Object> workflowRunning(WorkflowRunningDto runningDto) {
        if (runningDto == null) {
            throw new ServiceException("参数不能为空!");
        }
        LambdaQueryWrapper<Workflow> queryWrapper = new LambdaQueryWrapper<>();

        if(runningDto.getId() == null){
            queryWrapper.eq(Workflow::getAppId,runningDto.getAppId());
        }else {
            queryWrapper.eq(Workflow::getId,runningDto.getId());
        }
        Workflow workflow = workflowService.getOne(queryWrapper);

        if (workflow == null) {
            throw new ServiceException("流程信息有误,请核对后重试!");
        }

        Tinyflow tinyflow = workflow.toTinyflow();
        tinyflow.setLlmProvider(new LlmProvider() {
            @Override
            public Llm getLlm(Object id) {
                AigcModel aigcModel = aigcModelService.getById(id.toString());
                return modelBuildHandlers.get(AigcModelServiceEnum.getModelServiceByCode(aigcModel.getProvider()))
                        .buildLlm(aigcModel);
            }
        });

        tinyflow.setSearchEngineProvider(new SearchEngineProvider() {
            @Override
            public SearchEngine getSearchEngine(Object id) {
                if (id == null) {
                    log.warn("搜索引擎ID为空");
                    return null;
                }

                try {
                    // 从数据库获取搜索引擎配置
                    AigcWebSearchEngine engineConfig = aigcWebSearchEngineService.getById(id.toString());
                    if (engineConfig == null) {
                        log.warn("未找到ID为{}的搜索引擎配置", id);
                        return null;
                    }
                    // 使用工厂创建搜索引擎实例
                    return webSearchEngineFactory.getWebSearchEngine(engineConfig.getId());
                } catch (Exception e) {
                    log.error("创建搜索引擎实例失败", e);
                    return null;
                }
            }
        });

        tinyflow.setKnowledgeProvider(new KnowledgeProvider() {
            @Override
            public Knowledge getKnowledge(Object o) {
                return new Knowledge() {
                    @Override
                    public List<Document> search(String keyword, int limit, KnowledgeNode knowledgeNode, Chain chain) {
                        AigcDocs aigcDocs = new AigcDocs();
                        aigcDocs.setKnowledgeId(o.toString());
                        aigcDocs.setContent(keyword);

                        List<Map<String, Object>> docMaps = embeddingSearch(aigcDocs);
                        List<Document> results = new ArrayList<>();
                        docMaps.forEach(docMap -> {
                            String content = (String) docMap.get("text");
                            String knowledgeId = (String) docMap.get("knowledgeId");
                            Document document = new Document();
                            document.setContent(content);
                            document.setId(knowledgeId);
                            document.addMetadata(docMap);
                            results.add(document);
                        });
                        return results;
                    }
                };
            }
        });
        Chain chain = tinyflow.toChain();
        try{
            Map<String,Object> variables = chain.executeForResult(runningDto.getVariables());
            Map<String,Object> params = runningDto.getVariables();
            if(ObjectUtil.isNotEmpty(params)){
                Object isResultPdf =  params.get(WorkflowResultConst.IS_RESULT_PDF);
                if(ObjectUtil.isNotEmpty(isResultPdf) && "true".equals(isResultPdf.toString())){
                    String url = generatePdf(variables.get("output").toString());
                    variables.put(WorkflowResultConst.RESULT_PARAMS,url);
                }
            }
            return variables;
        }catch (ChainException e){
            throw new ServiceException("服务器繁忙,请稍后重试!");
        }
    }
    /**
     * 生成 PDF 文件
     * @param content 文件内容
     * @return 文件路径
     */
    public String generatePdf(String content) {
        String filePath = springFileStorageProperties.getDefaultPath() + UUID.randomUUID() + ".pdf";
        try {
            PdfWriter writer = new PdfWriter(filePath);
            PdfDocument pdf = new PdfDocument(writer);
            com.itextpdf.layout.Document document = new com.itextpdf.layout.Document(pdf);
            PdfFont font = PdfFontFactory.createFont("STSongStd-Light", "UniGB-UCS2-H");
            Paragraph paragraph = new Paragraph(content).setFont(font);
            document.add(paragraph);
            document.close();
            FileInfo info = fileStorageService.of(new File(filePath))
                    .setPath(DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN))
                    .upload();
            AigcOss oss = BeanUtil.copyProperties(info, AigcOss.class);
            return oss.getUrl();
        } catch (Exception e) {
            throw new RuntimeException("生成 PDF 文件失败", e);
        }
    }


    /**
     *  知识库检索
     * <AUTHOR>
     * @date 2025/5/15 11:08
     * @param data
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     */
    private List<Map<String, Object>> embeddingSearch(AigcDocs data) {
        if (StrUtil.isBlank(data.getKnowledgeId()) || StrUtil.isBlank(data.getContent())) {
            return List.of();
        }

        EmbeddingModel embeddingModel = embeddingProvider.getEmbeddingModel(data.getKnowledgeId());
        EmbeddingStore<TextSegment> embeddingStore = embeddingProvider.getEmbeddingStore(data.getKnowledgeId());
        Embedding queryEmbedding = embeddingModel.embed(data.getContent()).content();
        Filter filter = metadataKey(KNOWLEDGE).isEqualTo(data.getKnowledgeId());
        EmbeddingSearchResult<TextSegment> list = embeddingStore.search(EmbeddingSearchRequest
                .builder()
                .queryEmbedding(queryEmbedding)
                .filter(filter)
                .build());

        List<Map<String, Object>> result = new ArrayList<>();
        list.matches().forEach(i -> {
            TextSegment embedded = i.embedded();
            Map<String, Object> map = embedded.metadata().toMap();
            map.put("text", embedded.text());
            result.add(map);
        });
        return result;
    }
}
