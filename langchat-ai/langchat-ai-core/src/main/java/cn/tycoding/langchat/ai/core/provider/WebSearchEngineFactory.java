package cn.tycoding.langchat.ai.core.provider;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.tycoding.langchat.ai.biz.entity.AigcWebSearchEngine;
import cn.tycoding.langchat.ai.biz.service.AigcWebSearchEngineService;
import cn.tycoding.langchat.ai.biz.searchEngine.SearchEngineStrategyManager;
import dev.tinyflow.core.searchengine.SearchEngine;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 搜索引擎工厂类
 * 负责创建和管理搜索引擎实例
 *
 * <AUTHOR>
 * @since 2024/6/18
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WebSearchEngineFactory {

    private final AigcWebSearchEngineService webSearchEngineService;
    private final SearchEngineStrategyManager strategyManager;

    /**
     * 搜索引擎缓存，key为搜索引擎ID，value为搜索引擎实例
     */
    private final Map<String, SearchEngine> engineCache = new ConcurrentHashMap<>();
    
    /**
     * 搜索引擎配置缓存，key为搜索引擎ID，value为搜索引擎配置
     */
    private final Map<String, AigcWebSearchEngine> configCache = new ConcurrentHashMap<>();

    /**
     * 初始化搜索引擎
     */
    @Async
    @PostConstruct
    public void init() {
        engineCache.clear();
        configCache.clear();
        
        try {
            List<AigcWebSearchEngine> engines = webSearchEngineService.list();
            if (engines != null) {
                engines.forEach(engine -> {
                    if (engine.getStatus() != null && engine.getStatus() == 1) {
                        try {
                            // 缓存配置
                            configCache.put(engine.getId(), engine);
                            
                            // 创建搜索引擎实例
                            SearchEngine searchEngine = buildSearchEngine(engine);
                            if (searchEngine != null) {
                                engineCache.put(engine.getId(), searchEngine);
                                log.info("成功初始化搜索引擎: {}, 提供商: {}", engine.getName(), engine.getProvider());
                            }
                        } catch (Exception e) {
                            log.error("初始化搜索引擎失败: {}, 提供商: {}, 错误: {}", engine.getName(), engine.getProvider(), e.getMessage(), e);
                        }
                    }
                });
            }
        } catch (Exception e) {
            log.error("初始化搜索引擎工厂失败", e);
        }
    }

    /**
     * 获取搜索引擎实例
     *
     * @param id 搜索引擎ID
     * @return 搜索引擎实例
     */
    public SearchEngine getWebSearchEngine(String id) {
        if (StrUtil.isBlank(id)) {
            return null;
        }
        
        // 先从缓存获取
        SearchEngine engine = engineCache.get(id);
        if (engine != null) {
            return engine;
        }
        
        // 缓存未命中，从数据库获取配置并创建实例
        try {
            AigcWebSearchEngine config = webSearchEngineService.getById(id);
            if (config == null) {
                log.warn("未找到ID为{}的搜索引擎配置", id);
                return null;
            }
            
            // 更新配置缓存
            configCache.put(id, config);
            
            // 创建搜索引擎实例
            engine = buildSearchEngine(config);
            if (engine != null) {
                // 更新实例缓存
                engineCache.put(id, engine);
                return engine;
            }
        } catch (Exception e) {
            log.error("创建搜索引擎实例失败, engineId: {}, 错误: {}", id, e.getMessage(), e);
        }
        
        return null;
    }
    
    /**
     * 根据配置构建搜索引擎实例
     *
     * @param config 搜索引擎配置
     * @return 搜索引擎实例
     */
    private SearchEngine buildSearchEngine(AigcWebSearchEngine config) {
        if (config == null || config.getStatus() == null || config.getStatus() != 1) {
            return null;
        }
        
        try {
            // 构建搜索引擎参数
            Map<String, Object> params = new HashMap<>();
            params.put("apiKey", config.getApiKey());
            params.put("baseUrl", config.getBaseUrl());
            params.put("searchEndpoint", config.getSearchEndpoint());
            params.put("maxResults", config.getMaxResults());
            params.put("timeoutSeconds", config.getTimeoutSeconds());
            
            // 处理额外参数
            if (StrUtil.isNotBlank(config.getAdditionalParams())) {
                try {
                    Map<String, Object> additionalParams = JSONUtil.toBean(config.getAdditionalParams(), Map.class);
                    params.putAll(additionalParams);
                } catch (Exception e) {
                    log.warn("解析搜索引擎额外参数失败: {}, {}", config.getName(), e.getMessage());
                }
            }
            
            // 使用策略管理器创建搜索引擎实例
            return strategyManager.buildSearchEngine(config.getProvider(), params);
        } catch (Exception e) {
            log.error("构建搜索引擎实例失败: {}, 提供商: {}, 错误: {}", 
                    config.getName(), config.getProvider(), e.getMessage(), e);
            return null;
        }
    }

}