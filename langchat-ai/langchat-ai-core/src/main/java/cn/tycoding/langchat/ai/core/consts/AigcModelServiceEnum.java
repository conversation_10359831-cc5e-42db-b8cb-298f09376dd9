package cn.tycoding.langchat.ai.core.consts;

import cn.tycoding.langchat.common.core.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Description:
 * @date 2025年05月06日 15:17
 */
@Getter
@AllArgsConstructor
public enum AigcModelServiceEnum {
    /**
     * 智谱AI
     */
    ZHIPU(ProviderEnum.ZHIPU.name(), ModelServiceConstant.ZHIPU),

    /**
     * 千帆
     */
    QIAN_FAN(ProviderEnum.Q_FAN.name(), ModelServiceConstant.Q_FAN),
    /**
     * 千问
     */
    Q_WEN(ProviderEnum.Q_WEN.name(), ModelServiceConstant.Q_WEN),
    /**
     * OLLAMA
     */
    OLLAMA(ProviderEnum.OLLAMA.name(), ModelServiceConstant.OLLAMA),
    /**
     * OPENAI
     */
    OPENAI(ProviderEnum.OPENAI.name(), ModelServiceConstant.OPENAI),
    DEEPSEEK(ProviderEnum.DEEPSEEK.name(), ModelServiceConstant.OPENAI),
    /**
     * 硅基流动
     */
    SILICON(ProviderEnum.SILICON.name(), ModelServiceConstant.OPENAI),
    SPARK(ProviderEnum.SPARK.name(), ModelServiceConstant.OPENAI),
    DOUYIN(ProviderEnum.DOUYIN.name(), ModelServiceConstant.OPENAI),
    AZURE_OPENAI(ProviderEnum.AZURE_OPENAI.name(), ModelServiceConstant.OPENAI),
    CLAUDE(ProviderEnum.CLAUDE.name(), ModelServiceConstant.OPENAI),
    GEMINI(ProviderEnum.GEMINI.name(), ModelServiceConstant.OPENAI),
    ;
    /**
     * 模型名称
     */
    private final String modelCode;
    /**
     * 模型接口服务
     */
    private final String modelService;


   public static String getModelServiceByCode(String modelCode){
       for(AigcModelServiceEnum modelServiceEnum : AigcModelServiceEnum.values()) {
           if(modelServiceEnum.getModelCode().equals(modelCode)) {
               return modelServiceEnum.getModelService();
           }
       }
       throw new ServiceException("未找到对应模型处理方法");
   }
}
