/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.ai.biz.mapper;

import cn.tycoding.langchat.ai.biz.entity.AigcModelVersion;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 模型版本Mapper
 * 
 * <AUTHOR>
 * @since 2024/12/17
 */
@Mapper
public interface AigcModelVersionMapper extends BaseMapper<AigcModelVersion> {

    /**
     * 根据供应商ID和分类ID查询模型版本列表
     */
    @Select("SELECT v.*, p.name as provider_name, p.code as provider_code, c.name as category_name, c.code as category_code " +
            "FROM aigc_model_version v " +
            "LEFT JOIN aigc_model_provider p ON v.provider_id = p.id " +
            "LEFT JOIN aigc_model_category c ON v.category_id = c.id " +
            "WHERE v.provider_id = #{providerId} AND v.category_id = #{categoryId} AND v.status = 1 " +
            "ORDER BY v.sort_order ASC")
    List<AigcModelVersion> selectByProviderAndCategory(String providerId, String categoryId);

    /**
     * 根据分类ID查询模型版本列表
     */
    @Select("SELECT v.*, p.name as provider_name, p.code as provider_code, c.name as category_name, c.code as category_code " +
            "FROM aigc_model_version v " +
            "LEFT JOIN aigc_model_provider p ON v.provider_id = p.id " +
            "LEFT JOIN aigc_model_category c ON v.category_id = c.id " +
            "WHERE v.category_id = #{categoryId} AND v.status = 1 " +
            "ORDER BY p.sort_order ASC, v.sort_order ASC")
    List<AigcModelVersion> selectByCategory(String categoryId);

    /**
     * 查询所有启用的模型版本（带关联信息）
     */
    @Select("SELECT v.*, p.name as provider_name, p.code as provider_code, c.name as category_name, c.code as category_code " +
            "FROM aigc_model_version v " +
            "LEFT JOIN aigc_model_provider p ON v.provider_id = p.id " +
            "LEFT JOIN aigc_model_category c ON v.category_id = c.id " +
            "WHERE v.status = 1 AND p.status = 1 AND c.status = 1 " +
            "ORDER BY c.sort_order ASC, p.sort_order ASC, v.sort_order ASC")
    List<AigcModelVersion> selectAllWithRelations();
}
