/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.ai.biz.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.tycoding.langchat.ai.biz.entity.AigcModelProvider;
import cn.tycoding.langchat.ai.biz.mapper.AigcModelProviderMapper;
import cn.tycoding.langchat.ai.biz.service.AigcModelProviderService;
import cn.tycoding.langchat.common.core.utils.MybatisUtil;
import cn.tycoding.langchat.common.core.utils.QueryPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 模型供应商服务实现
 * 
 * <AUTHOR>
 * @since 2024/12/17
 */
@Service
@RequiredArgsConstructor
public class AigcModelProviderServiceImpl extends ServiceImpl<AigcModelProviderMapper, AigcModelProvider> implements AigcModelProviderService {

    @Override
    public Page<AigcModelProvider> page(AigcModelProvider data, QueryPage queryPage) {
        return (Page<AigcModelProvider>) this.page(MybatisUtil.wrap(data, queryPage),
                Wrappers.<AigcModelProvider>lambdaQuery()
                        .like(StrUtil.isNotBlank(data.getName()), AigcModelProvider::getName, data.getName())
                        .eq(StrUtil.isNotBlank(data.getCode()), AigcModelProvider::getCode, data.getCode())
                        .eq(data.getStatus() != null, AigcModelProvider::getStatus, data.getStatus())
                        .orderByAsc(AigcModelProvider::getSortOrder)
                        .orderByDesc(AigcModelProvider::getCreateTime));
    }

    @Override
    public List<AigcModelProvider> list(AigcModelProvider data) {
        return this.list(Wrappers.<AigcModelProvider>lambdaQuery()
                .like(StrUtil.isNotBlank(data.getName()), AigcModelProvider::getName, data.getName())
                .eq(StrUtil.isNotBlank(data.getCode()), AigcModelProvider::getCode, data.getCode())
                .eq(data.getStatus() != null, AigcModelProvider::getStatus, data.getStatus())
                .orderByAsc(AigcModelProvider::getSortOrder)
                .orderByDesc(AigcModelProvider::getCreateTime));
    }

    @Override
    public AigcModelProvider getByCode(String code) {
        return this.getOne(Wrappers.<AigcModelProvider>lambdaQuery()
                .eq(AigcModelProvider::getCode, code));
    }

    @Override
    public List<AigcModelProvider> getEnabledProviders() {
        return this.list(Wrappers.<AigcModelProvider>lambdaQuery()
                .eq(AigcModelProvider::getStatus, true)
                .orderByAsc(AigcModelProvider::getSortOrder));
    }

    @Override
    public List<AigcModelProvider> getProvidersByModelType(String modelType) {
        // 根据条件查询并返回AigcModelProvider对象列表
        // 使用Lambda表达式构建查询条件
        return this.list(Wrappers.<AigcModelProvider>lambdaQuery()
                // 状态为true（有效或可用）
                .eq(AigcModelProvider::getStatus, true)
                // 使用JSON函数来匹配支持的模型类型
                .apply("JSON_CONTAINS(supported_model_types, JSON_QUOTE({0}))", modelType)
                // 按照排序顺序字段升序排序
                .orderByAsc(AigcModelProvider::getSortOrder));
    }
}
