/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.ai.biz.searchEngine;

import cn.hutool.core.util.StrUtil;
import cn.tycoding.langchat.ai.biz.searchEngine.strategy.SearchEngineStrategy;
import cn.tycoding.langchat.common.core.exception.ServiceException;
import dev.tinyflow.core.searchengine.SearchEngine;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 搜索引擎策略管理器
 * 负责管理所有搜索引擎策略的注册和获取
 *
 * <AUTHOR>
 * @since 2024/6/18
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SearchEngineStrategyManager {

    private final List<SearchEngineStrategy> strategies;
    private final Map<String, SearchEngineStrategy> strategyMap = new HashMap<>();

    /**
     * 初始化策略映射
     */
    @PostConstruct
    public void init() {
        for (SearchEngineStrategy strategy : strategies) {
            String provider = strategy.getProvider();
            if (StrUtil.isNotBlank(provider)) {
                strategyMap.put(provider.toLowerCase(), strategy);
                log.info("注册搜索引擎策略: {}", provider);
            }
        }
        log.info("搜索引擎策略管理器初始化完成，共注册 {} 个策略", strategyMap.size());
    }

    /**
     * 根据提供商获取策略
     *
     * @param provider 提供商类型
     * @return 搜索引擎策略
     */
    public SearchEngineStrategy getStrategy(String provider) {
        if (StrUtil.isBlank(provider)) {
            throw new ServiceException("搜索引擎提供商不能为空");
        }

        SearchEngineStrategy strategy = strategyMap.get(provider.toLowerCase());
        if (strategy == null) {
            throw new ServiceException("不支持的搜索引擎提供商: " + provider);
        }

        return strategy;
    }

    /**
     * 构建搜索引擎实例
     *
     * @param provider 提供商类型
     * @param params 构建参数
     * @return 搜索引擎实例
     */
    public SearchEngine buildSearchEngine(String provider, Map<String, Object> params) {
        SearchEngineStrategy strategy = getStrategy(provider);
        return strategy.buildSearchEngine(params);
    }

    /**
     * 验证参数是否有效
     *
     * @param provider 提供商类型
     * @param params 参数
     * @return 是否有效
     */
    public boolean validateParams(String provider, Map<String, Object> params) {
        try {
            SearchEngineStrategy strategy = getStrategy(provider);
            return strategy.validateParams(params);
        } catch (Exception e) {
            log.warn("验证搜索引擎参数失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取所有支持的提供商
     *
     * @return 提供商列表
     */
    public Set<String> getSupportedProviders() {
        return strategyMap.keySet();
    }

    /**
     * 检查是否支持指定提供商
     *
     * @param provider 提供商类型
     * @return 是否支持
     */
    public boolean isSupported(String provider) {
        return StrUtil.isNotBlank(provider) && strategyMap.containsKey(provider.toLowerCase());
    }
}
