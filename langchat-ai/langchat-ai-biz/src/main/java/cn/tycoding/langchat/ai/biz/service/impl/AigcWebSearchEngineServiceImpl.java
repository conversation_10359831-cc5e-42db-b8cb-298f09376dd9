/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.ai.biz.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.tycoding.langchat.ai.biz.entity.AigcWebSearchEngine;
import cn.tycoding.langchat.ai.biz.mapper.AigcWebSearchEngineMapper;
import cn.tycoding.langchat.ai.biz.service.AigcWebSearchEngineService;
import cn.tycoding.langchat.common.core.utils.MybatisUtil;
import cn.tycoding.langchat.common.core.utils.QueryPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 搜索引擎服务实现
 *
 * <AUTHOR>
 * @since 2024/6/16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AigcWebSearchEngineServiceImpl extends ServiceImpl<AigcWebSearchEngineMapper, AigcWebSearchEngine> implements AigcWebSearchEngineService {

    @Override
    public Page<AigcWebSearchEngine> page(AigcWebSearchEngine data, QueryPage queryPage) {
        return (Page<AigcWebSearchEngine>) this.page(MybatisUtil.wrap(data, queryPage),
                Wrappers.<AigcWebSearchEngine>lambdaQuery()
                        .like(StrUtil.isNotBlank(data.getName()), AigcWebSearchEngine::getName, data.getName())
                        .eq(StrUtil.isNotBlank(data.getProvider()), AigcWebSearchEngine::getProvider, data.getProvider())
                        .eq(data.getStatus() != null, AigcWebSearchEngine::getStatus, data.getStatus())
                        .orderByDesc(AigcWebSearchEngine::getCreateTime));
    }

    @Override
    public List<AigcWebSearchEngine> list(AigcWebSearchEngine data) {
        return this.list(Wrappers.<AigcWebSearchEngine>lambdaQuery()
                .like(StrUtil.isNotBlank(data.getName()), AigcWebSearchEngine::getName, data.getName())
                .eq(StrUtil.isNotBlank(data.getProvider()), AigcWebSearchEngine::getProvider, data.getProvider())
                .eq(data.getStatus() != null, AigcWebSearchEngine::getStatus, data.getStatus())
                .orderByDesc(AigcWebSearchEngine::getCreateTime));
    }

    @Override
    public List<AigcWebSearchEngine> getEnabledSearchEngines() {
        return this.list(Wrappers.<AigcWebSearchEngine>lambdaQuery()
                .eq(AigcWebSearchEngine::getStatus, 1)
                .orderByDesc(AigcWebSearchEngine::getCreateTime));
    }
}