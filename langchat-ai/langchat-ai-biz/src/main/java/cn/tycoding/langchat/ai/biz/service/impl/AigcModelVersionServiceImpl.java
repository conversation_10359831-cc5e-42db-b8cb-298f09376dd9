/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.ai.biz.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.tycoding.langchat.ai.biz.entity.AigcModelCategory;
import cn.tycoding.langchat.ai.biz.entity.AigcModelProvider;
import cn.tycoding.langchat.ai.biz.entity.AigcModelVersion;
import cn.tycoding.langchat.ai.biz.mapper.AigcModelVersionMapper;
import cn.tycoding.langchat.ai.biz.service.AigcModelCategoryService;
import cn.tycoding.langchat.ai.biz.service.AigcModelProviderService;
import cn.tycoding.langchat.ai.biz.service.AigcModelVersionService;
import cn.tycoding.langchat.common.core.utils.MybatisUtil;
import cn.tycoding.langchat.common.core.utils.QueryPage;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 模型版本服务实现
 * 
 * <AUTHOR>
 * @since 2024/12/17
 */
@Service
@RequiredArgsConstructor
public class AigcModelVersionServiceImpl extends ServiceImpl<AigcModelVersionMapper, AigcModelVersion> implements AigcModelVersionService {

    private final AigcModelProviderService providerService;
    private final AigcModelCategoryService categoryService;

    @Override
    public Page<AigcModelVersion> page(AigcModelVersion data, QueryPage queryPage) {
        Page<AigcModelVersion> page = (Page<AigcModelVersion>) this.page(MybatisUtil.wrap(data, queryPage),
                Wrappers.<AigcModelVersion>lambdaQuery()
                        .like(StrUtil.isNotBlank(data.getModelName()), AigcModelVersion::getModelName, data.getModelName())
                        .eq(StrUtil.isNotBlank(data.getModelCode()), AigcModelVersion::getModelCode, data.getModelCode())
                        .eq(StrUtil.isNotBlank(data.getProviderId()), AigcModelVersion::getProviderId, data.getProviderId())
                        .eq(StrUtil.isNotBlank(data.getCategoryId()), AigcModelVersion::getCategoryId, data.getCategoryId())
                        .eq(data.getStatus() != null, AigcModelVersion::getStatus, data.getStatus())
                        .orderByAsc(AigcModelVersion::getSortOrder)
                        .orderByDesc(AigcModelVersion::getCreateTime));

        // 填充关联对象
        fillRelations(page.getRecords());
        return page;
    }

    @Override
    public List<AigcModelVersion> list(AigcModelVersion data) {
        List<AigcModelVersion> list = this.list(Wrappers.<AigcModelVersion>lambdaQuery()
                .like(StrUtil.isNotBlank(data.getModelName()), AigcModelVersion::getModelName, data.getModelName())
                .eq(StrUtil.isNotBlank(data.getModelCode()), AigcModelVersion::getModelCode, data.getModelCode())
                .eq(StrUtil.isNotBlank(data.getProviderId()), AigcModelVersion::getProviderId, data.getProviderId())
                .eq(StrUtil.isNotBlank(data.getCategoryId()), AigcModelVersion::getCategoryId, data.getCategoryId())
                .eq(data.getStatus() != null, AigcModelVersion::getStatus, data.getStatus())
                .orderByAsc(AigcModelVersion::getSortOrder)
                .orderByDesc(AigcModelVersion::getCreateTime));
        
        // 填充关联对象
        fillRelations(list);
        return list;
    }

    @Override
    public List<AigcModelVersion> getByProviderAndCategory(String providerId, String categoryId) {
        return baseMapper.selectByProviderAndCategory(providerId, categoryId);
    }

    @Override
    public List<AigcModelVersion> getByCategory(String categoryId) {
        List<AigcModelVersion> modelVersions = baseMapper.selectByCategory(categoryId);
        fillRelations(modelVersions);
        return modelVersions;
    }

    @Override
    public List<AigcModelVersion> getByCategoryCode(String categoryCode) {
        AigcModelCategory category = categoryService.getByCode(categoryCode);
        if (category == null) {
            return List.of();
        }

        return getByCategory(category.getId());
    }

    @Override
    public List<AigcModelVersion> getAllWithRelations() {
        return baseMapper.selectAllWithRelations();
    }

    @Override
    public AigcModelVersion getByModelCode(String modelCode) {
        AigcModelVersion version = this.getOne(Wrappers.<AigcModelVersion>lambdaQuery()
                .eq(AigcModelVersion::getModelCode, modelCode));
        if (version != null) {
            fillRelations(List.of(version));
        }
        return version;
    }

    /**
     * 填充关联对象
     */
    private void fillRelations(List<AigcModelVersion> versions) {
        for (AigcModelVersion version : versions) {
            if (StrUtil.isNotBlank(version.getProviderId())) {
                AigcModelProvider provider = providerService.getById(version.getProviderId());
                version.setProvider(provider);
            }
            if (StrUtil.isNotBlank(version.getCategoryId())) {
                AigcModelCategory category = categoryService.getById(version.getCategoryId());
                version.setCategory(category);
            }
        }
    }
}
