/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.ai.biz.service;

import cn.tycoding.langchat.ai.biz.entity.AigcModelProvider;
import cn.tycoding.langchat.common.core.utils.QueryPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 模型供应商服务接口
 * 
 * <AUTHOR>
 * @since 2024/12/17
 */
public interface AigcModelProviderService extends IService<AigcModelProvider> {

    /**
     * 分页查询
     */
    Page<AigcModelProvider> page(AigcModelProvider data, QueryPage queryPage);

    /**
     * 列表查询
     */
    List<AigcModelProvider> list(AigcModelProvider data);

    /**
     * 根据编码查询
     */
    AigcModelProvider getByCode(String code);

    /**
     * 获取所有启用的供应商
     */
    List<AigcModelProvider> getEnabledProviders();

    /**
     * 获取支持指定模型类型的供应商
     */
    List<AigcModelProvider> getProvidersByModelType(String modelType);
}
