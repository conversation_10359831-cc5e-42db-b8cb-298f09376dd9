/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.ai.biz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 模型版本实体
 * 
 * <AUTHOR>
 * @since 2024/12/17
 */
@Data
@Accessors(chain = true)
@TableName(value = "aigc_model_version", autoResultMap = true)
public class AigcModelVersion implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 供应商ID
     */
    private String providerId;

    /**
     * 分类ID
     */
    private String categoryId;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 模型编码
     */
    private String modelCode;

    /**
     * 显示名称
     */
    private String displayName;

    /**
     * 模型描述
     */
    private String description;

    /**
     * 版本号
     */
    private String version;

    /**
     * 最大Token数
     */
    private Integer maxTokens;

    /**
     * 上下文窗口大小
     */
    private Integer contextWindow;

    /**
     * 输入价格(每1K tokens)
     */
    private BigDecimal inputPrice;

    /**
     * 输出价格(每1K tokens)
     */
    private BigDecimal outputPrice;

    /**
     * 是否支持流式输出
     */
    private Boolean supportsStreaming;

    /**
     * 是否支持函数调用
     */
    private Boolean supportsFunctionCall;

    /**
     * 是否支持视觉
     */
    private Boolean supportsVision;

    /**
     * 支持的图片尺寸(JSON数组)
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> imageSizes;

    /**
     * 支持的图片质量(JSON数组)
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> imageQualities;

    /**
     * 支持的图片风格(JSON数组)
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> imageStyles;

    /**
     * 向量维度(仅向量模型)
     */
    private Integer embeddingDimensions;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 状态 0-禁用 1-启用
     */
    private Boolean status;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    // 关联对象
    @TableField(exist = false)
    private AigcModelProvider provider;

    @TableField(exist = false)
    private AigcModelCategory category;
}
