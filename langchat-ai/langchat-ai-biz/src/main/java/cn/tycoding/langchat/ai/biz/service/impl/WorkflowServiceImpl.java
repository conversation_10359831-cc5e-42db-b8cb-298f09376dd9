package cn.tycoding.langchat.ai.biz.service.impl;


import cn.tycoding.langchat.ai.biz.entity.Workflow;
import cn.tycoding.langchat.ai.biz.mapper.WorkflowMapper;
import cn.tycoding.langchat.ai.biz.service.WorkflowService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class WorkflowServiceImpl extends ServiceImpl<WorkflowMapper, Workflow> implements WorkflowService {

}