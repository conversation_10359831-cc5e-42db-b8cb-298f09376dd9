package cn.tycoding.langchat.ai.biz.entity;

import com.agentsflex.core.util.StringUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import dev.tinyflow.core.Tinyflow;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@TableName("workflows")
public class Workflow implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 名称
     */
    private String name;
    /**
     * 应用ID
     */
    private String appId;
    /**
     * 工作流
     */
    private String graph;

    /**
     * 封面图片
     */
    private String cover;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 描述
     */
    private String description;
    public Tinyflow toTinyflow() {
        String jsonContent = this.getGraph();
        if (StringUtil.noText(jsonContent)) {
            return null;
        }
        return new Tinyflow(jsonContent);
    }

}