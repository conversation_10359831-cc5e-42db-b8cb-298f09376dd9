/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.ai.biz.searchEngine.strategy;

import dev.tinyflow.core.searchengine.SearchEngine;

import java.util.Map;

/**
 * 搜索引擎策略接口
 * 定义了搜索引擎构建的统一接口
 *
 * <AUTHOR>
 * @since 2024/6/18
 */
public interface SearchEngineStrategy {

    /**
     * 获取策略支持的提供商类型
     *
     * @return 提供商类型
     */
    String getProvider();

    /**
     * 构建搜索引擎实例
     *
     * @param params 构建参数
     * @return 搜索引擎实例
     */
    SearchEngine buildSearchEngine(Map<String, Object> params);

    /**
     * 验证参数是否有效
     *
     * @param params 参数
     * @return 是否有效
     */
    default boolean validateParams(Map<String, Object> params) {
        return params != null && !params.isEmpty();
    }
}
