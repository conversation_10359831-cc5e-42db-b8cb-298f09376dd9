/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.ai.biz.dto;

import cn.tycoding.langchat.ai.biz.entity.AigcModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 模型供应商DTO
 * 用于返回供应商及其关联的模型列表
 * 
 * <AUTHOR>
 * @since 2024/12/23
 */
@Data
@Accessors(chain = true)
public class ModelProviderDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 供应商代码
     */
    private String model;

    /**
     * 供应商名称
     */
    private String name;

    /**
     * 该供应商下的模型列表
     */
    private List<AigcModel> models;
}
