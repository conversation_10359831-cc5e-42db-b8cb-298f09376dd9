/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.ai.biz.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.tycoding.langchat.ai.biz.entity.AigcModelCategory;
import cn.tycoding.langchat.ai.biz.mapper.AigcModelCategoryMapper;
import cn.tycoding.langchat.ai.biz.service.AigcModelCategoryService;
import cn.tycoding.langchat.common.core.utils.MybatisUtil;
import cn.tycoding.langchat.common.core.utils.QueryPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 模型分类服务实现
 * 
 * <AUTHOR>
 * @since 2024/12/17
 */
@Service
@RequiredArgsConstructor
public class AigcModelCategoryServiceImpl extends ServiceImpl<AigcModelCategoryMapper, AigcModelCategory> implements AigcModelCategoryService {

    @Override
    public Page<AigcModelCategory> page(AigcModelCategory data, QueryPage queryPage) {
        return (Page<AigcModelCategory>) this.page(MybatisUtil.wrap(data, queryPage),
                Wrappers.<AigcModelCategory>lambdaQuery()
                        .like(StrUtil.isNotBlank(data.getName()), AigcModelCategory::getName, data.getName())
                        .eq(StrUtil.isNotBlank(data.getCode()), AigcModelCategory::getCode, data.getCode())
                        .eq(data.getStatus() != null, AigcModelCategory::getStatus, data.getStatus())
                        .orderByAsc(AigcModelCategory::getSortOrder)
                        .orderByDesc(AigcModelCategory::getCreateTime));
    }

    @Override
    public List<AigcModelCategory> list(AigcModelCategory data) {
        return this.list(Wrappers.<AigcModelCategory>lambdaQuery()
                .like(StrUtil.isNotBlank(data.getName()), AigcModelCategory::getName, data.getName())
                .eq(StrUtil.isNotBlank(data.getCode()), AigcModelCategory::getCode, data.getCode())
                .eq(data.getStatus() != null, AigcModelCategory::getStatus, data.getStatus())
                .orderByAsc(AigcModelCategory::getSortOrder)
                .orderByDesc(AigcModelCategory::getCreateTime));
    }

    @Override
    public AigcModelCategory getByCode(String code) {
        return this.getOne(Wrappers.<AigcModelCategory>lambdaQuery()
                .eq(AigcModelCategory::getCode, code));
    }

    @Override
    public List<AigcModelCategory> getEnabledCategories() {
        return this.list(Wrappers.<AigcModelCategory>lambdaQuery()
                .eq(AigcModelCategory::getStatus, true)
                .orderByAsc(AigcModelCategory::getSortOrder));
    }
}
