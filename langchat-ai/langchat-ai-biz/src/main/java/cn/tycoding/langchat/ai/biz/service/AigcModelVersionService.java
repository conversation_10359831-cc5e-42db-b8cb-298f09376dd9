/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.ai.biz.service;

import cn.tycoding.langchat.ai.biz.entity.AigcModelVersion;
import cn.tycoding.langchat.common.core.utils.QueryPage;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 模型版本服务接口
 * 
 * <AUTHOR>
 * @since 2024/12/17
 */
public interface AigcModelVersionService extends IService<AigcModelVersion> {

    /**
     * 分页查询
     */
    IPage<AigcModelVersion> page(AigcModelVersion data, QueryPage queryPage);

    /**
     * 列表查询
     */
    List<AigcModelVersion> list(AigcModelVersion data);

    /**
     * 根据供应商ID和分类ID查询模型版本列表
     */
    List<AigcModelVersion> getByProviderAndCategory(String providerId, String categoryId);

    /**
     * 根据分类ID查询模型版本列表
     */
    List<AigcModelVersion> getByCategory(String categoryId);

    /**
     * 根据分类编码查询模型版本列表
     */
    List<AigcModelVersion> getByCategoryCode(String categoryCode);

    /**
     * 查询所有启用的模型版本（带关联信息）
     */
    List<AigcModelVersion> getAllWithRelations();

    /**
     * 根据模型编码查询
     */
    AigcModelVersion getByModelCode(String modelCode);
}
