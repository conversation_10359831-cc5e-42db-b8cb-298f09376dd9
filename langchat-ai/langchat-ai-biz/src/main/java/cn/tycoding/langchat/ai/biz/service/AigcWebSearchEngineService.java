package cn.tycoding.langchat.ai.biz.service;

import cn.tycoding.langchat.ai.biz.entity.AigcWebSearchEngine;
import cn.tycoding.langchat.common.core.utils.QueryPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 搜索引擎服务接口
 *
 * <AUTHOR>
 * @since 2024/6/16
 */
public interface AigcWebSearchEngineService extends IService<AigcWebSearchEngine> {

    /**
     * 分页查询
     */
    Page<AigcWebSearchEngine> page(AigcWebSearchEngine data, QueryPage queryPage);

    /**
     * 列表查询
     */
    List<AigcWebSearchEngine> list(AigcWebSearchEngine data);

    /**
     * 获取所有启用的搜索引擎
     */
    List<AigcWebSearchEngine> getEnabledSearchEngines();

}