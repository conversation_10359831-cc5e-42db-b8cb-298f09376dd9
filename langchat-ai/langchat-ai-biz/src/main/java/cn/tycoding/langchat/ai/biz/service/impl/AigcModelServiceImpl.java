/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.ai.biz.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.tycoding.langchat.ai.biz.component.ModelTypeEnum;
import cn.tycoding.langchat.ai.biz.dto.ModelProviderDto;
import cn.tycoding.langchat.ai.biz.entity.AigcModel;
import cn.tycoding.langchat.ai.biz.entity.AigcModelProvider;
import cn.tycoding.langchat.ai.biz.entity.AigcModelVersion;
import cn.tycoding.langchat.ai.biz.mapper.AigcModelMapper;
import cn.tycoding.langchat.ai.biz.service.AigcModelService;
import cn.tycoding.langchat.ai.biz.service.AigcModelProviderService;
import cn.tycoding.langchat.ai.biz.service.AigcModelVersionService;
import cn.tycoding.langchat.common.core.utils.QueryPage;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/1/19
 */
@Service
@RequiredArgsConstructor
public class AigcModelServiceImpl extends ServiceImpl<AigcModelMapper, AigcModel> implements AigcModelService {

    private final AigcModelProviderService providerService;

    @Override
    public List<AigcModel> getChatModels() {
        List<AigcModel> list = baseMapper.selectList(Wrappers.<AigcModel>lambdaQuery()
                .eq(AigcModel::getType, ModelTypeEnum.CHAT.name()));
        list.forEach(this::hide);
        return list;
    }

    @Override
    public List<AigcModel> getImageModels() {
        List<AigcModel> list = baseMapper.selectList(Wrappers.<AigcModel>lambdaQuery()
                .eq(AigcModel::getType, ModelTypeEnum.TEXT_IMAGE.name()));
        list.forEach(this::hide);
        return list;
    }

    @Override
    public List<AigcModel> getEmbeddingModels() {
        List<AigcModel> list = baseMapper.selectList(Wrappers.<AigcModel>lambdaQuery()
                .eq(AigcModel::getType, ModelTypeEnum.EMBEDDING.name()));
        list.forEach(this::hide);
        return list;
    }

    @Override
    public List<AigcModel> getSearchModels() {
        List<AigcModel> list = baseMapper.selectList(Wrappers.<AigcModel>lambdaQuery()
                .eq(AigcModel::getType, ModelTypeEnum.WEB_SEARCH.name()));
        list.forEach(this::hide);
        return list;
    }

    @Override
    public List<AigcModel> list(AigcModel data) {
        List<AigcModel> list = this.list(Wrappers.<AigcModel>lambdaQuery()
                .eq(StrUtil.isNotBlank(data.getType()), AigcModel::getType, data.getType())
                .eq(StrUtil.isNotBlank(data.getProvider()), AigcModel::getProvider, data.getProvider()));
        list.forEach(this::hide);
        return list;
    }

    @Override
    public Page<AigcModel> page(AigcModel data, QueryPage queryPage) {
        Page<AigcModel> page = new Page<>(queryPage.getPage(), queryPage.getLimit());
        Page<AigcModel> iPage = this.page(page, Wrappers.<AigcModel>lambdaQuery().eq(AigcModel::getProvider, data.getProvider()));
        iPage.getRecords().forEach(this::hide);
        return iPage;
    }

    @Override
    public AigcModel selectById(String id) {
        AigcModel model = this.getById(id);
        hide(model);
        return model;
    }

    @Override
    public List<ModelProviderDto> getAvailableProvidersAndVersions(String modelType) {
        // 从AigcModel表中获取指定类型的已配置模型
        List<AigcModel> configuredModels = this.list(new LambdaQueryWrapper<AigcModel>()
                .eq(AigcModel::getType, modelType));

        if (configuredModels.isEmpty()) {
            return new ArrayList<>();
        }

        List<AigcModelProvider> modelProviders = providerService.list();

        // 按供应商分组
        Map<String, List<AigcModel>> providerModelMap = configuredModels.stream()
                .collect(Collectors.groupingBy(AigcModel::getProvider));

        List<ModelProviderDto> result = new ArrayList<>();
        for (AigcModelProvider  provider : modelProviders) {
            List<AigcModel> providerModels = providerModelMap.get(provider.getCode());

            // 检查供应商是否支持该模型类型
            if (provider.getSupportedModelTypes() != null &&
                    provider.getSupportedModelTypes().contains(modelType)) {

                // 创建供应商DTO
                ModelProviderDto providerDto = new ModelProviderDto()
                        .setModel(provider.getCode())
                        .setName(provider.getName())
                        .setModels(providerModels);

                result.add(providerDto);
            }
        }

        return result;
    }

    private void hide(AigcModel model) {
        if (model == null || StrUtil.isBlank(model.getApiKey())) {
            return;
        }
        String key = StrUtil.hide(model.getApiKey(), 3, model.getApiKey().length() - 4);
        model.setApiKey(key);

        if (StrUtil.isBlank(model.getSecretKey())) {
            return;
        }
        String sec = StrUtil.hide(model.getSecretKey(), 3, model.getSecretKey().length() - 4);
        model.setSecretKey(sec);
    }
}

