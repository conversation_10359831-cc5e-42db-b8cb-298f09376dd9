/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.tycoding.langchat.ai.biz.searchEngine.strategy.impl;

import cn.hutool.core.util.StrUtil;
import cn.tycoding.langchat.ai.biz.searchEngine.strategy.SearchEngineStrategy;
import cn.tycoding.langchat.common.core.exception.ServiceException;
import com.agentsflex.core.chain.Chain;
import com.agentsflex.core.document.Document;
import com.agentsflex.core.util.Maps;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import dev.tinyflow.core.node.SearchEngineNode;
import dev.tinyflow.core.searchengine.BaseSearchEngine;
import dev.tinyflow.core.searchengine.SearchEngine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 博查AI搜索引擎策略实现
 *
 * <AUTHOR>
 * @since 2024/6/18
 */
@Slf4j
@Component
public class BochaaiSearchEngine implements SearchEngineStrategy {

    private static final String PROVIDER = "BOCHAAI";
    private static final String DEFAULT_BASE_URL = "https://api.bochaai.com/v1/web-search";

    @Override
    public String getProvider() {
        return PROVIDER;
    }

    @Override
    public boolean validateParams(Map<String, Object> params) {
        if (!SearchEngineStrategy.super.validateParams(params)) {
            return false;
        }
        
        // 验证必需参数
        String apiKey = (String) params.get("apiKey");
        return StrUtil.isNotBlank(apiKey);
    }

    @Override
    public SearchEngine buildSearchEngine(Map<String, Object> params) {
        if (!validateParams(params)) {
            throw new ServiceException("博查AI搜索引擎参数验证失败：缺少必需的apiKey参数");
        }

        return new BaseSearchEngine() {
            @Override
            public List<Document> search(String keyword, int limit, SearchEngineNode searchEngineNode, Chain chain) {
                try {
                    // 设置API参数
                    String apiKey = params.get("apiKey").toString();
                    String baseUrl = params.getOrDefault("baseUrl", DEFAULT_BASE_URL).toString();

                    // 设置请求头
                    Map<String, String> headers = new HashMap<>();
                    headers.put("Authorization", "Bearer " + apiKey);
                    headers.put("Content-Type", "application/json");

                    // 设置请求体
                    String requestBody = Maps.of("query", keyword)
                            .set("summary", true)
                            .set("freshness", params.getOrDefault("freshness", "noLimit"))
                            .set("count", limit)
                            .set("stream", false)
                            .toJSON();

                    // 发送请求
                    String responseString = httpClient.post(baseUrl, headers, requestBody);
                    //String responseString = "{\"code\":200,\"log_id\":\"5c80be8242772f55\",\"msg\":null,\"data\":{\"_type\":\"SearchResponse\",\"queryContext\":{\"originalQuery\":\"热门景点推荐\"},\"webPages\":{\"webSearchUrl\":\"https://bochaai.com/search?q=热门景点推荐\",\"totalEstimatedMatches\":10000000,\"value\":[{\"id\":\"https://api.bochaai.com/v1/#WebPages.0\",\"name\":\"热门推荐 - 民宿之家——专业民宿预订平台\",\"url\":\"http://minsuzj.com/hot.html\",\"displayUrl\":\"http://minsuzj.com/hot.html\",\"snippet\":\"热门都市 浪漫海滨 古城古镇 推荐城镇 秀丽风光 名山大川 面朝大海 历史名城 亲子欢乐 佛道胜地 温泉养生 徒步骑车 民族风情\",\"summary\":\"热门都市 浪漫海滨 古城古镇 推荐城镇 秀丽风光 名山大川 面朝大海 历史名城 亲子欢乐 佛道胜地 温泉养生 徒步骑车 民族风情\",\"siteName\":\"minsuzj.com\",\"siteIcon\":\"https://th.bochaai.com/favicon?domain_url=http://minsuzj.com/hot.html\",\"datePublished\":\"2021-12-19T17:34:04+08:00\",\"dateLastCrawled\":\"2021-12-19T17:34:04Z\",\"cachedPageUrl\":null,\"language\":null,\"isFamilyFriendly\":null,\"isNavigational\":null},{\"id\":\"https://api.bochaai.com/v1/#WebPages.1\",\"name\":\"热门景点\",\"url\":\"https://www.yingkou.gov.cn/005/005002/005002002/aboutimage.html\",\"displayUrl\":\"https://www.yingkou.gov.cn/005/005002/005002002/aboutimage.html\",\"snippet\":\"热门景点\\n山海广场——月亮湖公园 辽宁团山国家级海洋公园 白沙湾黄金海岸 仙人岛 熊岳植物园 营口辽河老街 虹溪谷温泉旅游度假区 望儿山风景旅游区 青龙山公园 蟠龙山公园 金泰城海滨温泉旅游度假区 赤\",\"summary\":\"热门景点\\n山海广场——月亮湖公园 辽宁团山国家级海洋公园 白沙湾黄金海岸 仙人岛 熊岳植物园 营口辽河老街 虹溪谷温泉旅游度假区 望儿山风景旅游区 青龙山公园 蟠龙山公园 金泰城海滨温泉旅游度假区 赤山风景旅游区\",\"siteName\":\"营口市人民政府\",\"siteIcon\":\"https://th.bochaai.com/favicon?domain_url=https://www.yingkou.gov.cn/005/005002/005002002/aboutimage.html\",\"datePublished\":\"2024-11-04T13:38:20+08:00\",\"dateLastCrawled\":\"2024-11-04T13:38:20Z\",\"cachedPageUrl\":null,\"language\":null,\"isFamilyFriendly\":null,\"isNavigational\":null},{\"id\":\"https://api.bochaai.com/v1/#WebPages.2\",\"name\":\"景点推荐\",\"url\":\"http://gs.cnr.cn/gslv/tj/\",\"displayUrl\":\"http://gs.cnr.cn/gslv/tj/\",\"snippet\":\"中央人民广播电台 在线收听 本期热点:玉龙雪山 高原姑苏:丽江古城 情死之地:云衫坪 日期:2008-6 景点推荐 您现在的位置:首页>> 景点推荐 · 水族馆“赏樱花”5万条沙丁鱼表演落英缤纷 20\",\"summary\":\"中央人民广播电台 在线收听 本期热点:玉龙雪山 高原姑苏:丽江古城 情死之地:云衫坪 日期:2008-6 景点推荐 您现在的位置:首页>> 景点推荐 · 水族馆“赏樱花”5万条沙丁鱼表演落英缤纷 2015-03-31 · 看世界上最美丽的夜空 2014-11-07 · 最适合自由行的国家 2014-11-07 · 实拍大话西游取景地 2014-11-04 · 热气球之旅 2014-02-20 ·\",\"siteName\":\"央广网甘肃频道\",\"siteIcon\":\"https://th.bochaai.com/favicon?domain_url=http://gs.cnr.cn/gslv/tj/\",\"datePublished\":\"2012-08-10T14:59:40+08:00\",\"dateLastCrawled\":\"2012-08-10T14:59:40Z\",\"cachedPageUrl\":null,\"language\":null,\"isFamilyFriendly\":null,\"isNavigational\":null},{\"id\":\"https://api.bochaai.com/v1/#WebPages.3\",\"name\":\"热门旅游景点_旅游_新浪上海\",\"url\":\"http://shanghai.sina.com/citylink/en/c_db.shtml\",\"displayUrl\":\"http://shanghai.sina.com/citylink/en/c_db.shtml\",\"snippet\":\"热门旅游景点介绍,发现最美热点旅游目的地,包含十大名山、十大热门景点、十大峡谷、十大最美大学和中国十大旅游城市等排行榜,无法拒绝的旅途生活.\",\"summary\":\"热门旅游景点介绍,发现最美热点旅游目的地,包含十大名山、十大热门景点、十大峡谷、十大最美大学和中国十大旅游城市等排行榜,无法拒绝的旅途生活.\",\"siteName\":\"shanghai.sina.com\",\"siteIcon\":\"https://th.bochaai.com/favicon?domain_url=http://shanghai.sina.com/citylink/en/c_db.shtml\",\"datePublished\":\"2025-01-05T17:12:03+08:00\",\"dateLastCrawled\":\"2025-01-05T17:12:03Z\",\"cachedPageUrl\":null,\"language\":null,\"isFamilyFriendly\":null,\"isNavigational\":null},{\"id\":\"https://api.bochaai.com/v1/#WebPages.4\",\"name\":\"热门景点\",\"url\":\"http://www.cqcs.gov.cn/app/tszl/rmjd/index.html\",\"displayUrl\":\"http://www.cqcs.gov.cn/app/tszl/rmjd/index.html\",\"snippet\":\"热门景点\\n定慧寺\\n长寿古镇旅游风景区\\n长寿湖风景区\\n清迈良园\\n百里花海\\n长寿缆车\\n重庆钢铁工业旅游景区(国家3A级景区)\\n长寿湖乐温泉\\n三倒拐\\n醉美东山\\n黄草山\\n云龟山\",\"summary\":\"热门景点\\n定慧寺\\n长寿古镇旅游风景区\\n长寿湖风景区\\n清迈良园\\n百里花海\\n长寿缆车\\n重庆钢铁工业旅游景区(国家3A级景区)\\n长寿湖乐温泉\\n三倒拐\\n醉美东山\\n黄草山\\n云龟山\",\"siteName\":\"重庆市长寿区人民政府\",\"siteIcon\":\"https://th.bochaai.com/favicon?domain_url=http://www.cqcs.gov.cn/app/tszl/rmjd/index.html\",\"datePublished\":\"2009-02-14T15:31:30+08:00\",\"dateLastCrawled\":\"2009-02-14T15:31:30Z\",\"cachedPageUrl\":null,\"language\":null,\"isFamilyFriendly\":null,\"isNavigational\":null}],\"someResultsRemoved\":true},\"images\":{\"id\":null,\"readLink\":null,\"webSearchUrl\":null,\"value\":[{\"webSearchUrl\":null,\"name\":null,\"thumbnailUrl\":\"https://www.yingkou.gov.cn/yk/CommonDataPics/4fbcc8a5-5052-47f1-bc46-ad6dac051bfd/4fbcc8a5-5052-47f1-bc46-ad6dac051bfd228_173.jpg\",\"datePublished\":null,\"contentUrl\":\"https://www.yingkou.gov.cn/yk/CommonDataPics/4fbcc8a5-5052-47f1-bc46-ad6dac051bfd/4fbcc8a5-5052-47f1-bc46-ad6dac051bfd228_173.jpg\",\"hostPageUrl\":\"https://www.yingkou.gov.cn/005/005002/005002002/aboutimage.html\",\"contentSize\":null,\"encodingFormat\":null,\"hostPageDisplayUrl\":\"https://www.yingkou.gov.cn/005/005002/005002002/aboutimage.html\",\"width\":228,\"height\":173,\"thumbnail\":null},{\"webSearchUrl\":null,\"name\":null,\"thumbnailUrl\":\"https://www.yingkou.gov.cn/yk/CommonDataPics/5a51597b-ca07-4308-81cb-8a61bd64a7df/5a51597b-ca07-4308-81cb-8a61bd64a7df228_173.jpg\",\"datePublished\":null,\"contentUrl\":\"https://www.yingkou.gov.cn/yk/CommonDataPics/5a51597b-ca07-4308-81cb-8a61bd64a7df/5a51597b-ca07-4308-81cb-8a61bd64a7df228_173.jpg\",\"hostPageUrl\":\"https://www.yingkou.gov.cn/005/005002/005002002/aboutimage.html\",\"contentSize\":null,\"encodingFormat\":null,\"hostPageDisplayUrl\":\"https://www.yingkou.gov.cn/005/005002/005002002/aboutimage.html\",\"width\":228,\"height\":173,\"thumbnail\":null},{\"webSearchUrl\":null,\"name\":null,\"thumbnailUrl\":\"https://www.yingkou.gov.cn/yk/CommonDataPics/4e1d3fdf-0d7e-494d-9027-da9c543c3564/4e1d3fdf-0d7e-494d-9027-da9c543c3564228_173.jpg\",\"datePublished\":null,\"contentUrl\":\"https://www.yingkou.gov.cn/yk/CommonDataPics/4e1d3fdf-0d7e-494d-9027-da9c543c3564/4e1d3fdf-0d7e-494d-9027-da9c543c3564228_173.jpg\",\"hostPageUrl\":\"https://www.yingkou.gov.cn/005/005002/005002002/aboutimage.html\",\"contentSize\":null,\"encodingFormat\":null,\"hostPageDisplayUrl\":\"https://www.yingkou.gov.cn/005/005002/005002002/aboutimage.html\",\"width\":228,\"height\":173,\"thumbnail\":null},{\"webSearchUrl\":null,\"name\":null,\"thumbnailUrl\":\"http://www.cqcs.gov.cn/app/tszl/rmjd/W020240611377469964751_284.png\",\"datePublished\":null,\"contentUrl\":\"http://www.cqcs.gov.cn/app/tszl/rmjd/W020240611377469964751_284.png\",\"hostPageUrl\":\"http://www.cqcs.gov.cn/app/tszl/rmjd/index.html\",\"contentSize\":null,\"encodingFormat\":null,\"hostPageDisplayUrl\":\"http://www.cqcs.gov.cn/app/tszl/rmjd/index.html\",\"width\":284,\"height\":189,\"thumbnail\":null},{\"webSearchUrl\":null,\"name\":null,\"thumbnailUrl\":\"http://www.cqcs.gov.cn/app/tszl/rmjd/W020240104378298434299_284.png\",\"datePublished\":null,\"contentUrl\":\"http://www.cqcs.gov.cn/app/tszl/rmjd/W020240104378298434299_284.png\",\"hostPageUrl\":\"http://www.cqcs.gov.cn/app/tszl/rmjd/index.html\",\"contentSize\":null,\"encodingFormat\":null,\"hostPageDisplayUrl\":\"http://www.cqcs.gov.cn/app/tszl/rmjd/index.html\",\"width\":284,\"height\":230,\"thumbnail\":null}],\"isFamilyFriendly\":null},\"videos\":null}}";
                    log.info("博查AI搜索引擎搜索结果: {}", responseString);

                    // 解析响应
                    return parseResponse(responseString);

                } catch (Exception e) {
                    log.error("博查AI搜索引擎搜索失败: {}", e.getMessage(), e);
                    throw new ServiceException("搜索失败: " + e.getMessage());
                }
            }
        };
    }

    /**
     * 解析博查AI API响应
     */
    private List<Document> parseResponse(String responseString) {
        List<Document> results = new ArrayList<>();
        
        if (StrUtil.isBlank(responseString)) {
            return results;
        }

        try {
            JSONObject jsonObject = JSONObject.parseObject(responseString);
            JSONObject data = jsonObject.getJSONObject("data");
            if (data == null) {
                return results;
            }

            JSONObject webPages = data.getJSONObject("webPages");
            if (webPages == null) {
                return results;
            }

            JSONArray value = webPages.getJSONArray("value");
            if (value == null) {
                return results;
            }

            for (Object o : value) {
                JSONObject item = JSONObject.parseObject(o.toString());
                Document doc = new Document();
                
                doc.setId(item.getString("id"));
                doc.setContent(item.getString("snippet"));

                // 添加元数据
                doc.addMetadata("title", item.getString("name"));
                doc.addMetadata("url", item.getString("url"));
                doc.addMetadata("snippet", item.getString("snippet"));
                doc.addMetadata("summary", item.getString("summary"));
                doc.addMetadata("siteName", item.getString("siteName"));
                doc.addMetadata("source", PROVIDER);
                
                results.add(doc);
            }

        } catch (Exception e) {
            log.error("解析博查AI搜索结果失败: {}", e.getMessage(), e);
            throw new ServiceException("解析搜索结果失败: " + e.getMessage());
        }

        return results;
    }
}
