{"name": "langchat-ui", "version": "1.9.0", "author": {"name": "tycoding", "email": "<EMAIL>", "url": "https://github.com/tycoding"}, "private": true, "scripts": {"bootstrap": "pnpm install", "serve": "pnpm run dev", "dev": "vite", "build": "vite build && esno ./build/script/postBuild.ts", "build:no-cache": "pnpm clean:cache && npm run build", "report": "cross-env REPORT=true npm run build", "preview": "npm run build && vite preview", "preview:dist": "vite preview", "clean:cache": "rimraf node_modules/.cache/ && rimraf node_modules/.vite", "clean:lib": "rimraf node_modules", "build typecheck": "vuedx-typecheck . && vite build", "lint:eslint": "eslint \"{src}/**/*.{vue,ts,tsx}\" --fix", "lint:prettier": "prettier --write --loglevel warn \"src/**/*.{js,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged -c ./.husky/lintstagedrc.js", "lint:pretty": "pretty-quick --staged", "test prod gzip": "http-server dist --cors --gzip -c-1"}, "dependencies": {"@iconify/vue": "^4.1.1", "@tinyflow-ai/ui": "^0.1.5", "@tinyflow-ai/vue": "^0.1.5", "@traptitech/markdown-it-katex": "^3.6.0", "@types/uuid": "^9.0.2", "@vicons/antd": "^0.12.0", "@vicons/ionicons5": "^0.12.0", "@vueuse/core": "^9.13.0", "axios": "^1.4.0", "blueimp-md5": "^2.19.0", "date-fns": "^4.1.0", "echarts": "^5.4.3", "element-plus": "^2.9.9", "element-resize-detector": "^1.2.4", "highlight.js": "^11.9.0", "html2canvas": "^1.4.1", "jspdf": "^2.5.2", "lodash-es": "^4.17.21", "markdown-it": "^14.1.0", "markdown-it-link-attributes": "^4.0.1", "markmap-lib": "^0.18.9", "markmap-view": "^0.18.9", "mitt": "^3.0.1", "naive-ui": "^2.39.0", "pinia": "^2.1.6", "pnpm": "^10.14.0", "qs": "^6.11.2", "uuid": "^9.0.0", "vue": "^3.3.4", "vue-router": "^4.2.4", "vue-types": "^4.2.1", "vue3-tree-org": "^4.2.2"}, "devDependencies": {"@commitlint/cli": "^17.7.0", "@commitlint/config-conventional": "^17.7.0", "@types/lodash": "^4.14.197", "@types/node": "^18.17.4", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@vitejs/plugin-vue": "^3.2.0", "@vitejs/plugin-vue-jsx": "^2.1.1", "@vue/compiler-sfc": "^3.3.4", "@vue/eslint-config-typescript": "^11.0.3", "autoprefixer": "^10.4.14", "commitizen": "^4.3.0", "core-js": "^3.32.0", "cross-env": "^7.0.3", "crypto-js": "^4.1.1", "dotenv": "^16.3.1", "eslint": "^8.46.0", "eslint-config-prettier": "^8.10.0", "eslint-define-config": "1.12.0", "eslint-plugin-jest": "^27.2.3", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.17.0", "esno": "^0.16.3", "husky": "^8.0.3", "jest": "^29.6.2", "less": "^4.2.0", "less-loader": "^11.1.3", "lint-staged": "^13.2.3", "postcss": "^8.4.27", "prettier": "^2.8.8", "pretty-quick": "^3.1.3", "rimraf": "^3.0.2", "stylelint": "^14.16.1", "stylelint-config-prettier": "^9.0.5", "stylelint-config-standard": "^29.0.0", "stylelint-order": "^5.0.0", "stylelint-scss": "^4.7.0", "tailwindcss": "^3.3.3", "typescript": "^4.9.5", "unplugin-vue-components": "^0.22.12", "vite": "^3.2.7", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.0", "vite-plugin-style-import": "^2.0.0", "vue-demi": "^0.13.11", "vue-eslint-parser": "^9.3.1", "vuedraggable": "^4.1.0"}, "lint-staged": {"*.{vue,js,ts,tsx}": "eslint --fix"}, "config": {"commitizen": {"path": "./node_modules/cz-customizable"}}, "keywords": ["vue", "naive-ui", "vue3", "ts", "tsx", "admin", "typescript"], "engines": {"node": ">=22"}}